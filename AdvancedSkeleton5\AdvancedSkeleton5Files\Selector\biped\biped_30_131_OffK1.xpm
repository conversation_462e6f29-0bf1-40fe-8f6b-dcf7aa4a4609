/* XPM */
static char *biped_30_131_OffK1[] = {
/* columns rows colors chars-per-pixel */
"38 46 61 1",
"  c #CF7C7C",
". c #D07C7C",
"X c #D17D7D",
"o c #D37D7D",
"O c #D47D7E",
"+ c #D47E7E",
"@ c #D57E7E",
"# c #D67F7F",
"$ c #D77F7F",
"% c #D87F7F",
"& c gray75",
"* c #D88080",
"= c #DA8080",
"- c #DC8181",
"; c #DF8282",
": c #E18383",
"> c #E28484",
", c #E38484",
"< c #E58484",
"1 c #E68585",
"2 c #EC8787",
"3 c #EE8888",
"4 c #EF8989",
"5 c #FC8E8D",
"6 c #FF8F8F",
"7 c #FF9292",
"8 c #FF9392",
"9 c #FF9393",
"0 c #FF9494",
"q c #FF9595",
"w c #FF9696",
"e c #FF9797",
"r c #FF9898",
"t c #FF999A",
"y c #FF9B9B",
"u c #FF9C9B",
"i c #FF9C9C",
"p c #FF9C9D",
"a c #FF9D9D",
"s c #FF9E9E",
"d c #FF9F9E",
"f c #FF9F9F",
"g c #FFA2A2",
"h c #FFA2A3",
"j c #FFA3A3",
"k c #FFA4A5",
"l c #FFA5A5",
"z c #FFA5A6",
"x c #FFA6A5",
"c c #FFA6A6",
"v c #FFA6A7",
"b c #FFA7A6",
"n c #FFA7A7",
"m c #FFA7A8",
"M c #FFA8A7",
"N c #FFA8A8",
"B c #FFA8A9",
"V c #FFA9A9",
"C c #FFA9AA",
"Z c #FFAAA9",
"A c #FFAAAA",
/* pixels */
"&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAv&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAn&",
"&nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAn&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAv&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&vAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&fAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&aAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&wnAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&6nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&2nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&;kAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&@fAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&.aAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&X7nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&%:ynAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&,@3aAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&::@wnAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&",
"&,,X7nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAb&",
"&,,XwnAAAAAAAAAAAAAAAAAAAAAAAAAAAAAny&",
"&,,@aAAAAAAAAAAAAAAAAAAAAAAAAAAAAnaw,&",
"&,,@aAAAAAAAAAAAAAAAAAAAAAAAAAAbnw3X;&",
"&,:@yAAAAAAAAAAAAAAAAAAAAAAAAAAnw;@:,&",
"&:<.yAAAAAAAAAAAAAAAAAAAAAAAAAAy<=,,,&",
"&,<.wnAAAAAAAAAAAAAAAAAAAAAAAAAwX<,,,&",
"&,:.ybAAAAAAAAAAAAAAAAAAAAAAAAf6@:,,,&",
"&,,.wbAAAAAAAAAAAAAAAAAAAAAAAAa2*,,,:&",
"&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&"
};
