import maya.cmds as cmds
import re

# Atributos relevantes
attributes = ['translateX', 'translateY', 'translateZ', 'rotateX', 'rotateY', 'rotateZ']

def get_selected_range():
    time_range = cmds.timeControl('timeControl1', query=True, rangeArray=True)
    if not time_range:
        cmds.warning("Selecciona un rango en el TimeSlider.")
        return None
    return int(time_range[0]), int(time_range[1])

def find_symmetrical_pairs():
    all_nodes = cmds.ls(type='transform')
    pairs = []

    for node in all_nodes:
        match = re.search(r'(_|\.|-)R$', node)
        if match:
            left_node = re.sub(r'(_|\.|-)R$', r'\1L', node)
            if cmds.objExists(left_node):
                pairs.append((node, left_node))
    return pairs

def mirror_swap_without_inversion():
    time_range = get_selected_range()
    if not time_range:
        return

    start, end = time_range
    pairs = find_symmetrical_pairs()

    if not pairs:
        cmds.warning("No se encontraron pares R/L válidos.")
        return

    cmds.undoInfo(openChunk=True)
    try:
        for frame in range(start, end + 1):
            cmds.currentTime(frame, update=False)

            for r_ctrl, l_ctrl in pairs:
                for attr in attributes:
                    r_attr = f"{r_ctrl}.{attr}"
                    l_attr = f"{l_ctrl}.{attr}"

                    if not cmds.objExists(r_attr) or not cmds.objExists(l_attr):
                        continue

                    try:
                        r_val = cmds.getAttr(r_attr)
                        l_val = cmds.getAttr(l_attr)

                        # ⬅️➡️ Intercambio directo (sin invertir)
                        cmds.setAttr(r_attr, l_val)
                        cmds.setAttr(l_attr, r_val)

                        cmds.setKeyframe(r_attr, time=frame)
                        cmds.setKeyframe(l_attr, time=frame)
                    except Exception as e:
                        cmds.warning(f"Error intercambiando {r_attr} ↔ {l_attr}: {e}")
    finally:
        cmds.undoInfo(closeChunk=True)

    print(f"✅ Animación intercambiada simétricamente sin inversión entre {start} y {end}.")

mirror_swap_without_inversion()
