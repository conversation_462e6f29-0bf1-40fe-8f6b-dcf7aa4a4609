import maya.cmds as cmds

def redirect_selection():
    sel = cmds.ls(selection=True)
    if not sel:
        return
    # Evitar redirigir si pertenece al namespace "picker_biped"
    if ":" in sel[0]:
        ns = sel[0].split(":")[0]
        if ns == "picker_biped":
            return
    shapes = cmds.listRelatives(sel[0], shapes=True) or []
    for shape in shapes:
        if cmds.objectType(shape) == "nurbsCurve":
            parent = cmds.listRelatives(sel[0], parent=True)
            if parent:
                cmds.select(parent[0], replace=True)
            break

if cmds.optionVar(exists="toggleScriptJobID"):
    jobID = cmds.optionVar(q="toggleScriptJobID")
    cmds.scriptJob(kill=jobID, force=True)
    cmds.optionVar(remove="toggleScriptJobID")
    cmds.warning("ScriptJob desactivado.")
else:
    jobID = cmds.scriptJob(event=["SelectionChanged", redirect_selection], protected=True)
    cmds.optionVar(intValue=("toggleScriptJobID", jobID))
    cmds.warning("ScriptJob activado.")
