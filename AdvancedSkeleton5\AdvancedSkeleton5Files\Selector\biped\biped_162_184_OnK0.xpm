/* XPM */
static char *biped_162_184_OnK0[] = {
/* columns rows colors chars-per-pixel */
"18 31 33 1",
"  c #6DD044",
". c #6ED244",
"X c #6FD544",
"o c #70D944",
"O c #71DB44",
"+ c #71DD44",
"@ c #71DE44",
"# c #72DD44",
"$ c #72DF44",
"% c #73E244",
"& c #74E744",
"* c #75E844",
"= c #76EC44",
"- c #77F144",
"; c #7AF844",
": c #7AFB44",
"> c #7CFF44",
", c #7EFF44",
"< c #80FF44",
"1 c #83FF44",
"2 c #84FF44",
"3 c #85FF44",
"4 c #86FF44",
"5 c #87FF44",
"6 c #88FF44",
"7 c #89FF44",
"8 c #8AFF44",
"9 c #8BFF44",
"0 c #8CFF44",
"q c #8DFF44",
"w c #8EFF44",
"e c #8FFF44",
"r c gray75",
/* pixels */
"rrrrrrrrrrrrrrrrrr",
"r8eeeeeeeeeeeeee4r",
"r8eeeeeeeeeeeeee4r",
"r8eeeeeeeeeeeeee4r",
"r8eeeeeeeeeeeeee4r",
"r8eeeeeeeeeeeee8,r",
"r8eeeeeeeeeeeee8,r",
"r8eeeeeeeeeeeee8,r",
"reeeeeeeeeeeeee8:r",
"r8eeeeeeeeeeeee8-r",
"r8eeeeeeeeeeeee8=r",
"r8eeeeeeeeeeeee8&r",
"reeeeeeeeeeeeee4&r",
"r8eeeeeeeeeeeee8&r",
"reeeeeeeeeeeeee4&r",
"r8eeeeeeeeeeeee4%r",
"r8eeeeeeeeeeeee4$r",
"r8eeeeeeeeeeeee4$r",
"r8eeeeeeeeeeeee8$r",
"r8eeeeeeeeeeeee4$r",
"reeeeeeeeeeeeee4Or",
"r8eeeeeeeeeeeee4$r",
"r8eeeeeeeeeeeee8Or",
"r8eeeeeeeeeeeee4$r",
"r8eeeeeeeeeeeee4Or",
"r8eeeeeeeeeeeee4Or",
"r8eeeeeeeeeeeee4.r",
"r8eeeeeeeeeeee8, r",
"r4eeeeeeeeeeee8: r",
"r4eeeeeeeeeeee4&Or",
"rrrrrrrrrrrrrrrrrr"
};
