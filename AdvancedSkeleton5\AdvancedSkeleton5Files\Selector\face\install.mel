string $currentShelf = `tabLayout -query -selectTab $gShelfTopLevel`;
setParent $currentShelf;
string $asInstallScriptLocation=`asInstallScriptLocation`;
string $scriptName="face";
string $sourceFile=$asInstallScriptLocation+"../"+$scriptName+".mel";
string $command="source \""+$sourceFile+"\"";
string $iconExt="png";
if (`asMayaVersionAsFloat`<2012)
	$iconExt="xpm";
string $icon=$asInstallScriptLocation+"face_background32.png";
if (!`file -q -ex $sourceFile`)
	error ("Something went wrong, can not find: \""+$sourceFile+"\"");
shelfButton
	-command $command
	-annotation $scriptName
	-label $scriptName
	-image $icon
	-image1 $icon
	-sourceType "mel"
;
print ("\n// "+$scriptName+" has been added to current shelf.\n");

global proc asInstallScriptLocator (){}

global proc string asInstallScriptLocation ()
{
string $whatIs=`whatIs asInstallScriptLocator`;
string $fullPath=`substring $whatIs 25 999`;
string $buffer[];
string $slash="/";
if (`gmatch $whatIs "*\\\\*"`)//sourced from ScriptEditor
	$slash="\\";
int $numTok=`tokenize $fullPath $slash $buffer`;
int $numLetters=size($fullPath);
int $numLettersLastFolder=size($buffer[$numTok-1]);
string $scriptLocation=`substring $fullPath 1 ($numLetters-$numLettersLastFolder)`;
return $scriptLocation;
}

global proc float asMayaVersionAsFloat ()
{
float $version=2012;
if (`exists getApplicationVersionAsFloat`)
	return `getApplicationVersionAsFloat`;
string $versionString=`about -v`;
string $tempString[];
string $char;
tokenize $versionString $tempString;
//default to 2012, if versionString is not all numbers
for ($i=0;$i<size($tempString[0]);$i++)
	{
	$char=`substring $tempString[0] ($i+1) ($i+1)`;
	if (!`gmatch $char "[0-9]"`)
		return 2012;
	}
$version=$tempString[0];
return $version;
}