/* XPM */
static char *biped_188_13_OffK1[] = {
/* columns rows colors chars-per-pixel */
"15 15 40 1",
"  c #4444AA",
". c #5044A5",
"X c #5145A6",
"o c #5344A4",
"O c #5444A4",
"+ c #5747A4",
"@ c #6046A0",
"# c #6147A0",
"$ c #965891",
"% c #9A6294",
"& c #B0608A",
"* c #B1618A",
"= c #B2618A",
"- c #B3628A",
"; c #B56289",
": c #B66389",
"> c #B76B8C",
", c #B86E8D",
"< c #B06F90",
"1 c #B16F90",
"2 c #B4708F",
"3 c #B5708F",
"4 c #B6708E",
"5 c #BF718B",
"6 c #BC708C",
"7 c #BD708C",
"8 c #B27090",
"9 c #C0718B",
"0 c #D67985",
"q c #D67D87",
"w c #DA7C85",
"e c #DB7C85",
"r c gray75",
"t c #DA8086",
"y c #DA8087",
"u c #DB8086",
"i c #DD8185",
"p c #DC8086",
"a c #DE8185",
"s c #E38484",
/* pixels */
"rrrrrrrrrrrrrrr",
"rssssu3+8ussssr",
"rssssu< <ussssr",
"rssssu< 8wssssr",
"rssssu8 8issssr",
"riisiq< <qiiiir",
"r9,,6>% %>5,69r",
"r@o+.+. .o++.#r",
"r;====$ $&===;r",
"reeew0> >0wwwwr",
"rssssu3 3ussssr",
"rssssu8 8ussssr",
"rssssw8 <ussssr",
"rssssu8.2ussssr",
"rrrrrrrrrrrrrrr"
};
