/* XPM */
static char *asFace[] = {
/* columns rows colors chars-per-pixel */
"32 32 156 2",
"   c #040404",
".  c gray2",
"X  c gray4",
"o  c #101010",
"O  c #111111",
"+  c gray8",
"@  c gray10",
"#  c #202020",
"$  c #242323",
"%  c gray14",
"&  c #252525",
"*  c #252528",
"=  c #25272A",
"-  c #24282A",
";  c gray16",
":  c gray17",
">  c #2C2B2B",
",  c gray20",
"<  c #313535",
"1  c #353535",
"2  c #363435",
"3  c #373736",
"4  c #3C3C3C",
"5  c gray24",
"6  c #3E3E3E",
"7  c #434344",
"8  c #4F4A42",
"9  c gray29",
"0  c gray31",
"q  c #545049",
"w  c #5A554F",
"e  c #505050",
"r  c gray32",
"t  c #535353",
"y  c gray33",
"u  c gray34",
"i  c #5D5550",
"p  c gray36",
"a  c #5D5D5D",
"s  c #66615C",
"d  c #6A635E",
"f  c #606060",
"g  c #626262",
"h  c #696360",
"j  c #6B6661",
"k  c DimGray",
"l  c #6F6F6F",
"z  c gray44",
"x  c #747071",
"c  c #747373",
"v  c #757373",
"b  c #737374",
"n  c #747474",
"m  c #757574",
"M  c #747475",
"N  c gray46",
"B  c #767677",
"V  c #777777",
"C  c #797979",
"Z  c #7B7A7A",
"A  c #7B7B7B",
"S  c #7F7F7F",
"D  c gray51",
"F  c #838383",
"G  c #848280",
"H  c #848484",
"J  c gray52",
"K  c #868686",
"L  c gray53",
"P  c #898786",
"I  c #8D8B87",
"U  c #929292",
"Y  c #989391",
"T  c #989898",
"R  c #9A9A9A",
"E  c #9B9B9B",
"W  c #9D9D9D",
"Q  c #A2A2A2",
"!  c #A3A3A2",
"~  c gray64",
"^  c #A5A2A0",
"/  c #A4A4A4",
"(  c #A5A5A5",
")  c gray65",
"_  c #A8A5A5",
"`  c #ABA8A6",
"'  c #A9A9A9",
"]  c gray67",
"[  c #ACACAC",
"{  c gray68",
"}  c #AEAEAE",
"|  c #AFAFAF",
" . c #B2B2B2",
".. c gray70",
"X. c gray71",
"o. c #B6B6B6",
"O. c #B8B7B7",
"+. c gray72",
"@. c gray74",
"#. c gray",
"$. c gray76",
"%. c #C5C5C5",
"&. c gray78",
"*. c #CACACA",
"=. c #CBCBCB",
"-. c gray80",
";. c #CDCDCC",
":. c #CDCDCD",
">. c #CECECE",
",. c #D0D0D0",
"<. c gray82",
"1. c LightGray",
"2. c #D4D3D1",
"3. c #D6D5D3",
"4. c gray83",
"5. c #D5D5D5",
"6. c #D7D6D6",
"7. c #D8D8D8",
"8. c #DADADA",
"9. c gray86",
"0. c #DDDDDD",
"q. c #E0E1E1",
"w. c #E0E1E2",
"e. c #E2E2E2",
"r. c #E4E2E2",
"t. c #E4E4E4",
"y. c gray90",
"u. c #E6E5E4",
"i. c #E4E5E6",
"p. c #E5E6E7",
"a. c #E6E6E6",
"s. c #E7E6E6",
"d. c #E7E7E7",
"f. c #E8E7E6",
"g. c gray91",
"h. c #E9E9E9",
"j. c #EAEAEA",
"k. c gray93",
"l. c #EFEFEF",
"z. c #F0EFF0",
"x. c gray94",
"c. c gray95",
"v. c gray96",
"b. c #F6F6F6",
"n. c gray97",
"m. c #F8F8F8",
"M. c #F9F9F9",
"N. c #F9F9FA",
"B. c gray98",
"V. c #FBFBFB",
"C. c gray99",
"Z. c #FDFDFD",
"A. c #FEFDFD",
"S. c #FEFEFE",
"D. c gray100",
"F. c None",
/* pixels */
"F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.F.F.5 k / +.T a F.F.F.F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.u X.C.C.C.C.C.C.C.] 5 F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.L C.C.B.n.c.n.n.n.B.C.l.2 F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.t C.C.n.n.n.n.n.c.n.n.n.C.c.@ F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.{ C.n.n.n.c.n.n.n.n.n.n.B.C.W F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.B.C.n.B.n.n.n.n.n.B.n.n.n.C.&.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.t C.C.C.C.C.C.B.v.n.C.C.C.C.C.6.o F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.g C.^ n n n P d.B.d.G v n v ` f.$ F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.7 C.I h d i q 2.C.3.8 w d h Y 9.+ F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.a C.l.n , 2 :.B.C.C.@.2 > Z l.d.9 F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.& / B.e.n < = { h.h.r._ - & z d.n.T   F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.6 #.C.C.w.h.C.$.E +.C.p.e.C.C.*.o F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.y 3.C.n.C.C.C.@.X.~ C.C.C.n.C.1.# F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.e C.n.n.m.m.d.3.n.+.d.B.n.n.C.c.& F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.Z B.B.n.C.{ T 7.n / C.n.B.n.> F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.9.C.n.B.B.*.d.*.B.C.n.C.:.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.:.C.n.c.+.] ../ @.c.m.C.t F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.5 B.C.7.Z Z L n A 9.C.:.X F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.g j.C.,.L U L 3.C.0.: F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.F.A +.C.C.l.C.C.{ a   F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.F.F.z L 7.h.:.n l D F.F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.F.F.F./ L L A ] Z F.F.F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.",
"F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F.F."
};
