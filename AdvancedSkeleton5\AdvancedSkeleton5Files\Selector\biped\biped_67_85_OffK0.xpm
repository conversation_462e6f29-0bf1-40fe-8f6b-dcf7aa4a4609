/* XPM */
static char *biped_67_85_OffK0[] = {
/* columns rows colors chars-per-pixel */
"10 13 72 1",
"  c gray55",
". c gray56",
"X c #939393",
"o c gray58",
"O c #959595",
"+ c #979797",
"@ c #9A9A9A",
"# c #9B9B9B",
"$ c #9D9D9D",
"% c gray62",
"& c #A3A2A2",
"* c #A6A6A5",
"= c gray67",
"- c #B1B1B1",
"; c #B6B7B6",
": c #B7B7B7",
"> c #BCBDBC",
", c gray74",
"< c gray75",
"1 c #C5C5C5",
"2 c #C5C5C6",
"3 c #C6C7C6",
"4 c #CBCBCB",
"5 c #CECECE",
"6 c #CECECF",
"7 c #D4D2D2",
"8 c gray83",
"9 c #D6D5D6",
"0 c #D6D7D7",
"q c #D8D7D7",
"w c #DAD9D9",
"e c #E2E1E2",
"r c #E3E3E2",
"t c #E7E7E7",
"y c gray91",
"u c #EDECED",
"i c #EDEEED",
"p c #EFEEEF",
"a c #EFEFF0",
"s c #F0EFF0",
"d c #F5F4F5",
"f c #F6F5F6",
"g c #F6F6F7",
"h c #F9F7F8",
"j c #F9F9FA",
"k c #FAF9FA",
"l c #FBFBFB",
"z c #FCFBFB",
"x c #FCFBFC",
"c c #FDFDFC",
"v c #FDFDFD",
"b c #FDFDFE",
"n c #FDFDFF",
"m c #FDFEFC",
"M c #FDFEFD",
"N c #FDFEFE",
"B c #FFFDFD",
"V c #FEFCFF",
"C c #FEFDFE",
"Z c #FEFDFF",
"A c #FFFCFE",
"S c #FFFCFF",
"D c #FFFDFE",
"F c #FEFEFC",
"G c #FEFFFC",
"H c #FEFFFD",
"J c #FFFEFD",
"K c #FFFFFC",
"L c #FEFEFE",
"P c #FEFFFE",
"I c #FFFFFE",
"U c gray100",
/* pixels */
"<<<<<<<<<<",
"<%#X  .X#<",
"<#o#;<-&+<",
"<o%<tue4=<",
"<o:yzLVs1<",
"<*7lLLVd5<",
"<1pLLLVh7<",
"<0hLLLLlq<",
"<rLLLzLzw<",
"<uLLzzLl0<",
"<gLLzLLg5<",
"<zLzzLLp1<",
"<<<<<<<<<<"
};
