import maya.cmds as cmds
from importlib import reload # For easy reloading if you save this as a module

# Exported functions
__all__ = ['copy_attributes_selection_pipeline', 'copy_translateZ_selection']


def copy_translate_attrs(main_src, main_dst, attrs, do_comp=False, ik_list_for_comp=None, start=None, end=None):
    if start is None or end is None:
        start = int(cmds.playbackOptions(q=True, minTime=True))
        end = int(cmds.playbackOptions(q=True, maxTime=True))

    cmds.undoInfo(openChunk=True)
    try:
        for f in range(start, end + 1):
            cmds.currentTime(f)

            if do_comp and ik_list_for_comp is not None and any(attr in attrs for attr in ['translateZ', 'translateY']):
                val_hip = cmds.getAttr(f"{main_src}.translate")[0]
                val_hip_ty_original_local = val_hip[1]
                val_hip_tz_original_local = val_hip[2]

                hip_original_world_pos = cmds.xform(main_src, q=True, ws=True, t=True)
                ik_original_world_pos_map = {
                    ik_ctrl: cmds.xform(ik_ctrl, q=True, ws=True, t=True)
                    for ik_ctrl in ik_list_for_comp
                }

                # Aplicar a root: transferir Y y/o Z desde hip
                if 'translateY' in attrs:
                    cmds.setAttr(f"{main_dst}.translateY", val_hip_ty_original_local)
                    cmds.setKeyframe(main_dst, attribute='translateY', t=f, value=val_hip_ty_original_local)

                if 'translateZ' in attrs:
                    cmds.setAttr(f"{main_dst}.translateZ", val_hip_tz_original_local)
                    cmds.setKeyframe(main_dst, attribute='translateZ', t=f, value=val_hip_tz_original_local)

                # Restaurar posición original world-space de hip
                cmds.xform(main_src, ws=True, t=hip_original_world_pos)
                new_hip_local_translates = cmds.getAttr(f"{main_src}.translate")[0]
                cmds.setKeyframe(main_src, attribute='translateX', t=f, value=new_hip_local_translates[0])
                cmds.setKeyframe(main_src, attribute='translateY', t=f, value=new_hip_local_translates[1])
                cmds.setKeyframe(main_src, attribute='translateZ', t=f, value=new_hip_local_translates[2])

                # Restaurar IKs
                for ik_ctrl in ik_list_for_comp:
                    cmds.xform(ik_ctrl, ws=True, t=ik_original_world_pos_map[ik_ctrl])
                    new_ik_local_translates = cmds.getAttr(f"{ik_ctrl}.translate")[0]
                    cmds.setKeyframe(ik_ctrl, attribute='translateX', t=f, value=new_ik_local_translates[0])
                    cmds.setKeyframe(ik_ctrl, attribute='translateY', t=f, value=new_ik_local_translates[1])
                    cmds.setKeyframe(ik_ctrl, attribute='translateZ', t=f, value=new_ik_local_translates[2])

            # Copia estándar para cualquier atributo restante
            for attr in attrs:
                if do_comp and attr in ['translateZ', 'translateY'] and ik_list_for_comp is not None:
                    continue
                val = cmds.getAttr(f"{main_src}.{attr}")
                cmds.setAttr(f"{main_dst}.{attr}", val)
                cmds.setKeyframe(main_dst, attribute=attr, t=f, value=val)

    finally:
        cmds.undoInfo(closeChunk=True)
        cmds.currentTime(start)


def copy_attributes_selection_pipeline():
    win = 'copyAttrWin'
    if cmds.window(win, exists=True):
        cmds.deleteUI(win)
    
    cmds.window(win, title='Copy Attributes', sizeable=False, widthHeight=(320, 400)) 

    main_scroll_layout = cmds.scrollLayout(horizontalScrollBarThickness=16, verticalScrollBarThickness=16, childResizable=True)
    form = cmds.formLayout(parent=main_scroll_layout, numberOfDivisions=100)

    instructions_text = cmds.text(
        label=(
            "Orden de Selección:\n"
            "1. Copia Estándar (sin compensación):\n"
            "   - Origen\n"
            "   - Destino\n"
            "   (Total: 2 objetos)\n\n"
            "2. Con Compensación Y/Z:\n"
            "   - Controlador Cadera (Hip)\n"
            "   - Controlador Raíz (Root)\n"
            "   - Control IK 1 [IK 2, etc.]\n"
            "   (Mínimo: 3 objetos)"
        ),
        align='left',
        ww=True,
        height=120
    )

    cmds.formLayout(form, edit=True,
                    attachForm=[(instructions_text, 'top', 5),
                                (instructions_text, 'left', 5),
                                (instructions_text, 'right', 5)])

    axes = ['translateX', 'translateY', 'translateZ', 'rotateX', 'rotateY', 'rotateZ']
    cbs = {}
    last_ui_element = instructions_text

    for ax in axes:
        cbs[ax] = cmds.checkBox(label=ax, value=(ax in ['translateZ']))
        cmds.formLayout(form, edit=True, attachControl=(cbs[ax], 'top', 5, last_ui_element))
        cmds.formLayout(form, edit=True, attachForm=(cbs[ax], 'left', 15))
        last_ui_element = cbs[ax]

    cb_comp = cmds.checkBox(label='Compensar Y/Z world-space (mín. 3 sel.)', value=True)
    cmds.formLayout(form, edit=True, attachControl=(cb_comp, 'top', 10, last_ui_element))
    cmds.formLayout(form, edit=True, attachForm=(cb_comp, 'left', 5))
    last_ui_element = cb_comp

    btn = cmds.button(label='Execute', height=30,
                      command=lambda *_: on_execute(cbs, cb_comp))
    cmds.formLayout(form, edit=True,
                    attachControl=(btn, 'top', 15, last_ui_element),
                    attachForm=[(btn, 'left', 5), (btn, 'bottom', 5), (btn, 'right', 5)])

    cmds.showWindow(win)



def on_execute(cbs, cb_comp):
    """
    Se ejecuta cuando se presiona el botón 'Execute'.
    Valida la selección y llama a copy_translate_attrs con los parámetros correctos.
    """
    sels = cmds.ls(selection=True, type='transform') 
    
    if not sels:
        cmds.warning('No se han seleccionado objetos.')
        return
        
    count = len(sels) 
    do_comp = cmds.checkBox(cb_comp, q=True, value=True)

    attrs = [ax for ax, cb_widget in cbs.items() if cmds.checkBox(cb_widget, q=True, value=True)]
    if not attrs:
        cmds.error('Seleccione al menos un atributo para copiar.')
        return

    if do_comp: 
        if count < 3:
            cmds.error('Para compensación, seleccione al menos 3 objetos: Cadera (Hip), Raíz (Root), Control_IK1, [Control_IK2...].')
            return
        
        if 'translateZ' not in attrs:
            cmds.warning("La compensación Z está activa, pero 'translateZ' no está seleccionado para copiar. "
                         "Se copiarán otros atributos seleccionados de Cadera a Raíz. "
                         "La compensación de la Cadera y los IK no tendrá efecto si 'translateZ' no se procesa.")

        hip_sel = sels[0]
        root_sel = sels[1]
        ik_sel_list = sels[2:]

        copy_translate_attrs(main_src=hip_sel, main_dst=root_sel,
                             attrs=attrs,
                             do_comp=True,
                             ik_list_for_comp=ik_sel_list) # Parámetros actualizados
        cmds.confirmDialog(title='OK', message='Copia con compensación completada.', button=['OK'])

    else: # Sin compensación (do_comp es False)
        if count != 2:
            cmds.error('Para copia estándar (sin compensación), seleccione 2 objetos: Origen, luego Destino.')
            return
        src_obj, dst_obj = sels 
        copy_translate_attrs(main_src=src_obj, main_dst=dst_obj, attrs=attrs, do_comp=False)
        cmds.confirmDialog(title='OK', message='Copia estándar completada.', button=['OK'])


def copy_translateZ_selection():
    """
    Alias para abrir directamente la UI.
    Esta es la función que probablemente llamarías desde un shelf button.
    """
    copy_attributes_selection_pipeline()

# Ejemplo de cómo usarlo desde el Shelf o el Script Editor:
# import nombre_del_archivo_py # (ej. import copyZAnimCompensated)
# from importlib import reload
# reload(nombre_del_archivo_py) # Asegúrate de que el nombre del módulo sea correcto
# nombre_del_archivo_py.copy_translateZ_selection()
