/* XPM */
static char *biped_194_195_OnK1[] = {
/* columns rows colors chars-per-pixel */
"23 23 34 1",
"  c #FF6666",
". c #FF6B66",
"X c #FF6D66",
"o c #FF6E66",
"O c #FF7166",
"+ c #FF7266",
"@ c #FF7966",
"# c #FF7A66",
"$ c #FF8E66",
"% c #FF8F66",
"& c #FF9666",
"* c #FF9766",
"= c #FF9866",
"- c #FF9B66",
"; c #FF9C66",
": c #FF9D66",
"> c #FF9E66",
", c #FFAC66",
"< c #FFAD66",
"1 c #FFB766",
"2 c #FFB866",
"3 c #FFC766",
"4 c #FFC866",
"5 c #FFD266",
"6 c #FFD366",
"7 c #FFE266",
"8 c #FFE366",
"9 c #FFF766",
"0 c #FFF866",
"q c #FFF966",
"w c #FFFB66",
"e c #FFFC66",
"r c #FFFF66",
"t c gray75",
/* pixels */
"ttttttttttttttttttttttt",
"trrrrr983,:&:,48qrrrrrt",
"trrrr81%@+XXX+@%18rrrrt",
"trrq6:@.       .+,4qrrt",
"trr4-+           +-6qrt",
"tr8:+             X:8rt",
"tq1@               @1qt",
"t8$.               X$8t",
"t4@                 @4t",
"t,+                 +,t",
"t:X                 X:t",
"t&X                 X-t",
"t:X                 X:t",
"t,+                 +,t",
"t4@                 @4t",
"t8$X               .$8t",
"t91+               +1qt",
"tr8,+             +,8rt",
"trq4-+           +-4rrt",
"trrq4:@.       .@:4rrrt",
"trrrr81$@+XXX+@$18qrrrt",
"trrrrr983,-&-,389rrrrrt",
"ttttttttttttttttttttttt"
};
