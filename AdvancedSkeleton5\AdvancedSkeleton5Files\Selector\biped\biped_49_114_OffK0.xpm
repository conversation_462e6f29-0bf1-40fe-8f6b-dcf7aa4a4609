/* XPM */
static char *biped_49_114_OffK0[] = {
/* columns rows colors chars-per-pixel */
"10 16 46 1",
"  c gray75",
". c #ECEDED",
"X c #EFEEEF",
"o c #F0EEEF",
"O c #F1EFF1",
"+ c #F1F0F0",
"@ c #F1F1F0",
"# c #F1F1F1",
"$ c #F1F1F2",
"% c #F2F1F1",
"& c #F2F1F2",
"* c gray95",
"= c #F3F3F4",
"- c #FCFCFD",
"; c #FDFCFC",
": c #FDFDFC",
"> c #FDFDFD",
", c #FCFCFE",
"< c #FDFDFF",
"1 c #FDFEFC",
"2 c #FDFEFD",
"3 c #FDFFFD",
"4 c #FDFEFE",
"5 c #FDFEFF",
"6 c #FDFFFE",
"7 c #FDFFFF",
"8 c #FEFCFD",
"9 c #FEFDFD",
"0 c #FFFDFC",
"q c #FFFDFD",
"w c #FEFDFE",
"e c #FEFDFF",
"r c #FFFDFE",
"t c #FFFDFF",
"y c #FEFEFD",
"u c #FEFFFD",
"i c #FFFEFC",
"p c #FFFEFD",
"a c #FFFFFD",
"s c #FEFEFE",
"d c #FEFEFF",
"f c #FEFFFE",
"g c #FEFFFF",
"h c #FFFEFE",
"j c #FFFFFE",
"k c gray100",
/* pixels */
"          ",
" www,111. ",
" ww;11wwX ",
" w;w1www$ ",
" ww;wwww$ ",
" w;;w;ww$ ",
" ;wwwwww$ ",
" ww;wwww$ ",
" wwwwwww$ ",
" 11wwwwwX ",
" wwwwwwwX ",
" wwwwwwwO ",
" wwwwwwwO ",
" w1wwwww= ",
" ww;wwww* ",
"          "
};
