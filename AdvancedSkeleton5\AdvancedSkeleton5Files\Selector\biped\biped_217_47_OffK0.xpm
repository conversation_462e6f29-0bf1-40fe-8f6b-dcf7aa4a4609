/* XPM */
static char *biped_217_47_OffK0[] = {
/* columns rows colors chars-per-pixel */
"32 15 149 2",
"   c #8B8B8B",
".  c gray55",
"X  c #8D8D8D",
"o  c #8E8E8E",
"O  c gray56",
"+  c #908F8F",
"@  c #909090",
"#  c #909190",
"$  c #919090",
"%  c gray57",
"&  c #929292",
"*  c #939293",
"=  c gray58",
"-  c #959595",
";  c #969596",
":  c gray59",
">  c gray60",
",  c #99999A",
"<  c #9A9A9A",
"1  c #9B9B9B",
"2  c #9B9C9B",
"3  c #9D9D9D",
"4  c #9D9D9E",
"5  c gray62",
"6  c #9FA0A0",
"7  c #A2A3A3",
"8  c gray65",
"9  c #A6A7A7",
"0  c #A9A9AA",
"q  c #ACACAD",
"w  c #B4B4B4",
"e  c gray72",
"r  c #BCBC<PERSON>",
"t  c gray",
"y  c gray75",
"u  c #C0C0C0",
"i  c #C0C0C1",
"p  c #C0C1C0",
"a  c #C1C1C1",
"s  c #C0C2C1",
"d  c gray76",
"f  c #C6C7C6",
"g  c #C7C7C6",
"h  c #C8C9C8",
"j  c #CACBCA",
"k  c #CBCCCB",
"l  c #CECECD",
"z  c #CFD0CF",
"x  c #D0CFD0",
"c  c #D3D2D2",
"v  c #D4D4D3",
"b  c gray83",
"n  c #D5D4D5",
"m  c #D5D5D5",
"M  c #D6D5D5",
"N  c #D7D5D6",
"B  c #D7D8D7",
"V  c #D8D6D7",
"C  c #DAD9D9",
"Z  c #DAD9DA",
"A  c #DBDBDA",
"S  c gainsboro",
"D  c #DDDDDD",
"F  c #DDDEDD",
"G  c #DEDDDD",
"H  c gray87",
"J  c #DFE0E0",
"K  c #E0E0E1",
"L  c #E1E3E2",
"P  c #E4E5E4",
"I  c #E4E5E6",
"U  c #E6E5E4",
"Y  c #E6E5E5",
"T  c #E7E7E8",
"R  c #E8E7E7",
"E  c #E8E8EA",
"W  c #E8E9EA",
"Q  c #E9EAE9",
"!  c #EEEEEE",
"~  c #EFF0EE",
"^  c #F2F1F2",
"/  c #F2F2F3",
"(  c #F3F4F3",
")  c #F3F4F4",
"_  c #F5F4F5",
"`  c gray96",
"'  c #F6F5F7",
"]  c #F6F6F6",
"[  c #F6F6F7",
"{  c gray97",
"}  c #F7F7F8",
"|  c #F8F7F8",
" . c #F8F8F8",
".. c #F8F8F9",
"X. c #F9F9FA",
"o. c #F9FAF9",
"O. c #F9FAFA",
"+. c #FAF9F9",
"@. c #FAF9FA",
"#. c #FAFAF9",
"$. c gray98",
"%. c #FAFAFB",
"&. c #FAFBFA",
"*. c #FAFCFB",
"=. c #FCFDFC",
"-. c #FCFDFD",
";. c #FDFCFC",
":. c #FDFDFC",
">. c #FDFDFD",
",. c #FCFDFE",
"<. c #FDFCFE",
"1. c #FDFCFF",
"2. c #FDFDFE",
"3. c #FDFDFF",
"4. c #FCFEFD",
"5. c #FCFFFD",
"6. c #FDFEFC",
"7. c #FDFEFD",
"8. c #FDFFFC",
"9. c #FDFFFD",
"0. c #FDFEFE",
"q. c #FDFEFF",
"w. c #FDFFFE",
"e. c #FDFFFF",
"r. c #FEFCFD",
"t. c #FEFDFC",
"y. c #FEFDFD",
"u. c #FFFDFC",
"i. c #FFFDFD",
"p. c #FEFCFE",
"a. c #FEFDFE",
"s. c #FEFDFF",
"d. c #FFFDFE",
"f. c #FFFDFF",
"g. c #FEFEFC",
"h. c #FEFEFD",
"j. c #FEFFFC",
"k. c #FEFFFD",
"l. c #FFFEFD",
"z. c #FFFFFC",
"x. c #FFFFFD",
"c. c #FEFEFE",
"v. c #FEFEFF",
"b. c #FEFFFE",
"n. c #FEFFFF",
"m. c #FFFEFE",
"M. c #FFFEFF",
"N. c #FFFFFE",
"B. c gray100",
/* pixels */
"y y y y y y y y t t u t t u u t u u t u u y y u y y y y y y y y ",
"y q q 8 7 3 > : % + X X   X X = > 3 5 5 5 5 3 5 5 3 5 5 3 1 > y ",
"y I I L H S S C V c l h g u w 8 1 : + + + + + + + % % * : > 6 y ",
"y n.n.=.n.n.n.n.n.| | _ ( ~ Y C l h r e u u u u u u d f l z n y ",
"t n.n.=.n.r.u.r.u.u.r.n.n.n.n.r.n.&.( ! ( _ ' ' _ _ _ =.&.n.n.y ",
"t u.n.r.n.n.n.r.u.n.n.n.=.n.n.n.n.n.n.=.=.n.r.r.n.n.n.=.n.n.=.y ",
"t n.n.n.n.n.u.u.n.n.n.n.=.n.n.n.n.=.=.n.=.r.n.n.n.u.=.n.n.n.n.y ",
"t n.n.&.n.n.u.n.n.n.=.=.=.=.n.n.n.n.=.=.n.n.n.u.=.n.=.n.n.=.n.y ",
"t n.u.r.u.u.r.n.=.n.=.=.&.n.r.&.=.=.=.n.n.&.u.n.n.n.n.=.n.u.r.y ",
"t n.r.r.n.u.n.=.n.u.u.n.n.n.n.-.n.n.n.n.u.u.u.=.=.n.=.r.u.u.=.y ",
"y r.r.r.r.n.n.r.r.=.=.n.r.-.n.n.n.n.&.n.n.=.n.n.n.n.=.n.| n.n.y ",
"y r.n.r.n.n.r.r.n.n.=.u.u.n.=.n.n.=.r.r.n.n.n.n.n.=.r.n.n.n.^ y ",
"y n.n.n.=.n.n.n.=.=.r.| | u.u.| | &.u.{ { { { | &.| u.| | ' v u ",
"y E E E E R R R Y U L S S S S S S S S C V n n B S S S S S n 0 y ",
"y y y y t t t u t t u t y y y y y y y y y y y y y y y y y y y y "
};
