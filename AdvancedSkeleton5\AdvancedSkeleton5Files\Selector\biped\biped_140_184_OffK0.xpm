/* XPM */
static char *biped_140_184_OffK0[] = {
/* columns rows colors chars-per-pixel */
"18 31 134 2",
"   c #8B8B8B",
".  c #8E8E8E",
"X  c #8F9090",
"o  c #959595",
"O  c #959696",
"+  c #989897",
"@  c #989898",
"#  c #989998",
"$  c #989999",
"%  c #999998",
"&  c gray60",
"*  c #999A99",
"=  c #9D9D9D",
"-  c #A2A2A2",
";  c #A2A2A3",
":  c #A2A3A3",
">  c gray64",
",  c #A3A4A4",
"<  c #A7A7A7",
"1  c #ADACAD",
"2  c gray70",
"3  c #B4B4B4",
"4  c #BCBCBC",
"5  c gray75",
"6  c #C4C5C4",
"7  c #C8C8C8",
"8  c #CBCCCB",
"9  c #D3D3D2",
"0  c #D7D8D8",
"q  c #D9DAD9",
"w  c #DBDCDC",
"e  c #DDDFDD",
"r  c #DEDEDF",
"t  c #DFDFE0",
"y  c #E1E1E1",
"u  c #E0E1E2",
"i  c #E0E2E1",
"p  c #E1E2E1",
"a  c #E0E2E2",
"s  c #E2E2E1",
"d  c #E1E2E4",
"f  c #E4E4E4",
"g  c #E4E5E5",
"h  c gray90",
"j  c #E4E6E6",
"k  c #E6E5E5",
"l  c #E7E7E7",
"z  c #E8E9E8",
"x  c #E9E9EA",
"c  c #EAEBEA",
"v  c #EAECEB",
"b  c #EBEDEC",
"n  c #ECECEB",
"m  c #ECEDEC",
"M  c gray93",
"N  c #EDEEEC",
"B  c #EEEDED",
"V  c #EEEDEE",
"C  c #EFEDEE",
"Z  c #EEEEEE",
"A  c #EEEFEE",
"S  c #EFEEEF",
"D  c #EFEFEF",
"F  c #EEEFF0",
"G  c #EFF0EF",
"H  c #EEF0F0",
"J  c #F0F0EE",
"K  c #F0F1F1",
"L  c #F1F0F1",
"P  c #F1F2F2",
"I  c #F2F0F0",
"U  c #F2F2F0",
"Y  c #F2F2F1",
"T  c gray95",
"R  c #F4F3F3",
"E  c #F6F6F5",
"W  c #F8F8F7",
"Q  c #F9F9F9",
"!  c #F9FAFA",
"~  c gray98",
"^  c #FBFCFB",
"/  c #FCFDFC",
"(  c #FCFDFD",
")  c #FDFCFD",
"_  c #FDFDFC",
"`  c #FDFDFD",
"'  c #FCFCFE",
"]  c #FCFDFE",
"[  c #FCFDFF",
"{  c #FDFCFE",
"}  c #FDFCFF",
"|  c #FDFDFE",
" . c #FDFDFF",
".. c #FCFEFD",
"X. c #FCFFFD",
"o. c #FDFEFC",
"O. c #FDFEFD",
"+. c #FDFFFC",
"@. c #FDFFFD",
"#. c #FCFEFE",
"$. c #FCFEFF",
"%. c #FDFEFE",
"&. c #FDFEFF",
"*. c #FDFFFE",
"=. c #FDFFFF",
"-. c #FEFCFC",
";. c #FEFCFD",
":. c #FEFDFC",
">. c #FEFDFD",
",. c #FFFCFD",
"<. c #FFFDFC",
"1. c #FFFDFD",
"2. c #FEFCFE",
"3. c #FEFCFF",
"4. c #FEFDFE",
"5. c #FEFDFF",
"6. c #FFFCFE",
"7. c #FFFDFE",
"8. c #FFFDFF",
"9. c #FEFEFC",
"0. c #FEFEFD",
"q. c #FEFFFC",
"w. c #FEFFFD",
"e. c #FFFEFC",
"r. c #FFFEFD",
"t. c #FFFFFD",
"y. c #FEFEFE",
"u. c #FEFEFF",
"i. c #FEFFFE",
"p. c #FEFFFF",
"a. c #FFFEFE",
"s. c #FFFEFF",
"d. c #FFFFFE",
"f. c gray100",
/* pixels */
"5 5 5 5 5 5 5 5 5 5 5 5 5 5 5 5 5 5 ",
"5 s ~ | 6.| | | | | | | | | | | M 5 ",
"5 e ~ | | | | e.| 6.6.| | e.| | A 5 ",
"5 q W | | | | | | | 6.| | | e.| c 5 ",
"5 9 E | | e.e.| X.| | | | | | | c 5 ",
"5 8 R | e.| | | | | | | | | | | M 5 ",
"5 6 I | | | e.| | e.X.e.| | e.e.M 5 ",
"5 4 M | | | e.e.e.e.| e.| X.e.| A 5 ",
"5 3 c | | | | | e.e.e.X.X.| | | D 5 ",
"5 1 l e.| | | | e.e.e.| X.~ | | M 5 ",
"5 < l | | | | | e.e.X.| | | | | P 5 ",
"5 , k | | | | | | X.e.~ | | | | P 5 ",
"5 > k e.| | | | | | | | | | e.| P 5 ",
"5 , j | | | | | | | | | | | | e.P 5 ",
"5 : f | | e.| | | | | | X.e.| | I 5 ",
"5 = s X.| | | | | | | | X.| e.| I 5 ",
"5 $ s | e.| | | | | e.| | | | | D 5 ",
"5 # u | X.| | | | e.| | | | | | D 5 ",
"5 # s | | | | | | | | | | | | | P 5 ",
"5 $ u | | | | | e.| | | | | e.| A 5 ",
"5 # s | | | | | | | | | | e.e.| D 5 ",
"5 # s | | | | | | | | | | | | | C 5 ",
"5 # i e.e.| | | | | | | | e.| e.M 5 ",
"5 # i | | | | | | | e.| e.| | | M 5 ",
"5 # s | ~ | | | | | | | | e.| | M 5 ",
"5 o r | X.X.| | | | | | e.| | | M 5 ",
"5 X 0 ~ e.X.e.| | | | | | e.| | c 5 ",
"5   7 P e.| e.| | | | | | e.| e.c 5 ",
"5 . 2 l | | | | | | | | | | | e.k 5 ",
"5 o : w | | | | | | | | | | | ~ e 5 ",
"5 5 5 5 5 5 5 5 5 5 5 5 5 5 5 5 5 5 "
};
