/* XPM */
static char *biped_139_3_OffK0[] = {
/* columns rows colors chars-per-pixel */
"42 32 236 2",
"   c gray54",
".  c #8A8B8A",
"X  c #8B8B8B",
"o  c #8C8C8B",
"O  c gray55",
"+  c #8D8D8D",
"@  c #8E8E8E",
"#  c #8E8F8F",
"$  c gray56",
"%  c #909090",
"&  c #909091",
"*  c gray57",
"=  c #919291",
"-  c #929292",
";  c #939292",
":  c #939393",
">  c gray58",
",  c #949495",
"<  c #959494",
"1  c #959595",
"2  c gray59",
"3  c #979797",
"4  c #989898",
"5  c gray60",
"6  c #999A99",
"7  c #9A9A9A",
"8  c #9B9A9B",
"9  c #9B9B9B",
"0  c gray61",
"q  c #9C9D9C",
"w  c #9C9D9D",
"e  c #9D9D9D",
"r  c #9D9E9E",
"t  c gray62",
"y  c #9E9F9F",
"u  c #9F9E9F",
"i  c #9F9F9F",
"p  c #A0A0A0",
"a  c #A2A2A2",
"s  c #A3A4A3",
"d  c #A4A4A4",
"f  c #A4A5A4",
"g  c #A5A5A5",
"h  c #A6A6A5",
"j  c gray65",
"k  c #A7A7A7",
"l  c #A7A8A8",
"z  c #A8A9A9",
"x  c #A9A9A9",
"c  c #AAAAAA",
"v  c #AAAAAB",
"b  c #AAABAB",
"n  c #ABAAAA",
"m  c #ABABAA",
"M  c gray67",
"N  c #ABACAC",
"B  c #ACACAB",
"V  c #ACACAC",
"C  c gray68",
"Z  c #B4B5B5",
"A  c #B5B4B4",
"S  c #B6B5B6",
"D  c #B7B7B7",
"F  c #B8B7B8",
"G  c gray72",
"H  c #B8B9B8",
"J  c #B9B9BA",
"K  c #B9BBBA",
"L  c #BBBABA",
"P  c #BBBBBB",
"I  c #BCBCBC",
"U  c #BDBCBC",
"Y  c #BEBEBF",
"T  c gray75",
"R  c #C0C0C0",
"E  c #C0C0C1",
"W  c #C5C4C5",
"Q  c #C5C5C5",
"!  c #C8C8C7",
"~  c #C8C8C8",
"^  c #C8C9C8",
"/  c #C8C9C9",
"(  c #C9CBCA",
")  c #CAC9C9",
"_  c #CACBCA",
"`  c #CACBCB",
"'  c #CBCBCC",
"]  c #CECECE",
"[  c #CECFCE",
"{  c #D0CFD0",
"}  c #D2D2D1",
"|  c #D2D2D2",
" . c #D3D3D2",
".. c LightGray",
"X. c #D3D4D4",
"o. c #D4D3D4",
"O. c #D4D4D3",
"+. c #D5D4D3",
"@. c #D5D5D5",
"#. c #D6D7D5",
"$. c #D7D6D5",
"%. c #D7D7D7",
"&. c #D7D8D7",
"*. c #DAD9DA",
"=. c #DBDADB",
"-. c #DBDBDC",
";. c #DCDBDB",
":. c #DCDCDD",
">. c #DDDDDC",
",. c #DDDDDD",
"<. c #DFDEDE",
"1. c #DFDFDF",
"2. c #E0DFDF",
"3. c #E1E1DF",
"4. c gray88",
"5. c #E0E1E1",
"6. c #E1E1E0",
"7. c #E1E3E3",
"8. c #E2E2E2",
"9. c #E2E2E3",
"0. c #E2E3E3",
"q. c #E3E2E3",
"w. c #E3E4E3",
"e. c #E2E4E4",
"r. c #E4E3E3",
"t. c #E4E3E4",
"y. c #E4E4E3",
"u. c #E4E5E3",
"i. c #E4E4E4",
"p. c #E4E4E5",
"a. c #E5E4E4",
"s. c gray90",
"d. c #E6E7E6",
"f. c #E7E7E7",
"g. c #E7E9E8",
"h. c #E9E7E8",
"j. c gray91",
"k. c #EAE9E9",
"l. c #EAE9EA",
"z. c #EAEAEB",
"x. c #EAEBEB",
"c. c #EBEBEA",
"v. c gray92",
"b. c #EBEBEC",
"n. c #ECECEC",
"m. c #EDECEC",
"M. c gray93",
"N. c #ECEDEE",
"B. c #EEEFEF",
"V. c #EFEEEE",
"C. c #F0EFEF",
"Z. c #F0EFF0",
"A. c gray94",
"S. c #F0F0F1",
"D. c #F1F1F1",
"F. c gray95",
"G. c #F2F3F3",
"H. c #F3F3F3",
"J. c #F3F4F4",
"K. c #F4F3F3",
"L. c #F5F3F4",
"P. c #F4F4F5",
"I. c #F5F4F4",
"U. c gray96",
"Y. c #F5F6F5",
"T. c #F7F5F7",
"R. c #F6F6F5",
"E. c #F6F6F6",
"W. c gray97",
"Q. c #F7F7F8",
"!. c #F7F9F9",
"~. c #F8F7F8",
"^. c #F9F8F7",
"/. c #F9F9F7",
"(. c #F9F9FA",
"). c #F9FBFA",
"_. c #FBF9F9",
"`. c gray98",
"'. c #FAFAFB",
"]. c #FBFBFB",
"[. c #FBFAFC",
"{. c #FBFBFC",
"}. c #FAFCFC",
"|. c #FBFCFC",
" X c #FBFCFD",
".X c #FBFDFC",
"XX c #FCFBFA",
"oX c #FCFBFB",
"OX c #FCFCFB",
"+X c #FCFDFD",
"@X c #FDFCFD",
"#X c #FDFDFC",
"$X c #FDFDFD",
"%X c #FCFCFE",
"&X c #FCFDFE",
"*X c #FCFDFF",
"=X c #FDFCFE",
"-X c #FDFDFE",
";X c #FDFDFF",
":X c #FCFEFD",
">X c #FCFFFD",
",X c #FDFEFC",
"<X c #FDFEFD",
"1X c #FDFFFD",
"2X c #FCFFFE",
"3X c #FCFFFF",
"4X c #FDFEFE",
"5X c #FDFEFF",
"6X c #FDFFFE",
"7X c #FDFFFF",
"8X c #FEFCFC",
"9X c #FEFDFC",
"0X c #FEFDFD",
"qX c #FFFDFC",
"wX c #FFFDFD",
"eX c #FEFCFE",
"rX c #FEFDFE",
"tX c #FEFDFF",
"yX c #FFFCFF",
"uX c #FFFDFE",
"iX c #FFFDFF",
"pX c #FEFEFC",
"aX c #FEFEFD",
"sX c #FEFFFC",
"dX c #FEFFFD",
"fX c #FFFEFD",
"gX c #FFFFFC",
"hX c #FFFFFD",
"jX c #FEFEFE",
"kX c #FEFEFF",
"lX c #FEFFFE",
"zX c #FEFFFF",
"xX c #FFFEFE",
"cX c #FFFEFF",
"vX c #FFFFFE",
"bX c gray100",
/* pixels */
"T T T T T T T T Y E Y E T T T T T T T T R R T R T T T T T T T T T T T T T T T T T T ",
"T t t t t t t t u u 3 + + 9 v P ! @.<.r.d.d.0.>.| ^ K x < + + 9 t t t t t t t t t T ",
"T t t t t t t t 8 % 8 Y >.z.S.L.T.(._..X.XyX.X%X(.T.F.S.l.*.K 9 % t t t t t t t t T ",
"T t t t t t t 9 % v %.S.(.aXaXaXaXaXaXaXaXaX.X.XaXaXaXaXaX(.S.@.C % 9 t t t t t t T ",
"T t t t t t t % M :.Q.aXyXaXaXaXaXaXaX.XaXaXaXaX.XaXyXyXaXaXaXQ.*.x % t t t t t t T ",
"T t t t t t < a o.Q.aXaXaXaXaXaXaXaXyXaXaXaXaXaXaXaXyXyXaXaXaXaXT.X.d < t t t t t T ",
"T t t t t t % E B.aXaXaX.XaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXB.R & t t t t t T ",
"T t t t t 9 9 &.%XaXaXaXyXaXaXyXaXaXyXyXaXaXaXaXaXaXaXaXaXaXaXaXaX.X@.4 9 t t t t T ",
"T t t t t < k 3.aXaXaXaXyXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXyXyXaX1.d 3 u t t t T ",
"T t t t t < x 0.aXaXaXaXaX.X.XyXaXaXaXaX.XaXaX.XaXyXaXaXaXaXaXaXaXaX0.x - t t t t T ",
"T t t t t : n e.aXyXyXaXaXaXaX.XyXaXaXaXaXaXaX.XaXyXaXaX%XaXaXaXaXaXe.x - u u t t T ",
"T t t t t > M e.aXaXaXaXaXaXaX.X.XaXaXaX.XaXaXaXaXaXyX%XyXyXaXaXaXaXr.v - u u t t T ",
"T t t t 9 $ x q.aXaXaXaXyXaX.XaXaX.XaXaXaXaXaXaX.XaXyXaX].aXyX.XaXaXr.k % 9 t t t T ",
"T t t : % o x 0.aXaXaXaXaXaXaX.XaXaXaX.XaXaXaXaXaXaX.XaXaXaXaXaXaXaX0.d o : % t t T ",
"T t t . E k x 0.aXaX%XaXyXaXaXaXaXaXaX.XaXaXaXaXaXaXaXaXaXaXyXaXaXaX5.k k E . t t T ",
"T t t .  .` K 0.aXyXaXaXaXaXyX.XaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaX3.Z ! +.. t t T ",
"E t t X | h.| f.%XaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaX%X5.` d.| . t t T ",
"T t t X [ J.S.J..XaXaXaXaX.X.XaXaXaXaXaXaXaXaXaX.XaXaXaXaXaXaXaXaX%XS.b.S.| . t t T ",
"T t t + W G.%XyXaXaXaX.XaX.XaXaXaXyXaXaXaXaXaX.X.XaXaXaXyXaXaXyXaX.X%X%XS.W . t t T ",
"T t t + F V.aXaXaXaXaXaXaXaXaXaXaXaXaXyX.XaXaXaXaXaXaXaXaXaXaXaXaX.XaX.XS.K $ t t T ",
"T t t % C z.aXaXaXaXaXaXaX.XaXaX.XaXaXaXaXaXaXaXaXaXaXaXaXyXaXaXaXaXaXaXz.b % t t T ",
"T t i 3 a e.aXaXaXaXyXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXyXaXaXaX.Xe.t 9 t t T ",
"T t t t - X.Q.aXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXyXaX.XaXaX!.@.% t t t T ",
"T t t t $ F m.aXaXaXaXaXaXaX.XaXaXaXaXaXaXaXaXaXaXaXaXaXaXyXaXyXaXaXaXb.K & t t t T ",
"T t t t < t =.XXaXaXaXaXaXaXaX.XaXaXaXaXaXaXaX.XaXaXaXaXaX.XaXaX.XaX%X-.t 3 t t t T ",
"T t t t t # ^ T.aXaXaXaX.XaXaXaXaXaXaX.XaXaXaXaXaXaXaXaXaXaXaX.XaXaXL.Q $ t t t t T ",
"T t t t i % Z x.aX.XaXaXaXaXaXaXaXaXaXaX.XaXaXaXaXaXaXaXaX.XaX.XaXyXl.Z & t t t t T ",
"T t t t t < t [ d.z.Q.aXaX.XaXaXaXaXaXaXaXaX.XaXaXaXaXaXaX.XyXQ.x.l.[ t < u t t t T ",
"T t t t t t % t b K >.aXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaX.X.XaX1.K C t & t t t t t T ",
"T t t t t t t < % < ( aXaXaXaXaXaXaXaXyXaXaXaXaX.XaXaXaXaXaX.X( , & < t t t t t t T ",
"T t t t t t t t 0 : T T.aXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXaXP.I : t t t t t t t t T ",
"T T T T T T T T T T T Y R R E Y Y R Y Y Y R Y R Y Y Y R R Y R Y T T T T T T T T T T "
};
