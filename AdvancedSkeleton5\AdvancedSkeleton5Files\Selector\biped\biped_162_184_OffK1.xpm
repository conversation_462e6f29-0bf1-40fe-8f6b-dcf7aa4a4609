/* XPM */
static char *biped_162_184_OffK1[] = {
/* columns rows colors chars-per-pixel */
"18 31 53 1",
"  c #D07C7C",
". c #D27D7D",
"X c #D47E7E",
"o c gray75",
"O c #D98080",
"+ c #DA8181",
"@ c #DD8182",
"# c #DD8281",
"$ c #DD8282",
"% c #DE8181",
"& c #DE8182",
"* c #DE8282",
"= c #DF8282",
"- c #E28384",
"; c #E78685",
": c #E88685",
"> c #E88686",
", c #E98686",
"< c #EC8788",
"1 c #F18A8A",
"2 c #F98C8D",
"3 c #FB8D8D",
"4 c #FF9090",
"5 c #FF9393",
"6 c #FF9595",
"7 c #FF9696",
"8 c #FF9999",
"9 c #FF9B9B",
"0 c #FF9B9C",
"q c #FF9D9D",
"w c #FF9D9E",
"e c #FF9E9E",
"r c #FF9E9F",
"t c #FF9F9E",
"y c #FF9F9F",
"u c #FFA09F",
"i c #FFA0A0",
"p c #FFA0A1",
"a c #FFA1A1",
"s c #FFA2A1",
"d c #FFA2A2",
"f c #FFA3A2",
"g c #FFA3A3",
"h c #FFA3A4",
"j c #FFA4A3",
"k c #FFA4A4",
"l c #FFA5A5",
"z c #FFA6A6",
"x c #FFA7A7",
"c c #FFA8A8",
"v c #FFA9A9",
"b c #FFA9AA",
"n c #FFAAAA",
/* pixels */
"oooooooooooooooooo",
"ojnnnnnnnnnnnnnnro",
"ognnnnnnnnnnnnnnro",
"ognnnnnnnnnnnnnkro",
"ognnnnnnnnnnnnnn8o",
"ojnnnnnnnnnnnnnk6o",
"ohnnnnnnnnnnnnnk5o",
"oknnnnnnnnnnnnnk5o",
"ojnnnnnnnnnnnnnj2o",
"oknnnnnnnnnnnnng1o",
"oknnnnnnnnnnnnng<o",
"oknnnnnnnnnnnnnr;o",
"oknnnnnnnnnnnnny;o",
"oknnnnnnnnnnnnng;o",
"oknnnnnnnnnnnnnr;o",
"oxnnnnnnnnnnnnny-o",
"oknnnnnnnnnnnnny%o",
"oknnnnnnnnnnnnny%o",
"oknnnnnnnnnnnnnq%o",
"oknnnnnnnnnnnnnr%o",
"oknnnnnnnnnnnnnr*o",
"oknnnnnnnnnnnnnr%o",
"oknnnnnnnnnnnnnr%o",
"oknnnnnnnnnnnnnr*o",
"ojnnnnnnnnnnnnnr*o",
"ojnnnnnnnnnnnnnr+o",
"ognnnnnnnnnnnnn8Xo",
"ognnnnnnnnnnnnk5 o",
"ognnnnnnnnnnnng3.o",
"orxnnnnnnnnnnnr:Oo",
"oooooooooooooooooo"
};
