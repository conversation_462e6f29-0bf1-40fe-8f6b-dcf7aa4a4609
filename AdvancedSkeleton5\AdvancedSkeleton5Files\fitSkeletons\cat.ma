//Maya ASCII 2012 scene
//Name: cat.ma
//Last modified: Sun, Nov 10, 2019 09:56:57 AM
//Codeset: 1252
requires maya "2008";
fileInfo "application" "maya";
fileInfo "product" "Maya 2012";
fileInfo "version" "2012 x64";
fileInfo "cutIdentifier" "201201172029-821146";
fileInfo "osv" "Microsoft Business Edition, 64-bit  (Build 9200)\n";
createNode transform -s -n "persp";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -8.0360586771770848 2.9718023225834971 5.3241220579424784 ;
	setAttr ".r" -type "double3" -13.538352731783004 -58.999999999902442 0 ;
	setAttr ".rp" -type "double3" 2.2204460492503131e-016 -8.3266726846886741e-017 -1.1102230246251565e-016 ;
	setAttr ".rpt" -type "double3" 4.6548005711801995e-016 8.0465702861790355e-016 1.0100730749515512e-015 ;
createNode camera -s -n "perspShape" -p "persp";
	setAttr -k off ".v" no;
	setAttr ".fl" 34.999999999999979;
	setAttr ".ncp" 1;
	setAttr ".coi" 10.201567626574926;
	setAttr ".imn" -type "string" "persp";
	setAttr ".den" -type "string" "persp_depth";
	setAttr ".man" -type "string" "persp_mask";
	setAttr ".tp" -type "double3" -0.37539235317483932 0.14638904644190598 -1.0033363705481761 ;
	setAttr ".hc" -type "string" "viewSet -p %camera";
createNode transform -s -n "top";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 0 101.72777466931498 5.0591237182467452 ;
	setAttr ".r" -type "double3" -89.999999999999986 0 0 ;
	setAttr ".rp" -type "double3" 0 0 -1.4210854715202004e-014 ;
	setAttr ".rpt" -type "double3" 0 -1.4210854715202007e-014 1.4210854715202013e-014 ;
createNode camera -s -n "topShape" -p "top";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 92.956918141992944;
	setAttr ".ow" 64.431756082637719;
	setAttr ".imn" -type "string" "top";
	setAttr ".den" -type "string" "top_depth";
	setAttr ".man" -type "string" "top_mask";
	setAttr ".tp" -type "double3" -0.92121148222907756 8.6997414858987554 4.667703911888192 ;
	setAttr ".hc" -type "string" "viewSet -t %camera";
	setAttr ".o" yes;
createNode transform -s -n "front";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -0.15372727856671006 2.239107203669136 100.72708529824905 ;
createNode camera -s -n "frontShape" -p "front";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 98.166537423677852;
	setAttr ".ow" 1.9705223642136045;
	setAttr ".imn" -type "string" "front";
	setAttr ".den" -type "string" "front_depth";
	setAttr ".man" -type "string" "front_mask";
	setAttr ".tp" -type "double3" -0.16561579961777267 1.2374993051171232 2.5605478745711991 ;
	setAttr ".hc" -type "string" "viewSet -f %camera";
	setAttr ".o" yes;
createNode transform -s -n "side";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 104.01064278233328 0.17993935569693711 1.1534889484861535 ;
	setAttr ".r" -type "double3" 0 89.999999999999972 0 ;
	setAttr ".rpt" -type "double3" -3.1554436208840472e-030 0 9.4663308626521417e-030 ;
createNode camera -s -n "sideShape" -p "side";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 104.18953104098316;
	setAttr ".ow" 1.5249215098330402;
	setAttr ".imn" -type "string" "side";
	setAttr ".den" -type "string" "side_depth";
	setAttr ".man" -type "string" "side_mask";
	setAttr ".tp" -type "double3" -0.17888825864987723 1.2542197439701681 -1.5972945287633664 ;
	setAttr ".hc" -type "string" "viewSet -s %camera";
	setAttr ".o" yes;
createNode transform -n "FitSkeleton";
	addAttr -ci true -sn "visCylinders" -ln "visCylinders" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "visBoxes" -ln "visBoxes" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "visBones" -ln "visBones" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "lockCenterJoints" -ln "lockCenterJoints" -dv 1 -min 0 -max 
		1 -at "bool";
	addAttr -ci true -sn "visGap" -ln "visGap" -dv 0.75 -min 0 -max 1 -at "double";
	addAttr -ci true -k true -sn "visGeo" -ln "visGeo" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visGeoType" -ln "visGeoType" -min 0 -max 3 -en "cylinders:boxes:spheres:bones" 
		-at "enum";
	addAttr -ci true -sn "visSpheres" -ln "visSpheres" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visPoleVector" -ln "visPoleVector" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "visJointOrient" -ln "visJointOrient" -min 0 -max 1 
		-at "bool";
	addAttr -ci true -k true -sn "visJointAxis" -ln "visJointAxis" -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "preRebuildScript" -ln "preRebuildScript" -dt "string";
	addAttr -ci true -sn "postRebuildScript" -ln "postRebuildScript" -dt "string";
	addAttr -ci true -sn "run" -ln "run" -dt "string";
	addAttr -ci true -m -im false -sn "drivingSystem" -ln "drivingSystem" -at "message";
	addAttr -ci true -m -sn "drivingSystem_Fingers_R" -ln "drivingSystem_Fingers_R" 
		-dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -m -sn "drivingSystem_Fingers_L" -ln "drivingSystem_Fingers_L" 
		-dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -m -sn "drivingSystem_Toes_R" -ln "drivingSystem_Toes_R" -dv 1 
		-min 0 -max 1 -at "bool";
	addAttr -ci true -m -sn "drivingSystem_Toes_L" -ln "drivingSystem_Toes_L" -dv 1 
		-min 0 -max 1 -at "bool";
	setAttr -l on ".v";
	setAttr ".ove" yes;
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr ".visBoxes" yes;
	setAttr ".visGap" 1;
	setAttr -k on ".visGeoType" 1;
	setAttr ".run" -type "string" ";setAttr FKIKSpine_M.FKIKBlend 10;;setAttr FKIKSplineTail_M.FKIKBlend 10;setAttr FKIKSplineNeck_M.FKIKBlend 10;;setAttr FKIKSplineTail_M.FKIKBlend 0;";
	setAttr -s 60 ".drivingSystem";
	setAttr -s 15 ".drivingSystem_Fingers_R";
	setAttr -s 15 ".drivingSystem_Fingers_R";
	setAttr -s 15 ".drivingSystem_Fingers_L";
	setAttr -s 15 ".drivingSystem_Fingers_L";
	setAttr -s 15 ".drivingSystem_Toes_R";
	setAttr -s 15 ".drivingSystem_Toes_R";
	setAttr -s 15 ".drivingSystem_Toes_L";
	setAttr -s 15 ".drivingSystem_Toes_L";
createNode nurbsCurve -n "FitSkeletonShape" -p "FitSkeleton";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 29;
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		1.5172483967188066 9.2904669627656704e-017 -1.5172483967188028
		-2.448002266024856e-016 1.3138704379522408e-016 -2.1457132601285687
		-1.5172483967188042 9.2904669627656754e-017 -1.5172483967188042
		-2.1457132601285687 4.7966965723117612e-032 -8.0001365883018366e-016
		-1.5172483967188048 -9.2904669627656729e-017 1.5172483967188037
		-6.4654528127065897e-016 -1.313870437952241e-016 2.1457132601285696
		1.5172483967188028 -9.2904669627656754e-017 1.5172483967188042
		2.1457132601285687 -6.067395004808312e-032 9.7422714017963347e-016
		1.5172483967188066 9.2904669627656704e-017 -1.5172483967188028
		-2.448002266024856e-016 1.3138704379522408e-016 -2.1457132601285687
		-1.5172483967188042 9.2904669627656754e-017 -1.5172483967188042
		;
createNode joint -n "Root" -p "FitSkeleton";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.4995579999729969 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "inbetweenJoints" -ln "inbetweenJoints" -dv 2 -min 
		0 -at "long";
	addAttr -ci true -k true -sn "unTwister" -ln "unTwister" -min 0 -max 1 -at "bool";
	addAttr -ci true -k true -sn "centerBtwFeet" -ln "centerBtwFeet" -dv 1 -min 0 -max 
		1 -at "bool";
	addAttr -ci true -k true -sn "numMainExtras" -ln "numMainExtras" -min 0 -at "long";
	setAttr ".t" -type "double3" 4.8266925206663512e-036 2.3950266115552568 -0.84733706902543837 ;
	setAttr -l on ".tx";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".ro" 3;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -89.999999999989768 -87.4357282118455 -90.000000000009962 ;
	setAttr ".pa" -type "double3" 2.1569712459551385e-014 1.9864576294588575e-014 0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 1;
	setAttr -k on ".fat" 0.25507311905617369;
	setAttr ".fatYabs" 0.25507313013076782;
	setAttr ".fatZabs" 0.25507313013076782;

createNode joint -n "Spine1" -p "Root";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.25507311905617369 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.49011630602775397 -1.0658141036401503e-014 1.0399443759985331e-015 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" -1.5080861582472313e-015 -6.0594960855961919e-014 6.5082100169447213e-013 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.4461368656467089e-013 1.608251338985737e-014 2.8513637743141222 ;
	setAttr -k on ".fat" 0.29904621524512448;
	setAttr ".fatYabs" 0.29904621839523315;
	setAttr ".fatZabs" 0.29904621839523315;

createNode joint -n "Hip" -p "Root";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.8275483175467393 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -0.45594467437299757 0.42209112824880957 -0.28168333838081672 ;
	setAttr ".r" -type "double3" 2.2696147395444213e-013 -8.326033265987604e-013 -1.5521106845541977e-012 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -179.99999999999991 1.2039857404733292e-014 41.726460754678286 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "HipBack";
	setAttr -k on ".fat" 0.20152508099951538;
	setAttr -k on ".fatZ" 0.7;
	setAttr ".fatYabs" 0.20152507722377777;
	setAttr ".fatZabs" 0.14106754958629608;
createNode joint -n "Knee" -p "Hip";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.3229548725558775 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.86714271926054076 -2.4424906541753444e-014 1.27675647831893e-014 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.3998375768521505e-014 -1.7837140652011193e-014 
		-92.64096019311755 ;
	setAttr ".pa" -type "double3" 0 7.3840344800430189e-005 0 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "HipBack";
	setAttr -k on ".fat" 0.2026199434314768;
	setAttr ".fatYabs" 0.20261994004249573;
	setAttr ".fatZabs" 0.20261994004249573;
createNode joint -n "Ankle" -p "Knee";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4338138454722005 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 1.1480062420072965 -1.9984014443252818e-014 -2.0705659409259169e-014 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.2285528840015281e-014 -1.8285353203110217e-014 
		85.416312377876608 ;
	setAttr ".pa" -type "double3" 0 0.00039045212256848152 0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "FootBack";
	setAttr -k on ".fat" 0.091309971715738389;
	setAttr ".fatYabs" 0.091309972107410431;
	setAttr ".fatZabs" 0.091309972107410431;
createNode joint -n "Toes1" -p "Ankle";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4338138454722005 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.6284011994692289 1.8873791418627661e-015 4.9960036108132044e-016 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 7.3670483200638101e-015 2.2012736456257956e-006 17.688795575525972 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "QToesBack";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.062404834147699741;
	setAttr ".fatYabs" 0.062404833734035492;
	setAttr ".fatZabs" 0.062404833734035492;
createNode joint -n "Toes2" -p "Toes1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4338138454722005 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.20030919140671377 -1.9984014443252818e-015 2.9410024415810199e-009 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "ToesEndBack";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.024404834147699738;
	setAttr ".fatYabs" 0.02440483495593071;
	setAttr ".fatZabs" 0.02440483495593071;
createNode joint -n "backFootSideOuter" -p "Toes1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.12202417073849868 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.1185405871092845 -0.056228121693131627 0.076093139788977748 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.0547603077730287e-014 -4.4839122232867167e-015 -3.1805546814635168e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "PinkyToe";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.024404834147699738;
	setAttr ".fatYabs" 0.02440483495593071;
	setAttr ".fatZabs" 0.02440483495593071;
createNode joint -n "backFootSideInner" -p "Toes1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.12202417073849868 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.11707272246342437 -0.055938185695702858 -0.072053578517742967 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.0547603077730287e-014 -4.4839122232867167e-015 -3.1805546814635168e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "BigToe";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.024404834147699738;
	setAttr ".fatYabs" 0.02440483495593071;
	setAttr ".fatZabs" 0.02440483495593071;
createNode joint -n "backHeel" -p "Toes1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.5765498585786919 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.036527259431968773 -0.1083253741980329 5.1018713143058392e-009 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.0547603077730287e-014 -4.4839122232867167e-015 -3.1805546814635168e-015 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Heel";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.11530997171573838;
	setAttr ".fatYabs" 0.11530996859073639;
	setAttr ".fatZabs" 0.11530996859073639;
createNode joint -n "IndexToe1" -p "Toes1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 1.7745087621534594e-009 -9.1896157350390695e-011 -0.050000000000000738 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 6.2660615324179545e-015 -9.5965553473879253e-015 33.826584782547833 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "IndexToe2" -p "IndexToe1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033817045 2.6367796834847468e-016 1.9721964894525712e-009 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "IndexToe3" -p "IndexToe2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.07086969003382193 8.7430063189231078e-016 1.5609382120196358e-009 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "IndexToe4" -p "IndexToe3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033819821 -4.163336342344337e-017 1.5609380454861821e-009 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "MiddleToe1" -p "Toes1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -9.1853102901495731e-010 -5.7629467864472872e-010 6.106226635438361e-016 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.3245421114654248e-005 -7.6629305511362412e-005 
		33.82683202173569 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "MiddleToe2" -p "MiddleToe1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033819932 1.0685896612017132e-015 -2.2755980433331047e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.9271964409564561e-009 -7.5449603856058698e-015 1.3638249145968565e-014 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "MiddleToe3" -p "MiddleToe2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.07086969003382193 5.5511151231257827e-016 1.9428902930940239e-015 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.9271964409564561e-009 -7.5449603856058698e-015 1.3638249145968565e-014 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "MiddleToe4" -p "MiddleToe3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033819932 -1.1102230246251565e-016 -5.5511151231257827e-017 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "RingToe1" -p "Toes1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -2.234820106750135e-009 -1.9691159813817194e-010 0.049999999999999711 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.3253079007758739e-005 -7.6629305511362371e-005 
		33.826832021735598 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "RingToe2" -p "RingToe1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033821264 9.1593399531575415e-016 -1.9640922221952906e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.9088657540569898e-009 1.3128596512493997e-014 1.3638200614555575e-014 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "RingToe3" -p "RingToe2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033817934 1.27675647831893e-015 1.27675647831893e-015 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.9088657540569898e-009 1.3128596512493997e-014 1.3638200614555575e-014 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "RingToe4" -p "RingToe3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033819488 4.9960036108132044e-016 5.5511151231257827e-016 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "PinkyToe1" -p "Toes1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -3.5536014131309912e-009 1.809080663051077e-010 0.099999999999999589 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.3255478868134901e-005 -7.6629305511362317e-005 
		33.826832021735598 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "PinkyToe2" -p "PinkyToe1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033823041 1.762479051592436e-015 -1.8664753076436114e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 3.0703451986258095e-010 3.5869203352502025e-015 7.2770427202155569e-015 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "PinkyToe3" -p "PinkyToe2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033815047 7.9103390504542404e-016 7.2164496600635175e-016 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 3.0703451986258095e-010 3.5869203352502025e-015 7.2770427202155569e-015 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "PinkyToe4" -p "PinkyToe3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.07086969003382193 1.5265566588595902e-016 8.8817841970012523e-016 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "Tail0" -p "Root";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "flipOrient" -ln "flipOrient" -dv 1 -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" -0.39589847889101359 0.034045264281664434 -4.3412517639242223e-017 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" -6.2341086653591647e-012 1.0554874906164521e-013 -2.1949802995450153e-012 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -179.9999999999977 9.7725991977260438e-016 176.90315175605846 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "0Tail";
	setAttr -k on ".fat" 0.12202417073849868;
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;
createNode joint -n "Tail1" -p "Tail0";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.30000000000000027 -1.0658141036401503e-014 -5.526519577461712e-016 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.3671480605547149e-012 3.4515300619750212e-015 -4.969616689786743e-016 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "30";
	setAttr -k on ".fat" 0.12202417073849868;
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;
createNode joint -n "Tail2" -p "Tail1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.30000000000000249 -1.6431300764452317e-014 -1.3785315145865734e-016 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.3671480605547149e-012 3.4515300619750212e-015 -4.969616689786743e-016 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "1";
	setAttr -k on ".fat" 0.12202417073849868;
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;
createNode joint -n "Tail3" -p "Tail2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.30000000000000004 3.1974423109204508e-014 -8.4670006458501905e-017 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.3671480605547149e-012 3.4515300619750212e-015 -4.969616689786743e-016 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "29";
	setAttr -k on ".fat" 0.12202417073849868;
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;
createNode joint -n "Tail4" -p "Tail3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.30000000000001315 -6.6613381477509392e-015 -2.748970323360557e-017 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.3671480605547149e-012 3.4515300619750212e-015 -4.969616689786743e-016 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "2";
	setAttr -k on ".fat" 0.12202417073849868;
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;
createNode joint -n "Tail5" -p "Tail4";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.29999999999999272 2.3092638912203256e-014 -1.6638167805137063e-017 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.3671480605547149e-012 3.4515300619750212e-015 -4.969616689786743e-016 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "28";
	setAttr -k on ".fat" 0.12202417073849868;
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;
createNode joint -n "Tail6" -p "Tail5";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.30000000000000071 -7.1054273576010019e-015 1.4758155092496115e-017 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "3";
	setAttr -k on ".fat" 0.12202417073849868;
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;

createNode joint -n "Spine2" -p "Spine1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29904621524512448 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.48988500096718918 7.1054273576010019e-015 5.1809335189780044e-016 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" 5.9042182185265374e-017 2.3054341632553068e-015 -1.2960760326963826e-013 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.1280171409848587e-014 -5.1695595455892551e-015 
		2.9340491441670324 ;
	setAttr -k on ".fat" 0.34301931143407527;
	setAttr ".fatYabs" 0.34301930665969849;
	setAttr ".fatZabs" 0.34301930665969849;
createNode joint -n "Spine3" -p "Spine2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.34301931143407527 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.49015810227652112 -8.8817841970012523e-016 -1.9722695877214669e-017 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" 7.687514353224446e-018 6.3992685550885345e-016 -5.0809361036379685e-013 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -6.6076658409656115e-014 4.7957967411108638e-015 1.3765352618396656 ;
	setAttr -k on ".fat" 0.38699240762302606;
	setAttr ".fatYabs" 0.38699239492416382;
	setAttr ".fatZabs" 0.38699239492416382;
createNode joint -n "Chest" -p "Spine3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.1396060751118258 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "freeOrient" -ln "freeOrient" -dv 1 -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 0.48999666439911016 -4.4408920985006262e-015 -5.4726897395173175e-018 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.7962927705556823e-013 -5.2622662732944749e-015 -8.1951239132327576 ;
	setAttr ".pa" -type "double3" 8.9511097085308541e-017 1.5252296155034558e-015 0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Chest";
	setAttr -k on ".fat" 0.43096550381197685;
	setAttr ".fatYabs" 0.43096551299095154;
	setAttr ".fatZabs" 0.43096551299095154;

createNode joint -n "Neck" -p "Chest";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 4.4519209697165998 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.32505309631582135 -1.7763568394002505e-015 -6.2667198250920642e-017 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.8297359042188613e-014 2.1076872506270605e-013 -10 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "0Neck";
	setAttr -k on ".fat" 0.23233890461500986;
	setAttr ".fatYabs" 0.23233890533447266;
	setAttr ".fatZabs" 0.23233890533447266;

createNode joint -n "Scapula" -p "Chest";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.5609724984406954 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.067645435441803681 -0.14927400935563107 -0.28199999999999925 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -179.99999999999997 -1.4124500153760508e-030 51.431142003066661 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "LegAimFront";
	setAttr -k on ".fat" 0.41732266392566553;
	setAttr -k on ".fatZ" 0.82;
	setAttr ".fatYabs" 0.41732266545295715;
	setAttr ".fatZabs" 0.34220457077026367;
createNode joint -n "Shoulder" -p "Scapula";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.6799301464676359 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	addAttr -ci true -k true -sn "flipOrient" -ln "flipOrient" -dv 1 -min 0 -max 1 -at "bool";
	setAttr ".t" -type "double3" 0.63712434503245718 4.8849813083506888e-015 2.9420910152566648e-015 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.3860016151420766e-014 -1.0126896573030209e-014 -76.710311580348076 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "HipFront";
	setAttr -k on ".fat" 0.2330247775791765;
	setAttr ".fatYabs" 0.23302477598190308;
	setAttr ".fatZabs" 0.23302477598190308;
createNode joint -n "Elbow" -p "Shoulder";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4590705413009739 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.8011542070232478 8.7707618945387367e-015 2.2093438190040615e-014 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.4849828139575569e-015 1.6652282271389974e-014 53.774170409682171 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "4";
	setAttr -k on ".fat" 0.14903625610774801;
	setAttr ".fatYabs" 0.14903625845909119;
	setAttr ".fatZabs" 0.14903625845909119;
createNode joint -n "Wrist" -p "Elbow";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4590705413009739 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.92253716456537793 2.6645352591003757e-015 -1.3267165144270621e-014 ;
	setAttr ".ro" 3;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -8.0270785457731814e-016 1.0702703592965032e-015 20.042272708705362 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "FootFront";
	setAttr -k on ".fat" 0.10602417073849867;
	setAttr ".fatYabs" 0.10602416843175888;
	setAttr ".fatZabs" 0.10602416843175888;
createNode joint -n "Fingers1" -p "Wrist";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4590705413009739 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.3037594217040479 -2.2204460492503131e-016 -1.7319479184152442e-014 ;
	setAttr ".r" -type "double3" 2.185277498513738e-013 6.2226991790460848e-013 -2.2899993706537197e-013 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 1.7355833936888187e-014 5.0366562305899109e-015 38.70034567425332 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "QToesFront";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.070404834147699755;
	setAttr ".fatYabs" 0.070404835045337677;
	setAttr ".fatZabs" 0.070404835045337677;
createNode joint -n "Fingers2" -p "Fingers1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 2.4590705413009739 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.16355884621508321 -4.4408920985006262e-016 -1.7208456881689926e-015 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "ToesEndFront";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.024404834147699738;
	setAttr ".fatYabs" 0.02440483495593071;
	setAttr ".fatZabs" 0.02440483495593071;
createNode joint -n "frontFootSideInner" -p "Fingers1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.12202417073849868 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.077776116199695045 -0.029340084791673493 -0.071012454125314428 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.3721681116084156e-016 -7.9293304325868277e-015 
		0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "BigToe";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.024404834147699738;
	setAttr ".fatYabs" 0.02440483495593071;
	setAttr ".fatZabs" 0.02440483495593071;
createNode joint -n "frontFootSideOuter" -p "Fingers1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.12202417073849868 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.079208782561912772 -0.030096858340501464 0.076481361122393066 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.3721681116084156e-016 -7.9293304325868277e-015 
		0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "PinkyToe";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.024404834147699738;
	setAttr ".fatYabs" 0.02440483495593071;
	setAttr ".fatZabs" 0.02440483495593071;
createNode joint -n "frontHeel" -p "Fingers1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.5765498585786919 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -0.026135120821314306 -0.061731463061124259 -3.8857805861880479e-016 ;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.3721681116084156e-016 -7.9293304325868277e-015 
		0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Heel";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.11530997171573838;
	setAttr ".fatYabs" 0.11530996859073639;
	setAttr ".fatZabs" 0.11530996859073639;
createNode joint -n "IndexFinger1" -p "Fingers1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" -4.4408920985006262e-016 -8.8817841970012523e-016 -0.050316661619187047 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -2.4700291004192332e-015 -7.5360510750322286e-015 
		17.155760846016424 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "IndexFinger2" -p "IndexFinger1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033818822 1.6653345369377348e-016 -2.8968519250049951e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -3.016796683671633e-013 9.708956113100679e-013 1.5902773407312469e-014 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "IndexFinger3" -p "IndexFinger2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033819488 9.3675067702747583e-016 -7.0094305359980069e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -3.016796683671633e-013 9.708956113100679e-013 1.5902773407312469e-014 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "IndexFinger4" -p "IndexFinger3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.07086969003381971 6.9388939039072284e-017 -7.0094349768901054e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "MiddleFinger1" -p "Fingers1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0 -8.8817841970012523e-016 -0.00031666161918675328 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.1255413010105912e-005 -7.4800655121569639e-005 
		17.156008085202728 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "MiddleFinger2" -p "MiddleFinger1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033821264 8.81239525796218e-016 -2.2755936024410062e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.1222500179251026e-009 3.1193784088237649e-014 6.3615946770568981e-015 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "MiddleFinger3" -p "MiddleFinger2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033820376 5.7592819402429996e-016 1.7208456881689926e-015 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.1222500179251026e-009 3.1193784088237649e-014 6.3615946770568981e-015 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "MiddleFinger4" -p "MiddleFinger3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033820154 -1.3183898417423734e-016 -5.5511151231257827e-017 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "RingFinger1" -p "Fingers1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 1.1102230246251565e-015 -7.2164496600635175e-016 0.049683338380813014 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.1259211887905871e-005 -7.4800655121569815e-005 
		17.156008085202654 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "RingFinger2" -p "RingFinger1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033821264 4.5102810375396984e-016 -1.964089446637729e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.3847737233299225e-009 -1.651432987520993e-014 3.1809914641803938e-015 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "RingFinger3" -p "RingFinger2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033816601 1.214306433183765e-015 9.9920072216264089e-016 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 2.3847737233299225e-009 -1.651432987520993e-014 3.1809914641803938e-015 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "RingFinger4" -p "RingFinger3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033820376 5.0653925498522767e-016 8.3266726846886741e-016 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "PinkyFinger1" -p "Fingers1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 8.8817841970012523e-016 -7.7715611723760958e-016 0.099683338380813558 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.1260402342088859e-005 -7.4800655121569652e-005 
		17.156008085202664 ;
	setAttr ".pa" -type "double3" -0.00019030234564052423 0.00053514845282692043 25.864574245063647 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Toes";
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "PinkyFinger2" -p "PinkyFinger1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033820154 1.3114509478384662e-015 -1.8664719769745375e-010 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 7.6558315069033074e-010 -2.202040067129286e-015 -1.272183047455018e-014 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "PinkyFinger3" -p "PinkyFinger2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033816823 6.5225602696727947e-016 1.1102230246251565e-015 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 7.6558315069033074e-010 -2.202040067129286e-015 -1.272183047455018e-014 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;
createNode joint -n "PinkyFinger4" -p "PinkyFinger3";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.29999999999999993 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.070869690033821708 1.8735013540549517e-016 6.6613381477509392e-016 ;
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 5;
	setAttr ".radi" 0.3;
	setAttr -k on ".fat" 0.02;
	setAttr ".fatYabs" 0.019999999552965164;
	setAttr ".fatZabs" 0.019999999552965164;

createNode joint -n "Neck1" -p "Neck";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.23233890461500986 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.26250780521071859 6.6613381477509392e-016 1.3644400812668054e-015 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" 2.5848550526770409e-014 -2.9545028447160472e-013 1.6745620397905415e-012 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -4.7121972971619972e-015 -3.214045604452795e-015 -10.000000000000004 ;
	setAttr -k on ".fat" 0.20089260307667325;
	setAttr ".fatYabs" 0.20089259743690491;
	setAttr ".fatZabs" 0.20089259743690491;
createNode joint -n "Neck2" -p "Neck1";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.20089260307667325 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.26249455893988349 7.1054273576010019e-015 1.3535742557325564e-015 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" 3.0399064043747133e-014 -3.4746289197500694e-013 -4.1983321795318433e-013 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.9978017566442397e-014 -2.0422854644767535e-014 
		-9.9999999999999964 ;
	setAttr -k on ".fat" 0.16944630153833665;
	setAttr ".fatYabs" 0.16944630444049835;
	setAttr ".fatZabs" 0.16944630444049835;
createNode joint -n "Head" -p "Neck2";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.1940176038839925 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.26180877406245662 -1.1102230246251565e-015 1.5877056661634609e-015 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".ro" 5;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.9978017566442429e-014 -2.0422854644767617e-014 
		0 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "2";
	setAttr -k on ".fat" 0.13800000000000004;
	setAttr ".fatYabs" 0.1379999965429306;
	setAttr ".fatZabs" 0.1379999965429306;

createNode joint -n "HeadEnd" -p "Head";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 1.1940176038839925 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.29139801592224801 -1.3322676295501878e-014 2.0100736924817786e-016 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "18";
	setAttr -k on ".fat" 0.12202417073849868;
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;

createNode joint -n "Jaw" -p "Head";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.5 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.015619878308713364 0.18675086513278716 -9.1785061659917922e-017 ;
	setAttr -l on ".tz";
	setAttr ".r" -type "double3" -2.2877657075074474e-014 -2.2668061640073694e-014 -3.5304156964245035e-012 ;
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" -1.5753372512221993e-014 5.8107544511984722e-015 90.527331630670076 ;
	setAttr ".otp" -type "string" "16";
	setAttr -k on ".fat" 0.12202417073849868;
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;
createNode joint -n "JawEnd" -p "Jaw";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.5 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.63210497686441158 -3.8635761256955448e-014 2.5008115257926603e-016 ;
	setAttr -l on ".tz";
	setAttr -l on ".rx";
	setAttr -l on ".ry";
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "6";
	setAttr -k on ".fat" 0.12202417073849868;
	setAttr ".fatYabs" 0.12202417105436325;
	setAttr ".fatZabs" 0.12202417105436325;
createNode joint -n "Eye" -p "Head";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.13852234887235859 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "noFlip" -ln "noFlip" -dv 1 -min 0 -max 1 -at "bool";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.22831565632732298 0.35728018318545152 -0.18402524244984339 ;
	setAttr ".ro" 2;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".jo" -type "double3" 24.794774758996954 34.638847582582443 67.570522454106737 ;
	setAttr ".dl" yes;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "Eye";
	setAttr -k on ".fat" 0.033806149499797128;
	setAttr ".fatYabs" 0.033806148916482925;
	setAttr ".fatZabs" 0.033806148916482925;
createNode joint -n "EyeEnd" -p "Eye";
	addAttr -ci true -k true -sn "fat" -ln "fat" -dv 0.13852234887235859 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatY" -ln "fatY" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "fatZ" -ln "fatZ" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "fatYabs" -ln "fatYabs" -at "double";
	addAttr -ci true -sn "fatZabs" -ln "fatZabs" -at "double";
	setAttr ".t" -type "double3" 0.033806149499812199 -1.6875389974302379e-014 2.042810365310288e-014 ;
	setAttr ".ro" 1;
	setAttr ".mnrl" -type "double3" -360 -360 -360 ;
	setAttr ".mxrl" -type "double3" 360 360 360 ;
	setAttr ".typ" 18;
	setAttr ".otp" -type "string" "16";
	setAttr -k on ".fat" 0.033806149499797128;
	setAttr ".fatYabs" 0.033806148916482925;
	setAttr ".fatZabs" 0.033806148916482925;

createNode lightLinker -s -n "lightLinker1";
	setAttr -s 2 ".lnk";
	setAttr -s 2 ".slnk";
createNode displayLayerManager -n "layerManager";
	setAttr ".cdl" 14;
	setAttr -s 14 ".dli[1:13]"  14 2 3 4 5 6 7 8 
		9 10 11 12 13;
createNode displayLayer -n "defaultLayer";
createNode renderLayer -n "defaultRenderLayer";
	setAttr ".g" yes;
createNode animCurveUA -n "SDK1FKIndexFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 20;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -20;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -29.999999999999996;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 20;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -20;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -29.999999999999996;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger3_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKIndexFinger1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger3_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexFinger2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKIndexFinger1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger3_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger3_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleFinger1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger3_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKRingFinger1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger3_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingFinger2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKRingFinger1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger3_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKPinkyFinger1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger3_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyFinger2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKPinkyFinger1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexToe1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 20;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingToe1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -20;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe1_R_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -29.999999999999996;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexToe1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 20;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingToe1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -20;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe1_L_rotateY";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "spread" -ln "spread" -smn -5 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -29.999999999999996;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexToe3_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexToe2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKIndexToe1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexToe3_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKIndexToe2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKIndexToe1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "indexCurl" -ln "indexCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe3_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe3_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKMiddleToe1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "middleCurl" -ln "middleCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingToe3_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingToe2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKRingToe1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingToe3_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKRingToe2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKRingToe1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "ringCurl" -ln "ringCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe3_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe2_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKPinkyToe1_R_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe3_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK1FKPinkyToe2_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;
createNode animCurveUA -n "SDK2FKPinkyToe1_L_rotateZ";
	addAttr -ci true -sn "drivingSystemOut" -ln "drivingSystemOut" -at "message";
	addAttr -ci true -sn "pinkyCurl" -ln "pinkyCurl" -smn -2 -smx 10 -at "float";
	setAttr ".tan" 2;
	setAttr ".wgt" no;
	setAttr -s 2 ".ktv[0:1]"  0 0 10 -90;
	setAttr ".pre" 4;
	setAttr ".pst" 4;

connectAttr "SDK1FKRingFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger3_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKIndexFinger1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger3_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger3_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKRingFinger1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger3_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKPinkyFinger1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexFinger3_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKIndexFinger1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger3_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleFinger1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingFinger3_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKRingFinger1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyFinger3_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKPinkyFinger1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingToe1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexToe1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe1_R_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexToe2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexToe3_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKIndexToe1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe3_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingToe2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingToe3_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKRingToe1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe2_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe3_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKPinkyToe1_R_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingToe1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexToe1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe1_L_rotateY.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexToe2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKIndexToe3_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKIndexToe1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe3_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKMiddleToe1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingToe2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKRingToe3_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKRingToe1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe2_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK1FKPinkyToe3_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "SDK2FKPinkyToe1_L_rotateZ.drivingSystemOut" "FitSkeleton.drivingSystem"
		 -na;
connectAttr "Root.s" "Hip.is";
connectAttr "Hip.s" "Knee.is";
connectAttr "Knee.s" "Ankle.is";
connectAttr "Ankle.s" "Toes1.is";
connectAttr "Toes1.s" "Toes2.is";
connectAttr "Toes1.s" "backFootSideOuter.is";
connectAttr "Toes1.s" "backFootSideInner.is";
connectAttr "Toes1.s" "backHeel.is";
connectAttr "Toes1.s" "IndexToe1.is";
connectAttr "IndexToe1.s" "IndexToe2.is";
connectAttr "IndexToe2.s" "IndexToe3.is";
connectAttr "IndexToe3.s" "IndexToe4.is";
connectAttr "Toes1.s" "MiddleToe1.is";
connectAttr "MiddleToe1.s" "MiddleToe2.is";
connectAttr "MiddleToe2.s" "MiddleToe3.is";
connectAttr "MiddleToe3.s" "MiddleToe4.is";
connectAttr "Toes1.s" "RingToe1.is";
connectAttr "RingToe1.s" "RingToe2.is";
connectAttr "RingToe2.s" "RingToe3.is";
connectAttr "RingToe3.s" "RingToe4.is";
connectAttr "Toes1.s" "PinkyToe1.is";
connectAttr "PinkyToe1.s" "PinkyToe2.is";
connectAttr "PinkyToe2.s" "PinkyToe3.is";
connectAttr "PinkyToe3.s" "PinkyToe4.is";
connectAttr "Root.s" "Tail0.is";
connectAttr "Tail0.s" "Tail1.is";
connectAttr "Tail1.s" "Tail2.is";
connectAttr "Tail2.s" "Tail3.is";
connectAttr "Tail3.s" "Tail4.is";
connectAttr "Tail4.s" "Tail5.is";
connectAttr "Tail5.s" "Tail6.is";
connectAttr "Root.s" "Spine1.is";
connectAttr "Spine1.s" "Spine2.is";
connectAttr "Spine2.s" "Spine3.is";
connectAttr "Spine3.s" "Chest.is";
connectAttr "Chest.s" "Scapula.is";
connectAttr "Scapula.s" "Shoulder.is";
connectAttr "Shoulder.s" "Elbow.is";
connectAttr "Elbow.s" "Wrist.is";
connectAttr "Wrist.s" "Fingers1.is";
connectAttr "Fingers1.s" "Fingers2.is";
connectAttr "Fingers1.s" "frontFootSideInner.is";
connectAttr "Fingers1.s" "frontFootSideOuter.is";
connectAttr "Fingers1.s" "frontHeel.is";
connectAttr "Fingers1.s" "IndexFinger1.is";
connectAttr "IndexFinger1.s" "IndexFinger2.is";
connectAttr "IndexFinger2.s" "IndexFinger3.is";
connectAttr "IndexFinger3.s" "IndexFinger4.is";
connectAttr "Fingers1.s" "MiddleFinger1.is";
connectAttr "MiddleFinger1.s" "MiddleFinger2.is";
connectAttr "MiddleFinger2.s" "MiddleFinger3.is";
connectAttr "MiddleFinger3.s" "MiddleFinger4.is";
connectAttr "Fingers1.s" "RingFinger1.is";
connectAttr "RingFinger1.s" "RingFinger2.is";
connectAttr "RingFinger2.s" "RingFinger3.is";
connectAttr "RingFinger3.s" "RingFinger4.is";
connectAttr "Fingers1.s" "PinkyFinger1.is";
connectAttr "PinkyFinger1.s" "PinkyFinger2.is";
connectAttr "PinkyFinger2.s" "PinkyFinger3.is";
connectAttr "PinkyFinger3.s" "PinkyFinger4.is";
connectAttr "Chest.s" "Neck.is";
connectAttr "Neck.s" "Neck1.is";
connectAttr "Neck1.s" "Neck2.is";
connectAttr "Neck2.s" "Head.is";
connectAttr "Head.s" "Jaw.is";
connectAttr "Jaw.s" "JawEnd.is";
connectAttr "Head.s" "Eye.is";
connectAttr "Eye.s" "EyeEnd.is";
connectAttr "Head.s" "HeadEnd.is";
relationship "link" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
connectAttr "layerManager.dli[0]" "defaultLayer.id";
connectAttr "FitSkeleton.drivingSystem_Fingers_R[1]" "SDK1FKIndexFinger1_R_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[0]" "SDK1FKRingFinger1_R_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[2]" "SDK1FKPinkyFinger1_R_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[1]" "SDK1FKIndexFinger1_L_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[0]" "SDK1FKRingFinger1_L_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[2]" "SDK1FKPinkyFinger1_L_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[4]" "SDK1FKIndexFinger3_R_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[3]" "SDK1FKIndexFinger2_R_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[5]" "SDK2FKIndexFinger1_R_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[4]" "SDK1FKIndexFinger3_L_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[3]" "SDK1FKIndexFinger2_L_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[5]" "SDK2FKIndexFinger1_L_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[7]" "SDK1FKMiddleFinger3_R_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[6]" "SDK1FKMiddleFinger2_R_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[8]" "SDK1FKMiddleFinger1_R_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[7]" "SDK1FKMiddleFinger3_L_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[6]" "SDK1FKMiddleFinger2_L_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[8]" "SDK1FKMiddleFinger1_L_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[10]" "SDK1FKRingFinger3_R_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[9]" "SDK1FKRingFinger2_R_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[11]" "SDK2FKRingFinger1_R_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[10]" "SDK1FKRingFinger3_L_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[9]" "SDK1FKRingFinger2_L_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[11]" "SDK2FKRingFinger1_L_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[13]" "SDK1FKPinkyFinger3_R_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[12]" "SDK1FKPinkyFinger2_R_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_R[14]" "SDK2FKPinkyFinger1_R_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[13]" "SDK1FKPinkyFinger3_L_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[12]" "SDK1FKPinkyFinger2_L_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Fingers_L[14]" "SDK2FKPinkyFinger1_L_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[1]" "SDK1FKIndexToe1_R_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[0]" "SDK1FKRingToe1_R_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[2]" "SDK1FKPinkyToe1_R_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[1]" "SDK1FKIndexToe1_L_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[0]" "SDK1FKRingToe1_L_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[2]" "SDK1FKPinkyToe1_L_rotateY.spread"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[4]" "SDK1FKIndexToe3_R_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[3]" "SDK1FKIndexToe2_R_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[5]" "SDK2FKIndexToe1_R_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[4]" "SDK1FKIndexToe3_L_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[3]" "SDK1FKIndexToe2_L_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[5]" "SDK2FKIndexToe1_L_rotateZ.indexCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[7]" "SDK1FKMiddleToe3_R_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[6]" "SDK1FKMiddleToe2_R_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[8]" "SDK1FKMiddleToe1_R_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[7]" "SDK1FKMiddleToe3_L_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[6]" "SDK1FKMiddleToe2_L_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[8]" "SDK1FKMiddleToe1_L_rotateZ.middleCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[10]" "SDK1FKRingToe3_R_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[9]" "SDK1FKRingToe2_R_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[11]" "SDK2FKRingToe1_R_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[10]" "SDK1FKRingToe3_L_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[9]" "SDK1FKRingToe2_L_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[11]" "SDK2FKRingToe1_L_rotateZ.ringCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[13]" "SDK1FKPinkyToe3_R_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[12]" "SDK1FKPinkyToe2_R_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_R[14]" "SDK2FKPinkyToe1_R_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[13]" "SDK1FKPinkyToe3_L_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[12]" "SDK1FKPinkyToe2_L_rotateZ.pinkyCurl"
		;
connectAttr "FitSkeleton.drivingSystem_Toes_L[14]" "SDK2FKPinkyToe1_L_rotateZ.pinkyCurl"
		;
connectAttr "defaultRenderLayer.msg" ":defaultRenderingList1.r" -na;
connectAttr "defaultRenderLayer.msg" ":defaultRenderingList1.r" -na;
// End of cat.ma
