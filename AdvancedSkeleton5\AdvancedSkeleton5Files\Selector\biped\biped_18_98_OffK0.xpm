/* XPM */
static char *biped_18_98_OffK0[] = {
/* columns rows colors chars-per-pixel */
"10 10 55 1",
"  c gray57",
". c #939293",
"X c #949495",
"o c gray59",
"O c #979797",
"+ c gray60",
"@ c gray61",
"# c #9D9D9D",
"$ c gray62",
"% c #A5A6A6",
"& c gray66",
"* c #A9A9A9",
"= c #ACACAC",
"- c #B6B6B7",
"; c #B7B8B7",
": c #BCBCBD",
"> c gray74",
", c gray75",
"< c #C1C1C1",
"1 c #C2C3C3",
"2 c #CDCECE",
"3 c gray82",
"4 c #D1D2D1",
"5 c #D6D6D5",
"6 c gray84",
"7 c #D7D7D7",
"8 c #D8D8D8",
"9 c gray85",
"0 c #D9DAD9",
"q c #DFDFDE",
"w c #E2E2E2",
"e c #E2E2E3",
"r c #E8E8E9",
"t c #E9EAEA",
"y c #EAEBEA",
"u c gray94",
"i c #F4F5F5",
"p c #F6F6F5",
"a c #F8F8F7",
"s c #F8F8F8",
"d c #FAFBFB",
"f c #FBFBFB",
"g c gray99",
"h c #FDFDFC",
"j c #FDFDFD",
"k c #FDFDFF",
"l c #FDFEFD",
"z c #FDFEFE",
"x c #FDFFFE",
"c c #FDFFFF",
"v c #FEFDFE",
"b c #FEFEFC",
"n c #FEFEFD",
"m c #FEFEFF",
"M c gray100",
/* pixels */
",,,,,,,,,,",
",;371.O@$,",
",7pay-+.@,",
",wnndw=++,",
",qndnf3% ,",
",0dnndu,.,",
",2innnd7&,",
",:rnndny1,",
",*0nnnns8,",
",,,,,,,,,,"
};
