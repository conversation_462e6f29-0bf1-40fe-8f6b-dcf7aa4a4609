//Maya ASCII 2016 scene
//Name: asMotionFace.ma
//Last modified: Sat, Oct 12, 2019 11:12:28 AM
//Codeset: 1252
requires maya "2016";
requires "stereoCamera" "10.0";
currentUnit -l centimeter -a degree -t pal;
fileInfo "application" "maya";
fileInfo "product" "Maya 2016";
fileInfo "version" "2016";
fileInfo "cutIdentifier" "************-990260";
fileInfo "osv" "Microsoft Windows 8 Business Edition, 64-bit  (Build 9200)\n";
createNode animCurveTA -n "EyeBrowInner_L_rotateZ";
	rename -uid "D388CDF2-4151-816A-751E-6995FB14957C";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 -5.3143696758161676 85 -42.514957406529341 93 -48.58852275031925 108 -48.58852275031925
		 113 -43.274153074503083 121 -6.0735653437899089 129 0 144 0 149 0 157 0 165 0 180 0
		 185 -4.7412037445078798 193 -37.929629956063039 201 -42.68125392858083 216 -42.68125392858083
		 221 -43.714307516996477 229 -50.945682635906032 237 -52.126315308381059 252 -52.126315308381059
		 257 -54.265522927433267 265 -69.239976260798727 273 -71.38388543505765 288 -71.38388543505765
		 293 -63.454279020762925 301 -7.947034120699775 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.87248975038528442 0.94362378120422363 
		1 1 0.43397760391235352 0.60961300134658813 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.48863247036933899 -0.3310200572013855 
		0 0 0.90092366933822632 0.79269915819168091 0 0 0 0 0;
createNode animCurveTA -n "EyeBrowInner_R_rotateZ";
	rename -uid "AEF6F2B3-4DFA-A4F3-FAED-6AB921A46CEF";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 -6.163373933151548 13 -50.077413206856328
		 21 -56.350847388814152 36 -56.350847388814152 41 -50.187473455662605 49 -7.043855923601769
		 57 0 72 0 77 -5.8120083566046876 85 -46.496066852837501 93 -53.138362117528573 108 -53.138362117528573
		 113 -47.326353760923887 121 -6.6422952646910716 129 0 144 0 149 0 157 0 165 0 180 0
		 185 -6.7997008178651397 193 -54.397606542921118 201 -61.21225175818816 216 -61.21225175818816
		 221 -54.517161722136329 229 -7.6515314697735173 237 0 252 0 257 -7.4082872732965876
		 265 -59.266298186372701 273 -66.690867409720482 288 -66.690867409720482 293 -59.282580136423903
		 301 -7.4245692233477874 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.45827180147171021 0.63553333282470703 
		1 1 0.45827180147171021 0.63553333282470703 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.88881212472915649 -0.77207350730895996 
		0 0 0.88881212472915649 0.77207350730895996 0 0 0 0 0;
createNode animCurveTA -n "EyeBrowOuter_L_rotateX";
	rename -uid "BC1D0A39-4912-9316-B865-1287D3DF060E";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 -0.30689242862796806
		 193 -2.4551394290237445 201 -2.7627063465058397 216 -2.7627063465058397 221 -2.4605353398567633
		 229 -0.34533829331323007 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTA -n "EyeBrowOuter_L_rotateY";
	rename -uid "FA277608-4C65-9EB5-EB77-8E91C99023EE";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0.040367880455411932
		 193 0.32294304364329546 201 0.3633996447150929 216 0.3633996447150929 221 0.32365280857437961
		 229 0.04542495558938664 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTA -n "EyeBrowOuter_L_rotateZ";
	rename -uid "FBE6756C-43F3-E2BA-B7E2-20963E00E460";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 8.0620192386534466
		 193 64.496153909227573 201 72.575891871482455 216 72.575891871482455 221 64.637903698039054
		 229 9.071986483935305 237 0 252 0 257 4.4373797260882117 265 35.499037808705694 273 39.946170017708383
		 288 39.946170017708383 293 37.007760645109478 301 16.438895036917156 309 13.494027621741109
		 324 13.494027621741109 329 11.80727416902347 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.65238958597183228 0.8085867166519165 
		1 1 0.79260724782943726 0.90087074041366577 1 1 0.91477751731872559 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.75788384675979614 0.58837699890136719 
		0 0 -0.60973256826400757 -0.43408748507499695 0 0 -0.40395793318748474 0 0;
createNode animCurveTA -n "EyeBrowOuter_R_rotateZ";
	rename -uid "8DC14BBB-40ED-4B45-CB28-53934D1BD9F3";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 2.4293524227194561 13 19.738488434595581
		 21 22.211222150577886 36 22.211222150577886 41 19.781869727858428 49 2.776402768822237
		 57 0 72 0 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 5.3867209604406305
		 193 43.093767683525044 201 48.492327591131478 216 48.492327591131478 221 43.188479260851473
		 229 6.0615409488914338 237 0 252 0 257 4.904235079164704 265 39.233880633317632 273 44.148894251117866
		 288 44.148894251117866 293 41.172094458756753 301 20.334495912228938 309 17.351153702741755
		 324 17.351153702741755 329 15.182259489899035 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.61447268724441528 0.77926290035247803 
		1 1 0.78876042366027832 0.89864671230316162 1 1 0.86959326267242432 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.78893810510635376 0.62669724225997925 
		0 0 -0.6147007942199707 -0.43867319822311401 0 0 -0.49376872181892395 0 0;
createNode animCurveTA -n "EyeBrowRegion_L_rotateX";
	rename -uid "0B0A4896-4593-8D70-5597-EF9F0951A6A0";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 -0.45067061312491119
		 193 -3.6053649049992895 201 -4.057026002988211 216 -4.057026002988211 221 -3.6132887839113761
		 229 -0.50712825037352616 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTA -n "EyeBrowRegion_L_rotateY";
	rename -uid "C4202F7A-484E-1F04-7894-3CA451BC823E";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 -0.24918203375322415
		 193 -1.9934562700257932 201 -2.2431859566004526 216 -2.2431859566004526 221 -1.9978374925972782
		 229 -0.28039824457505658 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTA -n "EyeBrowRegion_L_rotateZ";
	rename -uid "00A27028-4F3C-CF47-A672-119D78089127";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0.14957364697607517
		 193 1.1965891758086014 201 1.3464915560747335 216 1.3464915560747335 221 1.1992190421290598
		 229 0.16831144450934166 237 0 252 0 257 0 265 0 273 0 288 0 293 -1.809487384139282
		 301 -14.475899073114256 309 -16.289363352603296 324 -16.289363352603296 329 -14.253192933527885
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.90372425317764282 0.95868539810180664 
		1 1 0.88244986534118652 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 -0.42811506986618042 -0.28446850180625916 
		0 0 0.47040638327598572 0 0;
createNode animCurveTA -n "EyeBrowRegion_R_rotateZ";
	rename -uid "FB82A0B6-4D2F-F6E2-BCB0-A8881FCFF73E";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 -2.1790304706376196
		 301 -17.432243765100957 309 -19.616063313696021 324 -19.616063313696021 329 -17.164055399484017
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.86860179901123047 0.94168698787689209 
		1 1 0.84153139591217041 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 -0.49551072716712952 -0.33649015426635742 
		0 0 0.54020828008651733 0 0;
createNode animCurveTA -n "FKHead_M_rotateX";
	rename -uid "B0702E8C-4812-36D0-C1D5-5BB433F2F5CE";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.061034204737808392 13 0.49590291349469312
		 21 0.55802701474567673 36 0.55802701474567673 41 1.587998375859752 49 8.7977979036582781
		 57 9.9749080306457945 72 9.9749080306457945 77 6.6428579921440969 85 -16.681492277367788
		 93 -20.489549464226876 108 -20.489549464226876 113 -18.4079151944477 121 -3.8364753059934689
		 129 -2.4910166278819306 144 -2.4910166278819306 149 -1.8409233817179427 157 2.7909909972004723
		 165 3.452693051331674 180 3.452693051331674 185 2.0110445757911237 193 -8.0804947529927276
		 201 -9.5253116867212793 216 -9.5253116867212793 221 -8.5097419174739084 229 -1.4007535327423142
		 237 -0.24010236788817671 252 -0.24010236788817671 257 0.26780137788386316 265 3.8231275982881425
		 273 4.3321476160289114 288 4.3321476160289114 293 3.7013643507646172 301 -0.71411850608544425
		 309 -1.346288108196473 324 -1.346288108196473 329 -1.1780020946719139 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.9912751317024231 0.99654948711395264 
		1 1 0.98663729429244995 0.99469274282455444 1 1 0.99903088808059692 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.13180889189243317 0.083000898361206055 
		0 0 -0.1629321277141571 -0.10288963466882706 0 0 0.044014520943164825 0 0;
createNode animCurveTA -n "FKHead_M_rotateY";
	rename -uid "38E52898-47B9-C7FD-333B-469FADC02ACE";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.41819121170842821 13 3.3978035951309793
		 21 3.8234625070484864 36 3.8234625070484864 41 2.5709395838591509 49 -6.1967208784661985
		 57 -7.6281756478254374 72 -7.6281756478254374 77 -5.9542860624985767 85 5.7629410347894501
		 93 7.6759577037344338 108 7.6759577037344338 113 7.3303754327138071 121 4.9112995355694231
		 129 4.5578105317033613 144 4.5578105317033613 149 4.8551485550225726 157 6.9736819711719562
		 165 7.2763296020504393 180 7.2763296020504393 185 8.9719191541420855 193 20.841046018783608
		 201 22.540362141319413 216 22.540362141319413 221 19.628494743935764 229 -0.75457703774976537
		 237 -4.082425491902506 252 -4.082425491902506 257 -3.7568435444015273 265 -1.4777699118946721
		 273 -1.1514723996739105 288 -1.1514723996739105 293 -0.82322201136350159 301 1.4745307068093616
		 309 1.8035025245446288 324 1.8035025245446288 329 1.5780647089765503 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.99638700485229492 0.9985777735710144 
		1 1 0.99632781744003296 0.99855446815490723 1 1 0.99826294183731079 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.084929272532463074 0.053314425051212311 
		0 0 0.08562026172876358 0.053750131279230118 0 0 -0.05891701951622963 0 0;
createNode animCurveTA -n "FKHead_M_rotateZ";
	rename -uid "42FD83E7-4D6C-753B-792A-18946E2740E4";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.3733316118058041 13 3.0333193459221581
		 21 3.413317593653066 36 3.413317593653066 41 2.4425852544559761 49 -4.3525411199236537
		 57 -5.4619495075774704 72 -5.4619495075774704 77 -9.5890985540021472 85 -38.479141878974886
		 93 -43.195883646317377 108 -43.195883646317377 113 -39.533067931902082 121 -13.893357930995016
		 129 -10.276908797880756 144 -10.276908797880756 149 -11.838631569196481 157 -22.965906314821019
		 165 -24.555516992767384 180 -24.555516992767384 185 -24.649811482794547 193 -25.30987291298468
		 201 -25.404374643649263 216 -25.404374643649263 221 -22.84928434249241 229 -4.9636522343944556
		 237 -2.0435490330723414 252 -2.0435490330723414 257 -2.5641776488640278 265 -6.208577959405833
		 273 -6.7303508139135451 288 -6.7303508139135451 293 -7.4086578081247971 301 -12.156806767603561
		 309 -12.836604546417474 324 -12.836604546417474 329 -11.23202897811529 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.99083852767944336 0.99637532234191895 
		1 1 0.98459595441818237 0.99387055635452271 1 1 0.92195665836334229 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.1350516676902771 -0.085065506398677826 
		0 0 -0.17484506964683533 -0.11054997146129608 0 0 0.38729307055473328 0 0;
createNode animCurveTA -n "FKNeck_M_rotateX";
	rename -uid "4E68C4B2-4823-7A18-40BA-5D9C48054002";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 -0.62498879836375976 13 -5.078033986705548
		 21 -5.7141832993258035 36 -5.7141832993258035 41 -5.6573310479570065 49 -5.2593652883754292
		 57 -5.19439128681109 72 -5.19439128681109 77 -5.2468311504700083 85 -5.6139101960824345
		 93 -5.6738414688354837 108 -5.6738414688354837 113 -4.9980527204819367 121 -0.26753148200710886
		 129 0.50479851611123039 144 0.50479851611123039 149 0.50479851611123039 157 0.50479851611123039
		 165 0.50479851611123039 180 0.50479851611123039 185 1.311051716846217 193 6.9548241219911215
		 201 7.7628493077826679 216 7.7628493077826679 221 6.9137876647439382 229 0.97035616347283327
		 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTA -n "FKNeck_M_rotateY";
	rename -uid "467C27C3-4C70-49B5-8478-82B5EA1929C9";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.63629635532902928 13 5.169907887048363
		 21 5.817566677293982 36 5.817566677293982 41 4.0351432529808076 49 -8.4418207172114172
		 57 -10.478876059283618 72 -10.478876059283618 77 -8.8898854175083137 85 2.2330490749188172
		 93 4.0490383798048812 108 4.0490383798048812 113 2.3017401717635519 121 -9.929347284525754
		 129 -11.926259522287275 144 -11.926259522287275 149 -11.926259522287275 157 -11.926259522287275
		 165 -11.926259522287275 180 -11.926259522287275 185 -11.635298710224408 193 -9.5985730257843471
		 201 -9.3069727394092556 216 -9.3069727394092556 221 -8.7576732939322248 229 -4.912577175593011
		 237 -4.2848063807621193 252 -4.2848063807621193 257 -3.8088330157116403 265 -0.47701946035828285
		 273 0 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.99232548475265503 0.99696779251098633 
		1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.12365333735942841 0.077815532684326172 
		0 0 0 0 0 0 0 0 0;
createNode animCurveTA -n "FKNeck_M_rotateZ";
	rename -uid "47C97D35-4479-EE85-6494-5E857C8CF93E";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.19915852348063187 13 1.618163003280134
		 21 1.820877928965777 36 1.820877928965777 41 1.8232134983357922 49 1.8395624839258988
		 57 1.8422317060630591 72 1.8422317060630591 77 4.9805535958664997 85 26.948806824490585
		 93 30.535460412837374 108 30.535460412837374 113 28.639572308751173 121 15.36835558014776
		 129 13.201626318334959 144 13.201626318334959 149 14.469035998894267 157 23.499329972879327
		 165 24.789371969162907 180 24.789371969162907 185 24.047339142144359 193 18.85310935301451
		 201 18.109445684617899 216 18.109445684617899 221 16.128725062862816 229 2.263680710577237
		 237 0 252 0 257 1.8501856344016412 265 14.80148507521313 273 16.6557370516684 288 16.6557370516684
		 293 15.397513064709052 301 6.5899451559936377 309 5.3289558415904263 324 5.3289558415904263
		 329 4.6628363613916228 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.89998030662536621 0.95692580938339233 
		1 1 0.94979739189147949 0.97937053442001343 1 1 0.98513239622116089 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.43593060970306396 0.29033276438713074 
		0 0 -0.31286570429801941 -0.2020726352930069 0 0 -0.17179708182811737 0 0;
createNode animCurveTA -n "Jaw_M_rotateX";
	rename -uid "517D460A-42B2-AA2B-679E-328E8525637B";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0.058383055328133412
		 49 0.4670644426250673 57 0.53378793442864836 72 0.53378793442864836 77 0.47540487910051493
		 85 0.066723491803581059 93 0 108 0 113 -0.11531769578545392 121 -0.92254156628363138
		 129 -1.0543332186098644 144 -1.0543332186098644 149 -0.93901552282441048 157 -0.11737694035305135
		 165 0 180 0 185 0.59794953721157762 193 4.783596297692621 201 5.3828600097112567
		 216 5.3828600097112567 221 4.7608085981223542 229 0.40644871700003815 237 -0.30446718195870709
		 252 -0.30446718195870709 257 -0.27064575427530579 265 -0.033895760491496674 273 0
		 288 0 293 -0.29427654559342492 301 -2.3542123647473994 309 -2.649135671979491 324 -2.649135671979491
		 329 -2.3179937129820547 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.99996078014373779 0.99998462200164795 
		1 1 0.99704545736312866 0.99883764982223511 1 1 0.99626320600509644 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.0088540902361273766 0.0055461009033024311 
		0 0 -0.076813869178295135 -0.048200666904449463 0 0 0.08636888861656189 0 0;
createNode animCurveTA -n "Jaw_M_rotateY";
	rename -uid "BDFA51B4-4741-8EC5-9DD2-A7B5867FE472";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 -0.28761966207119927 121 -2.3009572965695941 129 -2.6296654817938219
		 144 -2.6296654817938219 149 -2.3420458197226228 157 -0.29275572746532763 165 0 180 0
		 185 0 193 0 201 0 216 0 221 0.37483013639289176 229 2.998641091143134 237 3.4270183898778672
		 252 3.4270183898778672 257 3.0463315326038365 265 0.38152353168562181 273 0 288 0
		 293 -0.096194712344799385 301 -0.76955769875839508 309 -0.86596382805340288 324 -0.86596382805340288
		 329 -0.75771834954672757 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.99507027864456177 0.99805712699890137 
		1 1 0.99968308210372925 0.99987560510635376 1 1 0.99959874153137207 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.099172361195087433 -0.06230541318655014 
		0 0 -0.025175759568810463 -0.015772467479109764 0 0 0.028327256441116333 0 0;
createNode animCurveTA -n "Jaw_M_rotateZ";
	rename -uid "187E24A2-47DF-116A-743F-5A8CE4249159";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 -0.84903036773393947 121 -6.7922429418715158 129 -7.7625633621388754
		 144 -7.7625633621388754 149 -6.9135329944049362 157 -0.86419162430061736 165 0 180 0
		 185 0 193 0 201 0 216 0 221 -0.80446862192404966 229 -6.4357489753923973 237 -7.3551416861627397
		 252 -7.3551416861627397 257 -6.5381032420211262 265 -0.81883413302983588 273 0 288 0
		 293 -0.24409208433855081 301 -1.9527366747084065 309 -2.1973652251663824 324 -2.1973652251663824
		 329 -1.9226945720205846 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.97787952423095703 0.99114358425140381 
		1 1 0.99796444177627563 0.99919986724853516 1 1 0.99742454290390015 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.20916877686977386 0.13279494643211365 
		0 0 -0.06377314031124115 -0.039995264261960983 0 0 0.071723483502864838 0 0;
createNode animCurveTL -n "AimEye_L_translateX";
	rename -uid "84538C5A-4711-2B3A-1FF4-9F95EB1C4B23";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0.18966430399825734 229 1.5173144319860588 237 1.7340736365554956
		 252 1.7340736365554956 257 1.5414458278072656 265 0.19305116656965482 273 0 288 0
		 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.327056884765625 0.48361802101135254 
		1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.94500464200973511 -0.87527912855148315 
		0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "AimEye_L_translateY";
	rename -uid "BBB2FA30-43A9-8A72-8F70-059C6D3AD9D4";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0.061171747318913547 229 0.48937397855130838 237 0.559284546915781
		 252 0.559284546915781 257 0.49715699104500943 265 0.062264099949608409 273 0 288 0
		 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.73157292604446411 0.86363106966018677 
		1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.68176323175430298 -0.50412434339523315 
		0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "AimEye_L_translateZ";
	rename -uid "8C9B1F53-4493-66FE-EA3D-83A85D30FB4B";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0.02134345889775003 229 0.17074767118200024 237 0.1951401956365717
		 252 0.1951401956365717 257 0.17346324519354434 265 0.021724592092352724 273 0 288 0
		 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.95099085569381714 0.97988337278366089 
		1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.30921903252601624 -0.19957111775875092 
		0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "AimEye_M_translateX";
	rename -uid "B823B9F7-437F-A880-3471-9F9293A7EAF9";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.074459637631345651 13 0.60498455575468335
		 21 0.68077382977230305 36 0.68077382977230305 41 0.60631419214095739 49 0.08509672872153784
		 57 0 72 0 77 0 85 0 93 0 108 0 113 0.98724868393818321 121 7.8979894715054657 129 9.0262736817205322
		 144 9.0262736817205322 149 8.0390249977823487 157 1.1282842102150665 165 0 180 0
		 185 0.54616181646875195 193 4.3692945317500156 201 4.916656703859358 216 4.916656703859358
		 221 3.6152828352320112 229 -5.4943342451594166 237 -5.4943342451594166 252 -5.4943342451594166
		 257 -4.8840017057191005 265 -0.61167392963688805 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.10858411341905594 0.17179214954376221 
		1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.99408721923828125 0.98513323068618774 
		0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "AimEye_M_translateY";
	rename -uid "E04FF7BE-**************-CB989367F66F";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.026370904937712328 13 0.21426360261891267
		 21 0.24110541657336987 36 0.24110541657336987 41 0.21473451163565754 49 0.03013817707167124
		 57 0 72 0 77 0 85 0 93 0 108 0 113 0.070342406911798258 121 0.56273925529438606 129 0.64313057747929836
		 144 0.64313057747929836 149 0.57278817056750009 157 0.080391322184912295 165 0 180 0
		 185 -0.21916311604631861 193 -1.7533049283705489 201 -1.9729497215949912 216 -1.9729497215949912
		 221 -1.7288109220873702 229 -0.019839325534022789 237 -0.019839325534022692 252 -0.019839325534022692
		 257 -0.017635494206390778 265 -0.0022086749129673691 273 0 288 0 293 0 301 0 309 0
		 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.9994540810585022 0.9997856616973877 
		1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.033039454370737076 0.020701909437775612 
		0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "AimEye_M_translateZ";
	rename -uid "36B4AAA1-4769-0EF0-4B40-F98C11226032";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.0013463755244755185 13 0.010939301136363587
		 21 0.012309719080919025 36 0.012309719080919025 41 0.010963343556443506 49 0.0015387148851148773
		 57 0 72 0 77 0 85 0 93 0 108 0 113 0.24139040302451042 121 1.9311232241960834 129 2.2069979705098097
		 144 2.2069979705098097 149 1.9656075674852993 157 0.27587474631372633 165 0 180 0
		 185 0.092611207958995773 193 0.74088966367196618 201 0.83370441274735541 216 0.83370441274735541
		 221 0.52544237044948061 229 -1.6323919256356429 237 -1.6323919256356429 252 -1.6323919256356429
		 257 -1.4510593264744571 265 -0.18173113234615546 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.3450666069984436 0.50619453191757202 
		1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.93857824802398682 0.86241930723190308 
		0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "AimEye_R_translateX";
	rename -uid "5DD3BE50-4E83-892F-83C9-B68F7EDD3BF6";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 -0.35940488402721615 121 -2.8752390722177292 129 -3.2859875111059762
		 144 -3.2859875111059762 149 -2.9265826270787603 157 -0.41074843888824697 165 0 180 0
		 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0
		 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "Cheek_L_translateX";
	rename -uid "0EBF2E55-411C-0CC1-CCFE-7FB3C4104D1F";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 -0.0041179520642967695 13 -0.033458360522411253
		 21 -0.037649847444999038 36 -0.037649847444999038 41 -0.033531895380702267 49 -0.0047062309306248815
		 57 0 72 0 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0
		 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0
		 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[22:39]"  1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[22:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[22:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "Cheek_L_translateY";
	rename -uid "C9134AA8-4B42-8F5F-558B-4C8B889066C8";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.081020616078116989 13 0.65829250563470054
		 21 0.7407599184284982 36 0.7407599184284982 41 0.65973930235038125 49 0.092594989803562289
		 57 0 72 0 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0
		 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0
		 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[22:39]"  1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[22:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[22:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "Cheek_L_translateZ";
	rename -uid "7A96FF7D-4259-0470-ED10-FC8042D8E659";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 -0.0040796954978076089 13 -0.033147525919686824
		 21 -0.037300073122812423 36 -0.037300073122812423 41 -0.033220377625004814 49 -0.004662509140351552
		 57 0 72 0 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0
		 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0
		 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[22:39]"  1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[22:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[22:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "ctrlBrow_L_translateX";
	rename -uid "365AFCC8-491B-4D43-9587-409D7D50D893";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 -0.032755986650520708 157 -0.26204789320416566
		 165 -0.29948330651904648 180 -0.29948330651904648 185 -0.26672731986852577 193 -0.037435413314880817
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "ctrlBrow_L_translateY";
	rename -uid "7006F52A-4516-3E2C-BF34-7E9BEABB750A";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0.003568022917001242
		 49 0.028544183336009936 57 0.032621923812582783 72 0.032621923812582783 77 0.13842890089558155
		 85 0.87907774047657283 93 1 108 1 113 0.89540858246098542 121 0.16326865968788329
		 129 0.043735611071866597 144 0.043735611071866597 149 -0.070422971389118794 157 -0.86953304861601655
		 165 -1 180 -1 185 -0.82452013596683926 193 0.4038389122652859 201 0.60438732830318398
		 216 0.60438732830318398 221 0.53828246427002324 229 0.07554841603789797 237 0 252 0
		 257 -0.079861326205162425 265 -0.6388906096412994 273 -0.73016069673291362 288 -0.73016069673291362
		 293 -0.65029937052775122 301 -0.091270087091614216 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.64083939790725708 0.75981384515762329 
		1 1 0.64083939790725708 0.75981384515762329 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.76767498254776001 -0.65014070272445679 
		0 0 0.76767498254776001 0.65014070272445679 0 0 0 0 0;
createNode animCurveTL -n "ctrlBrow_R_translateX";
	rename -uid "9A82F0BE-40D4-F984-625E-DE892835206C";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 -0.032755986650520708 157 -0.26204789320416566
		 165 -0.29948330651904648 180 -0.29948330651904648 185 -0.26672731986852577 193 -0.037435413314880817
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "ctrlBrow_R_translateY";
	rename -uid "CA8883BF-4DAD-C82F-274B-1AA01D8C2F32";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 -0.024932315051075588 13 -0.19945852040860471
		 21 -0.22795259475269108 36 -0.22795259475269108 41 -0.19945225678461426 49 5.0108991923536461e-005
		 57 0.032621923812582783 72 0.032621923812582783 77 0.13842890089558155 85 0.87907774047657283
		 93 1 108 1 113 0.89540858246098542 121 0.16326865968788329 129 0.043735611071866597
		 144 0.043735611071866597 149 -0.070422971389118794 157 -0.86953304861601655 165 -1
		 180 -1 185 -0.82452013596683926 193 0.4038389122652859 201 0.60438732830318398 216 0.60438732830318398
		 221 0.53828246427002324 229 0.07554841603789797 237 0 252 0 257 -0.079861326205162425
		 265 -0.6388906096412994 273 -0.73016069673291362 288 -0.73016069673291362 293 -0.65029937052775122
		 301 -0.091270087091614216 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.64083939790725708 0.75981384515762329 
		1 1 0.64083939790725708 0.75981384515762329 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.76767498254776001 -0.65014070272445679 
		0 0 0.76767498254776001 0.65014070272445679 0 0 0 0 0;
createNode animCurveTL -n "ctrlCheek_L_translateY";
	rename -uid "772B697A-4F36-EAF7-5F5A-E982294549B9";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.019493970202835931 13 0.15595176162268745
		 21 0.1782305847116428 36 0.1782305847116428 41 0.15873661450880686 49 0.022278823088955346
		 57 0 72 0 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0
		 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0
		 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "ctrlEye_L_translateX";
	rename -uid "9AD84063-4669-93F9-D86F-4F96E64EFB80";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 -0.0085701591817926547 87.995833333333337 -0.068561273454341237 88 -0.078355741090675696
		 108 -0.078355741090675696 113 -0.069785581908883038 121 -0.0097944676363344585 129 0
		 144 0 149 0 157 0 165 0 180 0 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0
		 265 0 273 0 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "ctrlEye_L_translateY";
	rename -uid "9B807234-4CE5-9508-D88D-8F935061D1C8";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0.064456393492132918 87.995833333333337 0.51565114793706335 88 0.58931559764235808
		 108 0.58931559764235808 113 0.52485920415022513 121 0.073664449705294732 129 0 144 0
		 149 0 157 0 165 0 180 0 185 0.025437139507199953 193 0.20349711605759963 201 0.23256813263725673
		 216 0.23256813263725673 221 0.20713099313005678 229 0.029071016579657105 237 0 252 0
		 257 -0.055115395146499213 265 -0.4409231611719937 273 -0.50391218419656425 288 -0.50391218419656425
		 293 -0.44879678905006504 301 -0.062989023024570545 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.77071768045425415 0.86107224225997925 
		1 1 0.77071774005889893 0.86107224225997925 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.6371767520904541 -0.50848263502120972 
		0 0 0.63717681169509888 0.50848263502120972 0 0 0 0 0;
createNode animCurveTL -n "ctrlEye_R_translateX";
	rename -uid "1D93CAE8-4879-6C68-B8D4-9AA02B85916D";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 -0.0085701591817926547 87.995833333333337 -0.068561273454341237 88 -0.078355741090675696
		 108 -0.078355741090675696 113 -0.069785581908883038 121 -0.0097944676363344585 126 0
		 144 0 149 0 157 0 165 0 180 0 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0
		 265 0 273 0 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[5:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kot[5:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 18 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[5:39]"  1 1 1 1 0.99430787563323975 0.98765486478805542 
		1 1 0.99183815717697144 0.99111467599868774 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 
		1 1 1 1 1 1;
	setAttr -s 40 ".kiy[5:39]"  0 0 0 0 -0.10654497146606445 -0.15664559602737427 
		0 0 0.12750329077243805 0.1330106109380722 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 
		0 0 0 0 0 0;
	setAttr -s 40 ".kox[5:39]"  1 1 1 1 0.99430787563323975 0.98765486478805542 
		1 1 0.99183821678161621 0.99111461639404297 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 
		1 1 1 1 1 1;
	setAttr -s 40 ".koy[5:39]"  0 0 0 0 -0.10654497146606445 -0.15664559602737427 
		0 0 0.12750329077243805 0.13301059603691101 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 
		0 0 0 0 0 0;
createNode animCurveTL -n "ctrlEye_R_translateY";
	rename -uid "9AFEE023-4D1D-1FBD-9EB0-0DA225C930C3";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0.064456393492132918 87.995833333333337 0.51565114793706335 88 0.58931559764235808
		 108 0.58931559764235808 113 0.52485920415022513 121 0.073664449705294732 126 0 144 0
		 149 0 157 0 165 0 180 0 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 -0.055115395146499213
		 265 -0.4409231611719937 273 -0.50391218419656425 288 -0.50391218419656425 293 -0.44879678905006504
		 301 -0.062989023024570545 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[5:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kot[5:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 18 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[5:39]"  1 1 1 1 0.77861720323562622 0.64243704080581665 
		1 1 0.71892368793487549 0.70381069183349609 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0.77071768045425415 
		0.86107224225997925 1 1 0.77071768045425415 0.86107224225997925 1 1 1 1 1;
	setAttr -s 40 ".kiy[5:39]"  0 0 0 0 0.6274992823600769 0.76633846759796143 
		0 0 -0.69508904218673706 -0.71038758754730225 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -0.6371767520904541 
		-0.50848263502120972 0 0 0.6371767520904541 0.50848263502120972 0 0 0 0 0;
	setAttr -s 40 ".kox[5:39]"  1 1 1 1 0.77861720323562622 0.64243698120117188 
		1 1 0.71892368793487549 0.70381069183349609 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0.77071768045425415 
		0.86107224225997925 1 1 0.77071774005889893 0.86107224225997925 1 1 1 1 1;
	setAttr -s 40 ".koy[5:39]"  0 0 0 0 0.62749922275543213 0.76633846759796143 
		0 0 -0.69508904218673706 -0.71038758754730225 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -0.6371767520904541 
		-0.50848263502120972 0 0 0.63717681169509888 0.50848263502120972 0 0 0 0 0;
createNode animCurveTL -n "ctrlMouth_M_translateX";
	rename -uid "CCED0DA9-46A5-2650-1210-2E8E7B62A2EC";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0.015231074370445977 85 0.12375247925987355 93 0.13925553710122035 108 0.13925553710122035
		 113 0.17463030333601137 121 0.42225366697954841 129 0.46268197124788096 144 0.46268197124788096
		 149 0.51157165502127466 157 0.85379944143503017 165 0.90967336574748003 180 0.90967336574748003
		 185 0.81017784136884941 193 0.113709170718435 201 0 216 0 221 -0.10149027384035397
		 229 -0.81192219072283178 237 -0.92791107511180781 252 -0.92791107511180781 257 -0.82642080127145379
		 265 -0.11598888438897603 273 0 288 0 293 0.051790505570321034 301 0.41432404456256827
		 309 0.47351319378579232 324 0.47351319378579232 329 0.41432404456256827 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[22:39]"  1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[22:39]"  1 1 1 0.54902255535125732 1 1 1 0.54902255535125732 
		0.67690801620483398 1 1 0.78970396518707275 0.87440097332000732 1 1 0.7477993369102478 
		1 1;
	setAttr -s 40 ".koy[22:39]"  0 0 0 -0.83580756187438965 0 0 0 0.83580756187438965 
		0.73606759309768677 0 0 0.61348807811737061 0.4852040708065033 0 0 -0.66392475366592407 
		0 0;
createNode animCurveTL -n "ctrlMouth_M_translateY";
	rename -uid "DD46BFCF-40FC-A361-47F3-01BDAC59AF42";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 -0.048421263433863479 85 -0.88105265792923293 93 -1 108 -1 113 -0.94851966953804756
		 121 -0.063885499942895607 129 -0.084663880236026334 144 -0.084663880236026334 149 -0.10058351472515104
		 157 -0.73629281251050949 165 -0.75448668049808054 180 -0.75448668049808054 185 -0.67196469981860296
		 193 -0.094310835062260123 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0
		 293 -0.109375 301 -0.875 309 -1 324 -1 329 -0.875 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[22:39]"  1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[22:39]"  1 1 1 1 1 1 1 1 1 1 1 0.5204625129699707 
		0.64911895990371704 1 1 0.47058784961700439 1 1;
	setAttr -s 40 ".koy[22:39]"  0 0 0 0 0 0 0 0 0 0 0 -0.85388457775115967 
		-0.76068699359893799 0 0 0.88235312700271606 0 0;
createNode animCurveTL -n "ctrlMouthCorner_L_translateX";
	rename -uid "4453308B-4B6F-2A1F-D0D6-C3A21F94DBEC";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 37 ".ktv[0:36]"  1 0 17 0.36080646929017174 36 0.36080646929017174
		 49 0.94112727851054512 57 1 72 1 77 0.890625 85 0.125 93 0 108 0 113 0 121 0 129 0
		 144 0 149 0.028520249858117262 157 0.2281619988649381 165 0.26075657013135783 180 0.26075657013135783
		 185 0.34161132027324059 193 0.90759457126641974 201 1 216 1 221 0.890625 229 0.125
		 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 37 ".kit[2:36]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 37 ".kot[2:36]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 37 ".kix[2:36]"  1 0.87550073862075806 1 1 0.52046245336532593 
		0.64911919832229614 1 1 1 1 1 1 0.91940027475357056 0.95634657144546509 1 1 0.63616484403610229 
		0.75582605600357056 1 1 0.52046245336532593 0.64911895990371704 1 1 1 1 1 1 1 1 1 
		1 1 1 1;
	setAttr -s 37 ".kiy[2:36]"  0 0.48321676254272461 0 0 -0.85388457775115967 
		-0.76068669557571411 0 0 0 0 0 0 0.39332324266433716 0.29223501682281494 0 0 0.7715531587600708 
		0.65477246046066284 0 0 -0.85388457775115967 -0.76068699359893799 0 0 0 0 0 0 0 0 
		0 0 0 0 0;
	setAttr -s 37 ".kox[2:36]"  1 0.87550073862075806 1 1 0.52046245336532593 
		0.64911919832229614 1 1 1 1 1 1 0.91940027475357056 0.95634651184082031 1 1 0.63616484403610229 
		1 1 1 0.5204625129699707 1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 37 ".koy[2:36]"  0 0.48321676254272461 0 0 -0.85388463735580444 
		-0.76068669557571411 0 0 0 0 0 0 0.39332324266433716 0.29223501682281494 0 0 0.7715531587600708 
		0 0 0 -0.85388457775115967 0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "ctrlMouthCorner_L_translateY";
	rename -uid "6BF0E8AE-43AF-35B8-D121-91B2AD17A395";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 37 ".ktv[0:36]"  1 0 17 0.18747573650165292 36 0.18747573650165292
		 49 1 57 1 72 1 77 0.890625 85 0.125 93 0 108 0 113 0 121 0 129 0 144 0 149 0.032684059221187119
		 157 0.26147247376949695 165 0.2988256843079965 180 0.2988256843079965 185 0.37551662508680939
		 193 0.9123532105384996 201 1 216 1 221 0.890625 229 0.125 237 0 252 0 257 0 265 0
		 273 0 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 37 ".kit[2:36]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 37 ".kot[2:36]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 37 ".kix[2:36]"  1 1 1 1 0.52046245336532593 0.64911919832229614 
		1 1 1 1 1 1 0.89789754152297974 0.94380342960357666 1 1 0.65606093406677246 0.77262783050537109 
		1 1 0.52046245336532593 0.64911895990371704 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 37 ".kiy[2:36]"  0 0 0 0 -0.85388457775115967 -0.76068669557571411 
		0 0 0 0 0 0 0.44020450115203857 0.33050739765167236 0 0 0.754707932472229 0.63485920429229736 
		0 0 -0.85388457775115967 -0.76068699359893799 0 0 0 0 0 0 0 0 0 0 0 0 0;
	setAttr -s 37 ".kox[2:36]"  1 1 1 1 0.52046245336532593 0.64911919832229614 
		1 1 1 1 1 1 0.89789754152297974 0.94380342960357666 1 1 0.65606099367141724 1 1 1 
		0.5204625129699707 1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 37 ".koy[2:36]"  0 0 0 0 -0.85388463735580444 -0.76068669557571411 
		0 0 0 0 0 0 0.4402044415473938 0.33050739765167236 0 0 0.75470787286758423 0 0 0 
		-0.85388457775115967 0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "ctrlMouthCorner_R_translateX";
	rename -uid "CF18BB5B-4B18-C19E-D915-1096F19F2E59";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 37 ".ktv[0:36]"  1 0 17 0.70752427018570274 36 0.70752427018570274
		 49 0.875 57 1 72 1 77 0.890625 85 0.125 93 0 108 0 113 0 121 0 129 0 144 0 149 0.012433463704826998
		 157 0.099467709638615981 165 0.11367738244413254 180 0.11367738244413254 185 0.21061891873930555
		 193 0.88920967280551655 201 1 216 1 221 0.890625 229 0.125 237 0 252 0 257 0 265 0
		 273 0 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 37 ".kit[2:36]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 37 ".kot[2:36]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 37 ".kix[2:36]"  1 0.94439142942428589 1 1 0.52046245336532593 
		0.64911919832229614 1 1 1 1 1 1 0.98304945230484009 0.99124312400817871 1 1 0.56664109230041504 
		0.69357335567474365 1 1 0.52046245336532593 0.64911895990371704 1 1 1 1 1 1 1 1 1 
		1 1 1 1;
	setAttr -s 37 ".kiy[2:36]"  0 0.32882332801818848 0 0 -0.85388457775115967 
		-0.76068669557571411 0 0 0 0 0 0 0.18334081768989563 0.13204924762248993 0 0 0.82396471500396729 
		0.72038602828979492 0 0 -0.85388457775115967 -0.76068699359893799 0 0 0 0 0 0 0 0 
		0 0 0 0 0;
	setAttr -s 37 ".kox[2:36]"  1 0.94439142942428589 1 1 0.52046245336532593 
		0.64911919832229614 1 1 1 1 1 1 0.98304939270019531 0.99124318361282349 1 1 0.56664109230041504 
		1 1 1 0.5204625129699707 1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 37 ".koy[2:36]"  0 0.32882332801818848 0 0 -0.85388463735580444 
		-0.76068669557571411 0 0 0 0 0 0 0.18334081768989563 0.13204926252365112 0 0 0.82396471500396729 
		0 0 0 -0.85388457775115967 0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "ctrlMouthCorner_R_translateY";
	rename -uid "E12589D4-4D23-5731-87BC-2DA0B6AE6811";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 37 ".ktv[0:36]"  1 0 17 0.68972611922893678 36 0.68972611922893678
		 49 0.91390506839704189 57 1 72 1 77 0.890625 85 0.125 93 0 108 0 113 0 121 0 129 0
		 144 0 149 -0.046234204444722593 157 -0.36987363555778074 165 -0.42271272635174939
		 180 -0.42271272635174939 185 -0.2671035219070268 193 0.82216090920603135 201 1 216 1
		 221 0.890625 229 0.125 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 37 ".kit[2:36]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 37 ".kot[2:36]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 37 ".kix[2:36]"  1 0.93805289268493652 1 1 0.52046245336532593 
		0.64911919832229614 1 1 1 1 1 1 0.82172811031341553 0.89608168601989746 1 1 0.39380502700805664 
		0.51436549425125122 1 1 0.52046245336532593 0.64911895990371704 1 1 1 1 1 1 1 1 1 
		1 1 1 1;
	setAttr -s 37 ".kiy[2:36]"  0 0.34649208188056946 0 0 -0.85388457775115967 
		-0.76068669557571411 0 0 0 0 0 0 -0.56987971067428589 -0.44388923048973083 0 0 0.91919398307800293 
		0.8575710654258728 0 0 -0.85388457775115967 -0.76068699359893799 0 0 0 0 0 0 0 0 
		0 0 0 0 0;
	setAttr -s 37 ".kox[2:36]"  1 0.93805289268493652 1 1 0.52046245336532593 
		0.64911919832229614 1 1 1 1 1 1 0.82172811031341553 0.89608168601989746 1 1 0.39380505681037903 
		1 1 1 0.5204625129699707 1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 37 ".koy[2:36]"  0 0.34649208188056946 0 0 -0.85388463735580444 
		-0.76068669557571411 0 0 0 0 0 0 -0.56987971067428589 -0.44388923048973083 0 0 0.91919398307800293 
		0 0 0 -0.85388457775115967 0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "ctrlNose_L_translateX";
	rename -uid "2DAE73F9-4A59-A1CF-1A7F-0FB36DF54059";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0.109375 229 0.875 237 1 252 1 257 1 265 1 273 1 288 1 293 0.890625
		 301 0.125 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.5204625129699707 0.64911895990371704 
		1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 -0.85388457775115967 -0.76068699359893799 
		0 0 0 0 0;
createNode animCurveTL -n "ctrlNose_L_translateY";
	rename -uid "B5BFEDA6-4BA6-368C-4428-D29E3573A4A7";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0.040415742659180923
		 49 0.32332594127344738 57 0.36951536145536845 72 0.36951536145536845 77 0.32909961879618754
		 85 0.04618942018192107 93 0 108 0 113 0 121 0 129 0 144 0 149 0.020468386717229163
		 157 0.1637470937378333 165 0.18713953570038092 180 0.18713953570038092 185 0.19624174805391528
		 193 0.25995723452865577 201 0.27035976293269504 216 0.27035976293269504 221 0.24078916386193153
		 229 0.033794970366586879 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "ctrlNose_R_translateX";
	rename -uid "C446E8F3-4198-912D-C3AB-EEA7E548C269";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0.109375 229 0.875 237 1 252 1 257 1 265 1 273 1 288 1 293 0.890625
		 301 0.125 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.5204625129699707 0.64911895990371704 
		1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 -0.85388457775115967 -0.76068699359893799 
		0 0 0 0 0;
createNode animCurveTL -n "ctrlNose_R_translateY";
	rename -uid "EBCED9CA-476A-B2B3-F9C3-95BAE419A885";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0.040415742659180923
		 49 0.32332594127344738 57 0.36951536145536845 72 0.36951536145536845 77 0.32909961879618754
		 85 0.04618942018192107 93 0 108 0 113 0 121 0 129 0 144 0 149 0.020468386717229163
		 157 0.1637470937378333 165 0.18713953570038092 180 0.18713953570038092 185 0.16667114898315175
		 193 0.023392441962547611 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0
		 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "EyeBrowInner_L_translateX";
	rename -uid "5CDAE7D4-403B-6EE8-B609-EBA9B009F16D";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 -0.032998616950321275 229 -0.2639889356025702 237 -0.30170164068865163
		 252 -0.30170164068865163 257 -0.28339436075766561 265 -0.15524340124076363 273 -0.13689588552970952
		 288 -0.13689588552970952 293 -0.12198727382673205 301 -0.017626991905889774 309 -0.0026856140233453216
		 324 -0.0026856140233453216 329 -0.0023499122704271564 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.96430164575576782 0.98552697896957397 
		1 1 0.97589540481567383 0.99033141136169434 1 1 0.99998736381530762 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.2648063600063324 0.16951864957809448 
		0 0 0.21823889017105103 0.13872122764587402 0 0 0.0050354674458503723 0 0;
createNode animCurveTL -n "EyeBrowInner_L_translateY";
	rename -uid "4E535459-4C13-977C-2331-1D857AEC5BD0";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.017076253388151851 13 0.13874455877873379
		 21 0.15612574526310263 36 0.15612574526310263 41 0.21167301131902699 49 0.60050387371049752
		 57 0.66398646348869672 72 0.66398646348869672 77 0.65282733068908072 85 0.57471340109176861
		 93 0.56196010646363603 108 0.56196010646363603 113 0.50049571981917584 121 0.070245013307954518
		 129 0 144 0 149 0 157 0 165 0 180 0 185 0.056209553551528048 193 0.44967642841222438
		 201 0.50600951944408545 216 0.50600951944408545 221 0.44992696951364847 229 0.057349120000589671
		 237 -0.006745222777052591 252 -0.006745222777052591 257 0.10281432089757461 265 0.86973112661996499
		 273 0.97953146050047046 288 0.97953146050047046 293 0.8831692560370229 301 0.20863382479289028
		 309 0.11205983526468796 324 0.11205983526468796 329 0.098052355856601964 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.51982277631759644 0.69679701328277588 
		1 1 0.56894636154174805 0.74130707979202271 1 1 0.97863131761550903 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.85427403450012207 0.71726834774017334 
		0 0 -0.82237464189529419 -0.6711660623550415 0 0 -0.20562256872653961 0 0;
createNode animCurveTL -n "EyeBrowInner_L_translateZ";
	rename -uid "C359EA46-4F01-C85E-7C92-2DAA39A541FF";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0.00016707423736646396 229 0.0013365938989317117 237 0.0015275358844933848
		 252 0.0015275358844933848 257 0.0013578511121680697 265 0.00017005770589086523 273 0
		 288 0 293 0.0027994995237183634 301 0.022395996189746907 309 0.025201648459671242
		 324 0.025201648459671242 329 0.022051442402212337 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.99999672174453735 0.99999868869781494 
		1 1 0.99911946058273315 0.99965423345565796 1 1 0.99888545274734497 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.0025452657137066126 -0.0015942903701215982 
		0 0 0.041955556720495224 0.02629392221570015 0 0 -0.047200467437505722 0 0;
createNode animCurveTL -n "EyeBrowInner_R_translateX";
	rename -uid "129A968A-495F-101F-F486-4FB0962B73DC";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 -0.018849547621715368 265 -0.15079638097372294
		 273 -0.16968735617262889 288 -0.16968735617262889 293 -0.14972902565465315 301 -0.010020712028822987
		 309 0.0099814829518296323 324 0.0099814829518296323 329 0.0087337975828509287 337 0
		 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.96227544546127319 0.9846767783164978 
		1 1 0.95799088478088379 0.982868492603302 1 1 0.99982494115829468 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.27207711338996887 -0.17438927292823792 
		0 0 0.28679877519607544 0.1843082457780838 0 0 -0.01871202141046524 0 0;
createNode animCurveTL -n "EyeBrowInner_R_translateY";
	rename -uid "*************-13ED-8D01-6E8E251F1159";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.0023962699122155842 13 0.019469693036751622
		 21 0.021908753483113913 36 0.021908753483113913 41 0.084092960957495647 49 0.51938241327816781
		 57 0.59045007896317547 72 0.59045007896317547 77 0.58733398822103833 85 0.56552135302607853
		 93 0.56196010646363614 108 0.56196010646363614 113 0.50049571981917595 121 0.070245013307954518
		 129 0 144 0 149 0 157 0 165 0 180 0 185 0.064010169570283729 193 0.51208135656226983
		 201 0.57623220782391682 216 0.57623220782391682 221 0.51320681009317592 229 0.072029025977989658
		 237 0 252 0 257 0.10741471723304154 265 0.8593177378643323 273 0.96696853139898498
		 288 0.96696853139898498 293 0.8729939313092544 301 0.21517173068114048 309 0.12099059300879511
		 324 0.12099059300879511 329 0.10586676888269572 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.52733659744262695 0.70385199785232544 
		1 1 0.5786023736000061 0.74961614608764648 1 1 0.97522032260894775 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.84965646266937256 0.71034657955169678 
		0 0 -0.81560969352722168 -0.66187286376953125 0 0 -0.22123613953590393 0 0;
createNode animCurveTL -n "EyeBrowInner_R_translateZ";
	rename -uid "EBCDAF54-45C1-2A4C-ABF5-53B29157164C";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0.00034143228456766822
		 301 0.0027314582765413458 309 0.0030736409617344376 324 0.0030736409617344376 329 0.0026894358415176329
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.99998688697814941 0.99999487400054932 
		1 1 0.99998342990875244 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0.0051214220002293587 0.0032079494558274746 
		0 0 -0.0057629868388175964 0 0;
createNode animCurveTL -n "EyeBrowMid1_L_translateX";
	rename -uid "31779633-4A49-C8DB-294F-31BFCB6AC6C7";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 -0.012316148727044892
		 301 -0.098529189816359136 309 -0.11087240700214479 324 -0.11087240700214479 329 -0.097013356126876699
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.98335987329483032 0.99337118864059448 
		1 1 0.97906780242919922 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 -0.18166828155517578 -0.11495069414377213 
		0 0 0.20353445410728455 0 0;
createNode animCurveTL -n "EyeBrowMid1_L_translateY";
	rename -uid "8822CB2A-4518-94C7-181E-079DF305949E";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.037544738249148682 13 0.30505099827433307
		 21 0.34326617827793082 36 0.34326617827793082 41 0.30572144002878215 49 0.042908272284741367
		 57 0 72 0 77 -0.048353270685136153 85 -0.38682616548108922 93 -0.44208704626410195
		 108 -0.44208704626410195 113 -0.39373377557896577 121 -0.055260880783012722 129 0
		 144 0 149 0 157 0 165 0 180 0 185 -0.053111831814331635 193 -0.42489465451465308
		 201 -0.47812321562967558 216 -0.47812321562967558 221 -0.45562974864200517 229 -0.29817547972831226
		 237 -0.27246866031383177 252 -0.27246866031383177 257 -0.28369317566867552 265 -0.36226478315258198
		 273 -0.3735139677719419 288 -0.3735139677719419 293 -0.28912226715351363 301 0.30161963717548412
		 309 0.38619681405900791 324 0.38619681405900791 329 0.33792221230163194 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.98612046241760254 0.99448490142822266 
		1 1 0.61988240480422974 0.78357082605361938 1 1 0.80994910001754761 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.16603103280067444 -0.10487958043813705 
		0 0 0.78469467163085938 0.62130248546600342 0 0 -0.58650016784667969 0 0;
createNode animCurveTL -n "EyeBrowMid1_L_translateZ";
	rename -uid "21BE31A0-466B-A0F4-6C97-B8AFF1F5543C";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0.0015617647725707361
		 301 0.012494118180565889 309 0.01405931540318623 324 0.01405931540318623 329 0.012301900977787952
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.99972569942474365 0.99989235401153564 
		1 1 0.99965274333953857 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0.023420069366693497 0.014672158285975456 
		0 0 -0.026352087035775185 0 0;
createNode animCurveTL -n "EyeBrowMid1_R_translateX";
	rename -uid "AF5CAFDF-45D2-6689-03CB-DEA02FA7D45F";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 -0.01598932782695564
		 301 -0.12791462261564512 309 -0.14393909182244025 324 -0.14393909182244025 329 -0.1259467053446352
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.97242254018783569 0.9889029860496521 
		1 1 0.96545672416687012 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 -0.23322595655918121 -0.14856244623661041 
		0 0 0.26056331396102905 0 0;
createNode animCurveTL -n "EyeBrowMid1_R_translateY";
	rename -uid "C752B911-4CEF-08CA-BDE6-08B4912C9A3B";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 -0.06634310955252494 13 -0.53903776511426515
		 21 -0.60656557305165659 36 -0.60656557305165659 41 -0.5332555539067001 49 -0.020085419892004158
		 57 0 72 0 77 -0.04835327068513616 85 -0.38682616548108928 93 -0.44208704626410206
		 108 -0.44208704626410206 113 -0.39373377557896588 121 -0.055260880783012778 129 0
		 144 0 149 0 157 0 165 0 180 0 185 -0.062394446954257311 193 -0.49915557563405849
		 201 -0.56168715324096252 216 -0.56168715324096252 221 -0.50025262085523226 229 -0.070210894155120329
		 237 0 252 0 257 -0.041491419759822651 265 -0.33193135807858121 273 -0.37351396777194196
		 288 -0.37351396777194196 293 -0.29231115061457091 301 0.2761085694870265 309 0.35748985437441377
		 324 0.35748985437441377 329 0.31280362257761207 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.84899961948394775 0.93170589208602905 
		1 1 0.6345365047454834 0.79503089189529419 1 1 0.83065700531005859 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.52839350700378418 -0.36321374773979187 
		0 0 0.77289295196533203 0.60656905174255371 0 0 -0.55678451061248779 0 0;
createNode animCurveTL -n "EyeBrowMid1_R_translateZ";
	rename -uid "F60CD9EE-4987-95A7-E798-D7A232FDE78F";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0.0063352530881429373
		 301 0.050682024705143498 309 0.057031201426447187 324 0.057031201426447187 329 0.049902301248141288
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.99551510810852051 0.9982331395149231 
		1 1 0.9943312406539917 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0.094602696597576141 0.059418421238660812 
		0 0 -0.10632742196321487 0 0;
createNode animCurveTL -n "EyeBrowMid2_L_translateX";
	rename -uid "4A23B3D2-4400-81C6-A6C0-84BC8551A6FB";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0.0045403346912948148
		 301 0.036322677530358519 309 0.040872990979216622 324 0.040872990979216622 329 0.035763867106814548
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.997688889503479 0.99909138679504395 
		1 1 0.99707627296447754 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0.067947685718536377 0.042620468884706497 
		0 0 -0.076412871479988098 0 0;
createNode animCurveTL -n "EyeBrowMid2_L_translateY";
	rename -uid "C2FE1C63-48E5-F9A6-5311-2685710876F2";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.0058630801365572157 13 0.04763752610952738
		 21 0.053605304105665975 36 0.053605304105665975 41 0.047742223969108759 49 0.0067006630132082495
		 57 0 72 0 77 -0.034591641682883256 85 -0.27673313346306605 93 -0.31626643824350403
		 108 -0.31626643824350403 113 -0.21712730072479622 121 0.47684666190615849 129 0.59014853335611028
		 144 0.59014853335611028 149 0.6103584798928603 157 0.75435434896720399 165 0.77492518740639593
		 180 0.77492518740639593 185 0.6137314584807696 193 -0.51462464399861485 201 -0.67617264485594575
		 216 -0.67617264485594575 221 -0.61168500944533444 229 -0.16027156157105549 237 -0.086571406816071256
		 252 -0.086571406816071256 257 -0.14118603585732642 265 -0.52348843914611254 273 -0.58012755616915423
		 288 -0.58012755616915423 293 -0.48863908294231928 301 0.15178022964552518 309 0.2431977113213134
		 324 0.2431977113213134 329 0.21279799740614924 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.7735634446144104 0.88321006298065186 
		1 1 0.58891981840133667 0.75929528474807739 1 1 0.90986853837966919 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.63371884822845459 -0.4689776599407196 
		0 0 0.80819141864776611 0.65074622631072998 0 0 -0.41489654779434204 0 0;
createNode animCurveTL -n "EyeBrowMid2_L_translateZ";
	rename -uid "29ACFDE9-4E4D-96AE-3ED2-728B073CD264";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 -0.011954619124706627
		 301 -0.095636952997653013 309 -0.1076178460105458 324 -0.1076178460105458 329 -0.094165615259227578
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.98429989814758301 0.99375110864639282 
		1 1 0.98024296760559082 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 -0.17650412023067474 -0.11161909252405167 
		0 0 0.1977970153093338 0 0;
createNode animCurveTL -n "EyeBrowMiddle_M_translateY";
	rename -uid "87E72C76-47B2-EE86-1A57-E5B5AE60BF8D";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 -0.061643199861116202 157 -0.50085099887156914
		 165 -0.56359497015877669 180 -0.56359497015877669 185 -0.50098859529983053 193 -0.062743971287207545
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "EyeBrowOuter_L_translateX";
	rename -uid "ADFA5CED-4F99-36AB-1C4C-439BC96970FB";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0.039621571690263452
		 193 0.31697257352210761 201 0.35668122558971227 216 0.35668122558971227 221 0.31766921654083746
		 229 0.044585153198714034 237 0 252 0 257 0.014328416606223285 265 0.11462733284978628
		 273 0.13100266611404146 288 0.13100266611404146 293 0.14475909016099048 301 0.24105405848963363
		 309 0.2551286343830903 324 0.2551286343830903 329 0.22323755508520401 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.97767382860183716 0.98842024803161621 
		1 1 0.97936713695526123 0.99140673875808716 1 1 0.90209704637527466 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.21012797951698303 0.15174119174480438 
		0 0 0.20208904147148132 0.13081540167331696 0 0 -0.43153315782546997 0 0;
createNode animCurveTL -n "EyeBrowOuter_L_translateY";
	rename -uid "E99C330E-4238-70C9-250E-CD9D6C28C91D";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.010472214345006468 13 0.085086741553177553
		 21 0.095745959725773433 36 0.095745959725773433 41 0.085273745380766958 49 0.011968244965721686
		 57 0 72 0 77 0 85 0 93 0 108 0 113 0.064547495835824559 121 0.51637996668659647 129 0.59014853335611028
		 144 0.59014853335611028 149 0.65014575608896563 157 1.0776259680605602 165 1.138694569770788
		 180 1.138694569770788 185 0.98887328590707457 193 -0.059875701138918824 201 -0.21002626254958556
		 216 -0.21002626254958556 221 -0.18705464008322464 229 -0.026253282818698209 237 0
		 252 0 257 -0.046192774519833349 265 -0.36954219615866679 273 -0.41414857452796089
		 288 -0.41414857452796089 293 -0.32392551194478003 301 0.30763592613748614 309 0.39829841241331931
		 324 0.39829841241331931 329 0.34851111086165443 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.82196724414825439 0.92257875204086304 
		1 1 0.59427571296691895 0.76195377111434937 1 1 0.80122506618499756 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.5695347785949707 -0.38580876588821411 
		0 0 0.80426138639450073 0.64763146638870239 0 0 -0.59836310148239136 0 0;
createNode animCurveTL -n "EyeBrowOuter_L_translateZ";
	rename -uid "4F4DCF90-4F94-E07D-D332-FE85721AB38B";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 -0.0079591053636521494
		 193 -0.063672842909217195 201 -0.071649440812130122 216 -0.071649440812130122 221 -0.063812783223303388
		 229 -0.0089561801015162618 237 0 252 0 257 0 265 0 273 0 288 0 293 -0.0080072684437453596
		 301 -0.064058147549962877 309 -0.072083014385892291 324 -0.072083014385892291 329 -0.063072637587655758
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.99286401271820068 0.99718195199966431 
		1 1 0.99098968505859375 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 -0.1192520409822464 -0.07502119243144989 
		0 0 0.13393798470497131 0 0;
createNode animCurveTL -n "EyeBrowOuter_R_translateX";
	rename -uid "340C3734-454B-59CF-1543-FE87FD7D0004";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.017962370721479159 13 0.14594426211201816
		 21 0.16422738945352375 36 0.16422738945352375 41 0.14369896577183328 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0.049836705941444133
		 301 0.39869364753155306 309 0.4486398846948465 324 0.4486398846948465 329 0.39255989910799072
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.80094039440155029 0.90563410520553589 
		1 1 0.76525217294692993 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0.59874403476715088 0.42405992746353149 
		0 0 -0.64373058080673218 0 0;
createNode animCurveTL -n "EyeBrowOuter_R_translateY";
	rename -uid "CEA77D0F-4336-E39E-C087-CA8D5D69D033";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 -0.020010584425933526 13 -0.1625859984607099
		 21 -0.18295391475139225 36 -0.18295391475139225 41 -0.1643276565675213 49 -0.033943849280424654
		 57 0 72 0 77 0 85 0 93 0 108 0 113 0.09002569042714699 121 0.72020552341717592 129 0.82309202676248672
		 144 0.82309202676248672 149 0.84880738206746065 157 1.0320292886154001 165 1.0582038466936772
		 180 1.0582038466936772 185 0.89169919526839991 193 -0.27383336470854103 201 -0.44070396042266502
		 216 -0.44070396042266502 221 -0.39250196475143606 229 -0.055087995052833127 237 0
		 252 0 257 -0.034678726671985863 265 -0.2774298133758869 273 -0.31218475702956944
		 288 -0.31218475702956944 293 -0.22266881806716332 301 0.40394275466967938 309 0.49365543195947542
		 324 0.49365543195947542 329 0.43194850296454101 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.88715094327926636 0.95080244541168213 
		1 1 0.59729957580566406 0.76530623435974121 1 1 0.73387813568115234 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.46147942543029785 -0.3097979724407196 
		0 0 0.80201822519302368 0.64366632699966431 0 0 -0.67928111553192139 0 0;
createNode animCurveTL -n "EyeBrowOuter_R_translateZ";
	rename -uid "869B54BD-49D8-EAEB-F1D0-228F1E695569";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 -0.0023097391411755468 13 -0.018766630522051318
		 21 -0.02111761500503357 36 -0.02111761500503357 41 -0.018477913129404375 49 0 57 0
		 72 0 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 -0.01633292860578895
		 301 -0.1306634288463116 309 -0.14703225399848691 324 -0.14703225399848691 329 -0.12865322224867604
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.97127574682235718 0.98842924833297729 
		1 1 0.96403616666793823 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 -0.23795689642429352 -0.15168225765228271 
		0 0 0.26577103137969971 0 0;
createNode animCurveTL -n "EyeBrowRegion_L_translateX";
	rename -uid "A9F63357-406F-666F-18F6-93BA0152C938";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 -0.014415801017266864 13 -0.13015562129718841
		 21 -0.14668988133717731 36 -0.14668988133717731 41 -0.12812509813438269 49 0 57 0
		 72 0 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 -0.018237801599385709 157 -0.16120937602690405
		 165 -0.18163388665940672 180 -0.18163388665940672 185 -0.17772363820179637 193 -0.15035189899852383
		 201 -0.14643305658825947 216 -0.14643305658825947 221 -0.14597751593809399 229 -0.14278873138693607
		 237 -0.14226811350103274 252 -0.14226811350103274 257 -0.1428804610065269 265 -0.042134720900157227
		 273 -0.042748414224344743 288 -0.042748414224344743 293 -0.04805326720007224 301 -0.085187238030164619
		 309 -0.090503750023421134 324 -0.090503750023421134 329 -0.065833211654242621 337 0
		 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.99684906005859375 0.9987601637840271 
		1 1 0.98518967628479004 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 -0.07932213693857193 -0.049780551344156265 
		0 0 0.17146815359592438 0 0;
createNode animCurveTL -n "EyeBrowRegion_L_translateY";
	rename -uid "B7973D90-4F3A-9E6D-6B18-FABEB1CD4C44";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.0094920205502720088 13 0.077122666970960069
		 21 0.08678418788820122 36 0.08678418788820122 41 0.075936164402176071 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 -0.048001167215233474 157 -0.39000948362377197
		 165 -0.43886781453927748 180 -0.43886781453927748 185 -0.34246485796912207 193 0.33235583802196583
		 201 0.42897066922194571 216 0.42897066922194571 221 0.38205200227579539 229 0.053621333652743242
		 237 0 252 0 257 0 265 0 273 0 288 0 293 0.016859504574484444 301 0.13487603659587555
		 309 0.15177259502656765 324 0.15177259502656765 329 0.1328010206482467 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.9694790244102478 0.98768514394760132 
		1 1 0.96181315183639526 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0.24517427384853363 0.1564546525478363 
		0 0 -0.27370691299438477 0 0;
createNode animCurveTL -n "EyeBrowRegion_L_translateZ";
	rename -uid "6338F2C9-48E0-36DC-6A53-818D228F6023";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 -0.010145110984391834 13 -0.082429026748183656
		 21 -0.092755300428725346 36 -0.092755300428725346 41 -0.081160887875134674 49 0 57 0
		 72 0 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0.01045853421759553
		 193 0.083668273740764243 201 0.094149793747849 216 0.094149793747849 221 0.083852160056677824
		 229 0.011768724218479576 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "EyeBrowRegion_R_translateX";
	rename -uid "20D04ACB-4CDA-D84F-2D55-8FB6A2E3A055";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 -0.0054843551238098233 13 -0.057587623412849953
		 21 -0.065030947454141425 36 -0.065030947454141425 41 -0.056673530986726471 49 0 57 0
		 72 0 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 -0.019275295067103106 157 -0.16963901045210794
		 165 -0.1911195412213943 180 -0.1911195412213943 185 -0.1820138032521621 193 -0.11827363746753641
		 201 -0.10914788688738267 216 -0.10914788688738267 221 -0.097009857227883842 229 -0.012043649611391849
		 237 0 252 0 257 -0.018620836518144501 265 -0.062090764321229577 273 -0.08258492846697725
		 288 -0.08258492846697725 293 -0.085227971082708465 301 -0.10372926939282703 309 -0.10637812089342809
		 324 -0.10637812089342809 329 -0.080392980518580501 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.9929465651512146 0.99504274129867554 
		1 1 0.99921506643295288 0.99969184398651123 1 1 0.97970956563949585 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.11856298893690109 -0.099448390305042267 
		0 0 -0.039614558219909668 -0.024825353175401688 0 0 0.20042262971401215 0 0;
createNode animCurveTL -n "EyeBrowRegion_R_translateY";
	rename -uid "2177F1C0-45BA-0A36-0137-A2B8DFAEE5D7";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 -0.026187785470339939 13 -0.21277575694651202
		 21 -0.23943118144310802 36 -0.23943118144310802 41 -0.21324339597276809 49 -0.029928897680388505
		 57 0 72 0 77 0 85 0 93 0 108 0 113 -0.10332038472321711 121 -0.8265630777857369 129 -0.94464351746941355
		 144 -0.94464351746941355 149 -0.89551709099643717 157 -0.54549130237648014 165 -0.49548761828791488
		 180 -0.49548761828791488 185 -0.42984034365498985 193 0.029690578775485199 201 0.095482133132878233
		 216 0.095482133132878233 221 0.13819813803706976 229 0.43721017236641052 237 0.48602846368548658
		 252 0.48602846368548658 257 0.43203848541964274 265 0.054108637558735795 273 0 288 0
		 293 0.021658105353725119 301 0.17326484282980095 309 0.19497054841507272 324 0.19497054841507272
		 329 0.17059922986318862 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.77712076902389526 0.89181911945343018 
		1 1 0.95106989145278931 0.97991722822189331 1 1 0.9392087459564209 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.62935161590576172 -0.45239219069480896 
		0 0 0.30897587537765503 0.1994045078754425 0 0 -0.34334665536880493 0 0;
createNode animCurveTL -n "EyeBrowRegion_R_translateZ";
	rename -uid "AF0E9798-47B6-526E-944D-728A39114144";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0.013211578153396866
		 193 0.10569262522717493 201 0.11893323981607377 216 0.11893323981607377 221 0.1059249167111907
		 229 0.014866654977009217 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "FKHead_M_translateX";
	rename -uid "79E2C77F-46E2-B04E-0F1B-A09125E65EBD";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 -0.014905873250955371
		 301 -0.11924698600764297 309 -0.13418561941958945 324 -0.13418561941958945 329 -0.11741241699214078
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.97590386867523193 0.99033498764038086 
		1 1 0.96977674961090088 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 -0.21820071339607239 -0.13869623839855194 
		0 0 0.24399414658546448 0 0;
createNode animCurveTL -n "FKHead_M_translateY";
	rename -uid "815219C7-4673-17CB-57C6-F29AB375D6E8";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0.029971737750861926
		 301 0.23977390200689541 309 0.26981151170885814 324 0.26981151170885814 329 0.23608507274525087
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.91206598281860352 0.96256232261657715 
		1 1 0.89231252670288086 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0.41004344820976257 0.27106031775474548 
		0 0 -0.45141828060150146 0 0;
createNode animCurveTL -n "FKHead_M_translateZ";
	rename -uid "6577A758-446B-8B57-C342-B7A145AFCFEA";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0.0020983971172411297
		 301 0.016787176937929037 309 0.018890185916966302 324 0.018890185916966302 329 0.016528912677345513
		 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 0.99950498342514038 0.999805748462677 
		1 1 0.99937331676483154 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0.031460404396057129 0.019711898639798164 
		0 0 -0.035396937280893326 0 0;
createNode animCurveTL -n "FKNeck_M_translateX";
	rename -uid "521B600C-4C3D-AD34-6E95-D9A496C2B977";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "FKNeck_M_translateY";
	rename -uid "7E32DBE1-481B-CD38-1A5C-D7A4B2DE84E9";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "FKNeck_M_translateZ";
	rename -uid "DEECAE7A-4FD0-B2E8-1D9C-0C9D8EE1BBC4";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "innerLid_R_translateX";
	rename -uid "5BF58AC7-43BC-2D98-65AF-9397F716A21F";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 -0.0043497568665287461
		 49 0 57 0 72 0 77 0 85 -0.0049711507046042783 93 0 108 0 113 0 121 0 129 0 144 0
		 149 0 157 0 165 0 180 0 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0
		 273 0 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "innerLid_R_translateY";
	rename -uid "01B05E76-413E-97A3-7375-B2A4F329D256";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 -0.019863595962776349
		 49 0 57 0 72 0 77 0 85 -0.022701252528887245 93 0 108 0 113 0 121 0 129 0 144 0 149 0
		 157 0 165 0 180 0 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0
		 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "innerLid_R_translateZ";
	rename -uid "4BEB9191-43D4-4F21-2122-E886FF209D76";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0.0044480614666359657
		 49 0 57 0 72 0 77 0 85 0.0050834988190125302 93 0 108 0 113 0 121 0 129 0 144 0 149 0
		 157 0 165 0 180 0 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0
		 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "lowerInnerLid_L_translateY";
	rename -uid "0A38A081-441B-8755-BF60-50A718C433F7";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0.040159561225933213
		 49 0 57 0 72 0 77 0 85 0.25901577898587003 93 0.24356472866834686 108 0.24356472866834686
		 113 0.21692483647024641 121 0.030445591083543361 129 0 144 0 149 0.028204610182695537
		 157 0.22916245773440122 165 0.25787072167035918 180 0.25787072167035918 185 0.22922541445355904
		 193 0.028708263935957962 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0
		 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "lowerInnerLid_R_translateY";
	rename -uid "DC135DA2-4931-E805-2299-F78EFDC90AA0";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0.016285727720592041
		 49 0 57 0 72 0 77 0 85 0.23173139783690874 93 0.24356472866834691 108 0.24356472866834691
		 113 0.24117847787666299 121 0.22447472233487564 129 0.22174757857295119 144 0.22174757857295119
		 149 0.20831042670614167 157 0.11257071965512375 165 0.098893618647835468 180 0.098893618647835468
		 185 0.087908121459172109 193 0.011009641138528564 201 0 216 0 221 0 229 0 237 0 252 0
		 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "lowerOuterLid_L_translateY";
	rename -uid "C7C4B6B0-4386-DF54-D44F-0FBF0E3F63FC";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0.039229451034634891
		 49 0 57 0 72 0 77 0 85 0.04483365832529701 93 0 108 0 113 0 121 0 129 0 144 0 149 0.02681401107714626
		 157 0.21786384000181339 165 0.24515667270533725 180 0.24515667270533725 185 0.21792369270511058
		 193 0.027292832703523867 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0
		 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "lowerOuterLid_R_translateX";
	rename -uid "14A13168-4841-3ED7-BA18-889627B0F641";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0.044595078978302005
		 49 0 57 0 72 0 77 0 85 0.050965804546630855 93 0 108 0 113 0 121 0 129 0 144 0 149 0
		 157 0 165 0 180 0 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0
		 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "lowerOuterLid_R_translateY";
	rename -uid "CDEE4597-419C-135D-BC2E-6E8BC408205C";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0.044458080905333264
		 49 0 57 0 72 0 77 0 85 0.050809235320380841 93 0 108 0 113 0.024253641406416518 121 0.19402913125133214
		 129 0.22174757857295102 144 0.22174757857295102 149 0.19994031930208644 157 0.044563596997176241
		 165 0.022366922382189063 180 0.022366922382189063 185 0.019882315525769134 193 0.0024900675308296427
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "lowerOuterLid_R_translateZ";
	rename -uid "3CD83CF6-4152-992C-3B7E-06A8A52F6E39";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 -0.04711050436813944
		 49 0 57 0 72 0 77 0 85 -0.053840576420730812 93 0 108 0 113 0 121 0 129 0 144 0 149 0
		 157 0 165 0 180 0 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0
		 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "NoseCorner_L_translateX";
	rename -uid "5A04CB88-4855-0826-DB98-968013047DB4";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 -0.032775569042154451 13 -0.26630149846750489
		 21 -0.29966234552826926 36 -0.29966234552826926 41 -0.26688677648611481 49 -0.03745779319103365
		 57 0 72 0 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 -0.0038152070830742243
		 157 -0.030998557549978073 165 -0.034881893330964336 180 -0.034881893330964336 185 -0.031007073637217077
		 193 -0.0038833357809862634 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0
		 288 0 293 -0.010382605644204691 301 -0.083060845153637528 309 -0.093768432964340426
		 324 -0.093768432964340426 329 -0.082047378843797869 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[22:39]"  1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[22:39]"  1 1 1 1 1 1 1 1 1 1 1 0.98808890581130981 
		0.99499934911727905 1 1 0.98489373922348022 1 1;
	setAttr -s 40 ".koy[22:39]"  0 0 0 0 0 0 0 0 0 0 0 -0.15388421714305878 
		-0.099881753325462341 0 0 0.17316004633903503 0 0;
createNode animCurveTL -n "NoseCorner_L_translateY";
	rename -uid "DFA55451-4766-7E45-176B-EAB2B6F43DE6";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.032316179189257006 13 0.26256895591271318
		 21 0.29546220973034976 36 0.29546220973034976 41 0.26314603054109276 49 0.036932776216293706
		 57 0 72 0 77 -0.037416661667917919 85 -0.29933329334334335 93 -0.34209519239239239
		 108 -0.34209519239239239 113 -0.30467853072447448 121 -0.042761899049049035 129 0
		 144 0 149 0.049290122537685874 157 0.40048224561869772 165 0.45065254891598511 180 0.45065254891598511
		 185 0.40059226821364791 193 0.050170303297287389 201 0 216 0 221 0 229 0 237 0 252 0
		 257 0 265 0 273 0 288 0 293 0.041136095507313758 301 0.32908876405851006 309 0.37033510130910197
		 324 0.37033510130910197 329 0.32404321364546423 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[22:39]"  1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[22:39]"  1 1 1 1 1 1 1 1 1 1 1 0.85102808475494385 
		0.9326973557472229 1 1 0.8213951587677002 1 1;
	setAttr -s 40 ".koy[22:39]"  0 0 0 0 0 0 0 0 0 0 0 0.5251200795173645 
		0.36065986752510071 0 0 -0.57035952806472778 0 0;
createNode animCurveTL -n "NoseCorner_L_translateZ";
	rename -uid "00E24B65-4954-0A92-E6CB-DBB9AB025B41";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0.0065201759228958808 13 0.052976429373529026
		 21 0.059613037009333764 36 0.059613037009333764 41 0.053092861086437883 49 0.007451629626166717
		 57 0 72 0 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0.0010988628583396126
		 157 0.0089282607240093511 165 0.010046746133390743 180 0.010046746133390743 185 0.0089307135428895752
		 193 0.0011184854093813923 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0
		 293 0.0014709600801833811 301 0.011767680641467049 309 0.013158827855647755 324 0.013158827855647755
		 329 0.011513974373691786 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[22:39]"  1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[22:39]"  1 1 1 1 1 1 1 1 1 1 1 0.99975669384002686 
		0.99991500377655029 1 1 0.99969571828842163 1 1;
	setAttr -s 40 ".koy[22:39]"  0 0 0 0 0 0 0 0 0 0 0 0.022059053182601929 
		0.013040909543633461 0 0 -0.024665318429470062 0 0;
createNode animCurveTL -n "NoseCorner_R_translateX";
	rename -uid "F95FA264-49F7-CB71-DEDA-8FB0C3785846";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 -0.039273400871314222 121 -0.31418720697051378 129 -0.35907109368058715
		 144 -0.35907109368058715 149 -0.33514351669857712 157 -0.16465953070175565 165 -0.14030467555935258
		 180 -0.14030467555935258 185 -0.12471907317177802 193 -0.015619856458756048 201 0
		 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 -0.05235083134463956 301 -0.41880665075711648
		 309 -0.47549014849666876 324 -0.47549014849666876 329 -0.41605387993458515 337 0
		 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[22:39]"  1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[22:39]"  1 1 1 1 1 1 1 1 1 1 1 0.78649067878723145 
		0.88305777311325073 1 1 0.74642413854598999 1 1;
	setAttr -s 40 ".koy[22:39]"  0 0 0 0 0 0 0 0 0 0 0 -0.61760222911834717 
		-0.46926423907279968 0 0 0.66547060012817383 0 0;
createNode animCurveTL -n "NoseCorner_R_translateY";
	rename -uid "AFCDD70E-47BF-C45C-65DE-069D2E4B6E7D";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 -0.037416661667917919 85 -0.29933329334334335 93 -0.34209519239239239 108 -0.34209519239239239
		 113 -0.22784518413055943 121 0.57190487370227128 129 0.70247631171579461 144 0.70247631171579461
		 149 0.60132314147861099 157 -0.1193931964613224 165 -0.22235267330988431 180 -0.22235267330988431
		 185 -0.19765285242218963 193 -0.024754106208326965 201 0 216 0 221 0 229 0 237 0
		 252 0 257 0 265 0 273 0 288 0 293 0.055957338895313076 301 0.44765871116250461 309 0.50770212932822434
		 324 0.50770212932822434 329 0.44423936316219631 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[22:39]"  1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[22:39]"  1 1 1 1 1 1 1 1 1 1 1 0.7659459114074707 
		0.87142372131347656 1 1 0.72429656982421875 1 1;
	setAttr -s 40 ".koy[22:39]"  0 0 0 0 0 0 0 0 0 0 0 0.64290499687194824 
		0.49053105711936951 0 0 -0.68948858976364136 0 0;
createNode animCurveTL -n "NoseCorner_R_translateZ";
	rename -uid "99BFF454-41A9-B5A5-5C53-0F821F491707";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0.0096085208735353644 121 0.076868166988282915 129 0.087849333700894761
		 144 0.087849333700894761 149 0.081925545405034092 157 0.03971855379702683 165 0.033688983567311509
		 180 0.033688983567311509 185 0.029946677043110645 193 0.0037505313737046034 201 0
		 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0.015813709225038143 301 0.12650967380030514
		 309 0.14418176369975347 324 0.14418176369975347 329 0.12615904323728427 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[22:39]"  1 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[22:39]"  1 1 1 1 1 1 1 1 1 1 1 0.97300088405609131 
		0.98655200004577637 1 1 0.96534615755081177 1 1;
	setAttr -s 40 ".koy[22:39]"  0 0 0 0 0 0 0 0 0 0 0 0.23080152273178101 
		0.16344799101352692 0 0 -0.26097270846366882 0 0;
createNode animCurveTL -n "upperInnerLid_L_translateX";
	rename -uid "F62DC3CC-416F-9C6D-8528-36AC0A21CCDF";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 -0.057541287292961228
		 49 0 57 0 72 0 77 0 85 -0.065761471191955689 93 0 108 0 113 0 121 0 129 0 144 0 149 0
		 157 0 165 0 180 0 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0
		 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "upperInnerLid_L_translateY";
	rename -uid "28D4FC19-4AC1-09C5-CB73-14A933889323";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 -0.060452498826476353
		 49 0 57 0 72 0 77 0 85 -0.069088570087401546 93 0 108 0 113 0 121 0 129 0 144 0 149 -0.064087132352790357
		 157 -0.52070795036642159 165 -0.58593949579694038 180 -0.58593949579694038 185 -0.49357520622766471
		 193 0.15297482075726482 201 0.24554210876515423 216 0.24554210876515423 221 0.21868594061896549
		 229 0.030692763595644268 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "upperInnerLid_L_translateZ";
	rename -uid "09D463D7-4601-9026-7DC2-3383C29E00F0";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 -0.0060978834325721596
		 49 0 57 0 72 0 77 0 85 -0.0069690096372253263 93 0 108 0 113 0 121 0 129 0 144 0
		 149 0 157 0 165 0 180 0 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0
		 273 0 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "upperInnerLid_R_translateX";
	rename -uid "7BA750DA-437E-C3EE-7526-E6AB92A372F3";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 -0.074833323085614273
		 49 0 57 0 72 0 77 0 85 -0.085523797812130598 93 0 108 0 113 0 121 0 129 0 144 0 149 0
		 157 0 165 0 180 0 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0
		 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "upperInnerLid_R_translateY";
	rename -uid "B1BEF336-4B1B-F698-B4D5-17B6712481F5";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 -0.0038785792442221968 13 -0.031513456359305345
		 21 -0.035461295947174371 36 -0.035461295947174371 41 -0.10945044363674485 49 0 57 0
		 72 0 77 0 85 -0.088991687924334428 93 0 108 0 113 -0.027524171371684752 121 -0.22019337097347802
		 129 -0.25164956682683204 144 -0.25164956682683204 149 -0.29207066987211827 157 -0.58007102906978292
		 165 -0.62121393752659215 180 -0.62121393752659215 185 -0.54231555582600721 193 0.0099731160780877071
		 201 0.089044900815377304 216 0.089044900815377304 221 0.079305614788695411 229 0.011130612601922163
		 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "upperInnerLid_R_translateZ";
	rename -uid "6A56DEA6-4341-A171-50C7-728BC62AE23D";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 -0.010969507618989693
		 49 0 57 0 72 0 77 0 85 -0.012536580135988212 93 0 108 0 113 0 121 0 129 0 144 0 149 0
		 157 0 165 0 180 0 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0
		 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "upperOuterLid_L_translateY";
	rename -uid "27104E2C-47D9-9C34-2C4F-94ABC8A8B15F";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 -0.031146156324232434 157 -0.25306252013438851
		 165 -0.2847648578215537 180 -0.2847648578215537 185 -0.22585624703128218 193 0.18650402850061842
		 201 0.24554210876515423 216 0.24554210876515423 221 0.21868594061896549 229 0.030692763595644268
		 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTL -n "upperOuterLid_R_translateY";
	rename -uid "903EE949-486B-1EDB-067B-6CA20E29BE9B";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 -0.027524171371684752 121 -0.22019337097347802 129 -0.25164956682683204
		 144 -0.25164956682683204 149 -0.22412539545514729 157 -0.0280156744318934 165 0 180 0
		 185 0.0098914623708487893 193 0.079131698966790315 201 0.089044900815377248 216 0.089044900815377248
		 221 0.079305614788695356 229 0.011130612601922163 237 0 252 0 257 0 265 0 273 0 288 0
		 293 0 301 0 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTU -n "ctrlEye_L_blink";
	rename -uid "DA8983A4-4F90-AC73-ECA7-B78C4DDF250D";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 4 ".ktv[0:3]"  41 0 49 10 77 10 88 0;
createNode animCurveTU -n "ctrlEye_L_lowerLid";
	rename -uid "B9C4D111-4F4D-7CCF-6F09-AE8BFF450938";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 87.995833333333337 0 88 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0
		 185 0.6015625 193 4.8125 201 5.5 216 5.5 221 5.675 229 6.8999999999999995 237 7.1
		 252 7.1 257 6.4328124999999998 265 1.7625000000000002 273 1 288 1 293 0.890625 301 0.125
		 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.099426716566085815 0.1385415643453598 
		1 1 0.5204625129699707 0.64911895990371704 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 -0.99504488706588745 -0.99035662412643433 
		0 0 -0.85388457775115967 -0.76068699359893799 0 0 0 0 0;
createNode animCurveTU -n "ctrlEye_L_upperLid";
	rename -uid "009997D3-409A-8157-16AC-AABA1FFD3417";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 87.995833333333337 0 88 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0
		 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0.44843750000000004 265 3.5875000000000004
		 273 4.1000000000000005 288 4.1000000000000005 293 3.6515625000000007 301 0.51250000000000018
		 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 0.14704813063144684 0.20376333594322205 
		1 1 0.14704811573028564 0.20376333594322205 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0.98912930488586426 0.97902011871337891 
		0 0 -0.98912930488586426 -0.97902011871337891 0 0 0 0 0;
createNode animCurveTU -n "ctrlEye_R_blink";
	rename -uid "C75EBB43-4F1A-E6F4-1DBD-8D873804A944";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 11 ".ktv[0:10]"  0 0 16 6.4 41 6.4 49 10 77 10 88 0 113 0
		 126 6.0000000000000009 146 6.0000000000000009 154 1.982849936733218 163 0;
	setAttr -s 11 ".kit[2:10]"  1 18 18 18 18 18 18 18 
		18;
	setAttr -s 11 ".kot[2:10]"  1 18 18 18 18 18 18 18 
		18;
	setAttr -s 11 ".kix[2:10]"  1 1 1 1 1 1 1 0.11261239647865295 1;
	setAttr -s 11 ".kiy[2:10]"  0 0 0 0 0 0 0 -0.99363899230957031 0;
	setAttr -s 11 ".kox[2:10]"  1 1 1 1 1 1 1 0.11261238902807236 1;
	setAttr -s 11 ".koy[2:10]"  0 0 0 0 0 0 0 -0.99363899230957031 0;
createNode animCurveTU -n "ctrlEye_R_blinkCenter";
	rename -uid "7F43037A-4766-20B2-3AE0-F784D15C909B";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 4 ".ktv[0:3]"  0 2 16 3.8000000000000007 41 3.8000000000000007
		 49 2;
	setAttr -s 4 ".kit[2:3]"  1 18;
	setAttr -s 4 ".kot[2:3]"  1 18;
	setAttr -s 4 ".kix[2:3]"  1 1;
	setAttr -s 4 ".kiy[2:3]"  0 0;
	setAttr -s 4 ".kox[2:3]"  1 1;
	setAttr -s 4 ".koy[2:3]"  0 0;
createNode animCurveTU -n "ctrlEye_R_lowerLid";
	rename -uid "A9BCFCEA-4068-4FEE-5D77-B89A00D48867";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 87.995833333333337 0 88 0 108 0 113 0 121 0 126 0 144 0 149 1.09375 157 8.75
		 165 10 180 10 185 9.5078125 193 6.0625 201 5.5 216 5.5 221 4.8984375 229 0.6875 237 0
		 252 0 257 0.109375 265 0.875 273 1 288 1 293 0.890625 301 0.125 309 0 324 0 329 0
		 337 0 345 0;
	setAttr -s 40 ".kit[5:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kot[5:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 18 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[5:39]"  1 1 1 1 1 1 1 1 1 1 1 1 0.060839410871267319 
		0.085024252533912659 1 1 0.13422423601150513 0.18630951642990112 1 1 0.11014807224273682 
		0.15331701934337616 1 1 0.52046245336532593 0.64911890029907227 1 1 0.52046245336532593 
		0.64911895990371704 1 1 1 1 1;
	setAttr -s 40 ".kiy[5:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0.99814754724502563 
		0.99637889862060547 0 0 -0.99095094203948975 -0.98249107599258423 0 0 -0.99391520023345947 
		-0.98817706108093262 0 0 0.85388457775115967 0.76068699359893799 0 0 -0.85388457775115967 
		-0.76068699359893799 0 0 0 0 0;
	setAttr -s 40 ".kox[5:39]"  1 1 1 1 1 1 1 1 1 1 1 1 0.060839414596557617 
		0.085024252533912659 1 1 0.13422425091266632 0.18630951642990112 1 1 0.11014806479215622 
		1 1 1 0.52046245336532593 0.64911895990371704 1 1 0.5204625129699707 0.64911895990371704 
		1 1 1 1 1;
	setAttr -s 40 ".koy[5:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0.99814760684967041 
		0.99637889862060547 0 0 -0.9909510612487793 -0.98249107599258423 0 0 -0.9939151406288147 
		0 0 0 0.85388451814651489 0.76068699359893799 0 0 -0.85388457775115967 -0.76068699359893799 
		0 0 0 0 0;
createNode animCurveTU -n "ctrlEye_R_upperLid";
	rename -uid "75252DD9-4D7B-7108-67C2-C1A0531610FF";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 87.995833333333337 0 88 0 108 0 113 0 121 0 126 0 144 0 149 0 157 0 165 0 180 0
		 185 0 193 0 201 0 216 0 221 0 229 0 237 0 252 0 257 0.44843750000000004 265 3.5875000000000004
		 273 4.1000000000000005 288 4.1000000000000005 293 3.6515625000000007 301 0.51250000000000018
		 309 0 324 0 329 0 337 0 345 0;
	setAttr -s 40 ".kit[5:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kot[5:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 18 18 18 18 18 18 18 18 18 1 18 18 18 
		18 18 18 18 18 18 18 18 1 18;
	setAttr -s 40 ".kix[5:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 
		1 1 1 1 0.14704813063144684 0.20376336574554443 1 1 0.14704813063144684 0.20376333594322205 
		1 1 1 1 1;
	setAttr -s 40 ".kiy[5:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 
		0 0 0 0 0.98912930488586426 0.97902017831802368 0 0 -0.98912930488586426 -0.97902011871337891 
		0 0 0 0 0;
	setAttr -s 40 ".kox[5:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 
		1 1 1 1 0.14704813063144684 0.20376333594322205 1 1 0.14704811573028564 0.20376333594322205 
		1 1 1 1 1;
	setAttr -s 40 ".koy[5:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 
		0 0 0 0 0.98912930488586426 0.97902011871337891 0 0 -0.98912930488586426 -0.97902011871337891 
		0 0 0 0 0;
createNode animCurveTU -n "FKHead_M_Global";
	rename -uid "A1794A37-42BF-33D9-A16F-DC9B2D04D3A1";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 0 5 0 13 0 21 0 36 0 41 0 49 0 57 0 72 0
		 77 0 85 0 93 0 108 0 113 0 121 0 129 0 144 0 149 0 157 0 165 0 180 0 185 0 193 0
		 201 0 216 0 221 0 229 0 237 0 252 0 257 0 265 0 273 0 288 0 293 0 301 0 309 0 324 0
		 329 0 337 0 345 0;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTU -n "FKHead_M_scaleX";
	rename -uid "A604B19E-4951-DCF0-6B6F-D3982DEB640A";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 1 5 1 13 1 21 1 36 1 41 1 49 1 57 1 72 1
		 77 1 85 1 93 1 108 1 113 1 121 1 129 1 144 1 149 1 157 1 165 1 180 1 185 1 193 1
		 201 1 216 1 221 1 229 1 237 1 252 1 257 1 265 1 273 1 288 1 293 1 301 1 309 1 324 1
		 329 1 337 1 345 1;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTU -n "FKHead_M_scaleY";
	rename -uid "DC6FFE82-4DE1-61BE-37F2-EC9E1082E66B";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 1 5 1 13 1 21 1 36 1 41 1 49 1 57 1 72 1
		 77 1 85 1 93 1 108 1 113 1 121 1 129 1 144 1 149 1 157 1 165 1 180 1 185 1 193 1
		 201 1 216 1 221 1 229 1 237 1 252 1 257 1 265 1 273 1 288 1 293 1 301 1 309 1 324 1
		 329 1 337 1 345 1;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTU -n "FKHead_M_scaleZ";
	rename -uid "99ED5311-4018-972E-6624-17BAEB994C33";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 1 5 1 13 1 21 1 36 1 41 1 49 1 57 1 72 1
		 77 1 85 1 93 1 108 1 113 1 121 1 129 1 144 1 149 1 157 1 165 1 180 1 185 1 193 1
		 201 1 216 1 221 1 229 1 237 1 252 1 257 1 265 1 273 1 288 1 293 1 301 1 309 1 324 1
		 329 1 337 1 345 1;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTU -n "FKHead_M_visibility";
	rename -uid "40C8BE94-4040-912C-07C5-BA9D8F749763";
	setAttr ".tan" 5;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 1 5 1 13 1 21 1 36 1 41 1 49 1 57 1 72 1
		 77 1 85 1 93 1 108 1 113 1 121 1 129 1 144 1 149 1 157 1 165 1 180 1 185 1 193 1
		 201 1 216 1 221 1 229 1 237 1 252 1 257 1 265 1 273 1 288 1 293 1 301 1 309 1 324 1
		 329 1 337 1 345 1;
	setAttr -s 40 ".kit[0:39]"  9 18 9 9 9 18 9 9 
		9 18 9 9 9 18 9 9 9 18 9 9 9 18 9 9 9 
		18 1 9 9 18 9 9 9 18 9 9 9 18 1 9;
	setAttr -s 40 ".kot[25:39]"  18 5 5 5 5 5 5 5 
		5 5 5 5 18 5 5;
	setAttr -s 40 ".kix[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".kiy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTU -n "FKNeck_M_scaleX";
	rename -uid "9B923F9B-44ED-7A23-39A1-34A566E3180B";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 1 5 1 13 1 21 1 36 1 41 1 49 1 57 1 72 1
		 77 1 85 1 93 1 108 1 113 1 121 1 129 1 144 1 149 1 157 1 165 1 180 1 185 1 193 1
		 201 1 216 1 221 1 229 1 237 1 252 1 257 1 265 1 273 1 288 1 293 1 301 1 309 1 324 1
		 329 1 337 1 345 1;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTU -n "FKNeck_M_scaleY";
	rename -uid "4EF61953-44A4-1605-2E9D-AF99673EE7FA";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 1 5 1 13 1 21 1 36 1 41 1 49 1 57 1 72 1
		 77 1 85 1 93 1 108 1 113 1 121 1 129 1 144 1 149 1 157 1 165 1 180 1 185 1 193 1
		 201 1 216 1 221 1 229 1 237 1 252 1 257 1 265 1 273 1 288 1 293 1 301 1 309 1 324 1
		 329 1 337 1 345 1;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTU -n "FKNeck_M_scaleZ";
	rename -uid "A9CC26C5-4082-DF93-F8A5-D0A4F6560966";
	setAttr ".tan" 18;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 1 5 1 13 1 21 1 36 1 41 1 49 1 57 1 72 1
		 77 1 85 1 93 1 108 1 113 1 121 1 129 1 144 1 149 1 157 1 165 1 180 1 185 1 193 1
		 201 1 216 1 221 1 229 1 237 1 252 1 257 1 265 1 273 1 288 1 293 1 301 1 309 1 324 1
		 329 1 337 1 345 1;
	setAttr -s 40 ".kit[38:39]"  1 18;
	setAttr -s 40 ".kot[26:39]"  1 18 18 18 18 18 18 18 
		18 18 18 18 1 18;
	setAttr -s 40 ".kix[38:39]"  1 1;
	setAttr -s 40 ".kiy[38:39]"  0 0;
	setAttr -s 40 ".kox[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".koy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
createNode animCurveTU -n "FKNeck_M_visibility";
	rename -uid "4B0023E3-49DE-13A5-1540-80A65DC10D04";
	setAttr ".tan" 5;
	setAttr ".wgt" no;
	setAttr -s 40 ".ktv[0:39]"  1 1 5 1 13 1 21 1 36 1 41 1 49 1 57 1 72 1
		 77 1 85 1 93 1 108 1 113 1 121 1 129 1 144 1 149 1 157 1 165 1 180 1 185 1 193 1
		 201 1 216 1 221 1 229 1 237 1 252 1 257 1 265 1 273 1 288 1 293 1 301 1 309 1 324 1
		 329 1 337 1 345 1;
	setAttr -s 40 ".kit[0:39]"  9 18 9 9 9 18 9 9 
		9 18 9 9 9 18 9 9 9 18 9 9 9 18 9 9 9 
		18 1 9 9 18 9 9 9 18 9 9 9 18 1 9;
	setAttr -s 40 ".kot[25:39]"  18 5 5 5 5 5 5 5 
		5 5 5 5 18 5 5;
	setAttr -s 40 ".kix[26:39]"  1 1 1 1 1 1 1 1 1 1 1 1 1 1;
	setAttr -s 40 ".kiy[26:39]"  0 0 0 0 0 0 0 0 0 0 0 0 0 0;
select -ne :time1;
	setAttr -av -k on ".cch";
	setAttr -av -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".o" 172;
	setAttr -av ".unw" 172;
	setAttr -av -k on ".etw";
	setAttr -av -k on ".tps";
	setAttr -av -k on ".tms";
select -ne :hardwareRenderingGlobals;
	setAttr -k on ".ihi";
	setAttr ".vac" 2;
	setAttr -av ".aoam";
	setAttr -av ".aora";
	setAttr -av ".hfa";
	setAttr -av ".mbe";
	setAttr -av -k on ".mbsof";
select -ne :renderPartition;
	setAttr -av -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 52 ".st";
	setAttr -cb on ".an";
	setAttr -cb on ".pt";
select -ne :renderGlobalsList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
select -ne :defaultShaderList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 54 ".s";
select -ne :postProcessList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 2 ".p";
select -ne :defaultRenderingList1;
	setAttr -k on ".ihi";
	setAttr -s 5 ".r";
select -ne :initialShadingGroup;
	setAttr -av -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 36 ".dsm";
	setAttr -k on ".mwc";
	setAttr -cb on ".an";
	setAttr -cb on ".il";
	setAttr -cb on ".vo";
	setAttr -cb on ".eo";
	setAttr -cb on ".fo";
	setAttr -cb on ".epo";
	setAttr -k on ".ro" yes;
	setAttr -s 18 ".gn";
select -ne :initialParticleSE;
	setAttr -av -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".mwc";
	setAttr -cb on ".an";
	setAttr -cb on ".il";
	setAttr -cb on ".vo";
	setAttr -cb on ".eo";
	setAttr -cb on ".fo";
	setAttr -cb on ".epo";
	setAttr -k on ".ro" yes;
select -ne :defaultRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".macc";
	setAttr -k on ".macd";
	setAttr -k on ".macq";
	setAttr -k on ".mcfr";
	setAttr -cb on ".ifg";
	setAttr -k on ".clip";
	setAttr -k on ".edm";
	setAttr -k on ".edl";
	setAttr -cb on ".ren";
	setAttr -av -k on ".esr";
	setAttr -k on ".ors";
	setAttr -cb on ".sdf";
	setAttr -av -k on ".outf";
	setAttr -cb on ".imfkey";
	setAttr -k on ".gama";
	setAttr -k on ".an";
	setAttr -cb on ".ar";
	setAttr -k on ".fs" 1;
	setAttr -k on ".ef" 10;
	setAttr -av -k on ".bfs";
	setAttr -cb on ".me";
	setAttr -cb on ".se";
	setAttr -k on ".be";
	setAttr -cb on ".ep" 1;
	setAttr -k on ".fec";
	setAttr -av -k on ".ofc";
	setAttr -cb on ".ofe";
	setAttr -cb on ".efe";
	setAttr -cb on ".oft";
	setAttr -cb on ".umfn";
	setAttr -cb on ".ufe";
	setAttr -cb on ".pff";
	setAttr -cb on ".peie";
	setAttr -cb on ".ifp";
	setAttr -k on ".comp";
	setAttr -k on ".cth";
	setAttr -k on ".soll";
	setAttr -k on ".sosl";
	setAttr -k on ".rd";
	setAttr -k on ".lp";
	setAttr -av -k on ".sp";
	setAttr -k on ".shs";
	setAttr -av -k on ".lpr";
	setAttr -cb on ".gv";
	setAttr -cb on ".sv";
	setAttr -k on ".mm";
	setAttr -k on ".npu";
	setAttr -k on ".itf";
	setAttr -k on ".shp";
	setAttr -cb on ".isp";
	setAttr -k on ".uf";
	setAttr -k on ".oi";
	setAttr -k on ".rut";
	setAttr -cb on ".mb";
	setAttr -av -k on ".mbf";
	setAttr -k on ".afp";
	setAttr -k on ".pfb";
	setAttr -k on ".pram";
	setAttr -k on ".poam";
	setAttr -k on ".prlm";
	setAttr -k on ".polm";
	setAttr -cb on ".prm";
	setAttr -cb on ".pom";
	setAttr -cb on ".pfrm";
	setAttr -cb on ".pfom";
	setAttr -av -k on ".bll";
	setAttr -k on ".bls";
	setAttr -av -k on ".smv";
	setAttr -k on ".ubc";
	setAttr -k on ".mbc";
	setAttr -cb on ".mbt";
	setAttr -k on ".udbx";
	setAttr -k on ".smc";
	setAttr -k on ".kmv";
	setAttr -cb on ".isl";
	setAttr -cb on ".ism";
	setAttr -cb on ".imb";
	setAttr -k on ".rlen";
	setAttr -av -k on ".frts";
	setAttr -k on ".tlwd";
	setAttr -k on ".tlht";
	setAttr -k on ".jfc";
	setAttr -cb on ".rsb";
	setAttr -k on ".ope";
	setAttr -k on ".oppf";
	setAttr -cb on ".hbl";
select -ne :defaultResolution;
	setAttr -av -k on ".cch";
	setAttr -av -k on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -av -k on ".w" 640;
	setAttr -av -k on ".h" 480;
	setAttr -av -k on ".pa" 1;
	setAttr -av -k on ".al";
	setAttr -av -k on ".dar" 1.7779999971389771;
	setAttr -av -k on ".ldar";
	setAttr -av -k on ".dpi";
	setAttr -av -k on ".off";
	setAttr -av -k on ".fld";
	setAttr -av -k on ".zsl";
	setAttr -av -k on ".isu";
	setAttr -av -k on ".pdu";
select -ne :defaultLightSet;
	setAttr -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -k on ".mwc";
	setAttr -k on ".an";
	setAttr -k on ".il";
	setAttr -k on ".vo";
	setAttr -k on ".eo";
	setAttr -k on ".fo";
	setAttr -k on ".epo";
	setAttr -k on ".ro" yes;
select -ne :defaultObjectSet;
	setAttr -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -k on ".mwc";
	setAttr -k on ".an";
	setAttr -k on ".il";
	setAttr -k on ".vo";
	setAttr -k on ".eo";
	setAttr -k on ".fo";
	setAttr -k on ".epo";
	setAttr -k on ".ro" yes;
select -ne :defaultColorMgtGlobals;
	setAttr ".cme" no;
select -ne :hardwareRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k off ".ctrs" 256;
	setAttr -av -k off ".btrs" 512;
	setAttr -k off ".fbfm";
	setAttr -k off -cb on ".ehql";
	setAttr -k off -cb on ".eams";
	setAttr -k off -cb on ".eeaa";
	setAttr -k off -cb on ".engm";
	setAttr -k off -cb on ".mes";
	setAttr -k off -cb on ".emb";
	setAttr -av -k off -cb on ".mbbf";
	setAttr -k off -cb on ".mbs";
	setAttr -k off -cb on ".trm";
	setAttr -k off -cb on ".tshc";
	setAttr -k off ".enpt";
	setAttr -k off -cb on ".clmt";
	setAttr -k off -cb on ".tcov";
	setAttr -k off -cb on ".lith";
	setAttr -k off -cb on ".sobc";
	setAttr -k off -cb on ".cuth";
	setAttr -k off -cb on ".hgcd";
	setAttr -k off -cb on ".hgci";
	setAttr -k off -cb on ".mgcs";
	setAttr -k off -cb on ".twa";
	setAttr -k off -cb on ".twz";
	setAttr -k on ".hwcc";
	setAttr -k on ".hwdp";
	setAttr -k on ".hwql";
	setAttr -k on ".hwfr";
	setAttr -k on ".soll";
	setAttr -k on ".sosl";
	setAttr -k on ".bswa";
	setAttr -k on ".shml";
	setAttr -k on ".hwel";
select -ne :ikSystem;
	setAttr -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -av ".gsn";
	setAttr -k on ".gsv";
	setAttr -s 3 ".sol";
select -ne :asPickerfaceModelPanelViewSelectedSet;
	setAttr ".ihi" 0;
	setAttr -s 228 ".dsm";
	setAttr -s 5 ".gn";
// End of asMotionFace.ma
