/* XPM */
static char *biped_141_80_OffK0[] = {
/* columns rows colors chars-per-pixel */
"38 18 108 2",
"   c gray62",
".  c #9F9F9F",
"X  c #AAAAAA",
"o  c #ACABAB",
"O  c #ACACAC",
"+  c #ADAEAE",
"@  c #AEAEAD",
"#  c gray69",
"$  c #B1B1B1",
"%  c #B2B2B1",
"&  c #B2B2B2",
"*  c #B4B3B4",
"=  c #B6B5B6",
"-  c #B7B8B6",
";  c #B9B9B8",
":  c #BAB9B9",
">  c #BCBDBB",
",  c #BEBEBD",
"<  c gray75",
"1  c #BFC0C0",
"2  c #C0C1C1",
"3  c #CCCDCD",
"4  c #CDCDCD",
"5  c #D9DADA",
"6  c #DADBDA",
"7  c #E5E6E5",
"8  c #E7E7E7",
"9  c #EFEEEE",
"0  c #EEF0EE",
"q  c #EFF0EF",
"w  c #F0EFEF",
"e  c #F0F1F0",
"r  c #F3F3F5",
"t  c #F2F4F3",
"y  c #F3F4F2",
"u  c #F2F4F4",
"i  c #F3F4F4",
"p  c #F4F3F4",
"a  c #F4F4F4",
"s  c #F4F5F4",
"d  c #F5F4F4",
"f  c #F5F4F5",
"g  c #F5F5F6",
"h  c #F4F6F4",
"j  c #F5F6F4",
"k  c #F5F6F5",
"l  c #F6F4F4",
"z  c #F6F5F7",
"x  c #F6F6F4",
"c  c #F6F6F6",
"v  c gray97",
"b  c #F7F8F9",
"n  c #F9FAFA",
"m  c #FAFBFB",
"M  c #FBFCFC",
"N  c gray99",
"B  c #FCFCFD",
"V  c #FCFDFD",
"C  c #FDFCFD",
"Z  c #FDFDFD",
"A  c #FCFDFE",
"S  c #FCFDFF",
"D  c #FDFCFE",
"F  c #FDFCFF",
"G  c #FDFDFE",
"H  c #FDFDFF",
"J  c #FCFEFC",
"K  c #FCFEFD",
"L  c #FCFFFC",
"P  c #FCFFFD",
"I  c #FDFEFD",
"U  c #FDFFFC",
"Y  c #FDFFFD",
"T  c #FCFEFE",
"R  c #FCFFFF",
"E  c #FDFEFE",
"W  c #FDFEFF",
"Q  c #FDFFFE",
"!  c #FDFFFF",
"~  c #FEFCFD",
"^  c #FEFDFC",
"/  c #FEFDFD",
"(  c #FFFCFC",
")  c #FFFCFD",
"_  c #FFFDFC",
"`  c #FFFDFD",
"'  c #FEFCFE",
"]  c #FEFCFF",
"[  c #FEFDFE",
"{  c #FEFDFF",
"}  c #FFFDFE",
"|  c #FFFDFF",
" . c #FEFEFC",
".. c #FEFEFD",
"X. c #FEFFFC",
"o. c #FEFFFD",
"O. c #FFFEFC",
"+. c #FFFEFD",
"@. c #FFFFFC",
"#. c #FFFFFD",
"$. c #FEFEFE",
"%. c #FEFEFF",
"&. c #FEFFFE",
"*. c #FEFFFF",
"=. c #FFFEFE",
"-. c #FFFEFF",
";. c #FFFFFE",
":. c gray100",
/* pixels */
"< < 2 2 < < < < 2 2 < 2 < < 2 2 < 2 < < 2 2 < 2 < < 2 2 < < < < 2 2 < 2 < < ",
"< 0 V V V V V V V V V V V V V V V V m V V V V V V V V V V V V V V V V V w < ",
"< 9 V V V V V V V V V V V V V V V V V m V V V V V V V V V V V V V V V V w < ",
"< 7 M V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V 8 < ",
"< 6 m V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V 6 < ",
"< 4 v V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V b 4 < ",
"< 2 v V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V v 2 < ",
"< * v V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V v = < ",
"< # v V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V v O < ",
"< @ p V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V v X < ",
"< & p V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V r # < ",
"< & l V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V x & < ",
"< : l V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V v ; < ",
"< > l V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V b < < ",
"< - x V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V v ; < ",
"< X x V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V x X < ",
"<   y V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V V y . < ",
"< < < < < 2 2 < < < 2 < < < < < 2 2 < 2 < < < < 2 2 < < < < 2 2 < < < < < < "
};
