/* XPM */
static char *biped_104_96_OffK0[] = {
/* columns rows colors chars-per-pixel */
"30 30 126 2",
"   c #7DADBD",
".  c #7DADBE",
"X  c #7CAEBE",
"o  c #7CAEBF",
"O  c #7DAEBE",
"+  c #7DAEBF",
"@  c #7EADBC",
"#  c #7EADBD",
"$  c #7FADBC",
"%  c #7EAEBC",
"&  c #7EAEBD",
"*  c #7EAEBE",
"=  c #58BDDF",
"-  c #58BEDF",
";  c #59BEDF",
":  c #5BBCDC",
">  c #5BBCDD",
",  c #5BBDDD",
"<  c #5ABDDE",
"1  c #5FBBD9",
"2  c #5EBBDA",
"3  c #5DBCDB",
"4  c #5EBCDA",
"5  c #5EBCDB",
"6  c #5CBCDC",
"7  c #56BFE1",
"8  c #57BEE0",
"9  c #57BEE1",
"0  c #58BEE0",
"q  c #6AB6CF",
"w  c #6BB6CE",
"e  c #6BB6CF",
"r  c #6EB4CB",
"t  c #6FB4CB",
"y  c #6CB5CD",
"u  c #6DB4CC",
"i  c #6DB5CC",
"p  c #6DB5CD",
"a  c #6CB5CE",
"s  c #6CB6CD",
"d  c #6EB5CC",
"f  c #61BAD7",
"g  c #63B9D5",
"h  c #62B9D6",
"j  c #63B9D6",
"k  c #62BAD6",
"l  c #63BAD6",
"z  c #64B9D4",
"x  c #64B9D5",
"c  c #65B8D4",
"v  c #60BBD8",
"b  c #60BBD9",
"n  c #61BAD8",
"m  c #79AFC0",
"M  c #7AAFC0",
"N  c #7BAEC0",
"B  c #73B3C7",
"V  c #75B1C4",
"C  c #70B3C9",
"Z  c #78B0C2",
"A  c #79B0C2",
"S  c #47C5EF",
"D  c #4FC1E7",
"F  c #4FC2E7",
"G  c #49C4ED",
"H  c #48C4EE",
"J  c #50C1E7",
"K  c #51C1E6",
"L  c #50C2E7",
"P  c gray62",
"I  c #9E9E9F",
"U  c #9E9F9F",
"Y  c #9D9FA0",
"T  c #9E9FA0",
"R  c #8DA7AF",
"E  c #8FA5AD",
"W  c #8FA6AD",
"Q  c #8EA6AE",
"!  c #8FA6AE",
"~  c #84ABB7",
"^  c #85AAB6",
"/  c #87A9B5",
"(  c #86A9B6",
")  c #86AAB5",
"_  c #87AAB5",
"`  c #86AAB6",
"'  c #86ABB6",
"]  c #83ABB8",
"[  c #80ACBB",
"{  c #81ACBB",
"}  c #80ADBC",
"|  c #89A9B3",
" . c #8BA8B1",
".. c #8AA8B2",
"X. c #8AA9B2",
"o. c #88A9B4",
"O. c #96A3A7",
"+. c #97A2A6",
"@. c #97A3A6",
"#. c #93A4A9",
"$. c #92A4AA",
"%. c #92A5AA",
"&. c #92A5AB",
"*. c #93A5AA",
"=. c #90A5AC",
"-. c #90A6AD",
";. c #94A3A8",
":. c #95A3A8",
">. c #94A4A9",
",. c #95A4A8",
"<. c #9AA1A3",
"1. c #9BA0A2",
"2. c #9BA1A2",
"3. c #9BA1A3",
"4. c #98A1A4",
"5. c #98A2A5",
"6. c #99A2A4",
"7. c #99A2A5",
"8. c #9AA1A4",
"9. c #9AA2A4",
"0. c #9CA0A1",
"q. c #9DA0A0",
"w. c #9DA0A1",
"e. c #9CA1A2",
"r. c #9EA0A0",
"t. c gray75",
/* pixels */
"t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.",
"t.P P P P P P P P 8.X.C 5 8 H H 8 5 u R 3.P P P P P P P P t.",
"t.P P P P P U @.M < K f p B O O B p < F < A @.P P P P P P t.",
"t.P P P P U ) j = p ) ! >.@.8.3.@.;.R ] p 8 j ) P P P P P t.",
"t.P P P r.O 1 j O %.r.U U U U U U U U I ;.O j 1 O r.U P P t.",
"t.P P r.O 1 q _ r.U U P P P P P P P P U U 3.) q 1 O U P P t.",
"t.P P ) 1 p R r.r.P P P P P P P P P P P P U 3.R q 1 ) P P t.",
"t.P @.j z X.r.P P P P P P P P P P P P P P P U U X.z j 8.P t.",
"t.P O < O 3.P P P P P P P P P P P P P P P P P P 8.O 8 M P t.",
"t.8.5 q %.r.P P P P P P P P P P P P P P P P P P P ;.q = @.t.",
"t.) K ] r.P P P P P P P P P P P P P P P P P P P U U ^ 7 o.t.",
"t.p 5 R U P P P P P P P P P P P P P P P P P P P U U R 1 u t.",
"t.< r @.P P P P P P P P P P P P P P P P P P P P P P ;.q = t.",
"t.F Z @.P P P P P P P P P P P P P P P P P P P P P P @.M 7 t.",
"t.G X 3.P P P P P P P P P P P P P P P P P P P P P P 3.O G t.",
"t.G X 3.P P P P P P P P P P P P P P P P P P P P P P 3.O G t.",
"t.F o @.P P P P P P P P P P P P P P P P P P P P P P @.Z F t.",
"t.: u @.P P P P P P P P P P P P P P P P P P P P P P ;.p > t.",
"t.q j E P P P P P P P P P P P P P P P P P P P P P P ! f u t.",
"t.) F ) I P P P P P P P P P P P P P P P P P P P U r.) K ) t.",
"t.8.< p E P P P P P P P P P P P P P P P P P P P P %.p < 8.t.",
"t.U X - @ 3.P P P P P P P P P P P P P P P P P P r.$ - O U t.",
"t.r.@.j c R P P P P P P P P P P P P P P P P P U X.c j @.U t.",
"t.P P ) 1 q R r.P P P P P P P P P P P P P U r.R q 5 ) U P t.",
"t.P P r.O 5 q X.r.P P P P P P P P P P P P r._ q 1 O r.P P t.",
"t.P P U 3.O 5 c $ %.P P P P P P P P P 3.%.@ z 1 M r.U P P t.",
"t.P P P U r.' j < r ) ! >.@.3.3.@.;.R ) q 8 j ) r.U P P P t.",
"t.P P P P U U >.Z - K j r Z @ @ V u 1 F < A >.U U P P P P t.",
"t.P P P P P P U U 8.X.p < 8 S S 8 > u X.8.P U P P P P P P t.",
"t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t.t."
};
