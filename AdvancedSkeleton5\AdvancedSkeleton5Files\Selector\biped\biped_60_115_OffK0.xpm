/* XPM */
static char *biped_60_115_OffK0[] = {
/* columns rows colors chars-per-pixel */
"10 15 49 1",
"  c gray75",
". c #C9CBCA",
"X c #D3D3D4",
"o c #DBDADB",
"O c #DEDEDD",
"+ c #E1E1E2",
"@ c #E8E8E7",
"# c #E8E8E9",
"$ c #EFF0F0",
"% c gray94",
"& c gray95",
"* c #F5F5F6",
"= c #F7F6F8",
"- c #F8F7F9",
"; c #FAFBFB",
": c #FBFBFC",
"> c #FCFBFC",
", c #FCFDFD",
"< c #FDFDFD",
"1 c #FCFCFE",
"2 c #FCFDFE",
"3 c #FCFDFF",
"4 c #FDFCFE",
"5 c #FDFDFE",
"6 c #FDFDFF",
"7 c #FDFEFD",
"8 c #FDFFFD",
"9 c #FCFEFF",
"0 c #FCFFFE",
"q c #FDFEFE",
"w c #FDFEFF",
"e c #FDFFFE",
"r c #FDFFFF",
"t c #FEFCFD",
"y c #FEFDFD",
"u c #FFFDFD",
"i c #FEFCFE",
"p c #FEFDFE",
"a c #FEFDFF",
"s c #FFFCFF",
"d c #FFFDFE",
"f c #FFFDFF",
"g c #FEFEFD",
"h c #FEFFFD",
"j c #FEFEFE",
"k c #FEFEFF",
"l c #FEFFFE",
"z c #FFFEFE",
"x c #FFFEFF",
/* pixels */
"          ",
" O:yy9yy> ",
" @yyyyyy9 ",
" %yy99999 ",
" *9yy9y99 ",
" 99yyyyy9 ",
" yy9yyyy9 ",
" y99yyy9* ",
" y9yyyyy$ ",
" y9yyyyy# ",
" 9yyyyy:+ ",
" yy9y:y9o ",
" y9yyyy=X ",
" yyyyyy&. ",
"          "
};
