/* XPM */
static char *asBiped[] = {
/* columns rows colors chars-per-pixel */
"32 32 121 2",
"   c black",
".  c #202020",
"X  c gray16",
"o  c #2D2D2D",
"O  c #3A3A3A",
"+  c gray23",
"@  c gray26",
"#  c #434343",
"$  c #444344",
"%  c gray27",
"&  c #4C4C4C",
"*  c #515151",
"=  c #525253",
"-  c #5A5A5A",
";  c gray36",
":  c #5F5F5F",
">  c #646464",
",  c #656565",
"<  c #676667",
"1  c #676767",
"2  c DimGray",
"3  c #6A6A6A",
"4  c #6C6C6C",
"5  c #6F6F6F",
"6  c gray44",
"7  c #767676",
"8  c #797979",
"9  c gray48",
"0  c #7B7B7B",
"q  c #7F7E7F",
"w  c #818181",
"e  c gray52",
"r  c #888888",
"t  c #8C8C8B",
"y  c #8B8C8C",
"u  c #8E8E8E",
"i  c gray56",
"p  c gray57",
"a  c #939393",
"s  c gray59",
"d  c #979797",
"f  c #989898",
"g  c #9A9A9A",
"h  c gray62",
"j  c #A0A0A0",
"k  c gray63",
"l  c #A2A2A2",
"z  c #A9A9A9",
"x  c #AAAAAA",
"c  c gray67",
"v  c #AEAEAE",
"b  c #B2B1B1",
"n  c #B4B4B4",
"m  c #B7B6B7",
"M  c gray73",
"N  c #BBBCBB",
"B  c gray74",
"V  c gray75",
"C  c #C0C0C0",
"Z  c #C1C1C1",
"A  c #C1C2C1",
"S  c #C3C3C2",
"D  c #C3C3C3",
"F  c #C5C5C5",
"G  c #C6C7C6",
"H  c gray79",
"J  c gray80",
"K  c #CDCDCD",
"L  c #CECECE",
"P  c #D0D0D0",
"I  c #D1D0D0",
"U  c #D5D5D5",
"Y  c #D7D7D7",
"T  c #D8D8D8",
"R  c gray85",
"E  c #DADADA",
"W  c #DBDBDA",
"Q  c #DBDADB",
"!  c #DADBDB",
"~  c gray86",
"^  c #DBDCDB",
"/  c gainsboro",
"(  c #DDDCDC",
")  c #DCDCDD",
"_  c #DDDDDD",
"`  c gray87",
"'  c gray88",
"]  c #E1E1E1",
"[  c gray89",
"{  c #E4E4E3",
"}  c gray90",
"|  c #E6E6E6",
" . c #E7E7E7",
".. c #E9E8E8",
"X. c #E9E9E9",
"o. c #EAE9E9",
"O. c #EAEAEA",
"+. c #ECECEC",
"@. c gray93",
"#. c #EEEEEE",
"$. c #EFEEEF",
"%. c #EFEFEF",
"&. c #EFEFF0",
"*. c gray94",
"=. c gray95",
"-. c #F3F3F3",
";. c #F4F4F4",
":. c #F5F6F6",
">. c gray97",
",. c #F8F8F8",
"<. c #F9F9F9",
"1. c #F9F9FA",
"2. c #FBFAFB",
"3. c #FBFBFB",
"4. c gray99",
"5. c #FDFDFD",
"6. c #FEFDFD",
"7. c #FEFEFE",
"8. c #FEFFFF",
"9. c gray100",
"0. c None",
/* pixels */
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.R R 0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.` 7.7. .0.0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.&.7.7.,.0.0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.A 7.7.F 0.0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.0. .;.X 0.0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.z 0.0.0.0.0.0.0.0.0.0.0.0.` @.# 0.0.0.0.0.0.0.0.0.0.0.v 0.0.",
"0.B X.7.L B H Y R R ` ` ] =.7.7.7.7.&.` ` ` R R R H B L 7.@.V 0.",
"0.m Y L L ` ] ]  .+.;.7.7.7.7.7.7.7.7.7.7.;.@. .] ] ' Y J T m 9 ",
"0.0.0.0.0.0.0.0.0.0.0.% 2 A 7.7.7.7.I , $ 0.0.0.0.0.0.0.0.0.# 0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.; 7.7.7.7.7     0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.+ ,.7.7.7.*   0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.4.7.7.7.- 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.,.7.7.7.& 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.@.7.7.,.o 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.] 7.7.*.. 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.=.7.7.,.+ 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.7.7.7.7.2 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.l 7.7.7.7.g 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.B 7.7.7.7.M 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.F 7.X. .7.F 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.A 7.^ L 7.A 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.B 7.B l 7.A 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.n 7.l e 7.v 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.z 7.h q 7.z 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.p 7.g 9 7.d 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.r 7.w - 7.u 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.7.p 6 7.< 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.7.u , 7.= 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.7.y 2 7.> 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.=.r , ,.0.0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.0.0.,.p 2 7.5 0.0.0.0.0.0.0.0.0.0.0.0.0.",
"0.0.0.0.0.0.0.0.0.0.0.0.X.;.X.f 0  . . .i 0.0.0.0.0.0.0.0.0.0.0."
};
