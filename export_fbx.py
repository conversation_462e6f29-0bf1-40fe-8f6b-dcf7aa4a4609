import os
import maya.cmds as cmds
import maya.mel as mel
import time

WINDOW_NAME = 'fbxExportWin'
PROGRESS_BAR = 'fbxProgressBar'
STATUS_TEXT = 'fbxStatusText'

def export_all_fbx_with_anim():
    # ————— AVISO PREVIO —————
    cmds.confirmDialog(
        title='Advertencia de Exportación',
        message='⚠️ La animación se exportará al pulsar OK. Por favor, no toques nada hasta que termine el proceso.',
        button=['OK'], defaultButton='OK'
    )

    # ————— VENTANA PERSONALIZADA SIEMPRE AL FRENTE —————
    if cmds.window(WINDOW_NAME, exists=True):
        cmds.deleteUI(WINDOW_NAME)
    cmds.window(WINDOW_NAME,
                title='Exportación FBX',
                widthHeight=(350, 100),
                toolbox=True,
                minimizeButton=False,
                maximizeButton=False)
    cmds.columnLayout(adjustableColumn=True)
    cmds.text(STATUS_TEXT, label='⏳ Iniciando...', height=20)
    cmds.separator(height=8, style='in')
    cmds.progressBar(PROGRESS_BAR, maxValue=100, width=330, height=20)
    cmds.showWindow(WINDOW_NAME)
    cmds.refresh()
    time.sleep(0.1)

    # ————— MENSAJE EN PANTALLA DURANTE EXPORTACIÓN —————
    cmds.inViewMessage(
        amg='🔄 <hl>Exportando FBX... no toques nada hasta que termine.</hl>',
        pos='midCenter',
        fade=True,
        fadeStayTime=10
    )
    cmds.refresh()
    time.sleep(0.1)

    try:
        # 1. Fusionar animation layers
        cmds.text(STATUS_TEXT, edit=True, label='⚙️ Fusionando capas de animación...')
        cmds.refresh(); time.sleep(0.1)
        layers = cmds.ls(type="animLayer")
        if layers:
            layer_list = ", ".join([f'"{l}"' for l in layers])
            mel.eval(f'animLayerMerge({{{layer_list}}});')
        cmds.progressBar(PROGRESS_BAR, edit=True, progress=25)
        cmds.text(STATUS_TEXT, edit=True, label='✅ Capas fusionadas')
        cmds.refresh(); time.sleep(0.1)


        # 2. Bake simulación
        cmds.text(STATUS_TEXT, edit=True, label='⚙️ Horneando simulación...')
        cmds.refresh(); time.sleep(0.1)
        start = cmds.playbackOptions(q=True, minTime=True)
        end   = cmds.playbackOptions(q=True, maxTime=True)
        objs  = cmds.ls('WM:*', type='transform')
        if objs:
            cmds.bakeResults(objs,
                             simulation=True,
                             t=(start, end),
                             sampleBy=1,
                             hierarchy='below',
                             preserveOutsideKeys=True)
        cmds.progressBar(PROGRESS_BAR, edit=True, progress=50)
        cmds.text(STATUS_TEXT, edit=True, label='✅ Simulación horneada')
        cmds.refresh(); time.sleep(0.1)

        # 3. Obtener ruta de exportación
        cmds.text(STATUS_TEXT, edit=True, label='⚙️ Obteniendo ruta de exportación...')
        cmds.refresh(); time.sleep(0.1)
        if objs:
            cmds.select(objs, replace=True)
        scene = cmds.file(q=True, sn=True)
        if not scene:
            cmds.error("Debe guardar la escena antes de exportar.")
            return
        cmds.progressBar(PROGRESS_BAR, edit=True, progress=65)
        cmds.text(STATUS_TEXT, edit=True, label='🔍 Ruta obtenida')
        cmds.refresh(); time.sleep(0.1)

        # 4. Preparar plugin FBX
        cmds.text(STATUS_TEXT, edit=True, label='⚙️ Cargando plugin FBX...')
        cmds.refresh(); time.sleep(0.1)
        scene      = os.path.abspath(scene)
        export_dir = os.path.dirname(scene)
        base_name  = os.path.splitext(os.path.basename(scene))[0]
        path       = os.path.join(export_dir, f"{base_name}.fbx").replace("\\", "/")
        cmds.loadPlugin("fbxmaya", quiet=True)
        cmds.progressBar(PROGRESS_BAR, edit=True, progress=75)
        cmds.text(STATUS_TEXT, edit=True, label='🔌 Plugin listo')
        cmds.refresh(); time.sleep(0.1)

        # 5. Exportar a FBX
        cmds.text(STATUS_TEXT, edit=True, label='⚙️ Exportando a FBX...')
        cmds.refresh(); time.sleep(0.1)
        mel.eval(f'FBXExport -file "{path}" -s')
        cmds.progressBar(PROGRESS_BAR, edit=True, progress=100)
        cmds.text(STATUS_TEXT, edit=True, label='🎉 Exportación completada')
        cmds.refresh(); time.sleep(0.1)

    finally:
        # Cerrar la ventana personalizada al finalizar
        if cmds.window(WINDOW_NAME, exists=True):
            cmds.deleteUI(WINDOW_NAME)

    # 6. Confirmación final al usuario
    cmds.confirmDialog(
        title='Exportación finalizada',
        message=f'✅ Archivo FBX exportado correctamente en:\n{path}',
        button=['OK'], defaultButton='OK'
    )
