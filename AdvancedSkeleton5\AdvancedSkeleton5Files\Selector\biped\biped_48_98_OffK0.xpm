/* XPM */
static char *biped_48_98_OffK0[] = {
/* columns rows colors chars-per-pixel */
"11 13 52 1",
"  c gray75",
". c gray89",
"X c #E5E4E4",
"o c gray90",
"O c #E5E6E6",
"+ c #E6E5E5",
"@ c #E6E6E6",
"# c #E6E6E7",
"$ c #E7E6E6",
"% c #E7E7E8",
"& c #E7E8E9",
"* c #E8EAE9",
"= c #E9EAE9",
"- c #E9EAEA",
"; c #EAE9E9",
": c #EAE8EA",
"> c #EAEAEA",
", c #EBEBEC",
"< c #EEEDED",
"1 c #FDFDFD",
"2 c #FCFDFF",
"3 c #FDFCFE",
"4 c #FDFCFF",
"5 c #FDFDFE",
"6 c #FDFDFF",
"7 c #FCFEFD",
"8 c #FCFFFD",
"9 c #FDFEFD",
"0 c #FDFFFD",
"q c #FCFEFE",
"w c #FCFEFF",
"e c #FDFEFE",
"r c #FDFEFF",
"t c #FDFFFF",
"y c #FEFDFD",
"u c #FFFCFC",
"i c #FFFCFD",
"p c #FFFDFD",
"a c #FEFDFE",
"s c #FEFDFF",
"d c #FFFCFE",
"f c #FFFCFF",
"g c #FFFDFE",
"h c #FFFDFF",
"j c #FEFEFD",
"k c #FFFEFD",
"l c #FEFEFE",
"z c #FEFEFF",
"x c #FEFFFE",
"c c #FFFEFE",
"v c #FFFEFF",
"b c gray100",
/* pixels */
"           ",
" .1111111+ ",
" +1111111& ",
" +1111111% ",
" +1111111+ ",
" ;1111111% ",
" +1111111; ",
" ;1111111; ",
" &1111111> ",
" ;w111111, ",
" <1111111; ",
" <1111111; ",
"           "
};
