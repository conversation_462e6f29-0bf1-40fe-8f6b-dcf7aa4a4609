/* XPM */
static char *biped_198_146_OffK1[] = {
/* columns rows colors chars-per-pixel */
"15 15 41 1",
"  c #4444AA",
". c #5044A5",
"X c #5145A6",
"o c #5344A4",
"O c #5444A4",
"+ c #5747A4",
"@ c #6046A0",
"# c #6047A0",
"$ c #965891",
"% c #9A6294",
"& c #B0608A",
"* c #B1618A",
"= c #B2618A",
"- c #B3628A",
"; c #B56289",
": c #B66389",
"> c #B76B8C",
", c #B76E8D",
"< c #B86E8D",
"1 c #B06F90",
"2 c #B16F90",
"3 c #B4708F",
"4 c #B5708F",
"5 c #B6708E",
"6 c #BF718B",
"7 c #BC708C",
"8 c #BD708C",
"9 c #B27090",
"0 c #D67985",
"q c #D67D87",
"w c #DA7C85",
"e c #DB7C85",
"r c gray75",
"t c #DA8086",
"y c #DA8087",
"u c #DB8086",
"i c #DD8085",
"p c #DD8185",
"a c #DC8086",
"s c #DE8185",
"d c #E38484",
/* pixels */
"rrrrrrrrrrrrrrr",
"rddddu4+3uddddr",
"rddddu1 1uddddr",
"rddddu1 3uddddr",
"rddddu9 3uddddr",
"rdaaaq1 1qaapar",
"r6<6<,% %<7666r",
"r@OOO+. .O.oo#r",
"r;***&$ $&***;r",
"rwwwe0, >0eeeer",
"rddddu4 4uddddr",
"rddddu9 1uddddr",
"rdddde9 1uddddr",
"rddddu4.4uddddr",
"rrrrrrrrrrrrrrr"
};
