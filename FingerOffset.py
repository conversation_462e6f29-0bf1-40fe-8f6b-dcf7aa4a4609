import maya.cmds as cmds

# Agrupar en un chunk para permitir Ctrl+Z global
cmds.undoInfo(openChunk=True)
try:
    # Definir offsets por tipo de dedo
    finger_offsets = {
        "IndexFinger": 2,
        "MiddleFinger": 0,
        "ThumbFinger": 0,
        "RingFinger": -2,
        "PinkyFinger": -3
    }

    # Obtener selección actual
    selection = cmds.ls(selection=True, long=True)

    for ctrl in selection:
        # Determinar tipo de dedo y aplicar offset si corresponde
        matched = False
        for finger_name, offset in finger_offsets.items():
            if finger_name in ctrl:
                matched = True
                if offset != 0:
                    keys = cmds.keyframe(ctrl, query=True)
                    if keys:
                        cmds.keyframe(ctrl, edit=True, relative=True, timeChange=offset)
                break  # salir del loop de nombres al encontrar coincidencia

        if not matched:
            print(f"Control no reconocido (omitido): {ctrl}")

        # Establecer infinity a "cycle" en curvas conectadas
        anim_curves = cmds.listConnections(ctrl, type='animCurve') or []
        for curve in anim_curves:
            cmds.setInfinity(curve, pri='cycle', poi='cycle')

    # Mostrar infinity en el Graph Editor (opcional pero útil)
    cmds.animCurveEditor('graphEditor1GraphEd', edit=True, displayInfinities=True)
    cmds.optionVar(intValue=('graphEditorDisplayInfinities', 1))

except Exception as e:
    print("Error:", e)

finally:
    cmds.undoInfo(closeChunk=True)
