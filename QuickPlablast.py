import maya.cmds as cmds
import maya.mel as mel
import os

def apply_camera_preset_mel(cameraShape="perspShape", preset="Playblast02"):
    if not cmds.objExists(cameraShape):
        cmds.warning(f"No existe {cameraShape}")
        return
    try:
        mel.eval(f'applyPresetToNode("|persp|{cameraShape}", "", "", "{preset}", 1);')
        print(f"⚠️ Aviso: Si no existe el preset, no se aplicará nada.")
    except Exception as e:
        cmds.warning(f"Error al aplicar el preset con MEL: {e}")

def get_scene_name():
    scene_path = cmds.file(query=True, sceneName=True)
    if not scene_path:
        cmds.warning("La escena no está guardada.")
        return None
    return os.path.splitext(os.path.basename(scene_path))[0]

def make_playblast_window():
    if cmds.window("pbAdjustCamWin", exists=True):
        cmds.deleteUI("pbAdjustCamWin")

    cmds.window("pbAdjustCamWin", title="Ajusta Cámara - Playblast", widthHeight=(300, 100))
    cmds.columnLayout(adjustableColumn=True)
    cmds.text(label="Ajusta la cámara y pulsa OK para hacer Playblast")
    cmds.button(label="OK", command=lambda *args: finalize_playblast())
    cmds.button(label="Cancelar", command=lambda *args: cmds.deleteUI("pbAdjustCamWin"))
    cmds.showWindow("pbAdjustCamWin")

def finalize_playblast():
    cmds.deleteUI("pbAdjustCamWin", window=True)

    scene_name = get_scene_name()
    if not scene_name:
        return

    output_dir = r"C:\Perforce\TFG\_P\02_Animation_and_Rig\_Animations\_Playblast\Raw"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    output_path = os.path.join(output_dir, f"{scene_name}.mov")

    # 🔁 BORRAR el archivo si existe para poder sobrescribir
    if os.path.exists(output_path):
        try:
            os.remove(output_path)
            print("Archivo anterior eliminado para sobreescritura.")
        except Exception as e:
            cmds.warning(f"No se pudo eliminar el archivo anterior: {e}")
            return

    # Hacer playblast
    cmds.playblast(
        format="qt",
        filename=output_path,
        sequenceTime=False,
        clearCache=True,
        viewer=True,
        showOrnaments=False,
        framePadding=4,
        percent=100,
        compression="H.264",
        quality=100
    )
    print(f"✅ Playblast guardado: {output_path}")

def quick_playblast_with_preset():
    apply_camera_preset_mel()
    make_playblast_window()

quick_playblast_with_preset()
