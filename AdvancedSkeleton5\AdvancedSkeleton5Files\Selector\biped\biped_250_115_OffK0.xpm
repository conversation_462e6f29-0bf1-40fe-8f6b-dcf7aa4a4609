/* XPM */
static char *biped_250_115_OffK0[] = {
/* columns rows colors chars-per-pixel */
"10 15 50 1",
"  c gray75",
". c #C8C8C9",
"X c #D2D3D3",
"o c #DBDBDC",
"O c gainsboro",
"+ c #E2E1E3",
"@ c #E6E6E7",
"# c #E8E7E8",
"$ c #EDEDEE",
"% c #F0F1F1",
"& c #F0F1F2",
"* c #F6F5F4",
"= c #F7F9F7",
"- c #F7F8F8",
"; c #FAFBF9",
": c #FBFBFB",
"> c #FCFCFB",
", c #FDFDFB",
"< c gray99",
"1 c #FCFDFD",
"2 c #FDFDFC",
"3 c #FDFDFD",
"4 c #FDFDFE",
"5 c #FDFEFD",
"6 c #FCFEFF",
"7 c #FCFFFF",
"8 c #FDFEFE",
"9 c #FDFFFE",
"0 c #FDFFFF",
"q c #FEFDFD",
"w c #FFFDFD",
"e c #FEFCFE",
"r c #FEFCFF",
"t c #FEFDFE",
"y c #FEFDFF",
"u c #FFFDFE",
"i c #FFFDFF",
"p c #FEFEFC",
"a c #FEFEFD",
"s c #FEFFFD",
"d c #FFFEFD",
"f c #FFFFFD",
"g c #FEFEFE",
"h c #FEFEFF",
"j c #FEFFFE",
"k c #FEFFFF",
"l c #FFFEFE",
"z c #FFFEFF",
"x c #FFFFFE",
"c c gray100",
/* pixels */
"          ",
" aaaaaa:o ",
" aaaaaaa@ ",
" aaaaa:a& ",
" aaaaa:a- ",
" aaaaaaa: ",
" ;aaaaaa: ",
" *aaaaaaa ",
" $aaaaaaa ",
" #::aaaaa ",
" +aa6aaaa ",
" o:aaa6aa ",
" X;aaaa6a ",
" .&aaaaaa ",
"          "
};
