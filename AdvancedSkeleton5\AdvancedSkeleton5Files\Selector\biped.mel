//This file has been generated by AdvancedSkeletonSelector//
//Using AdvancedSkeleton Version: 4.0118//

global proc asSelectorbiped ()
{
if (`window -q -ex asSelectorbiped`)
	{
	showWindow asSelectorbiped;
	return;
	}

string $asSelectorScriptLocation=`asSelectorScriptLocation`;
string $asIconLocation=$asSelectorScriptLocation+"biped/";
string $ext=".xpm";
if (`asMayaVersionAsFloat`>=2011)
	$ext=".png";
window -rtf 1 -mb 1 -t biped asSelectorbiped;
menu -l File;
	menuItem -l "Create Reference.." -c "asReferenceBrowser 0";
	menuItem -l "Reference Editor" -c "ReferenceEditor";
	menuItem -d 1;
	menuItem -l "Export Fbx.." -c "asExportFbxBrowser asSelectorbiped";
	setParent..;
menu -l Edit;
	menuItem -l Refresh -c "asPopulateNameSpaceMenu asSelectorbiped";
	menuItem -l Filter -c "asFilterNameSpaceMenuUI asSelectorbiped";
	setParent..;
menu -l Display;
	menuItem -l "Controls" -c "asControlsVisibilityToggle";
	menuItem -l "Set HotKey" -c "asSetupControlVisibilityHotKeyDialog";
//	menuItem -d 1;
//	menuItem -l "Joints" -c "asJointsVisibilityToggle asSelectorbiped";
	menuItem -d 1;
	menuItem -l "GimbalLock" -c "asVisualizeGimbalLock asSelectorbiped";
//	menuItem -d 1;
//	menuItem -l "FaceCtrls detach" -c "asFaceCtrlsDetach asSelectorbiped";
	setParent..;
menu -l Pose -aob 1;
	menuItem -l Copy -c "asCopyToClipBoard asSelectorbiped 0";
	menuItem -l Paste -en 0 asSelectorbipedPosePaste;
	menuItem -d 1;
	menuItem -l Reset -c "asGoToBuildPose asSelectorbiped";
	menuItem -l Mirror -c "asMirror asSelectorbiped";
	menuItem -optionBox 1 -c "asMirrorOptions asSelectorbiped";
	setParent..;
menu -l Anim;
	menuItem -l Copy -c "asCopyToClipBoard asSelectorbiped 1";
	menuItem -l Paste -en 0 asSelectorbipedAnimPaste;
	menuItem -d 1;
	menuItem -l Clean -c "asDeleteStaticChannels asSelectorbiped";
	menuItem -d 1;
	menuItem -l Bake -c "asAnimBake asSelectorbiped";
	menuItem -d 1;
	menuItem -l SwitchFKIK -c asAutoSwitchFKIK;
	menuItem -l SwitchPivot -c asAutoSwitchPivot;
	menuItem -d 1;
	menuItem -l QuickIK -c asQuickIK;
	menuItem -d 1;
	menuItem -l TwistFlip -c "asTwistFlipUI asSelectorbiped";
	menuItem -d 1;
	menuItem -l MoCapMatcher -c "asMoCapMatcherUI asSelectorbiped";
	menuItem -l MoCapLibrary -c "asMoCapLibraryUI asSelectorbiped";
	menuItem -d 1;
	menuItem -l "Connect ARKit" -c "asConnectARKitUI asSelectorbiped";
	menuItem -d 1;
	menuItem -l "Auto lipsync" -c "asAutoLipSyncUI asSelectorbiped";
menu -l Dynamics;
	menuItem -l "Add to selected" -c "asDynAdd asSelectorbiped";
	menuItem -l "Remove from selected" -c "asDynRemove asSelectorbiped";
	menuItem -d 1;
	menuItem -l "Set Initial State" -c "evalEcho saveInitialState -all";
	menuItem -l "Interactive Playback" -c "evalEcho InteractivePlayback";
	menuItem -d 1;
	menuItem -l Bake -c "asDynBake asSelectorbiped";
menu -l Parent;
	menuItem -l "Add parent constraint" -c "asParentAdd asSelectorbiped 0";
	menuItem -l "Add parent constraint (Extra)" -c "asParentAdd asSelectorbiped 1";

columnLayout -adj 1;
rowLayout -nc 5 -adj 1 -cat 1 right 0 -cw 2 30 -cw 3 65 -cw 4 50 -cw 5 60;
optionMenu -cc asSelChange asSelectorbipedOptionMenu;
button -l set -c "asSetNameSpaceFromSelection asSelectorbiped";
checkBox -v `optionVar -q asShowSelection` -onc "optionVar -iv asShowSelection 1;asSelChangeToggle;" -ofc "optionVar -iv asShowSelection 0;asSelChangeToggle;" -l Selection asSelectorbipedSelectionCheckBox;
checkBox -v `optionVar -q asShowKeyed` -onc "optionVar -iv asShowKeyed 1;asSelChangeToggle;" -ofc "asSelChange;optionVar -iv asShowKeyed 0;asSelChangeToggle;" -l Keyed asSelectorbipedKeyedCheckBox;
checkBox -v `optionVar -q asShowExtra` -onc "optionVar -iv asShowExtra 1;asSelChangeToggle;" -ofc "asSelChange;optionVar -iv asShowExtra 0;asSelChangeToggle;" -l Extra asSelectorbipedExtraCheckBox;
setParent..;
formLayout asSelectorbipedFormLayout;

//Controls Begin//
if (`asMayaVersionAsFloat`>=2011 && `about -win`)
	image -en 0 -w 320 -h 240 -i ($asIconLocation+"biped_background"+$ext) "asSelectorbipedBGPicture";
else
	if (!`about -linux`)
		iconTextButton -en 0 -w 320 -h 240 -i ($asIconLocation+"biped_background.xpm") -di ($asIconLocation+"biped_background.xpm") "asSelectorbipedBGPicture";
iconTextButton -w 21 -h 21 -i ($asIconLocation+"biped_49_43_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKWrist_R\"};" -ann "FKWrist_R;" "asSelectorbiped:49:43";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKWrist_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKWrist_R\"};";
iconTextButton -w 21 -h 21 -i ($asIconLocation+"biped_250_43_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKWrist_L\"};" -ann "FKWrist_L;" "asSelectorbiped:250:43";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKWrist_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKWrist_L\"};";
iconTextButton -w 8 -h 17 -i ($asIconLocation+"biped_156_38_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKNeck_M\"};" -ann "FKNeck_M;" "asSelectorbiped:156:38";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKNeck_M\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKNeck_M\"};";
iconTextButton -w 13 -h 13 -i ($asIconLocation+"biped_73_221_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"RollToes_R\"};" -ann "RollToes_R;" "asSelectorbiped:73:221";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"RollToes_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"RollToes_R\"};";
iconTextButton -w 13 -h 13 -i ($asIconLocation+"biped_234_221_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"RollToes_L\"};" -ann "RollToes_L;" "asSelectorbiped:234:221";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"RollToes_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"RollToes_L\"};";
iconTextButton -w 13 -h 13 -i ($asIconLocation+"biped_55_221_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"RollToesEnd_R\"};" -ann "RollToesEnd_R;" "asSelectorbiped:55:221";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"RollToesEnd_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"RollToesEnd_R\"};";
iconTextButton -w 13 -h 13 -i ($asIconLocation+"biped_252_221_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"RollToesEnd_L\"};" -ann "RollToesEnd_L;" "asSelectorbiped:252:221";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"RollToesEnd_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"RollToesEnd_L\"};";
iconTextButton -w 24 -h 18 -i ($asIconLocation+"biped_288_10_OffK0"+$ext) -iol "All" -c "asSelect \"biped\" {\"ControlSet\"};" -ann "ControlSet" "asSelectorbiped:288:10";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"ControlSet\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"ControlSet\"};";
iconTextButton -w 50 -h 16 -i ($asIconLocation+"biped_26_180_OffK0"+$ext) -iol "fingers" -c "asSelect \"biped\" {\"Fingers_R\"};" -ann "Fingers_R" "asSelectorbiped:26:180";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"Fingers_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"Fingers_R\"};";
iconTextButton -w 50 -h 16 -i ($asIconLocation+"biped_244_180_OffK0"+$ext) -iol "fingers" -c "asSelect \"biped\" {\"Fingers_L\"};" -ann "Fingers_L" "asSelectorbiped:244:180";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"Fingers_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"Fingers_L\"};";
iconTextButton -w 15 -h 15 -i ($asIconLocation+"biped_209_92_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKIKSpine_M\"};" -ann "FKIKSpine_M;" "asSelectorbiped:209:92";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKIKSpine_M\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKIKSpine_M\"};";

	menuItem -d 1;
	menuItem -l Align -sm 1;
		menuItem -l FK2IK -c "asAlignFK2IK \"biped\" {\"FKIKSpine_M\"}";
		menuItem -l IK2FK -c "asAlignIK2FK \"biped\" {\"FKIKSpine_M\"}";
		setParent -menu ..;
	menuItem -l Switch -sm 1;
		menuItem -l FK2IK -c "asSwitchFK2IK \"biped\" {\"FKIKSpine_M\"}";
		menuItem -l IK2FK -c "asSwitchIK2FK \"biped\" {\"FKIKSpine_M\"}";

iconTextButton -w 30 -h 30 -i ($asIconLocation+"biped_104_96_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"HipSwinger_M\"};" -ann "HipSwinger_M;" "asSelectorbiped:104:96";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"HipSwinger_M\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"HipSwinger_M\"};";
iconTextButton -w 34 -h 18 -i ($asIconLocation+"biped_103_73_OffK0"+$ext) -iol "spine" -c "asSelect \"biped\" {\"FKChest_M\",\"FKSpine1_M\",\"FKRoot_M\"};" -ann "spine" "asSelectorbiped:103:73";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKChest_M\",\"FKSpine1_M\",\"FKRoot_M\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKChest_M\",\"FKSpine1_M\",\"FKRoot_M\"};";
iconTextButton -w 23 -h 23 -i ($asIconLocation+"biped_47_8_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"IKArm_R\"};" -ann "IKArm_R;" "asSelectorbiped:47:8";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"IKArm_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"IKArm_R\"};";
iconTextButton -w 23 -h 23 -i ($asIconLocation+"biped_250_8_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"IKArm_L\"};" -ann "IKArm_L;" "asSelectorbiped:250:8";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"IKArm_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"IKArm_L\"};";
iconTextButton -w 38 -h 46 -i ($asIconLocation+"biped_30_131_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKWrist_R\"};" -ann "FKWrist_R;" "asSelectorbiped:30:131";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKWrist_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKWrist_R\"};";
iconTextButton -w 38 -h 46 -i ($asIconLocation+"biped_252_131_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKWrist_L\"};" -ann "FKWrist_L;" "asSelectorbiped:252:131";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKWrist_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKWrist_L\"};";
iconTextButton -w 11 -h 20 -i ($asIconLocation+"biped_69_146_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKThumbFinger1_R\"};" -ann "FKThumbFinger1_R;" "asSelectorbiped:69:146";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKThumbFinger1_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKThumbFinger1_R\"};";
iconTextButton -w 11 -h 20 -i ($asIconLocation+"biped_240_146_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKThumbFinger1_L\"};" -ann "FKThumbFinger1_L;" "asSelectorbiped:240:146";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKThumbFinger1_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKThumbFinger1_L\"};";
iconTextButton -w 13 -h 8 -i ($asIconLocation+"biped_70_137_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKThumbFinger2_R\"};" -ann "FKThumbFinger2_R;" "asSelectorbiped:70:137";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKThumbFinger2_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKThumbFinger2_R\"};";
iconTextButton -w 13 -h 8 -i ($asIconLocation+"biped_237_137_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKThumbFinger2_L\"};" -ann "FKThumbFinger2_L;" "asSelectorbiped:237:137";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKThumbFinger2_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKThumbFinger2_L\"};";
iconTextButton -w 17 -h 12 -i ($asIconLocation+"biped_73_124_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKThumbFinger3_R\"};" -ann "FKThumbFinger3_R;" "asSelectorbiped:73:124";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKThumbFinger3_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKThumbFinger3_R\"};";
iconTextButton -w 17 -h 12 -i ($asIconLocation+"biped_230_124_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKThumbFinger3_L\"};" -ann "FKThumbFinger3_L;" "asSelectorbiped:230:124";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKThumbFinger3_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKThumbFinger3_L\"};";
iconTextButton -w 10 -h 15 -i ($asIconLocation+"biped_60_115_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKIndexFinger1_R\"};" -ann "FKIndexFinger1_R;" "asSelectorbiped:60:115";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKIndexFinger1_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKIndexFinger1_R\"};";
iconTextButton -w 10 -h 15 -i ($asIconLocation+"biped_250_115_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKIndexFinger1_L\"};" -ann "FKIndexFinger1_L;" "asSelectorbiped:250:115";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKIndexFinger1_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKIndexFinger1_L\"};";
iconTextButton -w 10 -h 13 -i ($asIconLocation+"biped_64_99_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKIndexFinger2_R\"};" -ann "FKIndexFinger2_R;" "asSelectorbiped:64:99";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKIndexFinger2_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKIndexFinger2_R\"};";
iconTextButton -w 10 -h 13 -i ($asIconLocation+"biped_246_99_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKIndexFinger2_L\"};" -ann "FKIndexFinger2_L;" "asSelectorbiped:246:99";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKIndexFinger2_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKIndexFinger2_L\"};";
iconTextButton -w 10 -h 16 -i ($asIconLocation+"biped_49_114_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKMiddleFinger1_R\"};" -ann "FKMiddleFinger1_R;" "asSelectorbiped:49:114";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKMiddleFinger1_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKMiddleFinger1_R\"};";
iconTextButton -w 10 -h 16 -i ($asIconLocation+"biped_261_114_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKMiddleFinger1_L\"};" -ann "FKMiddleFinger1_L;" "asSelectorbiped:261:114";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKMiddleFinger1_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKMiddleFinger1_L\"};";
iconTextButton -w 11 -h 13 -i ($asIconLocation+"biped_48_98_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKMiddleFinger2_R\"};" -ann "FKMiddleFinger2_R;" "asSelectorbiped:48:98";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKMiddleFinger2_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKMiddleFinger2_R\"};";
iconTextButton -w 11 -h 13 -i ($asIconLocation+"biped_261_98_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKMiddleFinger2_L\"};" -ann "FKMiddleFinger2_L;" "asSelectorbiped:261:98";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKMiddleFinger2_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKMiddleFinger2_L\"};";
iconTextButton -w 11 -h 15 -i ($asIconLocation+"biped_48_80_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKMiddleFinger3_R\"};" -ann "FKMiddleFinger3_R;" "asSelectorbiped:48:80";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKMiddleFinger3_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKMiddleFinger3_R\"};";
iconTextButton -w 11 -h 15 -i ($asIconLocation+"biped_261_80_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKMiddleFinger3_L\"};" -ann "FKMiddleFinger3_L;" "asSelectorbiped:261:80";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKMiddleFinger3_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKMiddleFinger3_L\"};";
iconTextButton -w 10 -h 15 -i ($asIconLocation+"biped_37_115_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKRingFinger1_R\"};" -ann "FKRingFinger1_R;" "asSelectorbiped:37:115";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKRingFinger1_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKRingFinger1_R\"};";
iconTextButton -w 10 -h 15 -i ($asIconLocation+"biped_273_115_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKRingFinger1_L\"};" -ann "FKRingFinger1_L;" "asSelectorbiped:273:115";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKRingFinger1_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKRingFinger1_L\"};";
iconTextButton -w 10 -h 13 -i ($asIconLocation+"biped_34_99_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKRingFinger2_R\"};" -ann "FKRingFinger2_R;" "asSelectorbiped:34:99";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKRingFinger2_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKRingFinger2_R\"};";
iconTextButton -w 10 -h 13 -i ($asIconLocation+"biped_276_99_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKRingFinger2_L\"};" -ann "FKRingFinger2_L;" "asSelectorbiped:276:99";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKRingFinger2_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKRingFinger2_L\"};";
iconTextButton -w 10 -h 13 -i ($asIconLocation+"biped_32_84_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKRingFinger3_R\"};" -ann "FKRingFinger3_R;" "asSelectorbiped:32:84";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKRingFinger3_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKRingFinger3_R\"};";
iconTextButton -w 10 -h 13 -i ($asIconLocation+"biped_278_84_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKRingFinger3_L\"};" -ann "FKRingFinger3_L;" "asSelectorbiped:278:84";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKRingFinger3_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKRingFinger3_L\"};";
iconTextButton -w 10 -h 10 -i ($asIconLocation+"biped_26_120_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKPinkyFinger1_R\"};" -ann "FKPinkyFinger1_R;" "asSelectorbiped:26:120";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKPinkyFinger1_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKPinkyFinger1_R\"};";
iconTextButton -w 10 -h 10 -i ($asIconLocation+"biped_284_120_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKPinkyFinger1_L\"};" -ann "FKPinkyFinger1_L;" "asSelectorbiped:284:120";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKPinkyFinger1_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKPinkyFinger1_L\"};";
iconTextButton -w 10 -h 10 -i ($asIconLocation+"biped_22_109_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKPinkyFinger2_R\"};" -ann "FKPinkyFinger2_R;" "asSelectorbiped:22:109";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKPinkyFinger2_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKPinkyFinger2_R\"};";
iconTextButton -w 10 -h 10 -i ($asIconLocation+"biped_288_109_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKPinkyFinger2_L\"};" -ann "FKPinkyFinger2_L;" "asSelectorbiped:288:109";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKPinkyFinger2_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKPinkyFinger2_L\"};";
iconTextButton -w 10 -h 10 -i ($asIconLocation+"biped_18_98_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKPinkyFinger3_R\"};" -ann "FKPinkyFinger3_R;" "asSelectorbiped:18:98";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKPinkyFinger3_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKPinkyFinger3_R\"};";
iconTextButton -w 10 -h 10 -i ($asIconLocation+"biped_292_98_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKPinkyFinger3_L\"};" -ann "FKPinkyFinger3_L;" "asSelectorbiped:292:98";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKPinkyFinger3_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKPinkyFinger3_L\"};";
iconTextButton -w 10 -h 13 -i ($asIconLocation+"biped_67_85_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKIndexFinger3_R\"};" -ann "FKIndexFinger3_R;" "asSelectorbiped:67:85";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKIndexFinger3_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKIndexFinger3_R\"};";
iconTextButton -w 10 -h 13 -i ($asIconLocation+"biped_243_85_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKIndexFinger3_L\"};" -ann "FKIndexFinger3_L;" "asSelectorbiped:243:85";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKIndexFinger3_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKIndexFinger3_L\"};";
iconTextButton -w 14 -h 13 -i ($asIconLocation+"biped_141_42_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKScapula_R\"};" -ann "FKScapula_R;" "asSelectorbiped:141:42";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKScapula_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKScapula_R\"};";
iconTextButton -w 14 -h 13 -i ($asIconLocation+"biped_165_42_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKScapula_L\"};" -ann "FKScapula_L;" "asSelectorbiped:165:42";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKScapula_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKScapula_L\"};";
iconTextButton -w 34 -h 20 -i ($asIconLocation+"biped_105_46_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKShoulder_R\"};" -ann "FKShoulder_R;" "asSelectorbiped:105:46";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKShoulder_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKShoulder_R\"};";
iconTextButton -w 34 -h 20 -i ($asIconLocation+"biped_181_46_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKShoulder_L\"};" -ann "FKShoulder_L;" "asSelectorbiped:181:46";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKShoulder_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKShoulder_L\"};";
iconTextButton -w 32 -h 15 -i ($asIconLocation+"biped_71_47_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKElbow_R\"};" -ann "FKElbow_R;" "asSelectorbiped:71:47";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKElbow_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKElbow_R\"};";
iconTextButton -w 32 -h 15 -i ($asIconLocation+"biped_217_47_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKElbow_L\"};" -ann "FKElbow_L;" "asSelectorbiped:217:47";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKElbow_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKElbow_L\"};";
iconTextButton -w 42 -h 32 -i ($asIconLocation+"biped_139_3_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKHead_M\"};" -ann "FKHead_M;" "asSelectorbiped:139:3";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKHead_M\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKHead_M\"};";
iconTextButton -w 15 -h 15 -i ($asIconLocation+"biped_117_13_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKIKArm_R\"};" -ann "FKIKArm_R;" "asSelectorbiped:117:13";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKIKArm_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKIKArm_R\"};";
	menuItem -d 1;
	menuItem -l Align -sm 1;
		menuItem -l FK2IK -c "asAlignFK2IK \"biped\" {\"FKIKArm_R\"}";
		menuItem -l IK2FK -c "asAlignIK2FK \"biped\" {\"FKIKArm_R\"}";
		setParent -menu ..;
	menuItem -l Switch -sm 1;
		menuItem -l FK2IK -c "asSwitchFK2IK \"biped\" {\"FKIKArm_R\"}";
		menuItem -l IK2FK -c "asSwitchIK2FK \"biped\" {\"FKIKArm_R\"}";
iconTextButton -w 15 -h 15 -i ($asIconLocation+"biped_188_13_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKIKArm_L\"};" -ann "FKIKArm_L;" "asSelectorbiped:188:13";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKIKArm_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKIKArm_L\"};";
	menuItem -d 1;
	menuItem -l Align -sm 1;
		menuItem -l FK2IK -c "asAlignFK2IK \"biped\" {\"FKIKArm_L\"}";
		menuItem -l IK2FK -c "asAlignIK2FK \"biped\" {\"FKIKArm_L\"}";
		setParent -menu ..;
	menuItem -l Switch -sm 1;
		menuItem -l FK2IK -c "asSwitchFK2IK \"biped\" {\"FKIKArm_L\"}";
		menuItem -l IK2FK -c "asSwitchIK2FK \"biped\" {\"FKIKArm_L\"}";
iconTextButton -w 21 -h 22 -i ($asIconLocation+"biped_85_9_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"PoleArm_R\"};" -ann "PoleArm_R;" "asSelectorbiped:85:9";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"PoleArm_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"PoleArm_R\"};";
iconTextButton -w 21 -h 22 -i ($asIconLocation+"biped_214_9_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"PoleArm_L\"};" -ann "PoleArm_L;" "asSelectorbiped:214:9";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"PoleArm_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"PoleArm_L\"};";
iconTextButton -w 20 -h 41 -i ($asIconLocation+"biped_138_141_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKHip_R\"};" -ann "FKHip_R;" "asSelectorbiped:138:141";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKHip_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKHip_R\"};";
iconTextButton -w 20 -h 41 -i ($asIconLocation+"biped_162_141_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKHip_L\"};" -ann "FKHip_L;" "asSelectorbiped:162:141";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKHip_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKHip_L\"};";
iconTextButton -w 18 -h 31 -i ($asIconLocation+"biped_140_184_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKKnee_R\"};" -ann "FKKnee_R;" "asSelectorbiped:140:184";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKKnee_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKKnee_R\"};";
iconTextButton -w 18 -h 31 -i ($asIconLocation+"biped_162_184_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKKnee_L\"};" -ann "FKKnee_L;" "asSelectorbiped:162:184";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKKnee_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKKnee_L\"};";
iconTextButton -w 19 -h 19 -i ($asIconLocation+"biped_139_219_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKAnkle_R\"};" -ann "FKAnkle_R;" "asSelectorbiped:139:219";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKAnkle_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKAnkle_R\"};";
iconTextButton -w 19 -h 19 -i ($asIconLocation+"biped_162_219_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKAnkle_L\"};" -ann "FKAnkle_L;" "asSelectorbiped:162:219";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKAnkle_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKAnkle_L\"};";
iconTextButton -w 15 -h 13 -i ($asIconLocation+"biped_123_225_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKToes_R\"};" -ann "FKToes_R;" "asSelectorbiped:123:225";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKToes_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKToes_R\"};";
iconTextButton -w 15 -h 13 -i ($asIconLocation+"biped_182_225_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKToes_L\"};" -ann "FKToes_L;" "asSelectorbiped:182:225";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKToes_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKToes_L\"};";
iconTextButton -w 13 -h 13 -i ($asIconLocation+"biped_89_221_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"RollHeel_R\"};" -ann "RollHeel_R;" "asSelectorbiped:89:221";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"RollHeel_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"RollHeel_R\"};";
iconTextButton -w 13 -h 13 -i ($asIconLocation+"biped_218_221_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"RollHeel_L\"};" -ann "RollHeel_L;" "asSelectorbiped:218:221";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"RollHeel_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"RollHeel_L\"};";
iconTextButton -w 23 -h 23 -i ($asIconLocation+"biped_103_195_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"IKLeg_R\"};" -ann "IKLeg_R;" "asSelectorbiped:103:195";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"IKLeg_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"IKLeg_R\"};";
iconTextButton -w 23 -h 23 -i ($asIconLocation+"biped_194_195_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"IKLeg_L\"};" -ann "IKLeg_L;" "asSelectorbiped:194:195";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"IKLeg_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"IKLeg_L\"};";
iconTextButton -w 21 -h 22 -i ($asIconLocation+"biped_104_168_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"PoleLeg_R\"};" -ann "PoleLeg_R;" "asSelectorbiped:104:168";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"PoleLeg_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"PoleLeg_R\"};";
iconTextButton -w 21 -h 22 -i ($asIconLocation+"biped_195_168_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"PoleLeg_L\"};" -ann "PoleLeg_L;" "asSelectorbiped:195:168";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"PoleLeg_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"PoleLeg_L\"};";
iconTextButton -w 15 -h 15 -i ($asIconLocation+"biped_107_146_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKIKLeg_R\"};" -ann "FKIKLeg_R;" "asSelectorbiped:107:146";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKIKLeg_R\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKIKLeg_R\"};";
	menuItem -d 1;
	menuItem -l Align -sm 1;
		menuItem -l FK2IK -c "asAlignFK2IK \"biped\" {\"FKIKLeg_R\"}";
		menuItem -l IK2FK -c "asAlignIK2FK \"biped\" {\"FKIKLeg_R\"}";
		setParent -menu ..;
	menuItem -l Switch -sm 1;
		menuItem -l FK2IK -c "asSwitchFK2IK \"biped\" {\"FKIKLeg_R\"}";
		menuItem -l IK2FK -c "asSwitchIK2FK \"biped\" {\"FKIKLeg_R\"}";
iconTextButton -w 15 -h 15 -i ($asIconLocation+"biped_198_146_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKIKLeg_L\"};" -ann "FKIKLeg_L;" "asSelectorbiped:198:146";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKIKLeg_L\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKIKLeg_L\"};";
	menuItem -d 1;
	menuItem -l Align -sm 1;
		menuItem -l FK2IK -c "asAlignFK2IK \"biped\" {\"FKIKLeg_L\"}";
		menuItem -l IK2FK -c "asAlignIK2FK \"biped\" {\"FKIKLeg_L\"}";
		setParent -menu ..;
	menuItem -l Switch -sm 1;
		menuItem -l FK2IK -c "asSwitchFK2IK \"biped\" {\"FKIKLeg_L\"}";
		menuItem -l IK2FK -c "asSwitchIK2FK \"biped\" {\"FKIKLeg_L\"}";
iconTextButton -w 38 -h 18 -i ($asIconLocation+"biped_141_102_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKRoot_M\"};" -ann "FKRoot_M;" "asSelectorbiped:141:102";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKRoot_M\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKRoot_M\"};";
iconTextButton -w 38 -h 18 -i ($asIconLocation+"biped_141_80_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKSpine1_M\"};" -ann "FKSpine1_M;" "asSelectorbiped:141:80";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKSpine1_M\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKSpine1_M\"};";
iconTextButton -w 38 -h 18 -i ($asIconLocation+"biped_141_58_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"FKChest_M\"};" -ann "FKChest_M;" "asSelectorbiped:141:58";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"FKChest_M\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"FKChest_M\"};";
iconTextButton -w 17 -h 17 -i ($asIconLocation+"biped_189_111_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"IKSpine1_M\"};" -ann "IKSpine1_M;" "asSelectorbiped:189:111";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"IKSpine1_M\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"IKSpine1_M\"};";
iconTextButton -w 17 -h 17 -i ($asIconLocation+"biped_189_91_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"IKSpine2_M\"};" -ann "IKSpine2_M;" "asSelectorbiped:189:91";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"IKSpine2_M\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"IKSpine2_M\"};";
iconTextButton -w 17 -h 17 -i ($asIconLocation+"biped_189_70_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"IKSpine3_M\"};" -ann "IKSpine3_M;" "asSelectorbiped:189:70";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"IKSpine3_M\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"IKSpine3_M\"};";
iconTextButton -w 33 -h 18 -i ($asIconLocation+"biped_7_10_OffK0"+$ext) -iol "Main" -c "asSelect \"biped\" {\"Main\"};" -ann "Main;" "asSelectorbiped:7:10";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"Main\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"Main\"};";
iconTextButton -w 48 -h 18 -i ($asIconLocation+"biped_136_122_OffK0"+$ext) -iol "" -c "asSelect \"biped\" {\"RootX_M\"};" -ann "RootX_M;" "asSelectorbiped:136:122";
popupMenu;
	menuItem -l Key -c "asKey \"biped\" {\"RootX_M\"};";
	menuItem -l LinearKey -c "asLinearKey \"biped\" {\"RootX_M\"};";
//Controls End//

formLayout -e
	-af "asSelectorbiped:49:43" left 49
	-af "asSelectorbiped:49:43" top 43
	-af "asSelectorbiped:250:43" left 250
	-af "asSelectorbiped:250:43" top 43
	-af "asSelectorbiped:156:38" left 156
	-af "asSelectorbiped:156:38" top 38
	-af "asSelectorbiped:73:221" left 73
	-af "asSelectorbiped:73:221" top 221
	-af "asSelectorbiped:234:221" left 234
	-af "asSelectorbiped:234:221" top 221
	-af "asSelectorbiped:55:221" left 55
	-af "asSelectorbiped:55:221" top 221
	-af "asSelectorbiped:252:221" left 252
	-af "asSelectorbiped:252:221" top 221
	-af "asSelectorbiped:288:10" left 288
	-af "asSelectorbiped:288:10" top 10
	-af "asSelectorbiped:26:180" left 26
	-af "asSelectorbiped:26:180" top 180
	-af "asSelectorbiped:244:180" left 244
	-af "asSelectorbiped:244:180" top 180
	-af "asSelectorbiped:209:92" left 209
	-af "asSelectorbiped:209:92" top 92
	-af "asSelectorbiped:104:96" left 104
	-af "asSelectorbiped:104:96" top 96
	-af "asSelectorbiped:103:73" left 103
	-af "asSelectorbiped:103:73" top 73
	-af "asSelectorbiped:47:8" left 47
	-af "asSelectorbiped:47:8" top 8
	-af "asSelectorbiped:250:8" left 250
	-af "asSelectorbiped:250:8" top 8
	-af "asSelectorbiped:30:131" left 30
	-af "asSelectorbiped:30:131" top 131
	-af "asSelectorbiped:252:131" left 252
	-af "asSelectorbiped:252:131" top 131
	-af "asSelectorbiped:69:146" left 69
	-af "asSelectorbiped:69:146" top 146
	-af "asSelectorbiped:240:146" left 240
	-af "asSelectorbiped:240:146" top 146
	-af "asSelectorbiped:70:137" left 70
	-af "asSelectorbiped:70:137" top 137
	-af "asSelectorbiped:237:137" left 237
	-af "asSelectorbiped:237:137" top 137
	-af "asSelectorbiped:73:124" left 73
	-af "asSelectorbiped:73:124" top 124
	-af "asSelectorbiped:230:124" left 230
	-af "asSelectorbiped:230:124" top 124
	-af "asSelectorbiped:60:115" left 60
	-af "asSelectorbiped:60:115" top 115
	-af "asSelectorbiped:250:115" left 250
	-af "asSelectorbiped:250:115" top 115
	-af "asSelectorbiped:64:99" left 64
	-af "asSelectorbiped:64:99" top 99
	-af "asSelectorbiped:246:99" left 246
	-af "asSelectorbiped:246:99" top 99
	-af "asSelectorbiped:49:114" left 49
	-af "asSelectorbiped:49:114" top 114
	-af "asSelectorbiped:261:114" left 261
	-af "asSelectorbiped:261:114" top 114
	-af "asSelectorbiped:48:98" left 48
	-af "asSelectorbiped:48:98" top 98
	-af "asSelectorbiped:261:98" left 261
	-af "asSelectorbiped:261:98" top 98
	-af "asSelectorbiped:48:80" left 48
	-af "asSelectorbiped:48:80" top 80
	-af "asSelectorbiped:261:80" left 261
	-af "asSelectorbiped:261:80" top 80
	-af "asSelectorbiped:37:115" left 37
	-af "asSelectorbiped:37:115" top 115
	-af "asSelectorbiped:273:115" left 273
	-af "asSelectorbiped:273:115" top 115
	-af "asSelectorbiped:34:99" left 34
	-af "asSelectorbiped:34:99" top 99
	-af "asSelectorbiped:276:99" left 276
	-af "asSelectorbiped:276:99" top 99
	-af "asSelectorbiped:32:84" left 32
	-af "asSelectorbiped:32:84" top 84
	-af "asSelectorbiped:278:84" left 278
	-af "asSelectorbiped:278:84" top 84
	-af "asSelectorbiped:26:120" left 26
	-af "asSelectorbiped:26:120" top 120
	-af "asSelectorbiped:284:120" left 284
	-af "asSelectorbiped:284:120" top 120
	-af "asSelectorbiped:22:109" left 22
	-af "asSelectorbiped:22:109" top 109
	-af "asSelectorbiped:288:109" left 288
	-af "asSelectorbiped:288:109" top 109
	-af "asSelectorbiped:18:98" left 18
	-af "asSelectorbiped:18:98" top 98
	-af "asSelectorbiped:292:98" left 292
	-af "asSelectorbiped:292:98" top 98
	-af "asSelectorbiped:67:85" left 67
	-af "asSelectorbiped:67:85" top 85
	-af "asSelectorbiped:243:85" left 243
	-af "asSelectorbiped:243:85" top 85
	-af "asSelectorbiped:141:42" left 141
	-af "asSelectorbiped:141:42" top 42
	-af "asSelectorbiped:165:42" left 165
	-af "asSelectorbiped:165:42" top 42
	-af "asSelectorbiped:105:46" left 105
	-af "asSelectorbiped:105:46" top 46
	-af "asSelectorbiped:181:46" left 181
	-af "asSelectorbiped:181:46" top 46
	-af "asSelectorbiped:71:47" left 71
	-af "asSelectorbiped:71:47" top 47
	-af "asSelectorbiped:217:47" left 217
	-af "asSelectorbiped:217:47" top 47
	-af "asSelectorbiped:139:3" left 139
	-af "asSelectorbiped:139:3" top 3
	-af "asSelectorbiped:117:13" left 117
	-af "asSelectorbiped:117:13" top 13
	-af "asSelectorbiped:188:13" left 188
	-af "asSelectorbiped:188:13" top 13
	-af "asSelectorbiped:85:9" left 85
	-af "asSelectorbiped:85:9" top 9
	-af "asSelectorbiped:214:9" left 214
	-af "asSelectorbiped:214:9" top 9
	-af "asSelectorbiped:138:141" left 138
	-af "asSelectorbiped:138:141" top 141
	-af "asSelectorbiped:162:141" left 162
	-af "asSelectorbiped:162:141" top 141
	-af "asSelectorbiped:140:184" left 140
	-af "asSelectorbiped:140:184" top 184
	-af "asSelectorbiped:162:184" left 162
	-af "asSelectorbiped:162:184" top 184
	-af "asSelectorbiped:139:219" left 139
	-af "asSelectorbiped:139:219" top 219
	-af "asSelectorbiped:162:219" left 162
	-af "asSelectorbiped:162:219" top 219
	-af "asSelectorbiped:123:225" left 123
	-af "asSelectorbiped:123:225" top 225
	-af "asSelectorbiped:182:225" left 182
	-af "asSelectorbiped:182:225" top 225
	-af "asSelectorbiped:89:221" left 89
	-af "asSelectorbiped:89:221" top 221
	-af "asSelectorbiped:218:221" left 218
	-af "asSelectorbiped:218:221" top 221
	-af "asSelectorbiped:103:195" left 103
	-af "asSelectorbiped:103:195" top 195
	-af "asSelectorbiped:194:195" left 194
	-af "asSelectorbiped:194:195" top 195
	-af "asSelectorbiped:104:168" left 104
	-af "asSelectorbiped:104:168" top 168
	-af "asSelectorbiped:195:168" left 195
	-af "asSelectorbiped:195:168" top 168
	-af "asSelectorbiped:107:146" left 107
	-af "asSelectorbiped:107:146" top 146
	-af "asSelectorbiped:198:146" left 198
	-af "asSelectorbiped:198:146" top 146
	-af "asSelectorbiped:141:102" left 141
	-af "asSelectorbiped:141:102" top 102
	-af "asSelectorbiped:141:80" left 141
	-af "asSelectorbiped:141:80" top 80
	-af "asSelectorbiped:141:58" left 141
	-af "asSelectorbiped:141:58" top 58
	-af "asSelectorbiped:189:111" left 189
	-af "asSelectorbiped:189:111" top 111
	-af "asSelectorbiped:189:91" left 189
	-af "asSelectorbiped:189:91" top 91
	-af "asSelectorbiped:189:70" left 189
	-af "asSelectorbiped:189:70" top 70
	-af "asSelectorbiped:7:10" left 7
	-af "asSelectorbiped:7:10" top 10
	-af "asSelectorbiped:136:122" left 136
	-af "asSelectorbiped:136:122" top 122
	asSelectorbipedFormLayout;

asPopulateNameSpaceMenu "asSelectorbiped";
asShowSelJob;
asSelChange;
setFocus asSelectorbipedFormLayout;
showWindow;
}
asSelectorbiped;

//-- ASTools Procedures Starts Here --//
global proc asSelChange ()
{
global int $asfileLoading;
global int $asSelChangeSwitching;
if ($asfileLoading)
	return;
if (!`optionVar -q asShowSelection` && !`optionVar -q asShowKeyed` && !$asSelChangeSwitching)
	return;
int $undoState=`undoInfo -q -state`;
string $sel[]=`ls -sl`;
string $name,$obj,$nodeType,$projectName,$ann;
string $ctls[],$buffer[],$connections[];
int $numLetters,$numTok,$keyed;
string $ext=".xpm";
if (`asMayaVersionAsFloat`>=2011)
	$ext=".png";
if (`about -linux`)
	$ext=".xpm";
string $currImage,$buttonImageFile,$buttonImageFileOnK0,$buttonImageFileOnK1,$buttonImageFileOffK0,$buttonImageFileOffK1;
string $windows[]=`lsUI -windows`;
string $layout;
for ($window in $windows)
	{
	$layout="";
	if (size($window)>11)
		$layout="asSelector"+`substring $window 11 999`+"FormLayout";
	if (!`formLayout -q -ex $layout`)
		continue;
	$numLetters=size($layout);
	$name=`substring $layout 11 ($numLetters-10)`;
	$ctls=`formLayout -q -ca $layout`;
	for ($ctl in $ctls)
		{
		if (!`iconTextButton -q -ex $ctl`)
			continue;
		if (`optionVar -q asShowSelection` && !$asSelChangeSwitching)
			$selState="On";
		else
			$selState="Off";
		$keyed=0;
		$ann=`iconTextButton -q -ann $ctl`;
		$numTok=`tokenize $ann ";" $buffer`;
		for ($i=0;$i<$numTok;$i++)
			{
			$obj=`asSelectorResolveNameSpace $name $buffer[$i]`;
			if (!`stringArrayCount $obj $sel`)
				$selState="Off";
			if (`optionVar -q asShowKeyed` && !$asSelChangeSwitching && `objExists $obj`)
				{
				$connections=`listConnections -s 1 -d 0 $obj`;
				for ($node in $connections)
					{
					$nodeType=`objectType $node`;
					if (`gmatch $nodeType "animCurve*"`)
						$keyed=1;
					}
				}
			}

		$currImage=`iconTextButton -q -i1 $ctl`;
		if ($currImage=="")
			return;
		$numTok=`tokenize $currImage "_" $buffer`;
		if ($numTok<3)
			continue;
		$projectName=$buffer[0];
		for ($b=1;$b<size($buffer)-3;$b++)
			$projectName+="_"+$buffer[$b];
		$buttonImageFile=$projectName+"_"+$buffer[$numTok-3]+"_"+$buffer[$numTok-2]+"_"+$selState+"K"+$keyed+$ext;

		if ($buttonImageFile!=$currImage)
			iconTextButton -e -i $buttonImageFile $ctl;		
		}
	}

//PickerSelChange
if (!`optionMenu -q -ex asPickerOptionMenu`)
	return;
int $selectedTabIndex,$overrideColor;
string $picCtrls[],$selPicCtrls[],$selChrCtrls[],$tabLabels[];
string $picNs,$chrNs,$picCtrl,$chrCtrl;

if (`optionMenu -q -ex asPickerOptionMenu`)
	$chrNs=`optionMenu -q -v asPickerOptionMenu`;
if ($chrNs==":")
	$chrNs="";
if (`tabLayout -q -ex asPickerTabLayout`)
	{
	$tabLabels=`tabLayout -q -tl asPickerTabLayout`;
	$selectedTabIndex=`tabLayout -q -selectTabIndex asPickerTabLayout`;
	$picNs="picker_"+$tabLabels[$selectedTabIndex-1]+":";
	}
$picCtrls=`ls -type transform ($picNs+"*")`;
$selPicCtrls=`ls -sl -type transform ($picNs+"*")`;
$selChrCtrls=`ls -sl -type transform ($chrNs+"*")`;

//All .overrideColor==0 off first
if ($undoState) undoInfo -stateWithoutFlush 0;

for ($i=0;$i<size($picCtrls);$i++)
	{
	$overrideColor=0;
	$chrCtrl=`substitute $picNs $picCtrls[$i] $chrNs`;
	if (`optionVar -q asShowExtra`) $chrCtrl=`substitute "FK" $chrCtrl "FKExtra"`;
	if (`objExists $chrCtrl`)
		if (`optionVar -q asShowKeyed` && `asHaveAnimation $chrCtrl`)
			$overrideColor=13;
	setAttr ($picCtrls[$i]+".overrideColor") $overrideColor;
	}

if (!size($selPicCtrls) && !size($selChrCtrls))
	{
	if ($undoState) undoInfo -stateWithoutFlush 1;
	return;
	}

for ($i=0;$i<size($sel);$i++)
	{
	if (`optionVar -q asShowSelection`)
		$overrideColor=14;
	if (`gmatch $sel[$i] ($picNs+"*")`) // $picNs > $chrNs
		{
		$chrCtrl=`substitute $picNs $sel[$i] $chrNs`;
		if (`optionVar -q asShowExtra`) $chrCtrl=`substitute "FK" $chrCtrl "FKExtra"`;
		if (`objExists $chrCtrl` && `optionVar -q asShowSelection`)
			{
			select -d  $sel[$i];
			select -add $chrCtrl;
			if (`optionVar -q asShowKeyed` && `asHaveAnimation $chrCtrl`)
				$overrideColor=13;
			setAttr ($sel[$i]+".overrideColor") $overrideColor;
			}
		else
			print ("// "+$chrCtrl+" not found.\n");
		}
	if (`gmatch $sel[$i] ($chrNs+"*")`) // $chrNs > $picNs
		{
		if ($chrNs=="")
			$picCtrl=$picNs+$sel[$i];
		else
			$picCtrl=`substitute $chrNs $sel[$i] $picNs`;
		if (`optionVar -q asShowExtra`)
			{
			if (!`gmatch $picCtrl "*Extra*"`) continue;
			$picCtrl=`substitute "FKExtra" $picCtrl "FK"`;
			}
		if (`objExists $picCtrl`)
			setAttr ($picCtrl+".overrideColor") $overrideColor;
		}
	}
if ($undoState) undoInfo -stateWithoutFlush 1;
}

global proc asFL ()
{
string $framLayouts[]=`lsUI -type frameLayout`;
for ($i=0;$i<size($framLayouts);$i++)
	if (`gmatch $framLayouts[$i] "as*FrameLayout"`)
		optionVar -iv $framLayouts[$i] `frameLayout -q -cl $framLayouts[$i]`;
}

global proc asLockAttr (string $object, int $trans, int $rot, int $scale, int $vis)
{
setAttr -l $trans -k (!$trans) ($object+".translateX");
setAttr -l $trans -k (!$trans) ($object+".translateY");
setAttr -l $trans -k (!$trans) ($object+".translateZ");
setAttr -l $rot -k (!$rot) ($object+".rotateX");
setAttr -l $rot -k (!$rot) ($object+".rotateY");
setAttr -l $rot -k (!$rot) ($object+".rotateZ");
setAttr -l $scale -k (!$scale) ($object+".scaleX");
setAttr -l $scale -k (!$scale) ($object+".scaleY");
setAttr -l $scale -k (!$scale) ($object+".scaleZ");
setAttr -l $vis -k (!$vis) ($object+".visibility");
}

global proc float asMayaVersionAsFloat ()
{
float $version=2012;
if (`about -v`=="2016 Extension 2")
	return 2016.5;
if (`exists getApplicationVersionAsFloat`)
	return `getApplicationVersionAsFloat`;
string $versionString=`about -v`;
string $tempString[];
string $char;
tokenize $versionString $tempString;
//default to 2012, if versionString is not all numbers
for ($i=0;$i<size($tempString[0]);$i++)
	{
	$char=`substring $tempString[0] ($i+1) ($i+1)`;
	if (!`gmatch $char "[0-9]"`)
		return 2012;
	}
$version=$tempString[0];
return $version;
}

global proc asSelChangeToggle ()
{
global int $asSelChangeSwitching;
$asSelChangeSwitching=1;
string $sel[]=`ls -sl`;
select -cl;
asSelChange;
$asSelChangeSwitching=0;
select $sel;
}

global proc asCharChange (string $uiName)
{
string $gridOrder[];
if (`gridLayout -q -ex ($uiName+"GridLayout")`)
	{
	$gridOrder=`gridLayout -q -go ($uiName+"GridLayout")`;
	for ($i=1;$i<size($gridOrder)+1;$i++)
		if (`floatSlider -q -ex ($uiName+"FloatSlider"+$i)`)
			{
			$ann=`floatSlider -q -ann ($uiName+"FloatSlider"+$i)`;
			$resolvedName=`asPoserResolveNameSpace $uiName $ann`;
			if (`objExists $resolvedName`)
				connectControl ($uiName+"FloatSlider"+$i) $resolvedName;
			}
	}
asSelChange;
}

global proc asShowSelJob ()
{
global int $asSelChangeScripJobNr;
if ($asSelChangeScripJobNr)
	return;
$asSelChangeScripJobNr=`scriptJob -e "SelectionChanged" "asSelChange"`;
}

global proc string asSelectorResolveNameSpace (string $name, string $obj)
{
string $nameSpace;
string $tempString[];
if (`optionMenu -q -ex ("asSelector"+$name+"OptionMenu")`)
	$nameSpace=`optionMenu -q -v ("asSelector"+$name+"OptionMenu")`;
else if (`optionMenu -q -ex ("asPickerOptionMenu")`)
	$nameSpace=`optionMenu -q -v ("asPickerOptionMenu")`;
else if (`gmatch $name "*:*"`)
	{
	tokenize $name ":" $tempString;
	for ($i=0;$i<size($tempString)-1;$i++)
		$nameSpace+=$tempString[$i]+":";
	}
if ($nameSpace==":")
	$nameSpace="";
string $extraObj;
//Extra control
if (`checkBox -q -ex ("asSelector"+$name+"ExtraCheckBox")`)
	if (`checkBox -q -v ("asSelector"+$name+"ExtraCheckBox")`)
		if (size($obj)>2)
			{
			$extraObj=`substring $obj 1 2`+"Extra"+`substring $obj 3 99`;
			if (`gmatch $obj "*RootX*"`)
				$extraObj=`substring $obj 1 4`+"Extra"+`substring $obj 5 99`;
			if (`objExists ($nameSpace+$extraObj)`)
				return ($nameSpace+$extraObj);
			}
return ($nameSpace+$obj);
}

global proc asSelect (string $name, string $objs[])
{
for ($i=0;$i<size($objs);$i++)
	$objs[$i]=`asSelectorResolveNameSpace $name $objs[$i]`;

int $modifier=`getModifiers`;
if (($modifier %  2)==0)
	select -cl;
if ($objs[0]=="")
	{
	select -cl;
	return;
	}
for ($obj in $objs)
	if (!`objExists $obj`)
		error ("Object:\""+$obj+"\" does not exists !");
for ($obj in $objs)
	select -tgl $obj;
//enable hotKeys
string $formLayout="asSelector"+$name+"FormLayout";
if (`formLayout -q -ex $formLayout`)
	setFocus $formLayout;
}

global proc asKey (string $name, string $objs[])
{
for ($i=0;$i<size($objs);$i++)
	$objs[$i]=`asSelectorResolveNameSpace $name $objs[$i]`;

for ($obj in $objs)
	setKeyframe $obj;
select `ls -sl`;
asSelChange;
}

global proc asLinearKey (string $name, string $objs[])
{
for ($i=0;$i<size($objs);$i++)
	$objs[$i]=`asSelectorResolveNameSpace $name $objs[$i]`;

for ($obj in $objs)
	setKeyframe -itt linear -ott linear $obj;
select `ls -sl`;
asSelChange;
}

global proc asAlignIK2FK (string $name, string $objs[])
{
asAssembleAlignSwitchCmd $name $objs "asAlignFKIK" "IK2FK";
}

global proc asAlignFK2IK (string $name, string $objs[])
{
asAssembleAlignSwitchCmd $name $objs "asAlignFKIK" "FK2IK";
}

global proc asSwitchIK2FK (string $name, string $objs[])
{
asAssembleAlignSwitchCmd $name $objs "asSwitchFKIK" "IK2FK";
}

global proc asSwitchFK2IK (string $name, string $objs[])
{
asAssembleAlignSwitchCmd $name $objs "asSwitchFKIK" "FK2IK";
}

global proc asAssembleAlignSwitchCmd (string $name, string $objs[], string $alignSwitchCmd, string $W2K)
{
global int $asBakeFKIK;
$asBakeFKIK=0;
int $numLetters=size($objs[0]);
string $IK=`substring $objs[0] 5 ($numLetters-2)`;
string $side=`substring $objs[0] ($numLetters-1) $numLetters`;
eval ($alignSwitchCmd+" "+$name+" "+$IK+" "+$side+" "+$W2K);
}

global proc asAlignFKIK (string $name, string $IK, string $side, string $W2K)
{
global int $asBakeFKIK;
string $sel[]=`ls -sl`;
string $nameSpace=`asSelectorResolveNameSpace $name ""`;
string $controlCurve=$nameSpace+"FKIK"+$IK+$side;
if (!`objExists $controlCurve`)
	error ("Object:\""+$controlCurve+"\" does not exists !");
string $startJoint=`getAttr ($controlCurve+".startJoint")`;
string $middleJoint=`getAttr ($controlCurve+".middleJoint")`;
string $endJoint=`getAttr ($controlCurve+".endJoint")`;
string $curveCmd,$roo,$childLabel,$toesJoint,$qToesJoint,$legAimJoint;
string $tempLoc1[],$tempLoc2[],$tempConstraint[],$tempConstraintAttrs[],$chainJoints[],$tempString[],$tempString2[];
int $isSplineIK,$numIkCtrls,$cvNr,$ikNr;
float $IKCurveLenght,$stiff,$middleJointLenght,$endJointLenght;
float $b=1;
if ($side=="_L") $b=-1;
float $pos[],$rot[],$posA[],$posB[],$tempFloat[],$alignIkToFloat[];
if (!`objExists ($nameSpace+"Pole"+$IK+$side)`)
	$isSplineIK=1;
string $requiredObj[]={($nameSpace+"Main"),($nameSpace+"FKX"+$startJoint+$side),($nameSpace+"IKX"+$startJoint+$side),($nameSpace+"IKX"+$middleJoint+$side)};
if ($isSplineIK)
	{
	$chainJoints=`asgetChainJoints ($nameSpace+$startJoint+$side) ($nameSpace+$endJoint+$side)`;
	for ($i=0;$i<size($chainJoints);$i++)
		$chainJoints[$i]=`substitute $nameSpace $chainJoints[$i] ""`;
	for ($i=1;$i<99;$i++)
		{
		if (!`objExists ($nameSpace+"IK"+$IK+$i+$side)`)
			break;
		$numIkCtrls=$i;
		}
	}
else
	$requiredObj=`stringArrayCatenate $requiredObj {($nameSpace+"Pole"+$IK+$side),($nameSpace+"FKX"+$endJoint+$side)}`;
for ($obj in $requiredObj)
	if (!`objExists $obj`)
		error ("Object:\""+$obj+"\" does not exists !");
float $charsize=`getAttr ($nameSpace+"Main.height")`;
int $autoKey=`autoKeyframe -q -st`;
if ($autoKey)
	autoKeyframe -st 0;
if ($asBakeFKIK)
	autoKeyframe -st 1;

string $deleteObjs[]={"IK2FKTempCurve","IK2FKTempXform1","IK2FKTempXform2","IK2FKTempCurveInfo"};
for ($i=0;$i<size($deleteObjs);$i++)
	if (`objExists $deleteObjs[$i]`)
		delete $deleteObjs[$i];

//$toesJoint
if (`objExists $endJoint`)
	{
	$tempString=`listRelatives -type joint -ad $endJoint`;
	for ($y=0;$y<size($tempString);$y++)
		{
		if (`getAttr ($tempString[$y]+".drawLabel")`)
			{
			$childLabel=`getAttr ($tempString[$y]+".otherType")`;
			if (`gmatch $childLabel "*Toes*"` && !`gmatch $childLabel "*QToes*"` && !`gmatch $childLabel "*ToesEnd*"`)
				{
				$tempString2[0]=`substitute ($nameSpace+":") $tempString[$y] ""`;
				if (`objExists ($nameSpace+"FK"+$tempString2[0]+$side)`)
					$toesJoint=$tempString2[0];
				}
			}
		}
	}

if ($W2K=="FK2IK" && !$isSplineIK)
	{
	//Qtoes
	if (`objExists $endJoint`)
		{
		$tempString=`listRelatives -type joint -ad $endJoint`;
		for ($y=0;$y<size($tempString);$y++)
			{
			if (`getAttr ($tempString[$y]+".drawLabel")`)
				{
				$childLabel=`getAttr ($tempString[$y]+".otherType")`;
				if (`gmatch $childLabel "*QToes*"`)
					{
					$tempString2[0]=`substitute ($nameSpace+":") $tempString[$y] ""`;
					if (`objExists ($nameSpace+"FK"+$tempString2[0]+$side)`)
						$qToesJoint=$tempString2[0];
					}
				}
			}
		}

	//Default values for RollHeel, RollToes, RollToesEnd, and UD attrs such as roll,etc
	$tempString=`listAttr -ud ($nameSpace+"IK"+$IK+$side)`;
	for ($i=0;$i<size($tempString);$i++)
		{
		if (`getAttr -type ($nameSpace+"IK"+$IK+$side+"."+$tempString[$i])`=="string")
			continue;//skip any custom added string attributes
		$tempFloat=`attributeQuery -listDefault -n ($nameSpace+"IK"+$IK+$side) $tempString[$i]`;
		setAttr ($nameSpace+"IK"+$IK+$side+"."+$tempString[$i]) $tempFloat[0];
		}
	$tempString=`listRelatives -ad -type transform ($nameSpace+"IK"+$IK+$side)`;
	for ($i=0;$i<size($tempString);$i++)
		if (`sets -im ($nameSpace+"ControlSet") $tempString[$i]`)
			{
			if (!`getAttr -l ($tempString[$i]+".tx")`)
				setAttr ($tempString[$i]+".t") -type float3 0 0 0;
			if (!`getAttr -l ($tempString[$i]+".rx")`)
				setAttr ($tempString[$i]+".r") -type float3 0 0 0;
			}
	//zero out leg-aim & toes-aim, for easier alignment
	if (`attributeExists toesAim ($nameSpace+"IK"+$IK+$side)`) setAttr ($nameSpace+"IK"+$IK+$side+".toesAim") 0;
	if (`attributeExists legAim ($nameSpace+"IK"+$IK+$side)`) setAttr ($nameSpace+"IK"+$IK+$side+".legAim") 0;

	$tempFloat=`xform -q -ws -t ($nameSpace+"FK"+$endJoint+$side)`;
	if ($qToesJoint!="")
		$tempFloat=`xform -q -ws -t ($nameSpace+"FK"+$qToesJoint+$side)`;
	xform -ws -t $tempFloat[0] $tempFloat[1] $tempFloat[2] ($nameSpace+"IK"+$IK+$side);
	$tempLoc1=`spaceLocator`;
	$tempConstraint=`pointConstraint ($nameSpace+"FKX"+$startJoint+$side) ($nameSpace+"FKX"+$endJoint+$side) $tempLoc1[0]`;
	$tempConstraintAttrs=`listAttr -ud $tempConstraint`;

	$middleJointLenght=`getAttr ($nameSpace+"FKOffset"+$middleJoint+$side+".tx")`;
	$endJointLenght=`getAttr ($nameSpace+"FKOffset"+$endJoint+$side+".tx")`;
	setAttr ($tempLoc1[0]+"_pointConstraint1."+$tempConstraintAttrs[0]) `abs($endJointLenght)`;
	setAttr ($tempLoc1[0]+"_pointConstraint1."+$tempConstraintAttrs[1]) `abs($middleJointLenght)`;
	delete $tempConstraint[0];
	$tempConstraint=`aimConstraint -aimVector 1 0 0 ($nameSpace+"FKX"+$middleJoint+$side) $tempLoc1[0]`;
	$tempLoc2=`spaceLocator`;
	parent $tempLoc2[0] $tempLoc1[0];
	setAttr -type float3 ($tempLoc2[0]+".translate") ($charsize/3.333) 0 0;
	$tempFloat=`xform -q -ws -t $tempLoc2[0]`;
	xform -ws -t $tempFloat[0] $tempFloat[1] $tempFloat[2] ($nameSpace+"Pole"+$IK+$side);
	delete $tempLoc1;
	$roo=`xform -q -roo ($nameSpace+"IK"+$IK+$side)`;
	xform -p 1 -roo $roo ($nameSpace+"AlignIKTo"+$endJoint+$side);
	$tempFloat=`xform -q -ws -ro ($nameSpace+"AlignIKTo"+$endJoint+$side)`;
	xform -ws -ro $tempFloat[0] $tempFloat[1] $tempFloat[2] ($nameSpace+"IK"+$IK+$side);
	if (`objExists $toesJoint` && `objExists ($nameSpace+"AlignIKToToes"+$side)`)
		{
		if (`attributeExists "roll" ($nameSpace+"IK"+$IK+$side)`)
			setAttr ($nameSpace+"IK"+$IK+$side+".roll") 0;
		$rot=`xform -q -ws -ro ($nameSpace+"AlignIKToToes"+$side)`;
		xform -ws -ro $rot[0] $rot[1] $rot[2] ($nameSpace+"IKToes"+$side);
		}
	$tempFloat=`getAttr ($nameSpace+$endJoint+$side+".s")`;
	setAttr ($nameSpace+"IK"+$IK+$side+".s") -type float3 $tempFloat[0] $tempFloat[1] $tempFloat[2];
	//stretchy FK (translated FK ctrls)
//	$posA=`getAttr ($nameSpace+"FK"+$middleJoint+$side+".t")`;
//	$posB=`getAttr ($nameSpace+"FK"+$endJoint+$side+".t")`;
//	if ($posA[0]+$posA[1]+$posA[2]+$posB[0]+$posB[1]+$posB[2]!=0)
		{
		setAttr ($nameSpace+"IK"+$IK+$side+".stretchy") 10;
		setAttr ($nameSpace+"IK"+$IK+$side+".volume") 10;
		//position
		$tempFloat=`xform -q -ws -t ($nameSpace+"FKX"+$endJoint+$side)`;
		xform -ws -t $tempFloat[0] $tempFloat[1] $tempFloat[2] ($nameSpace+"IK"+$IK+$side);

		if (`objExists ($nameSpace+"IKX"+$middleJoint+$side+"_IKLenght"+$side)` && `objExists ("IKX"+$middleJoint+$side+"_IKmessureDiv"+$side)`)
			{//Lenght1 & Lenght2
			$pos=`xform -q -ws -t ($nameSpace+"FK"+$startJoint+$side)`;
			$posA=`xform -q -ws -t ($nameSpace+"FK"+$middleJoint+$side)`;
			$posB=`xform -q -ws -t ($nameSpace+"FK"+$endJoint+$side)`;
			$tempFloat[0]=`mag<<$pos[0]-$posA[0],$pos[1]-$posA[1],$pos[2]-$posA[2]>>`;
			$tempFloat[1]=`mag<<$posA[0]-$posB[0],$posA[1]-$posB[1],$posA[2]-$posB[2]>>`;
			setAttr ($nameSpace+"IK"+$IK+$side+".Lenght1") (($tempFloat[0]/(`getAttr ($nameSpace+"IKX"+$middleJoint+$side+"_IKLenght"+$side+".input2X")`*$b))/`getAttr ("IKX"+$middleJoint+$side+"_IKmessureDiv"+$side+".input1X")`);
			setAttr ($nameSpace+"IK"+$IK+$side+".Lenght2") (($tempFloat[1]/(`getAttr ($nameSpace+"IKX"+$endJoint+$side+"_IKLenght"+$side+".input2X")`*$b))/`getAttr ("IKX"+$middleJoint+$side+"_IKmessureDiv"+$side+".input1X")`);
			}
		}
	}
if ($W2K=="FK2IK" && $isSplineIK)
	{
	//first do Start and End IkCtrls
	$tempFloat=`xform -q -ws -t ($nameSpace+"AlignIKTo"+$chainJoints[0])`;
	xform -ws -t $tempFloat[0] $tempFloat[1] $tempFloat[2] ($nameSpace+"IK"+$IK+"1"+$side);
	$roo=`xform -q -roo ($nameSpace+"IK"+$IK+"1"+$side)`;
	xform -p 1 -roo $roo ($nameSpace+"AlignIKTo"+$chainJoints[0]);
	$tempFloat=`xform -q -ws -ro ($nameSpace+"AlignIKTo"+$chainJoints[0])`;
	xform -ws -ro $tempFloat[0] $tempFloat[1] $tempFloat[2] ($nameSpace+"IK"+$IK+"1"+$side);

	$tempFloat=`xform -q -ws -t ($nameSpace+"AlignIKTo"+$chainJoints[size($chainJoints)-1])`;
	xform -ws -t $tempFloat[0] $tempFloat[1] $tempFloat[2] ($nameSpace+"IK"+$IK+$numIkCtrls+$side);
	$roo=`xform -q -roo ($nameSpace+"IK"+$IK+$numIkCtrls+$side)`;
	xform -p 1 -roo $roo ($nameSpace+"AlignIKTo"+$chainJoints[size($chainJoints)-1]);
	$tempFloat=`xform -q -ws -ro ($nameSpace+"AlignIKTo"+$chainJoints[size($chainJoints)-1])`;
	xform -ws -ro $tempFloat[0] $tempFloat[1] $tempFloat[2] ($nameSpace+"IK"+$IK+$numIkCtrls+$side);

	$curveCmd="curve -n FK2IKCurve -d 3";
	for ($i=0;$i<size($chainJoints);$i++)
		{
		$pos=`xform -q -ws-t ($nameSpace+$chainJoints[$i])`;
		$curveCmd+=" -p "+$pos[0]+" "+$pos[1]+" "+$pos[2];
		}
	eval ($curveCmd);
	rebuildCurve -ch 0 -rpo 1 -rt 0 -end 1 -kr 0 -kcp 0 -kep 1 -kt 0 -s 0 -d 3 -tol 0.01 FK2IKCurve;
	if (`objExists tempPointOnCurveInfo`) delete tempPointOnCurveInfo;
	createNode -n tempPointOnCurveInfo pointOnCurveInfo;
	setAttr tempPointOnCurveInfo.turnOnPercentage 1;
	connectAttr -f FK2IKCurve.worldSpace[0] tempPointOnCurveInfo.inputCurve;

	//then do `inbeween` IkCtrls
	for ($i=2;$i<$numIkCtrls;$i++)
		{
		setAttr tempPointOnCurveInfo.parameter (($i-1.0)/($numIkCtrls-1.0));
		$pos=`getAttr tempPointOnCurveInfo.position`;
		xform -ws -t $pos[0] $pos[1] $pos[2] ($nameSpace+"IK"+$IK+$i+$side);
		}
	delete FK2IKCurve;
/*
	//removed, since FK ctrls can be moved (stretched) causing very non-unifor distribution of FK-joints,
	//and trasnferring this non-unifor distribution to the IK is probably not what the user wants
	//then do  IKcvCtrls
	for ($i=1;$i<size($chainJoints)-1;$i++)
		{
		$pos=`xform -q -ws -t ($nameSpace+"FKX"+$chainJoints[$i])`;
		xform -ws -t $pos[0] $pos[1] $pos[2] ($nameSpace+"IKcv"+$IK+$i+$side);
		}
*/
	print "// Switching from FK to Curve-Based-IK, Target might not fully Align.\n";
	}
if ($W2K=="IK2FK" && !$isSplineIK)
	{
	if (`attributeExists legAim ($nameSpace+"IK"+$IK+$side)`)
		{
		$tempString=`listConnections -s 0 -d 1 ($nameSpace+"IK"+$IK+"LegAimReverse"+$side)`;
		$legAimJoint=`substitute "LegAim" $tempString[0] ""`;
		$legAimJoint=`substitute ($side+"_orientConstraint1") $legAimJoint ""`;
		if (`getAttr ($nameSpace+"IK"+$IK+$side+".legAim")`>0)
			print ("// "+$nameSpace+"IK"+$IK+$side+".legAim is On, The FKIK will not correctly Align, for better results, turn Off the \"legAim\" attribute.\n");
		}
	if (`attributeExists toesAim ($nameSpace+"IK"+$IK+$side)`)
		if (`getAttr ($nameSpace+"IK"+$IK+$side+".toesAim")`>0)
			print ("// "+$nameSpace+"IK"+$IK+$side+".toesAim is On, The FKIK will not correctly Align, for better results, turn Off the \"toesAim\" attribute.\n");

	$tempFloat=`xform -q -ws -ro ($nameSpace+"IKX"+$startJoint+$side)`;
	xform -os -t 0 0 0 ($nameSpace+"FK"+$startJoint+$side);
	xform -ws -ro $tempFloat[0] $tempFloat[1] $tempFloat[2] ($nameSpace+"FK"+$startJoint+$side);
	$tempFloat=`xform -q -ws -ro ($nameSpace+"IKX"+$middleJoint+$side)`;
	xform -os -t 0 0 0 ($nameSpace+"FK"+$middleJoint+$side);
	xform -ws -ro $tempFloat[0] $tempFloat[1] $tempFloat[2] ($nameSpace+"FK"+$middleJoint+$side);
	$tempFloat=`xform -q -ws -ro ($nameSpace+"IKX"+$endJoint+$side)`;
	xform -os -t 0 0 0 ($nameSpace+"FK"+$endJoint+$side);
	xform -ws -ro $tempFloat[0] $tempFloat[1] $tempFloat[2] ($nameSpace+"FK"+$endJoint+$side);
	if (`objExists $toesJoint` && `objExists ($nameSpace+"IKXToes"+$side)`)
		{
		$tempFloat=`xform -q -ws -ro ($nameSpace+"IKXToes"+$side)`;
		xform -os -t 0 0 0 ($nameSpace+"FKToes"+$side);
		xform -ws -ro $tempFloat[0] $tempFloat[1] $tempFloat[2] ($nameSpace+"FKToes"+$side);
		}
	$tempFloat=`getAttr ($nameSpace+$endJoint+$side+".s")`;
	setAttr ($nameSpace+"FK"+$endJoint+$side+".s") -type float3 $tempFloat[0] $tempFloat[1] $tempFloat[2];
	//stretchy IK
//	if (`getAttr ($nameSpace+"IK"+$IK+$side+".stretchy")`>0.01)
		{
		//volume
		$tempFloat=`getAttr ($nameSpace+$startJoint+$side+".s")`;
		setAttr ($nameSpace+"FK"+$startJoint+$side+".s") -type float3 $tempFloat[0] $tempFloat[1] $tempFloat[2];
		$tempFloat=`getAttr ($nameSpace+$middleJoint+$side+".s")`;
		setAttr ($nameSpace+"FK"+$middleJoint+$side+".s") -type float3 $tempFloat[0] $tempFloat[1] $tempFloat[2];
		//position
		$tempFloat=`xform -q -ws -t ($nameSpace+"IKX"+$middleJoint+$side)`;
		xform -ws -t $tempFloat[0] $tempFloat[1] $tempFloat[2] ($nameSpace+"FK"+$middleJoint+$side);
		$tempFloat=`xform -q -ws -t ($nameSpace+"IKX"+$endJoint+$side)`;
		xform -ws -t $tempFloat[0] $tempFloat[1] $tempFloat[2] ($nameSpace+"FK"+$endJoint+$side);
		}
	}
if ($W2K=="IK2FK" && $isSplineIK)
	{
	for ($i=0;$i<size($chainJoints);$i++)
		{
		if (!`objExists ($nameSpace+"IKX"+$chainJoints[$i])` || !`objExists ($nameSpace+"FK"+$chainJoints[$i])`)
			continue;
		if (!`sets -im ($nameSpace+"ControlSet") ($nameSpace+"FK"+$chainJoints[$i])`)
			continue;
		// RootX_M.legLock warning
		if ($chainJoints[$i]=="Root")
			if (`attributeExists legLock RootX_M`)
				if (`getAttr RootX_M.legLock`!=10)
					print ("// Switching from IK to FK for the Spine, Set RootX_M.legLock to 10, for more accurate switching.\n");
		$pos=`xform -q -ws -t ($nameSpace+"IKX"+$chainJoints[$i])`;
		$rot=`xform -q -ws -ro ($nameSpace+"IKX"+$chainJoints[$i])`;
		//inbetween, to use last inbetweener`s rot
//		for ($numParts=0;$numParts<99;$numParts++)
//			if (!`objExists ($chainJoints[$i]+"Part"+($numParts+1)+$side)`)
//				break;
//		if ($numParts>0)
//			$rot=`xform -q -ws -roo $roo -ro ($nameSpace+$chainJoints[$i]+"Part"+$numParts+$side)`;
		xform -ws -t $pos[0] $pos[1] $pos[2] -ro $rot[0] $rot[1] $rot[2] ($nameSpace+"FK"+$chainJoints[$i]);
		}
	}

if ($autoKey)
	autoKeyframe -st 1;
if (!$autoKey && $asBakeFKIK)
	autoKeyframe -st 0;
select $sel;
}

global proc asSwitchFKIK (string $name, string $IK, string $side, string $W2K)
{
string $sel[]=`ls -sl`;
string $nameSpace=`asSelectorResolveNameSpace $name ""`;
string $controlCurve=$nameSpace+"FKIK"+$IK+$side;
string $poleCurve=$nameSpace+"Pole"+$IK+$side;
if (!`objExists $controlCurve`)
	error ("Object:\""+$controlCurve+"\" does not exists !");
string $startJoint=`getAttr ($controlCurve+".startJoint")`;
string $middleJoint=`getAttr ($controlCurve+".middleJoint")`;
string $endJoint=`getAttr ($controlCurve+".endJoint")`;
string $chainJoints[],$tempString[],$tempString2[];
string $toesJoint,$childLabel;
int $isSplineIK,$numIkCtrls;
if (!`objExists ($nameSpace+"Pole"+$IK+$side)`)
	$isSplineIK=1;
string $requiredObj[]={($nameSpace+"Main"),($nameSpace+"FKX"+$startJoint+$side),($nameSpace+"IKX"+$startJoint+$side),($nameSpace+"IKX"+$middleJoint+$side)};
if ($isSplineIK)
	{
	$chainJoints=`asgetChainJoints ($nameSpace+$startJoint) ($nameSpace+$endJoint)`;
	for ($i=0;$i<size($chainJoints);$i++)
		$chainJoints[$i]=`substitute $nameSpace $chainJoints[$i] ""`;
	for ($i=1;$i<99;$i++)
		{
		if (!`objExists ($nameSpace+"IK"+$IK+$i+$side)`)
			break;
		$numIkCtrls=$i;
		}
	}
else
	$requiredObj=`stringArrayCatenate $requiredObj {($nameSpace+"Pole"+$IK+$side),($nameSpace+"FKX"+$endJoint+$side)}`;
for ($obj in $requiredObj)
	if (!`objExists $obj`)
		error ("Object:\""+$obj+"\" does not exists !");
int $Blend;
int $BlendInverse=10;
int $onOff;
if ($W2K=="FK2IK")
	{
	$Blend=10;
	$BlendInverse=0;
	$onOff=1;
	}

if ($W2K=="FK2IK" && `getAttr ($controlCurve+".FKIKBlend")`>0)
	{
	warning ("Could not switch FK2IK, because \"FKIKBlend\" is not \"0\"\n");
	return;
	}
if ($W2K=="IK2FK" && `getAttr ($controlCurve+".FKIKBlend")`<10)
	{
	warning ("Could not switch IK2FK, because \"FKIKBlend\" is not \"10\"\n");
	return;
	}

//$toesJoint
if (`objExists $endJoint`)
	{
	$tempString=`listRelatives -type joint -ad $endJoint`;
	for ($y=0;$y<size($tempString);$y++)
		{
		if (`getAttr ($tempString[$y]+".drawLabel")`)
			{
			$childLabel=`getAttr ($tempString[$y]+".otherType")`;
			if (`gmatch $childLabel "*Toes*"` && !`gmatch $childLabel "*QToes*"` && !`gmatch $childLabel "*ToesEnd*"`)
				{
				$tempString2[0]=`substitute ($nameSpace+":") $tempString[$y] ""`;
				if (`objExists ($nameSpace+"FK"+$tempString2[0]+$side)`)
					$toesJoint=$tempString2[0];
				}
			}
		}
	}

int $autoKey=`autoKeyframe -q -st`;

currentTime (`currentTime -q` -1);
setAttr ($controlCurve+".FKIKBlend") $BlendInverse;
if ($autoKey)
	{
	if (!$isSplineIK)
		{
		setKeyframe ($controlCurve+".FKIKBlend");
		setKeyframe ($poleCurve+".follow");	

		setKeyframe ($nameSpace+"FK"+$startJoint+$side+".rotate");
		setKeyframe ($nameSpace+"FK"+$startJoint+$side+".scale");
		setKeyframe ($nameSpace+"FK"+$middleJoint+$side+".translate");
		setKeyframe ($nameSpace+"FK"+$middleJoint+$side+".rotate");
		setKeyframe ($nameSpace+"FK"+$middleJoint+$side+".scale");
		setKeyframe ($nameSpace+"FK"+$endJoint+$side+".translate");
		setKeyframe ($nameSpace+"FK"+$endJoint+$side+".rotate");
		setKeyframe ($nameSpace+"FK"+$endJoint+$side+".scale");
		if (`objExists $toesJoint` && `objExists ($nameSpace+"FKToes"+$side)`)
			setKeyframe ($nameSpace+"FKToes"+$side+".rotate");
		setKeyframe ($nameSpace+"IK"+$IK+$side+".translate");
		setKeyframe ($nameSpace+"IK"+$IK+$side+".rotate");
		setKeyframe ($nameSpace+"IK"+$IK+$side+".scale");
		setKeyframe ($nameSpace+"Pole"+$IK+$side+".translate");
		$tempString=`listAttr -ud ($nameSpace+"IK"+$IK+$side)`;
		for ($i=0;$i<size($tempString);$i++)
			setKeyframe ($nameSpace+"IK"+$IK+$side+"."+$tempString[$i]);
		if (`objExists $toesJoint` && `objExists ($nameSpace+"IKToes"+$side)`)
			setKeyframe ($nameSpace+"IKToes"+$side+".rotate");
		//also key all child-controls (ik heel/toes/toesEnd
		$tempString=`listRelatives -ad -type transform ($nameSpace+"IK"+$IK+$side)`;
		for ($i=0;$i<size($tempString);$i++)
			if (`sets -im ($nameSpace+"ControlSet") $tempString[$i]`)
				setKeyframe ($tempString[$i]+".r");
		}
	if ($isSplineIK)
		{
		setKeyframe ($controlCurve+".FKIKBlend");
		for ($i=0;$i<size($chainJoints);$i++)
			if (`objExists ($nameSpace+"FK"+$chainJoints[$i]+$side)`)
				{
				setKeyframe ($nameSpace+"FK"+$chainJoints[$i]+$side+".t");
				setKeyframe ($nameSpace+"FK"+$chainJoints[$i]+$side+".r");
				}
		for ($i=1;$i<$numIkCtrls+1;$i++)
			if (`objExists ($nameSpace+"IK"+$IK+$i+$side)`)
				{
				setKeyframe ($nameSpace+"IK"+$IK+$i+$side+".t");
				setKeyframe ($nameSpace+"IK"+$IK+$i+$side+".r");
				}
		}
	}

currentTime (`currentTime -q` +1);
asAlignFKIK $name $IK $side $W2K;
setAttr ($controlCurve+".FKIKBlend") $Blend;

if ($autoKey)
	{
	if (!$isSplineIK)
		{
		setAttr ($poleCurve+".follow") 0;

		setKeyframe ($controlCurve+".FKIKBlend");
		setKeyframe ($poleCurve+".follow");

		setKeyframe ($nameSpace+"FK"+$startJoint+$side+".rotate");
		setKeyframe ($nameSpace+"FK"+$startJoint+$side+".scale");
		setKeyframe ($nameSpace+"FK"+$middleJoint+$side+".translate");
		setKeyframe ($nameSpace+"FK"+$middleJoint+$side+".rotate");
		setKeyframe ($nameSpace+"FK"+$middleJoint+$side+".scale");
		setKeyframe ($nameSpace+"FK"+$endJoint+$side+".translate");
		setKeyframe ($nameSpace+"FK"+$endJoint+$side+".rotate");
		setKeyframe ($nameSpace+"FK"+$endJoint+$side+".scale");
		if (`objExists $toesJoint` && `objExists ($nameSpace+"FKToes"+$side)`)
			setKeyframe ($nameSpace+"FKToes"+$side+".rotate");
		setKeyframe ($nameSpace+"IK"+$IK+$side+".translate");
		setKeyframe ($nameSpace+"IK"+$IK+$side+".rotate");
		setKeyframe ($nameSpace+"IK"+$IK+$side+".scale");
		setKeyframe ($nameSpace+"Pole"+$IK+$side+".translate");
		$tempString=`listAttr -ud ($nameSpace+"IK"+$IK+$side)`;
		for ($i=0;$i<size($tempString);$i++)
			setKeyframe ($nameSpace+"IK"+$IK+$side+"."+$tempString[$i]);
		if (`objExists $toesJoint` && `objExists ($nameSpace+"IKToes"+$side)`)
			setKeyframe ($nameSpace+"IKToes"+$side+".rotate");
		//also key all child-controls (ik heel/toes/toesEnd
		$tempString=`listRelatives -ad -type transform ($nameSpace+"IK"+$IK+$side)`;
		for ($i=0;$i<size($tempString);$i++)
			if (`sets -im ($nameSpace+"ControlSet") $tempString[$i]`)
				setKeyframe ($tempString[$i]+".r");
		}
	if ($isSplineIK)
		{
		setKeyframe ($controlCurve+".FKIKBlend");
		for ($i=0;$i<size($chainJoints);$i++)
			if (`objExists ($nameSpace+"FK"+$chainJoints[$i]+$side)`)
				{
				setKeyframe ($nameSpace+"FK"+$chainJoints[$i]+$side+".t");
				setKeyframe ($nameSpace+"FK"+$chainJoints[$i]+$side+".r");
				}
		for ($i=1;$i<$numIkCtrls+1;$i++)
			if (`objExists ($nameSpace+"IK"+$IK+$i+$side)`)
				{
				setKeyframe ($nameSpace+"IK"+$IK+$i+$side+".t");
				setKeyframe ($nameSpace+"IK"+$IK+$i+$side+".r");
				}
		}
	}

//select ($nameSpace+"ControlSet");
//delete -staticChannels -unitlessAnimationCurves false -hierarchy none -controlPoints 0 -shape 1;
//removed, since this sometimes deletes non-static channels e..g IKLeg_L animation
select $sel;
}

global proc string[] asgetChainJoints (string $startJoint, string $endJoint)
{
int $startJointIsParentNr;
string $chainJoints[];
string $tempString[]=`ls -l $endJoint`;
tokenize $tempString[0] "|" $tempString;
for ($i=size($tempString)-1;$i>-1;$i--)
	{
	$startJointIsParentNr=$i;
	if ($tempString[$i]==$startJoint)
		{
		break;
		}
	}
for ($i=$startJointIsParentNr;$i<size($tempString);$i++)
	$chainJoints[size($chainJoints)]=$tempString[$i];
return $chainJoints;
}

global proc asPopulateNameSpaceMenu (string $name)
{
string $optionMenu=$name+"OptionMenu";
string $nameSpacesList[]=`namespaceInfo -r -lon`;
$nameSpacesList=`stringArrayRemove {"UI"} $nameSpacesList`;
$nameSpacesList[size($nameSpacesList)]="";
string $itemList[]=`optionMenu -q -ils $optionMenu`;
$nameSpacesList=`sort $nameSpacesList`;
for ($item in $itemList)
	deleteUI $item;

for ($nameSpace in $nameSpacesList)
	if (`attributeExists "version" ($nameSpace+":Main")` || `attributeExists "version" ($nameSpace+":FaceGroup")`)
		if (`asFilterCheck $name $nameSpace`)
			menuItem -p $optionMenu -l ($nameSpace+":");

if (!`optionMenu -q -ni ($name+"OptionMenu")`)
	{
	if ($name=="bodySetup" || `gmatch $name "asPoser*"`)
		menuItem -p $optionMenu -l "None";
	else
		menuItem -p $optionMenu -l ":";
	}
}

global proc int asFilterCheck (string $name, string $nameSpace)
{
int $return=0;
string $filterString=`optionVar -q ("asSelectorFilter_"+$name)`;
if ($filterString=="" || $filterString=="0")
	return 1;
string $references[]=`file -q -r`;
for ($i=0;$i<size($references);$i++)
	{
	$refNameSpace=`file -q -ns $references[$i]`;
	if ($refNameSpace==$nameSpace)
		if (`gmatch $references[$i] $filterString`)
			$return=1;
	}
return $return;
}

global proc asFilterNameSpaceMenuUI (string $name)
{
string $filterString=`optionVar -q ("asSelectorFilter_"+$name)`;
if ($filterString=="0")
	$filterString="";
if (`window -q -ex ("SelectorFilter_"+$name)`)
	deleteUI ("SelectorFilter_"+$name);
window ("SelectorFilter_"+$name);
columnLayout;
textFieldGrp -tx $filterString -cc ("asSetFilterNameSpaceMenu "+$name) -l "Reference File Filter. (e.g. *characters*)" -cw 1 200 ("asSelectorFilterTextFieldGrp_"+$name);
showWindow;
}

global proc asSetFilterNameSpaceMenu (string $name)
{
string $filterString=`textFieldGrp -q -tx ("asSelectorFilterTextFieldGrp_"+$name)`;
optionVar -sv ("asSelectorFilter_"+$name) $filterString;
asPopulateNameSpaceMenu $name;
}

global proc asSetNameSpaceFromSelection (string $uiName)
{
asPopulateNameSpaceMenu $uiName;
string $sel[]=`ls -sl`;
string $tempString[],$ils[];
if (size($sel))
	{
	tokenize $sel[0] ":" $tempString;
	$ils=`optionMenu -q -ils ($uiName+"OptionMenu")`;
	for ($i=0;$i<size($ils);$i++)
		if (`menuItem -q -l $ils[$i]`==($tempString[0]+":"))
			{
			optionMenu -e -sl ($i+1) ($uiName+"OptionMenu");
			asSelChange;
			}
	}
print "// Setting nameSpace from selected object\n";
}

global proc asCopyToClipBoard (string $uiName, int $anim)
{
string $cmd=`asPoserGetCmd $uiName $anim`;
if (!$anim)
	menuItem -e -en 1 -c $cmd ($uiName+"PosePaste");
else
	menuItem -e -en 1 -c ($cmd+" 0") ($uiName+"AnimPaste");
}

global proc string[] asGetControlSetsFromUI (string $uiName)
{
int $selectedTabIndex;
string $controlSets[];
string $tempString[],$tabLabels[];
string $controlSetsText,$nameSpace;

if ($uiName!="")
	$nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";

if (`text -q -ex ($uiName+"ControlSetsText")`)//Selector
	{
	$controlSetsText=`text -q -l ($uiName+"ControlSetsText")`;
	tokenize $controlSetsText $tempString;
	for ($i=0;$i<size($tempString);$i++)
		if (`objExists ($nameSpace+$tempString[$i])`)
			$controlSets[size($controlSets)]=$nameSpace+$tempString[$i];
	}
else if ($uiName=="asPicker" && `tabLayout -q -ex asPickerTabLayout`)//Picker
	{
	$tabLabels=`tabLayout -q -tl asPickerTabLayout`;
	$selectedTabIndex=`tabLayout -q -selectTabIndex asPickerTabLayout`;
	if (`gmatch $tabLabels[$selectedTabIndex-1] "*face*"`)
		$controlSets[0]=$nameSpace+"FaceControlSet";
	else
		$controlSets[0]=$nameSpace+"ControlSet";
	}
else if ($uiName=="asPoserDefault" && `menuItem -q -ex asPoserControlSetsMenu`)//PoserDesigner
	{
	$tempString=`menu -q -ia asPoserControlSetsMenu`;
	for ($i=0;$i<size($tempString);$i++)
		if (`menuItem -q -cb $tempString[$i]`)
			$controlSets[size($controlSets)]=$nameSpace+`menuItem -q -l $tempString[$i]`;
	}
else
	$controlSets[0]=$nameSpace+"ControlSet";

return $controlSets;
}

global proc asDeleteStaticChannels (string $uiName)
{
if (`confirmDialog -title "Confirm" -message 
	("Clean animation ?\n"
	+"This will delete static channels,\n"
	+"which means remove all animation where the value is not changing")
    -button "Yes" -button "No" -defaultButton "Yes"
    -cancelButton "No" -dismissString "No"`!="Yes")
	return;
string $sel[]=`ls -sl`;
string $controlSets[]=`asGetControlSetsFromUI $uiName`;
select $controlSets;
evalEcho "delete -staticChannels -unitlessAnimationCurves false -hierarchy none -controlPoints 0 -shape 1";
print ("// Static channels cleaned\n");
select $sel;
}
	
global proc string asPoserGetCmd (string $uiName, int $anim)
{
global string $gChannelBoxName;
global string $gMainProgressBar;
string $sel[]=`ls -sl`;
string $selectedMainAttrs[]=`channelBox -q -sma $gChannelBoxName`;
string $selectedShapeAttrs[]=`channelBox -q -ssa $gChannelBoxName`;
string $selectedHistoryAttrs[]=`channelBox -q -sha $gChannelBoxName`;
string $selectedOutputAttrs[]=`channelBox -q -soa $gChannelBoxName`;
string $selectedAttrs[];
$selectedAttrs=`stringArrayCatenate $selectedMainAttrs $selectedShapeAttrs`;
$selectedAttrs=`stringArrayCatenate $selectedAttrs $selectedHistoryAttrs`;
$selectedAttrs=`stringArrayCatenate $selectedAttrs $selectedOutputAttrs`;
int $ctrlButton,$altButton;
if ((`getModifiers`/4) %  2)
	$ctrlButton=1;
if ((`getModifiers`/8) %  2)
	$altButton=1;
int $onlySel;
if ($uiName=="asPoserDefault")
	{
	if (`checkBox -q -ex asPoserOnlySel`)
		$onlySel=`checkBox -q -v asPoserOnlySel`;
	}
else if ($altButton || $ctrlButton)
	$onlySel=1;
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
string $cmd;
string $controls[],$buffer[];
int $weightedTangents[];
int $onlyOneObj,$onlyOneAttr;
string $connectObj;
string $controlSets[]=`asGetControlSetsFromUI $uiName`;
if (!$onlySel && !size($controlSets))
	error "No ControlSets";

//determine the name for poserAnimFile
string $animationFile,$animationFilePath;
string $gridOrder[];
int $childNum;
if (`gmatch $uiName "asPoser*"`)
	{
	$gridOrder=`gridLayout -q -go ($uiName+"GridLayout")`;
	for ($i=1;$i<size($gridOrder)+1;$i++)
		if (`gmatch $gridOrder[$i-1] "asPoser*"`)
			$childNum=$i;
	$childNum++;

	$animationFile="untitled_"+$childNum;
	$animationFilePath=`asGetTempDirectory`+"AdvancedSkeleton/Poser/untitled/";
	}
else
	{
	$animationFile="ClipBoard";
	$animationFilePath=`asGetTempDirectory`+"AdvancedSkeleton/Selector/";
	}

string $animCurves[];

if ($onlySel)
	{
	for ($i=$y=0;$i<size($sel);$i++)
		{
		if (`gmatch $sel[$i] ($nameSpace+"*")`)
			{
			tokenize $sel[$i] ":" $buffer;
			$controls[$y]=$buffer[size($buffer)-1];
			$y++;
			}
		}
	}
else
	{
	if (!`objExists $controlSets[0]`)
		error ("Object :\""+$controlSets[0]+"\" does not exists !\n");
	$controls=`sets -q $controlSets`;
	for ($i=0;$i<size($controls);$i++)
		$controls[$i]=`substitute  $nameSpace $controls[$i] ""`;
	}
string $attrs[];
if (size($controls)<1)
	error "No Controls Available!";
evalDeferred ("progressBar -e -ep "+$gMainProgressBar);
progressBar -e -st "Storing Data" -bp -ii 1 -min 0 -max (size($controls)) $gMainProgressBar;
select -cl;

if ($anim)
	{
	createNode -n poserAnimationInfo transform;
	addAttr -ln "cmd" -dt "string" poserAnimationInfo;
	select poserAnimationInfo;
	}

	{
for ($obj in $controls)
	{
	progressBar -e -s 1 $gMainProgressBar;
	if (`progressBar -q -ic $gMainProgressBar`)
		error "Interrupted";
	$allKeyableAttrs=`listAttr -k -m -sn ($nameSpace+$obj)`;
	if ($onlySel && (size($selectedAttrs)>0))
		$attrs=$selectedAttrs;
	else
		$attrs=$allKeyableAttrs;
	for ($attr in $attrs)
		for ($allKeyableAttr in $allKeyableAttrs)
			{
			if (`getAttr -l ($nameSpace+$obj+"."+$attr)`)
				continue;
			if ($attr==$allKeyableAttr)
				{
				if (!$anim)
					$cmd+=$obj+"."+$attr+" "+`getAttr ($nameSpace+$obj+"."+$attr)`+";";
				else
					{
					//Animation
					$animCurves=`listConnections -type animCurve -s 1 -d 0 ($nameSpace+$obj+"."+$attr)`;
					for ($y=0;$y<size($animCurves);$y++)
						{
						select -add $animCurves[$y];
						$cmd+=$obj+"."+$attr+" "+`getAttr ($nameSpace+$obj+"."+$attr)`+" "+$animCurves[$y]+";";
						}
					}
				}
			}
		}
	}

if ($anim)
	{
	setAttr -type "string" poserAnimationInfo.cmd $cmd;
	if (!`file -q -ex $animationFilePath`)
		sysFile -md $animationFilePath;
	file -f -op "v=0" -typ "mayaAscii" -es ($animationFilePath+$animationFile+".ma");
	delete poserAnimationInfo;
	}

if ($cmd!="")
	{
	if (!$anim)
		$cmd="asSetAttrs "+$uiName+" \""+$cmd+"\"";
	else
		$cmd="asLoadAttrs "+$uiName;
	}

select $sel;
return $cmd;
}

global proc asSetAttrs (string $uiName, string $cmds)
{
int $ctrlButton,$altButton;
if ((`getModifiers`/4) %  2)
	$ctrlButton=1;
if ((`getModifiers`/8) %  2)
	$altButton=1;
string $buffer[],$buffer2[];
string $objAttr;
float $value;
int $numTok=`tokenize $cmds ";" $buffer`;
int $showWarning;
string $warningMsg="The following attributes can not be set:";

for ($i=0;$i<$numTok;$i++)
	{
	tokenize $buffer[$i] $buffer2;
	$objAttr=$buffer2[0];
	$value=$buffer2[1];
	$objAttr=`asPoserResolveNameSpace $uiName $objAttr`;
	if (($ctrlButton||$altButton) && `gmatch $objAttr "*Main.*"`)
		continue;
	if (`objExists $objAttr`)
		catch (`eval ("setAttr "+$objAttr+" "+$value)`);
	else
		{
		$showWarning=1;
		$warningMsg+=$objAttr+",";
		}
	}
if ($showWarning)
	warning $warningMsg;
}

global proc asLoadAttrs (string $uiName, int $childNum)
{
string $sel[]=`ls -sl`;
int $autoKey=`autoKeyframe -q -st`;
if ($autoKey)
	autoKeyframe -e -st 0;
createNode -n tempXform transform;
int $shiftButton,$ctrlButton,$altButton;
if (`getModifiers` %  2)
	$shiftButton=1;
if ((`getModifiers`/4) %  2)
	$ctrlButton=1;
if ((`getModifiers`/8) %  2)
	$altButton=1;
float $timeOffset=0;
if ($shiftButton)
	$timeOffset=`currentTime -q`;
string $buffer[],$buffer2[],$buffer3[],$tempString[];
string $obj,$attr,$objAttr,$animCurve,$newAnimCurve;
float $value;

string $animationFile,$projectName;

if ($childNum==0)
	{
	$projectName="Selector";
	$animationFile=`asGetTempDirectory`+"AdvancedSkeleton/Selector/ClipBoard.ma";
	}
else
	{
	string $icon=`iconTextButton -q -i ($uiName+"IconTextButton"+$childNum)`;
	string $tempString[];
	tokenize $icon "/" $tempString;
	$projectName=$tempString[size($tempString)-2];
	$animationFile=`substitute "[.][a-z][a-z][a-z]" $icon ".ma"`;
	}
file -r -type "mayaAscii" -namespace $projectName -options "v=0;p=17" $animationFile;

string $cmds=`getAttr ($projectName+":poserAnimationInfo.cmd")`;
int $numTok=`tokenize $cmds ";" $buffer`;
int $showWarning;
string $warningMsg="The following attributes can not be set:";
for ($i=0;$i<$numTok;$i++)
	{
	tokenize $buffer[$i] $buffer2;
	$objAttr=$buffer2[0];
	tokenize $objAttr "." $buffer3;
	$obj=$buffer3[0];
	$attr=$buffer3[1];
	$value=$buffer2[1];
	$animCurve=$buffer2[2];
	$objAttr=`asPoserResolveNameSpace $uiName $objAttr`;
	if (($ctrlButton||$altButton) && `gmatch $objAttr "*Main.*"`)
		continue;
	if (`objExists $objAttr`)
		{
		catch (`eval ("setAttr "+$objAttr+" "+$value)`);
		if (!`attributeExists $attr tempXform`)
			addAttr -k 1 -ln $attr -at double tempXform;
		connectAttr -f ($projectName+":"+$animCurve+".output") ("tempXform."+$attr);
		copyKey -time ":" -hierarchy none -at $attr tempXform;
		pasteKey -option merge -copies 1 -connect 0 -timeOffset $timeOffset -floatOffset 0 -valueOffset 0 {$objAttr};
		}
	else
		{
		$showWarning=1;
		$warningMsg+=$objAttr+",";
		}
	}
if ($showWarning)
	warning $warningMsg;

if (`objExists tempXform`)
	delete tempXform;
file -rr  $animationFile;
select $sel;
if ($autoKey)
	autoKeyframe -e -st 1;
}

global proc asKeyAttrs (string $uiName, string $cmds)
{
int $ctrlButton,$altButton;
if ((`getModifiers`/4) %  2)
	$ctrlButton=1;
if ((`getModifiers`/8) %  2)
	$altButton=1;
string $buffer[],$buffer2[],$spaceBuffer[];
string $objAttr,$previousObjAttr,$restOfBuffers;
float $time,$value,$currentValue;
float $currentTime=`currentTime -q`;
int $numTok=`tokenize $cmds ";" $buffer`;
int $showWarning,$firstValueSet;
string $warningMsg="The following attributes can not be keyed:";
float $firstValue;
for ($i=0;$i<$numTok;$i++)
	{
	tokenize $buffer[$i] $buffer2;
	$objAttr=$buffer2[0];
	$objAttr=`asPoserResolveNameSpace $uiName $objAttr`;
	$restOfBuffers="";
	if ($objAttr!=$previousObjAttr)
		$firstValueSet=0;
	$previousObjAttr=$objAttr;
	for ($y=1;$y<size($buffer2);$y++)
		$restOfBuffers+=$buffer2[$y]+" ";
	if (`objExists $objAttr`)
		{
		tokenize $restOfBuffers $spaceBuffer;
		for ($y=0;$y<size($spaceBuffer);$y++)
			{
			//TimeOffset
			if ($spaceBuffer[$y]=="-t" && ($ctrlButton||$altButton))
				{
				$time=$spaceBuffer[$y+1];
				$spaceBuffer[$y+1]=$time+$currentTime;
				}
			//ValueOffset
			if ($spaceBuffer[$y]=="-v" && $ctrlButton)
				{
				$value=$spaceBuffer[$y+1];
				$currentValue=`getAttr $objAttr`;
				if (!$firstValueSet)
				$firstValue=$value;
				$firstValueSet=1;
				$spaceBuffer[$y+1]=$value+$currentValue-$firstValue;
				}
			}
		$restOfBuffers="";
		for ($y=0;$y<size($spaceBuffer);$y++)
			$restOfBuffers+=$spaceBuffer[$y]+" ";
		eval ($restOfBuffers+$objAttr);
		}
	else
		{
		$showWarning=1;
		$warningMsg+=$objAttr+",";
		}
	}
if ($showWarning)
	warning $warningMsg;
}

global proc asPoseView (string $uiName, int $childNum)
{
string $icon=`iconTextButton -q -i ($uiName+"IconTextButton"+$childNum)`;
int $anim=`rowColumnLayout -q -ann ($uiName+"RowColumnLayout"+$childNum)`;
string $tempString[];
tokenize $icon "/" $tempString;
string $projectName=$tempString[size($tempString)-2];

string $mediaFile;
if ($anim)
	$mediaFile=`substitute "[.][a-z][a-z][a-z]" $icon ".avi"`;
else
	$mediaFile=`substitute "[.][a-z][a-z][a-z]" $icon ".jpg"`;
print ("// "+$mediaFile+"\n");
system ("load "+$mediaFile);
}

global proc asPoseObjects (string $uiName, string $button,string $action)
{
string $cmd=`iconTextButton -q -c $button`;
string $buffer[],$buffer2[];
string $objAttr,$value;
tokenize $cmd "\"" $buffer;
$cmd=$buffer[1];
int $numTok=`tokenize $cmd ";" $buffer`;
int $showWarning;
string $warningMsg="The following objects can not be found:";
if ($action==" select -add")
select -cl;
for ($i=0;$i<$numTok;$i++)
	{
	tokenize $buffer[$i] $buffer2;
	$objAttr=$buffer2[0];
	$value=$buffer2[1];
	tokenize $objAttr "." $buffer2;
	$obj=$buffer2[0];
	if ($action!=" select -add")
		$obj=$objAttr;
	$obj=`asPoserResolveNameSpace $uiName $obj`;
	if (`objExists $obj`)
		eval ($action+" "+$obj);
	else
		{
		$showWarning=1;
		$warningMsg+=$obj+",";
		}
	}
if ($showWarning)
	warning $warningMsg;
}

global proc asMirrorOptions (string $uiName)
{
if (`window -q -ex asMirrorOptions`)
	deleteUI asMirrorOptions;
window -t "Mirror Options" asMirrorOptions;
columnLayout -adj 1;
	separator -h 25 -st "none";
	rowLayout -nc 4 -cw 1 40;
		text -l "side:";
		radioCollection asMOSideRadioCollection;
		radioButton -label "Swap" asMOSideFlip;
		radioButton -label "Right To Left" asMOSideR2L;
		radioButton -label "Left To Right" asMOSideL2R;
		setParent..;
	rowLayout -nc 4 -cw 1 40;
		text -l "axis:";
		radioCollection asMOAxisRadioCollection;
		radioButton -label "X" asMOAxisX;
		radioButton -label "Y" asMOAxisY;
		radioButton -label "Z" asMOAxisZ;
		radioCollection -e -sl "asMOAxisX" asMOAxisRadioCollection;
		setParent..;
	rowLayout -nc 4 -cw 1 40;
		text -l "space:";
		radioCollection asMOSpaceRadioCollection;
		radioButton -label "World" asMOSpaceWorld;
		radioButton -label "Main" asMOSpaceMain;
		radioButton -label "RootX_M" asMOSpaceCenter;
		radioCollection -e -sl "asMOSpaceWorld" asMOSpaceRadioCollection;
		setParent..;
	rowLayout -nc 4 -cw 1 40;
		text -l "control:";
		radioCollection asMOSelOnlyRadioCollection;
		radioButton -label "All" asMOSelOnlyAll;
		radioButton -label "Selected" asMOSelOnlySel;
		radioCollection -e -sl "asMOSelOnlyAll" asMOSelOnlyRadioCollection;
		setParent..;

separator -st "none" -h 25;
button -w 100 -l "Mirror" -c ("asMirror "+$uiName);
showWindow;

string $optionVars[]={"asMOSide","asMOSpace","asMOAxis","asMOSelOnly"};
string $cia[];
string $radioCollection,$selected,$optionVarString;
for ($i=0;$i<size($optionVars);$i++)
	{
	$radioCollection=$optionVars[$i]+"RadioCollection";
	$cia=`radioCollection -q -cia $radioCollection`;
	$selected=$cia[0];
	if (`optionVar -ex $optionVars[$i]`)
		{
		$optionVarString=`optionVar -q $optionVars[$i]`;
		for ($y=0;$y<size($cia);$y++)
			if (`gmatch $cia[$y] ("*"+$optionVarString)`)
				$selected=`optionVar -q $optionVars[$i]`;
		}
	radioCollection -e -sl $selected $radioCollection;
	}
}

global proc asMirror (string $uiName)
{
string $side="asMOSideR2L";
string $space="asMOSpaceMain";
string $axis="asMOAxisX";
string $selOnly="asMOSelOnlyAll";
if (`window -q -ex asMirrorOptions`)
	{
	$side=`radioCollection -q -sl asMOSideRadioCollection`;
	$space=`radioCollection -q -sl asMOSpaceRadioCollection`;
	$axis=`radioCollection -q -sl asMOAxisRadioCollection`;
	$selOnly=`radioCollection -q -sl asMOSelOnlyRadioCollection`;
	optionVar -sv asMOSide $side;
	optionVar -sv asMOSpace $space;
	optionVar -sv asMOAxis $axis;
	optionVar -sv asMOSelOnly $selOnly;
	}
if (`optionVar -ex asMOSide`)
	$side=`optionVar -q asMOSide`;
if (`optionVar -ex asMOSpace`)
	$space=`optionVar -q asMOSpace`;
if (`optionVar -ex asMOAxis`)
	$axis=`optionVar -q asMOAxis`;
if (`optionVar -ex asMOSelOnly`)
	$selOnly=`optionVar -q asMOSelOnly`;
string $sel[]=`ls -sl`;
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
string $controlSets[]=`asGetControlSetsFromUI $uiName`;
string $controls[]=`sets -q $controlSets`;
string $sortedControls[];

for ($i=0;$i<size($controls);$i++)
	if ($controls[$i]==($nameSpace+"Main"))
		$sortedControls={($nameSpace+"Main"),($nameSpace+"RootX_M"),($nameSpace+"RootExtraX_M")};

for ($i=0;$i<size($controls);$i++)
	if (`attributeExists "stiff" $controls[$i]`)
		$sortedControls[size($sortedControls)]=$controls[$i];

for ($i=0;$i<size($controls);$i++)
	if (`attributeExists "followEnd" $controls[$i]`)
		$sortedControls[size($sortedControls)]=$controls[$i];	

for ($i=0;$i<size($controls);$i++)
	if (`gmatch $controls[$i] ($nameSpace+"IK*Spine*")`)
		$sortedControls[size($sortedControls)]=$controls[$i];

for ($i=0;$i<size($controls);$i++)
	if (`gmatch $controls[$i] ($nameSpace+"IK*Leg*")` || `gmatch $controls[$i] ($nameSpace+"IK*Arm*")`)
		$sortedControls[size($sortedControls)]=$controls[$i];

for ($i=0;$i<size($controls);$i++)
	if (`gmatch $controls[$i] ($nameSpace+"Pole*")`)
		$sortedControls[size($sortedControls)]=$controls[$i];

for ($i=0;$i<size($controls);$i++)
	if (`gmatch $controls[$i] ($nameSpace+"Roll*Heel*")`)
		$sortedControls[size($sortedControls)]=$controls[$i];
for ($i=0;$i<size($controls);$i++)
	if (`gmatch $controls[$i] ($nameSpace+"Roll*ToesEnd*")`)
		$sortedControls[size($sortedControls)]=$controls[$i];
for ($i=0;$i<size($controls);$i++)
	if (`gmatch $controls[$i] ($nameSpace+"Roll*Toes*")`)
		$sortedControls[size($sortedControls)]=$controls[$i];

for ($x=10;$x>0;$x--)
	for ($i=0;$i<size($controls);$i++)
		if (`gmatch $controls[$i] ($nameSpace+"Roll*"+$x+"*")`)
			$sortedControls[size($sortedControls)]=$controls[$i];

for ($i=0;$i<size($controls);$i++)
	if (`gmatch $controls[$i] ($nameSpace+"IK*Toes_*")`)
		$sortedControls[size($sortedControls)]=$controls[$i];

for ($i=0;$i<size($controls);$i++)
	if (!`stringArrayCount $controls[$i] $sortedControls`)
		$sortedControls[size($sortedControls)]=$controls[$i];

$controls=$sortedControls;
if ($selOnly=="asMOSelOnlySel")
	$controls=$sel;
string $allKeyableAttrs[],$tempString[];
string $source,$dest,$destAttr,$cmd,$loc1,$loc2;
float $pos[3],$rot[3],$pos2[3],$rot2[3];
int $wsXform[];
int $flip,$isFaceControl,$isClusterControl,$isClusterLocalOrientControl,$wsXformAttr;
string $flipAxis,$t0;
if ($axis=="asMOAxisX")
	{$flipAxis="X";$t0="tx";}
if ($axis=="asMOAxisY")
	{$flipAxis="Y";$t0="ty";}
if ($axis=="asMOAxisZ")
	{$flipAxis="Z";$t0="tz";}

createNode -n flipGroup transform;
if ($space=="asMOSpaceCenter")
	parent flipGroup ($nameSpace+"RootX_M");
if ($space=="asMOSpaceMain")
	parent flipGroup ($nameSpace+"Main");
xform -os -t 0 0 0 -ro 0 0 0 flipGroup;
if ($space=="asMOSpaceCenter" || $space=="asMOSpaceMain")
	parent -w flipGroup;
for ($i=0;$i<size($controls);$i++)
	{
	if ($side!="asMOSideFlip")
		if (`gmatch $controls[$i] "*_M"` || `gmatch $controls[$i] "*Main"`)
			continue;
	if ($side=="asMOSideR2L")
		{
		if (`gmatch $controls[$i] "*_L"`)
			continue;
		$dest=`substitute "_R" $controls[$i] "_L"`;
		}
	else if ($side=="asMOSideL2R")
		{
		if (`gmatch $controls[$i] "*_R"`)
			continue;
		$dest=`substitute "_L" $controls[$i] "_R"`;
		}
	else if ($side=="asMOSideFlip")
		{
		if (!`gmatch $controls[$i] "*_L"` && !`gmatch $controls[$i] "*_R"` && !`gmatch $controls[$i] "*_M"`)
			continue;
		if (`gmatch $controls[$i] "*FKExtraSpine1_M"`)
			continue;
		if (`gmatch $controls[$i] "*_L"`)
			$dest=`substitute "_L" $controls[$i] "_R"`;
		if (`gmatch $controls[$i] "*_R"`)
			$dest=`substitute "_R" $controls[$i] "_L"`;
		if (`gmatch $controls[$i] "*_M"`)
			$dest=$controls[$i];
		}
	if (!`objExists $dest`)
		continue;
	$isFaceControl=0;
	if (`objExists ($nameSpace+"FaceControlSet")`)
		if (`sets -im ($nameSpace+"FaceControlSet") $controls[$i]`)
			$isFaceControl=1;
	$isClusterControl=$isClusterLocalOrientControl=0;
	$tempString=`ls -l $controls[$i]`;
	if (`gmatch $tempString[0] "*CustomSystem|*"`)
		$isClusterControl=1;
	if ($isClusterControl)
		if (`attributeExists localOrient $controls[$i]`)
			$isClusterLocalOrientControl=1;
	$source=$controls[$i];
	$allKeyableAttrs=`listAttr -k -m -sn $controls[$i]`;
	for ($y=0;$y<size($allKeyableAttrs);$y++)
		{
		$destAttr=$allKeyableAttrs[$y];
		if ($destAttr=="v") continue;
		if (!`attributeExists $allKeyableAttrs[$y] $dest`) continue;//no matching attr on other side
		if			(`gmatch $destAttr "*_R*"`) $destAttr=`substitute "_R" $destAttr "_L"`;
		else if (`gmatch $destAttr "*_L*"`) $destAttr=`substitute "_L" $destAttr "_R"`;		
		$flip=1;
		if (`gmatch $controls[$i] ($nameSpace+"FK*_L")` || `gmatch $controls[$i] ($nameSpace+"FK*_R")`
		 || `gmatch $controls[$i] ($nameSpace+"Bend*_L")` || `gmatch $controls[$i] ($nameSpace+"Bend*_R")`
		 || `attributeExists ikLocal $controls[$i]`
		 || $isClusterLocalOrientControl)
		 	{
		 	if ($destAttr=="tx" || $destAttr=="ty" || $destAttr=="tz")
		 		$flip=-1;
		 	}
		else
			if ($destAttr=="tz" || $destAttr=="rx" || $destAttr=="ry")
				$flip=-1;
		if ($isFaceControl)
			$flip=1;
/*
		if (($controls[$i]==$nameSpace+"Eye_R" || $controls[$i]==$nameSpace+"Eye_L") && $destAttr== "ry")
			$flip=-1;//special case for eyes, as we dont need `crosseyed`
*/
		$wsXformAttr=0;
		if (`gmatch $controls[$i] ($nameSpace+"IK*")` || `gmatch $controls[$i] ($nameSpace+"Pole*")`
		|| `gmatch $controls[$i] ($nameSpace+"Roll*")`
		|| `gmatch $controls[$i] ($nameSpace+"RootX_M*")` || ($isClusterControl && !$isClusterLocalOrientControl))
			if ((!`gmatch $controls[$i] ($nameSpace+"IKLocal*")`) && !`attributeExists ikLocal $controls[$i]`)
				$wsXform[$i]=1;
		if (`gmatch $controls[$i] ($nameSpace+"Aim*")`)
			continue;
		if ($destAttr=="tx" || $destAttr=="ty" || $destAttr=="tz"
			||$destAttr=="rx" || $destAttr=="ry" || $destAttr=="rz")
			if ($wsXform[$i])
				$wsXformAttr=1;
		if (!$wsXformAttr)
			if (`getAttr -se ($dest+"."+$destAttr)`)
				$cmd+="setAttr "+$dest+"."+$destAttr+" "+(`getAttr ($source+"."+$allKeyableAttrs[$y])`*$flip)+";";
		}

	if ($wsXform[$i])
		{
		$tempString=`spaceLocator`;
		$loc1=$tempString[0];
		$tempString=`spaceLocator`;
		$loc2=$tempString[0];
		parent $loc2 $loc1;
		parent $loc1 $source;
		xform -os -t 0 0 0 -ro 0 0 0 $loc1;
		setAttr ($loc1+".rotateOrder") `getAttr ($source+".rotateOrder")`;
		setAttr ($loc2+".rotateOrder") `getAttr ($source+".rotateOrder")`;
		setAttr ("flipGroup.scale"+$flipAxis) 1;
		parent $loc1 flipGroup;
		setAttr ("flipGroup.scale"+$flipAxis) -1;
		setAttr ($loc1+".scaleX") -1;
		$pos=`xform -q -ws -t $loc2`;
		$rot=`xform -q -ws -ro $loc2`;
		$pos2=`getAttr ($source+".t")`;
		$rot2=`getAttr ($source+".r")`;
		$cmd+="xform -ws -t "+$pos[0]+" "+$pos[1]+" "+$pos[2]+" -ro "+$rot[0]+" "+$rot[1]+" "+$rot[2]+" "+$dest+";";
		}

	if (`attributeExists "mirror" $controls[$i]`)
		setAttr ($controls[$i]+".mirror") 0;
	}
delete flipGroup;
if ($cmd!="")
	eval ($cmd);
select $sel;
}

global proc asGoToBuildPoseOptions (string $nameSpace, string $controlSet)
{
if (`objExists asGoToBuildPoseOptions`)
	delete asGoToBuildPoseOptions;
createNode -n asGoToBuildPoseOptions transform;
addAttr -ln nameSpace -dt "string" asGoToBuildPoseOptions;
setAttr -type "string" asGoToBuildPoseOptions.nameSpace $nameSpace;
addAttr -ln controlSet -dt "string" asGoToBuildPoseOptions;
setAttr -type "string" asGoToBuildPoseOptions.controlSet $controlSet;
asGoToBuildPose bodySetup;
}

global proc asGoToBuildPose (string $uiName)
{
int $ctrlButton;
if ((`getModifiers`/4) %  2)
	$ctrlButton=1;
string $nameSpace;
string $controlSets[];
if ($uiName=="bodySetup")
	{
	$nameSpace="";
	$controlSets[0]="ControlSet";
	}
else if ($uiName=="faceSetup")
	{
	$nameSpace="";
	$controlSets[0]="FaceControlSet";
	}
else
	{
	$nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
	$controlSets=`asGetControlSetsFromUI $uiName`;
	}
if ($nameSpace==":")
	$nameSpace="";

if (`objExists asGoToBuildPoseOptions`)
	{
	$nameSpace=`getAttr asGoToBuildPoseOptions.nameSpace`;
	$controlSets[0]=`getAttr asGoToBuildPoseOptions.controlSet`;
	delete asGoToBuildPoseOptions;
	}

string $buildPose="buildPose";
if (`gmatch $controlSets[0] "*FaceControlSet"`)
    $buildPose="faceBuildPose";

string $tempString[],$tempString2[],$buffer[];
string $setAttrCmd,$cmd;


$setAttrCmd=`getAttr ($nameSpace+$buildPose+".udAttr")`;
tokenize $setAttrCmd ";" $tempString;
for ($y=0;$y<size($tempString);$y++)
	{
	$cmd=$tempString[$y];
	if ($cmd=="")
		continue;
	if ($nameSpace!="")
		{
		tokenize $cmd $buffer;
		if (`gmatch $cmd "xform*"`)
			$substituteWordNr=size($buffer)-1;
		else
			$substituteWordNr=1;
		$cmd="";
		for ($z=0;$z<size($buffer);$z++)
			{
			if ($z==$substituteWordNr)
				$cmd+=$nameSpace;
			$cmd+=$buffer[$z]+" ";
			}
		}
	if ($ctrlButton)
		if (`gmatch $cmd "*Main.*"`)
			continue;
	if (catchQuiet (`eval ($cmd)`)) warning ("Failed: "+$cmd+"\n");
	}

//run
string $run,$fitTopNode,$objAttr;
for ($i=0;$i<size($controlSets);$i++)
	{
	if (`gmatch $controlSets[$i] "*ControlSet"`)
		if (`objExists ($nameSpace+"FitSkeleton")`)
			$fitTopNode=$nameSpace+"FitSkeleton";
	if (`gmatch $controlSets[$i] "*FaceControlSet"`)
		$fitTopNode=($nameSpace+"FaceFitSkeleton");
	}

if (`objExists $fitTopNode`)
	if (`attributeExists "run" $fitTopNode`)
		{
		$run=`getAttr ($fitTopNode+".run")`;
		if ($run!="")
			{
			if ($nameSpace=="")
				catch (`eval ($run)`);
			else
				{
				tokenize $run ";" $tempString;
				for ($i=0;$i<size($tempString);$i++)
					{
					tokenize $tempString[$i] $tempString2;
					$objAttr=`substitute "\"" $tempString2[1] ""`;
					$objAttr=`substitute "\"" $objAttr ""`;
					$cmd=$tempString2[0]+" "+$nameSpace+$objAttr+" "+$tempString2[2]+";";
					catch (`eval ($cmd)`);
					}
				}
			}
		}
}

global proc asPoserupdateGridBlock (string $uiName, int $childNum)
{
string $existingPopUpMenus[]=`control -q -pma ($uiName+"IconTextButton"+$childNum)`;
for ($pop in $existingPopUpMenus)
	deleteUI $pop;

int $anim=`rowColumnLayout -q -ann ($uiName+"RowColumnLayout"+$childNum)`;
string $button=$uiName+"IconTextButton"+$childNum;
string $viewCmd="asPoseView "+$uiName+" "+$childNum;
string $selectCmd="asPoseObjects "+$uiName+" "+$button+"\" select -add\"";
string $keyCmd="asPoseObjects "+$uiName+" "+$button+" setKeyframe";
string $linearKeyCmd="asPoseObjects "+$uiName+" "+$button+" \"setKeyframe -itt linear -ott linear\"";

popupMenu -p ($uiName+"IconTextButton"+$childNum);
	menuItem -l "View" -c $viewCmd;
	if (!$anim)
		{
		menuItem -d 1;
		menuItem -l "Select" -c $selectCmd;
		menuItem -l "Key" -c $keyCmd;
		menuItem -l "LinearKey" -c $linearKeyCmd;
		}
	if ($uiName=="asPoserDefault")
		{
		menuItem -d 1;
		menuItem -l "Label Button" -c ("asPoserRename "+$childNum);
		menuItem -l "Remove Button" -c ("asPoserDeletePose "+$uiName+" "+$childNum);
		menuItem -l "Update icon" -c ("asPoserSnapShoot "+$uiName+" "+$childNum+" "+$anim+" 1 0");
		if ($anim)
			menuItem -l "Update movie" -c ("asPoserSnapShoot "+$uiName+" "+$childNum+" "+$anim+" 0 1");
		}
}

global proc string asPoserResolveNameSpace (string $uiName, string $obj)
{
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
return ($nameSpace+$obj);
}

global proc asDynRemove (string $uiName)
{
string $sel[]=`ls -sl`;
string $dynAttrs[]={"blend","baseGoal","tipGoal","conserve"};
string $tempString[];
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
string $dynObj;
if ($nameSpace==":")
	$nameSpace="";
if (!size($sel))
	error ("nothing selected");
for ($i=0;$i<size($sel);$i++)
	if (!`gmatch $sel[$i] ($nameSpace+"FK*")`)
		error ($sel[$i]+" is not a FK control");
for ($i=0;$i<size($sel);$i++)
	{
	$dynObj="dynamics"+`substitute ($nameSpace) $sel[$i] ""`;
	if(`objExists $dynObj`)
		{
		delete $dynObj;
		for ($y=0;$y<size($dynAttrs);$y++)
			if (`attributeExists $dynAttrs[$y] $sel[$i]`)
				deleteAttr ($sel[$i]+"."+$dynAttrs[$y]);
		$tempString=`listRelatives -c Dynamics`;
		if (!size($tempString))
			delete Dynamics;
		print ("// Dynamics for "+$sel[$i]+" removed\n");
		}
	else
		print ("// No dynamics for "+$sel[$i]+" found\n");
	}
}

global proc asDynAdd (string $uiName)
{
int $numCv,$form,$spans,$degrees,$numChar;
float $pos[],$posA[],$posB[];
string $sel[]=`ls -sl`;
string $selLong[]=`ls -sl -l`;
string $tempString[],$dynJoints[],$sorted[],$defJoints[],$dynJoints[],$deleteObjs[];
string $defJoint,$connectTo,$side,$sideLess,$curveCmd;
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
if (!size($sel))
	error ("nothing selected");
string $name=`substitute $nameSpace $sel[0] ""`;
string $dynTopNode="dynamics"+$name;
if (`objExists $dynTopNode`)
	error ("Object \""+$dynTopNode+"\" already exists. Delete this first, if you wish to make a new one.");

//sort by lenght of `long` name, to sort by parents befor children
for ($y=0;$y<9999;$y++)
	{
	for ($i=0;$i<size($sel);$i++)
		if (size($selLong[$i])==$y)
			$sorted[size($sorted)]=$sel[$i];
	if (size($sorted)==size($sel))
		break;
	}

for ($i=0;$i<size($sorted);$i++)
	{
	$defJoint="";
	if (`gmatch $sorted[$i] ($nameSpace+"FK*")`)
		$defJoint=`substitute ($nameSpace+"FK") $sorted[$i] ($nameSpace)`;
	if (!`objExists $defJoint`)
		error ("Unable to find DeformJoint for:\""+$sorted[$i]+"\".\n");
	$defJoints[size($defJoints)]=$defJoint;

	//include part joints
	$numChar=size($defJoint);
	$side=`substring $defJoint ($numChar-1) $numChar`;
	$sideLess=`substring $defJoint 1 ($numChar-2)`;
	for ($y=1;$y<99;$y++)
		{
		if (`objExists ($sideLess+"Part"+$y+$side)`)
			$defJoints[size($defJoints)]=($sideLess+"Part"+$y+$side);
		else
			break;
		}
	}
//include `end joint`
$tempString=`listRelatives -c -type joint $defJoints[size($defJoints)-1]`;
if ($tempString[0]!="")
	$defJoints[size($defJoints)]=$tempString[0];

$deleteObjs={("DynParticleArrayMapper"+$name),("DynParticleRamp"+$name),("DynIKEffector"+$name)};
for ($i=0;$i<size($deleteObjs);$i++)
	if (`objExists ($nameSpace+$deleteObjs[$i])`)
		delete ($nameSpace+$deleteObjs[$i]);

if (!`objExists "Dynamics"`)
	createNode -n Dynamics transform;
createNode -n $dynTopNode -p Dynamics transform;

$curveCmd="curve -d 1 ";
for ($i=0;$i<size($defJoints);$i++)
	{
	$pos=`xform -q -ws -t $defJoints[$i]`;
	$curveCmd+=" -p "+$pos[0]+" "+$pos[1]+" "+$pos[2]+" ";
	}
$tempString[0]=`eval ($curveCmd)`;
rename $tempString[0] ("DynCurve"+$name);
parent ("DynCurve"+$name) $dynTopNode;

//soft
$tempString=`soft -d -g 1 -c ("DynCurve"+$name)`;
rename $tempString[0] ("DynParticle"+$name);
$tempString=`listRelatives -p ("DynParticle"+$name)`;
rename $tempString[0] ("DynCurveSoft"+$name);
rename ("copyOfDynCurve"+$name)  ("DynCurve"+$name);
parent ("DynCurveSoft"+$name) $dynTopNode;
setAttr ("DynCurve"+$name+".v") 0;
setAttr ("DynCurveSoft"+$name+".dispCV") 1;

parentConstraint -mo ($nameSpace+$name) ("DynCurve"+$name);

//$numCv
$form=`getAttr ("DynCurve"+$name+".form")`;
$spans=`getAttr ("DynCurve"+$name+".spans")`;
$degrees=`getAttr ("DynCurve"+$name+".degree")`;
if ($form==2)
	$numCv=$spans;
else
	$numCv=$spans+$degrees;

//mass
for ($i=0;$i<$numCv;$i++)
	{
	$massPP = (1-((1.0/$numCv)*$i));
	particle -e -or $i -at mass -fv $massPP ("DynParticle"+$name);
	}

//arrayMapper
$tempString=`arrayMapper -target ("DynParticle"+$name) -destAttr goalPP -inputV mass -type ramp`;
rename $tempString[0] ("DynParticleArrayMapper"+$name);

$tempString=`listConnections -s 1 -d 0 ("DynParticleArrayMapper"+$name+".computeNodeColor")`;
rename $tempString[0] ("DynParticleRamp"+$name);
removeMultiInstance -break true ("DynParticleRamp"+$name+".colorEntryList[1]");
setAttr ("DynParticleRamp"+$name+".colorEntryList[0].position") 0;
setAttr ("DynParticleRamp"+$name+".colorEntryList[1].position") 1;
setAttr ("DynParticleRamp"+$name+".colorEntryList[0].color") -type double3 0 0 0;
setAttr ("DynParticleRamp"+$name+".colorEntryList[1].color") -type double3 1 1 1;

if (!`attributeExists blend $sorted[0]`) addAttr -k 1 -ln blend -at double -min 0 -max 1 -dv 1 $sorted[0];
if (!`attributeExists baseGoal $sorted[0]`) addAttr -k 1 -ln baseGoal -at double -min 0 -max 1 -dv 1 $sorted[0];
if (!`attributeExists tipGoal $sorted[0]`) addAttr -k 1 -ln tipGoal -at double -min 0 -max 1 -dv 0.6 $sorted[0];
if (!`attributeExists conserve $sorted[0]`) addAttr -k 1 -ln conserve -at double -min 0 -max 1 -dv 0.75 $sorted[0];
connectAttr ($sorted[0]+".baseGoal") ("DynParticleArrayMapper"+$name+".maxValue");
connectAttr ($sorted[0]+".tipGoal") ("DynParticleArrayMapper"+$name+".minValue");
connectAttr ($sorted[0]+".conserve") ("DynParticle"+$name+".conserve");

//splineIK
createNode -n ($defJoints[0]+"Offset") -p $dynTopNode transform;
$tempString=`listRelatives -p $defJoints[0]`;
parentConstraint $tempString[0] ($defJoints[0]+"Offset");
select $defJoints[0];
for ($i=0;$i<$numCv;$i++)
	{
	$dynJoints[$i]="dyn"+`substitute $nameSpace $defJoints[0] ""`+$i;
	joint -n $dynJoints[$i];
	$pos=`xform -q -ws -t ("DynCurveSoft"+$name+".cv["+$i+"]")`;
	xform -ws -t $pos[0] $pos[1] $pos[2] $dynJoints[$i];
	}
parent $dynJoints[0] ($defJoints[0]+"Offset");
$tempString=`ikHandle -n ("DynIKHandle"+$name) -ns 2 -sol ikSplineSolver -sj $dynJoints[0] -ee $dynJoints[size($dynJoints)-1]`;
rename $tempString[1] ("DynIKEffector"+$name);
string $ikCurve=`rename $tempString[2] ("DynIKCurve"+$name)`;
parent ("DynIKHandle"+$name) ("DynIKCurve"+$name) $dynTopNode;
connectAttr -f ("DynCurveSoft"+$name+".worldSpace[0]") ("DynIKCurve"+$name+".create");

//pairBlend
for ($i=0;$i<size($defJoints);$i++)
	{
	$connectTo=$defJoints[$i];
	if ($i>0)
		{
		$tempString[0]=`pairBlend -nd $connectTo -at tx -at ty -at tz`;
		rename $tempString[0] ("parBlendT"+$dynJoints[$i]);
		connectAttr -f ($dynJoints[$i]+".translate") ("parBlendT"+$dynJoints[$i]+".inTranslate2");
		connectAttr ($sorted[0]+".blend") ("parBlendT"+$dynJoints[$i]+".weight");
		}
	$tempString[0]=`pairBlend -nd $connectTo -at rx -at ry -at rz`;
	rename $tempString[0] ("parBlendR"+$dynJoints[$i]);
	connectAttr -f ($dynJoints[$i]+".rotate") ("parBlendR"+$dynJoints[$i]+".inRotate2");
	connectAttr ($sorted[0]+".blend") ("parBlendR"+$dynJoints[$i]+".weight");
	}

setAttr -l 1 ("DynIKHandle"+$name+".v") 0;

print "// Dynamics added.\n";
select $sorted[0];
}

global proc asParentAdd (string $uiName, int $extra)
{
string $sel[]=`ls -sl`;
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
string $tempString[];
string $child;

if (size($sel)<2) error "Select at least 2 objects";

string $parent=$sel[size($sel)-1];
print ("$parent="+$parent+"\n");
for ($i=0;$i<size($sel)-1;$i++)
	{
	$child=$sel[$i];
	if ($extra)
		{
		$tempString=`listRelatives -p $child`;
		if (`gmatch $tempString[0] "*Extra*"`)
			$child=$tempString[0];
		else
			print ("// No Extra control found for \""+$child+"\".\n");
		}
	$tempString=`parentConstraint -mo $parent $child`;
	parent -w $tempString[0];
	}
}

global proc asAutoSwitchFKIK ()
{
int $foundIK;
string $sel[]=`ls -sl`;
string $name,$nameSpace,$fkIkCtrl,$side,$ik2fk;
float $curFKIKBlend;
string $tempString[],$tempString2[],$fkIkCtrls[];
print "asAutoSwitchFKIK;\n";
for ($i=0;$i<size($sel);$i++)
	{
	$foundIK=0;
  if (`gmatch $sel[$i] "*FKIK*"`)
      {
      $fkIkCtrls[size($fkIkCtrls)]=$sel[$i];
      $foundIK=1;
      break;
      }
  if (!$foundIK)
  	{
	  $tempString[0]=$sel[$i];
	  for ($y=0;$y<99;$y++)
			{
			$tempString=`listRelatives -p $tempString[0]`;
			if ($tempString[0]=="") break;
			$tempString2=`listConnections -s 1 -d 0 ($tempString[0]+".v")`;
			if ($tempString2[0]=="") continue;
			if (`objectType $tempString2[0]`!="condition") continue;
			$tempString2=`listConnections -s 1 -d 0 ($tempString2[0]+".firstTerm")`;
			if ($tempString2[0]=="") continue;
			$fkIkCtrls[size($fkIkCtrls)]=$tempString2[0];
			$foundIK=1;
			break;
			}
		}
  if (!$foundIK)
		{
		$tempString=`listRelatives -s $sel[$i]`;
		if ($tempString[0]!="")
			$tempString=`listConnections -s 1 -d 0 ($tempString[0]+".v")`;

		if (`objectType $tempString[0]`=="plusMinusAverage")
			{//IKhybrid ctrl
			$tempString=`listConnections -sh 1 -s 1 -d 0 ($tempString[0]+".input1D[0]")`;
			if ($tempString[0]!="")
				$tempString=`listConnections -s 1 -d 0 ($tempString[0]+".v")`;
			}

		if (`objectType $tempString[0]`=="condition")
			{
			$tempString2=`listConnections -s 1 -d 0 ($tempString[0]+".firstTerm")`;
			if ($tempString2[0]!="")
				{
				$fkIkCtrls[size($fkIkCtrls)]=$tempString2[0];
				$foundIK=1;
				break;
				}
			}
			{
			$tempString2=`listConnections -s 1 -d 0 ($tempString[0]+".firstTerm")`;
			if ($tempString2[0]!="")
				{
				$fkIkCtrls[size($fkIkCtrls)]=$tempString2[0];
				$foundIK=1;
				break;
				}
			}
		}
	}
if (size($fkIkCtrls)==0)
    error "First select a control that is part of FK/IK setup";
for ($i=0;$i<size($fkIkCtrls);$i++)
print ($i+" : "+$fkIkCtrls[$i]+"\n");
for ($i=0;$i<size($fkIkCtrls);$i++)
    {
		$nameSpace="";
		$fkIkCtrl="";
		$side="";
		$name=$fkIkCtrls[$i];
		tokenize $fkIkCtrls[$i] ":" $tempString;
		if (size($tempString)>1)
			{
			for ($y=0;$y<size($tempString)-1;$y++)
				$nameSpace+=$tempString[$y]+":";
			$name=$tempString[size($tempString)-1];
			}
		tokenize $name "_" $tempString;
		$fkIkCtrl=$tempString[0];
		$side="_"+$tempString[1];
		$curFKIKBlend=`getAttr ($fkIkCtrls[$i]+".FKIKBlend")`;
		if ($curFKIKBlend>0 && $curFKIKBlend<5) setAttr ($fkIkCtrls[$i]+".FKIKBlend") 0;
		if ($curFKIKBlend>5 && $curFKIKBlend<10) setAttr ($fkIkCtrls[$i]+".FKIKBlend") 10;
		if ($curFKIKBlend>5) $ik2fk="IK2FK";
		else $ik2fk="FK2IK";
    asSwitchFKIK $fkIkCtrls[$i] `substitute "FKIK" $fkIkCtrl ""` $side $ik2fk;
    }
}

global proc asAutoSwitchPivot ()
{
string $sel[]=`ls -sl`;
if (size($sel)!=2)
	error "Select 1 target object + 1 control";
string $tempString[];
string $target=$sel[0];
string $control=$sel[1];
string $extraControl;
if (!`gmatch $control "*_*"` && `gmatch $target "*_*"`)//selection order was reversed
	{
	$target=$sel[1];
	$control=$sel[0];
	}
if (`gmatch $control "*Extra*"`)//extraControl was selected
	{
	$tempString=`listRelatives -c -type transform $control`;
	$control=$tempString[0];
	}
$tempString=`listRelatives -p $control`;
$extraControl=$tempString[0];
if (!`objExists $extraControl`)
	error ("parent of \""+$control+"\" is not a valid ExtraControl");
float $pos[]=`xform -q -ws -t $control`;
float $rot[]=`xform -q -ws -ro $control`;
float $extraPos[]=`xform -q -ws -t $extraControl`;
float $extraRot[]=`xform -q -ws -ro $extraControl`;
float $targetPos[]=`xform -q -ws -t $target`;
float $targetRot[]=`xform -q -ws -ro $target`;

print "asAutoSwitchPivot;\n";

int $autoKey=`autoKeyframe -q -st`;
if ($autoKey)
	autoKeyframe -st 0;
currentTime (`currentTime -q` -1);
setKeyframe ($extraControl+".t") ($extraControl+".r") ($control+".t") ($control+".r");

currentTime (`currentTime -q` +1);
xform -ws -t $targetPos[0] $targetPos[1] $targetPos[2] -ro $targetRot[0] $targetRot[1] $targetRot[2] $extraControl;
xform -ws -t $pos[0] $pos[1] $pos[2] -ro $rot[0] $rot[1] $rot[2] $control;
setKeyframe ($extraControl+".t") ($extraControl+".r") ($control+".t") ($control+".r");

if ($autoKey)
	autoKeyframe -st 1;

select $extraControl;
print ("// \""+$extraControl+"\" aligned to \""+$target+"\".\n");
}

global proc asQuickIK ()
{
int $otherEndJointAssumed;
float $dist;
float $posA[],$posB[];
string $startJoint,$endJoint,$ctrlPrefix,$startJointParent,$rotateAxis,$name,$ctrl;
string $sel[]=`ls -sl`;
string $joints[],$tempString[],$chainJointsReverse[],$chainJoints[],$chainJointNames[];
if (size($sel)!=2)
	error "Select Start control + End control";

for ($i=0;$i<2;$i++)
	{
	$joints[$i]=$sel[$i];
	$tempString[0]=`substitute "FK" $joints[$i] ""`;//Fk control
	if (`objExists $tempString[0]`)
		{
		$joints[$i]=$tempString[0];
		$ctrlPrefix="FK";
		}
	$tempString[0]=`substitute "_" $joints[$i] "Joint_"`;// Face control e.g tongue
	if (`objExists $tempString[0]`)
		{
		$joints[$i]=$tempString[0];
		$ctrlPrefix="";
		}
	}

$tempString=`listRelatives -ad -type joint $joints[0]`;
if (`stringArrayCount $joints[1] $tempString`)
	{
	$startJoint=$joints[0];
	$endJoint=$joints[1];
	}

$tempString=`listRelatives -ad -type joint $joints[1]`;
if (`stringArrayCount $joints[0] $tempString`)
	{
	$startJoint=$joints[1];
	$endJoint=$joints[0];
	}
if ($startJoint=="" || $endJoint=="")
	error "Unable to find Start & End joint for the IK, make sure to select 2 control that are in the same hierarchy.\n";

$name=`substituteAllString $startJoint ":" "_"`;
$tempString=`listRelatives -p $startJoint`;
$startJointParent=$tempString[0];

//if childJoint of $endJoint is a `actual` endJoint, then assume this was meant to be used.
$tempString=`listRelatives -ad -type joint $endJoint`;
if (size ($tempString)==1)
	{
	$endJoint=$tempString[0];
	$otherEndJointAssumed=1;
	}

//determine $chainJoints
$tempString=`ls -l $endJoint`;
tokenize $tempString[0] "|" $tempString;
for ($i=size($tempString)-1;$i>-1;$i--)
	{
	$chainJointsReverse[size($chainJointsReverse)]=$tempString[$i];
	if ($tempString[$i]==$startJoint)
		break;
	}
for ($i=size($chainJointsReverse)-1;$i>-1;$i--)
	$chainJoints[size($chainJoints)]=$chainJointsReverse[$i];

for ($i=0;$i<size($chainJoints);$i++)
	if (`objExists ("QuickIKjoint_"+$chainJoints[$i])`)
		error ("Another QuickIK already using the Joint:\"QuickIKjoint_"+$chainJoints[$i]+"\"\n");

$rotateAxis="z";
if (`gmatch $startJoint "*Finger*"`)
	$rotateAxis="y";

print ("StartJoint: "+$startJoint+", EndJoint: "+$endJoint+"\n");
if (!`objExists QuickIK`)
	createNode -n QuickIK transform;
if (`objExists ("QuickIK_"+$name+"Group")`)
	delete ("QuickIK_"+$name+"Group");
createNode -n ("QuickIK_"+$name+"Group") -p $startJoint transform;
parent ("QuickIK_"+$name+"Group") QuickIK;

for ($i=0;$i<size($chainJoints);$i++)
	$chainJointNames[$i]=`substituteAllString $chainJoints[$i] ":" "_"`;

for ($i=0;$i<size($chainJoints);$i++)
	{
	select $chainJoints[$i];
	joint -n ("QuickIKjoint_"+$chainJointNames[$i]);
	setAttr ("QuickIKjoint_"+$chainJointNames[$i]+".rotateOrder") `getAttr ($chainJoints[$i]+".rotateOrder")`;
	if ($rotateAxis=="y")
		setAttr ("QuickIKjoint_"+$chainJointNames[$i]+".preferredAngle") -type float3 0 10 0;
	if ($i>0)
		parent ("QuickIKjoint_"+$chainJointNames[$i]) ("QuickIKjoint_"+$chainJointNames[$i-1]);
	}

parent ("QuickIKjoint_"+$name) ("QuickIK_"+$name+"Group");
parentConstraint -mo $startJointParent ("QuickIKjoint_"+$name);

$posA=`xform -q -ws -t $startJoint`;
$posB=`xform -q -ws -t $endJoint`;
$dist=`mag<<$posA[0]-$posB[0],$posA[1]-$posB[1],$posA[2]-$posB[2]>>`;

spaceLocator -n ("QuickIK_"+$name);
parent ("QuickIK_"+$name) ("QuickIK_"+$name+"Group");
setAttr ("QuickIK_"+$name+"Shape.overrideEnabled") 1;
setAttr ("QuickIK_"+$name+"Shape.overrideColor") 13;
setAttr ("QuickIK_"+$name+"Shape.localScale") -type float3 ($dist/2.0) ($dist/2.0) ($dist/2.0);
duplicate -n ("QuickPoleVector_"+$name) ("QuickIK_"+$name);
setAttr ("QuickPoleVector_"+$name+"Shape.localScale") -type float3 ($dist/4.0) ($dist/4.0) ($dist/4.0);
xform -ws -t $posB[0] $posB[1] $posB[2] ("QuickIK_"+$name);

ikHandle -n ("QuickIK_"+$name+"Handle") -ns 2 -sol "ikRPsolver" -sj ("QuickIKjoint_"+$chainJointNames[0]) -ee ("QuickIKjoint_"+$chainJointNames[size($chainJointNames)-1]);
parent ("QuickIK_"+$name+"Handle") ("QuickIK_"+$name);
setAttr ("QuickIK_"+$name+"Handle.v") 0;

parent ("QuickPoleVector_"+$name) $startJoint;
xform -os -t 0 0 0 -ro 0 0 0 ("QuickPoleVector_"+$name);
if ($rotateAxis=="y")
	setAttr ("QuickPoleVector_"+$name+".tz") $dist;
if ($rotateAxis=="z")
	setAttr ("QuickPoleVector_"+$name+".ty") $dist;

//poleVector
poleVectorConstraint ("QuickPoleVector_"+$name) ("QuickIK_"+$name+"Handle");
$tempString[0]=`createNode annotationShape`;
$tempString=`listRelatives -p $tempString[0]`;
rename $tempString[0] ("QuickPoleAnnotation_"+$name);
setAttr ("QuickPoleAnnotation_"+$name+"Shape.overrideEnabled") 1;
setAttr ("QuickPoleAnnotation_"+$name+"Shape.overrideDisplayType") 2;
parent ("QuickPoleAnnotation_"+$name) ("QuickIK_"+$name);
xform -os -t 0 0 0 -ro 0 0 0  ("QuickPoleAnnotation_"+$name);
connectAttr ("QuickPoleVector_"+$name+"Shape.worldMatrix[0]") ("QuickPoleAnnotation_"+$name+"Shape.dagObjectMatrix[0]");
//move forward to match IkHandle pos
setAttr ("QuickPoleVector_"+$name+".tx") `getAttr ("QuickIK_"+$name+".tx")`;

//Offsets
createNode -n ("QuickIKOffset_"+$name) -p ("QuickIK_"+$name) transform;
parent ("QuickIKOffset_"+$name) ("QuickIK_"+$name+"Group");
parent ("QuickIK_"+$name) ("QuickIKOffset_"+$name);
createNode -n ("QuickPoleVectorOffset_"+$name) -p ("QuickPoleVector_"+$name) transform;
parent ("QuickPoleVectorOffset_"+$name) ("QuickIK_"+$name);
parent ("QuickPoleVector_"+$name) ("QuickPoleVectorOffset_"+$name);


//connect
for ($i=0;$i<size($chainJoints);$i++)
	{
	if ($i==(size($chainJoints)-1) && $otherEndJointAssumed)
		continue;
	if (`gmatch $chainJoints[$i] "*:*"`)//have nameSpace
		{
		tokenize $chainJoints[$i] ":" $tempString;
		$ctrl=$tempString[0]+":"+$ctrlPrefix+$tempString[1];
		}
	else
		$ctrl=$ctrlPrefix+$chainJoints[$i];
	if ($ctrlPrefix=="")
		$ctrl=`substitute "Joint" $chainJoints[$i] ""`;
	if (!`objExists $ctrl`)
		{
		warning ("Could not find controller:\""+$ctrl+"\"\n");
		continue;
		}
	orientConstraint ("QuickIKjoint_"+$chainJointNames[$i]) $ctrl;
	}

select ("QuickIK_"+$name);
}

global proc asSegmentScaleCompensate (string $uiName)
{
int $onOff;
string $onOffString;
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
string $fkXJoints[];

$fkXJoints=`ls -type joint ($nameSpace+"FKX*")`;
for ($i=0;$i<size($fkXJoints);$i++)
	{
	if ($i==0)
		{
		$onOff=!`getAttr ($fkXJoints[$i]+".segmentScaleCompensate")`;
		if ($onOff==0) $onOffString="OFF";
		if ($onOff==1) $onOffString="ON";
		print ("// switching "+$onOffString+" segmentScaleCompensate.\n");
		}
	setAttr ($fkXJoints[$i]+".segmentScaleCompensate") $onOff;
	}
}


global proc asTwistFlipUI (string $uiName)
{
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";

if (`window -q -ex asTwistFlip`)
    deleteUI asTwistFlip;
window -t TwistFlip asTwistFlip;
columnLayout;
separator -h 5 -st none;
text -l "Sets the amount of X-rotation, before `flipping` occours";
text -l "(e.g. twisting of the wrist)\n";
text -l "Note for 360, Constraint-Caching is needed for reliable results";
separator -h 20 -st none;
rowLayout -nc 4;
	text -w 80 -l "X-rotation:";
	optionMenu asTwistFlipOptionMenu;
		menuItem -l "180";
		menuItem -l "360";
	separator -w 10;
	button -w 100 -l "Ok, Set" -c ("asTwistFlipSet "+$uiName+" set");
	setParent..;
separator -h 10 -st none;
rowLayout -en 0 -nc 4 asTwistFlipCacheRowLayout;
	text -w 80 -l "Cache:";
	button -w 80 -l "Create" -c ("asTwistFlipSet "+$uiName+" cacheCreate");
	separator -w 10 -st none;
	button -w 80 -l "Delete" -c ("asTwistFlipSet "+$uiName+" cacheDelete");
showWindow;

//detect current option, and set OptionMenu accordingly
string $twistConstraints[]=`ls -type parentConstraint ($nameSpace+"TwistBalancer*")`;
if (size($twistConstraints))
	if (`getAttr ($twistConstraints[0]+".interpType")`!=1)
		{
		optionMenu -e -v "360" asTwistFlipOptionMenu;
		rowLayout -e -en 1 asTwistFlipCacheRowLayout;
		}
}

global proc asTwistFlipSet (string $uiName, string $action)
{
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";

string $asTwistFlipOption=`optionMenu -q -v asTwistFlipOptionMenu`;
int $interpType=1;
if ($asTwistFlipOption=="360")
	$interpType=0;
string $twistConstraints[]=`ls -type parentConstraint ($nameSpace+"TwistBalancer*")`;
for ($i=0;$i<size($twistConstraints);$i++)
	{
	if ($action=="set")
		{
		setAttr ($twistConstraints[$i]+".interpType") $interpType;
		setAttr ($nameSpace+"MainTwistFlipSetRange.value") -type float3 (!$interpType) (!$interpType) (!$interpType);
		if (`rowLayout -q -ex asTwistFlipCacheRowLayout`)
			rowLayout -e -en (!$interpType) asTwistFlipCacheRowLayout;
		}
	if (`getAttr -s ($twistConstraints[$i]+".target")`<2)// otherwice Error:No cache is needed when there is only a single target. 
		continue;
	if ($action=="cacheCreate")
		evalEcho ("parentConstraint -e -cc "+`playbackOptions -q -min`+" "+`playbackOptions -q -max`+" "+$twistConstraints[$i]);
	if ($action=="cacheDelete")
		evalEcho ("parentConstraint -e -dc "+$twistConstraints[$i]);
	}
dgdirty -a;
refresh;
print ("// Ok TwistFlip has been set.\n");
}

global proc asAnimBake (string $uiName)
{
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
string $tempString[]=`ls -type transform ($nameSpace+"FKIK*")`;
string $FkIks[];
for ($i=0;$i<size($tempString);$i++)
	if (`attributeExists FKIKBlend $tempString[$i]`)
//		if (!`gmatch $tempString[$i] "FKIKSp*ine*"`)
			$FkIks[size($FkIks)]=`substitute ($nameSpace+"FKIK") $tempString[$i] ""`;

if (`window -q -ex asAnimBake`)
    deleteUI asAnimBake;
window -t Bake asAnimBake;
formLayout asAnimBakeFormLayout;
columnLayout;
text -l "Bake:";
optionMenu asAnimBakeOptionMenu;
	menuItem -l "AnimationControls to BakeSkeleton";
//	menuItem -l "MotionCapture to FKControls";
	menuItem -l "ExtraControls to Controls";
	menuItem -l "QuickIK to FKControls";
	for ($i=0;$i<size($FkIks);$i++)
		menuItem -l ("FK to IK : "+$FkIks[$i]);
	for ($i=0;$i<size($FkIks);$i++)
		menuItem -l ("IK to FK : "+$FkIks[$i]);

separator -h 10;
setParent..;
button -l "Ok, Bake" -c ("asAnimBakeGo "+$uiName) asAnimBakeOkButton;

formLayout -e
	-af asAnimBakeOkButton "bottom" 0
	-af asAnimBakeOkButton "left" 0
	-af asAnimBakeOkButton "right" 0
	asAnimBakeFormLayout;

showWindow;
}

global proc asAnimBakeGo (string $uiName)
{
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
string $bake=`optionMenu -q -v asAnimBakeOptionMenu`;
string $limb;

if ($bake=="AnimationControls to BakeSkeleton")
	asAnimBakeSkeleton $uiName;
if ($bake=="MotionCapture to FKControls")
	asAnimBakeMoCapToFk $uiName;
if ($bake=="ExtraControls to Controls")
	asAnimBakeExtra $uiName;
if ($bake=="QuickIK to FKControls")
	asAnimBakeQuickIK $uiName;
if (`gmatch $bake "FK to IK : *"`)
	{
	$limb=`substitute "FK to IK : " $bake ""`;
	asAnimBakeFKIK $limb 1 $uiName;
	}
if (`gmatch $bake "IK to FK : *"`)
	{
	$limb=`substitute "IK to FK : " $bake ""`;
	asAnimBakeFKIK $limb 0 $uiName;
	}
}

global proc asAnimBakeExtra (string $uiName)
{
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
float $curTime=`currentTime -q`;

string $controlSets[]=`asGetControlSetsFromUI $uiName`;
string $controls[]=`sets -q $controlSets`;
string $tempString[],$tempString2[],$tempString3[],$extraControls[],$bakeControls[],$animCurves[];
for ($i=0;$i<size($controls);$i++)
	if (`gmatch $controls[$i] ($nameSpace+"FKExtra*")` || `gmatch $controls[$i] ($nameSpace+"RootExtraX*")`)
		$extraControls[size($extraControls)]=$controls[$i];

for ($i=0;$i<size($extraControls);$i++)
	{
	$controls[$i]=`substitute "Extra" $extraControls[$i] ""`;
	$bakeControls[$i]=$extraControls[$i]+"BAKER";
	$tempString=`listRelatives -p $extraControls[$i]`;
	createNode -n $bakeControls[$i] -p $tempString[0] transform;
	setAttr ($bakeControls[$i]+".rotateOrder") `getAttr ($controls[$i]+".rotateOrder")`;
	$tempString=`listRelatives -c -type transform $extraControls[$i]`;
	parentConstraint $tempString[0] $bakeControls[$i];
	scaleConstraint $tempString[0] $bakeControls[$i];
	}

//Bake
bakeResults -simulation true -t (`playbackOptions -q -min`+":"+`playbackOptions -q -max`) -sampleBy 1 -disableImplicitControl true -preserveOutsideKeys false -sparseAnimCurveBake false -removeBakedAttributeFromLayer false 
	-bakeOnOverrideLayer false -controlPoints false -shape false $bakeControls;
currentTime $curTime;
select $bakeControls;
evalEcho "delete -staticChannels -unitlessAnimationCurves false -hierarchy none -controlPoints 0 -shape 1";

//remove existing animation
for ($i=0;$i<size($extraControls);$i++)
	{
	$animCurves=`listConnections -s 1 -d 0 -type animCurve $extraControls[$i]`;
	if (size($animCurves)) delete $animCurves;
	xform -os -t 0 0 0 -ro 0 0 0 -s 1 1 1 $extraControls[$i];
	$animCurves=`listConnections -s 1 -d 0 -type animCurve $controls[$i]`;
	if (size($animCurves)) delete $animCurves;
	xform -os -t 0 0 0 -ro 0 0 0 -s 1 1 1 $controls[$i];
	}
//asGoToBuildPose bodySetup;

//Use baked animCurves
for ($i=0;$i<size($extraControls);$i++)
	{
	$animCurves=`listConnections -s 1 -d 0 -type animCurve $bakeControls[$i]`;
	for ($y=0;$y<size($animCurves);$y++)
		{
		$tempString=`listConnections -s 0 -d 1 -p 1 -c 1 $animCurves[$y]`;
		tokenize $tempString[1] "[.]" $tempString2;
		connectAttr $tempString[0] ($controls[$i]+"."+$tempString2[1]);
		tokenize $tempString[0] "[.]" $tempString3;
		rename $tempString3[0] ($controls[$i]+"_"+$tempString2[1]);
		}
	}
delete $bakeControls;
}

global proc asAnimBakeQuickIK (string $uiName)
{
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";

string $controlSets[]=`asGetControlSetsFromUI $uiName`;
string $controls[]=`sets -q $controlSets`;
string $bakeControls[],$tempString[];
for ($i=0;$i<size($controls);$i++)
	{
	$tempString=`listConnections -type constraint $controls[$i]`;
	if (size($tempString))
		$bakeControls[size($bakeControls)]=$controls[$i];
	}

//Bake
select $bakeControls;
bakeResults -simulation true -t (`playbackOptions -q -min`+":"+`playbackOptions -q -max`) -sampleBy 1 -disableImplicitControl true -preserveOutsideKeys true -sparseAnimCurveBake false -removeBakedAttributeFromLayer false 
	-bakeOnOverrideLayer false -controlPoints false -shape false $bakeControls;
select $bakeControls;
evalEcho "delete -staticChannels -unitlessAnimationCurves false -hierarchy none -controlPoints 0 -shape 1";

if (`objExists QuickIK`)
	delete QuickIK;
select -cl;
}

global proc asAnimBakeFKIK (string $limb, int $Fk2Ik, string $uiName)
{
global int $asBakeFKIK;
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
int $numChar=size($limb);
string $side=`substring $limb ($numChar-1) $numChar`;
string $sideLessLimb=`substring $limb 1 ($numChar-2)`;
float $curTime=`currentTime -q`;
float $start=`playbackOptions -q -min`;
float $end=`playbackOptions -q -max`;

int $autoKeyState=`autoKeyframe -q -state`;
if (!$autoKeyState)
	autoKeyframe -state 1;

currentTime $start;
if ($Fk2Ik)
	{
	setAttr ($nameSpace+"FKIK"+$limb+".FKIKBlend") 0;
	asSwitchFK2IK `substitute "asSelector" $uiName ""` {("FKIK"+$limb)};
	}
else
	{
	setAttr ($nameSpace+"FKIK"+$limb+".FKIKBlend") 10;
	asSwitchIK2FK `substitute "asSelector" $uiName ""` {("FKIK"+$limb)};
	}
$asBakeFKIK=1;
for ($i=$start;$i<$end+1;$i++)
	{
	currentTime $i;
	if ($Fk2Ik)
		asAlignFKIK `substitute "asSelector" $uiName ""` $sideLessLimb $side FK2IK;
	else
		asAlignFKIK `substitute "asSelector" $uiName ""` $sideLessLimb $side IK2FK;
	}
currentTime $curTime;
$asBakeFKIK=0;
if (!$autoKeyState)
	autoKeyframe -state 0;
}

global proc asAnimBakeSkeleton (string $uiName)
{
if (`confirmDialog -title "Confirm" -message 
	("Bake character ?\n"
	+"This creates a single joint chain for game engine export\n"
	+"The rig controllers will no longer work")
    -button "Yes" -button "No" -defaultButton "Yes"
    -cancelButton "No" -dismissString "No"`!="Yes")
	return;
string $sel[]=`ls -sl`;
string $tempString[],$tempString2[];
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
string $name=`substitute ":" $nameSpace ""`;
string $bakeNameSpace=$name+"Bake:";
namespace -add ($name+"Bake");
namespace -set ($name+"Bake");
$tempString=`listRelatives -c ($nameSpace+"DeformationSystem")`;
$tempString=`duplicate $tempString[0]`;
string $topBakeJoint=$tempString[0];
parent -w $topBakeJoint;
$tempString=`listRelatives -ad $topBakeJoint`;
for ($i=0;$i<size($tempString);$i++)
	if (`objectType $tempString[$i]`!="joint")
		delete $tempString[$i];
string $allBakeJoints[]=`listRelatives -ad -type joint $topBakeJoint`;
$allBakeJoints[size($allBakeJoints)]=$topBakeJoint;
sets -rm ($nameSpace+"DeformSet") $allBakeJoints;
//Disable Skincluster
string $skinClusters[]=`ls -type skinCluster`;
for ($i=0;$i<size($skinClusters);$i++)
    setAttr ($skinClusters[$i]+".nodeState") 1;
//Constraint bakeSkeleton
string $deformJoints[]=`listRelatives -ad -type joint ($nameSpace+"DeformationSystem")`;
for ($i=0;$i<size($deformJoints);$i++)
    {
		if ($nameSpace=="")
			$bakeDeformJoint=$bakeNameSpace+$deformJoints[$i];
		else
			$bakeDeformJoint=`substitute $nameSpace $deformJoints[$i] $bakeNameSpace`;
    parentConstraint $deformJoints[$i] $bakeDeformJoint;
    scaleConstraint $deformJoints[$i] $bakeDeformJoint;
    setAttr ($bakeDeformJoint+".segmentScaleCompensate") 0;
    }
//Bake
bakeResults -simulation true -t (`playbackOptions -q -min`+":"+`playbackOptions -q -max`) -sampleBy 1 -disableImplicitControl true -preserveOutsideKeys false -sparseAnimCurveBake false -removeBakedAttributeFromLayer false 
	-bakeOnOverrideLayer false -controlPoints false -shape false $allBakeJoints;
//Transfer deformation to bake skeleton
for ($i=0;$i<size($deformJoints);$i++)
{
    $tempString=`listConnections -s 0 -d 1 -p 1 -c 1 $deformJoints[$i]`;
    for ($y=0;$y<size($tempString);$y=$y+2)
        {        
        if ($nameSpace=="")
        	$newSource=$bakeNameSpace+$tempString[$y];
				else
	        $newSource=`substitute $nameSpace $tempString[$y] $bakeNameSpace`;
        catch (`connectAttr -f $newSource $tempString[$y+1]`);
        }
}
//Enable Skincluster
for ($i=0;$i<size($skinClusters);$i++)
    setAttr ($skinClusters[$i]+".nodeState") 0;

namespace -set ":";
select $allBakeJoints;
evalEcho "delete -staticChannels -unitlessAnimationCurves false -hierarchy none -controlPoints 0 -shape 1";
select $topBakeJoint;
print ("// Selected joint chain is ready for export, add geometry to selection and export\n");
}

global proc asCreateMoCap (string $uiName)
{
string $sel[]=`ls -sl`;
string $deformJoints[],$keyDeformJoints[],$tempString[],$tempString2[],$parent[],$names[],$extra[];
string $extr;
string $nameSpace;
if (`optionMenu -q -ex ($uiName+"OptionMenu")`)
	$nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
float $charHeight=`getAttr ($nameSpace+"Main.height")`;
if (`objExists ($nameSpace+"MoCap")`)
	error "MoCap skeleton already exists !";
if (!`objExists ($nameSpace+"Main")`)
	error "No AdvancedSKeleton In Scene!";
if (!`objExists ($nameSpace+"HipSwingerStabilizerTarget2")`)
	{
	string $dialog=`confirmDialog -t "Confirm"
	-m "HipSwinger controllers needs \"stabilize\" attribute for Mocap.\nAdd this now ?"
	-b "OK" -b "Skip" -b "Cancel" -db "Cancel"
	-ds "Cancel"`;
	if ($dialog=="Cancel")
		return;
	if ($dialog=="OK")
		{
		//Added for MoCap to rotate Root joint
		duplicate -n ($nameSpace+"HipSwingerStabilizerTarget2") ($nameSpace+"HipSwingerStabilizerTarget");
		parent ($nameSpace+"HipSwingerStabilizerTarget2") ($nameSpace+"FKExtraRoot_M");
		orientConstraint ($nameSpace+"HipSwingerStabilizerTarget2") ($nameSpace+"HipSwingerStabilizer");
		if (!`attributeExists stabilize ($nameSpace+"HipSwinger_M")`)
			addAttr -k 1 -ln stabilize -at double -min 0 -max 10 -dv 10 ($nameSpace+"HipSwinger_M");
		if (`objExists ($nameSpace+"HipSwingerStabilizerUnitConversion")`) delete ($nameSpace+"HipSwingerStabilizerUnitConversion");
		createNode -n ($nameSpace+"HipSwingerStabilizerUnitConversion") unitConversion;
		setAttr ($nameSpace+"HipSwingerStabilizerUnitConversion.conversionFactor") 0.1;
		connectAttr ($nameSpace+"HipSwinger_M.stabilize") ($nameSpace+"HipSwingerStabilizerUnitConversion.input");
		connectAttr ($nameSpace+"HipSwingerStabilizerUnitConversion.output") ($nameSpace+"HipSwingerStabilizer_orientConstraint1.HipSwingerStabilizerTargetW0");
		createNode -n ($nameSpace+"HipSwingerStabilizerReverse") reverse;
		connectAttr ($nameSpace+"HipSwingerStabilizerUnitConversion.output") ($nameSpace+"HipSwingerStabilizerReverse.inputX");
		connectAttr ($nameSpace+"HipSwingerStabilizerReverse.outputX") ($nameSpace+"HipSwingerStabilizer_orientConstraint1.HipSwingerStabilizerTarget2W1");
		}
	}

$deformJoints=`listRelatives -type joint -ad ($nameSpace+"DeformationSystem")`;
for ($a=$i=0;$a<size($deformJoints);$a++)
	{
	if (`gmatch $deformJoints[$a] "*_50"`)
		continue;
	if (`gmatch $deformJoints[$a] "*Slider[0-9]*"`)
		continue;
	if (`objExists FaceAllSet`)
		if (`sets -im FaceAllSet $deformJoints[$a]`)
			continue;
	tokenize $deformJoints[$a] "_" $tempString;
	$extr=`substitute $nameSpace ("FKExtra"+$tempString[0]+"_"+$tempString[1]) ""`;
	if (`gmatch $deformJoints[$a] "*Part[0-9]*"`)
		continue;
	$keyDeformJoints[$i]=$deformJoints[$a];
	$extra[$i]=$extr;
	$tempString[0]=$keyDeformJoints[$i];
	for ($y=0;$y<99;$y++)
		{
		$tempString=`listRelatives -p $tempString[0]`;
		if (!`gmatch $tempString[0] "*Part[0-9]*"`)
			{
			tokenize $tempString[0] "_" $tempString;
			$parent[$i]=`substitute $nameSpace ($tempString[0]+"_MoCap_"+$tempString[1]) ""`;
			break;
			}
		}
	tokenize $keyDeformJoints[$i] "_" $tempString;
	string $name=$tempString[0]+"_MoCap_"+$tempString[1];
	if (!`stringArrayCount $name $names`)
		{
		$names[$i]=`substitute $nameSpace $name ""`;
		$i++;
		}
	}

string $topJoint;
for ($i=0;$i<size($names);$i++)
	{
	select -cl;
	joint -n $names[$i];
	if ($i==size($names)-1)
		{
		createNode -n "MoCap" transform;
		createNode -n "CenterOffset" transform;
		asAlign "CenterOffset" ($nameSpace+"RootExtraX_M") 1 0 0 0;
//		parent "MoCap" "Group";
		parent "CenterOffset" "MoCap";
		parent $names[$i] "CenterOffset";
		connectAttr ($names[$i]+".translate") ($nameSpace+"RootExtraX_M.translate");
		$topJoint=$names[$i];
		}
	asAlign $names[$i] $keyDeformJoints[$i] 1 1 1 0;
	setAttr ($names[$i]+".rotateOrder") 1;
	if (`objExists $extra[$i]`)
		setAttr ($extra[$i]+".rotateOrder") 1;
	}

for ($i=0;$i<size($names);$i++)
	{
	if (`objExists $parent[$i]`)
		parent $names[$i] $parent[$i];
	}
for ($i=0;$i<size($names);$i++)
	{
	if (`objExists ($names[$i]+"_blendColor")`)
		delete ($names[$i]+"_blendColor");
	createNode -n ($names[$i]+"_blendColor") blendColors;
	connectAttr ($names[$i]+".rotate") ($names[$i]+"_blendColor.color1");
	addAttr -k 1 -ln "blend" -at double -min 0 -max 1 -dv 1 $names[$i];
	connectAttr ($names[$i]+".blend") ($names[$i]+"_blendColor.blender");
	if (`objExists ($nameSpace+$extra[$i])`)
		connectAttr ($names[$i]+"_blendColor.output") ($nameSpace+$extra[$i]+".rotate");
	}
setAttr "MoCap.translateZ" ($charHeight/-3);
//orientConstraint -mo $topJoint "RootExtraX_M";
select $sel;
}

global proc asDeleteMocap (string $uiName)
{
if (`objExists MoCap`)
	delete MoCap;
delete `ls -type blendColors "*_MoCap_*_blendColor"`;
}

global proc asSetAllFK (string $nameSpace)
{
string $controlSetMembers[]=`sets -q ($nameSpace+"ControlSet")`;
for ($i=0;$i<size($controlSetMembers);$i++)
	if (`attributeExists FKIKBlend $controlSetMembers[$i]`)
		setAttr ($controlSetMembers[$i]+".FKIKBlend") 0;

if (`attributeExists stabilize ($nameSpace+"HipSwinger_M")`)
	setAttr ($nameSpace+"HipSwinger_M.stabilize") 0;
}

global proc asConnectARKitUI (string $uiName)
{
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
string $blendShapes[]=`ls -type blendShape`;

if (`window -q -ex asConnectARKitUI`)
	deleteUI asConnectARKitUI;
window -t "Connect ARKit" asConnectARKitUI;
columnLayout -adj 1;
text -l "Connect to MocapX, FaceCap, Aquifer\nand other sofware that use Apple-ArKit";

separator -st none -h 10;
rowLayout -nc 3;
	button -c asARKChooseHeadRotNode "Choose Head Rotation Node";
	separator -st none -w 10;
	textField -ed 0 -tx "none" asConnectARKitHRTextField;
	setParent..;
separator -st none -h 5;
button -l "Connect Head Rotation" -c ("asARKConnectHeadRotation \""+$nameSpace+"\"");
separator -st none -h 5;
button -l "Disconnect Head Rotation" -c ("asARKDisconnectHeadRotation \""+$nameSpace+"\"");;
separator -st none -h 40;

optionMenu -l "BlendShape Node" asConnectARKitBSOptionMenu;
for ($i=0;$i<size($blendShapes);$i++)
	{
	if (`gmatch $blendShapes[$i] ($nameSpace+"*asFace*")`
	 || `gmatch $blendShapes[$i] ($nameSpace+"*Close*BS*")`
	 || `gmatch $blendShapes[$i] ($nameSpace+"*SquintRightToLeft*")`)
		continue;
	menuItem -l $blendShapes[$i];
	}

separator -st none -h 20;
button -l "Connect" -c ("asConnectARKit \""+$nameSpace+"\"");
button -l "Disconnect" -c ("asDisconnectARKit \""+$nameSpace+"\"");
separator -st none -h 10;
button -l "bake" -c ("asBakeARKit \""+$nameSpace+"\"");
showWindow;
}

global proc asARKChooseHeadRotNode ()
{
string $sel[]=`ls -sl`;
textField -e -tx $sel[0] asConnectARKitHRTextField;
}

global proc asARKConnectHeadRotation (string $nameSpace)
{
int $autoKeyState=`autoKeyframe -q -state`;
string $arHeadRotObject=`textField -q -tx asConnectARKitHRTextField`;
string $tempString[];

if (!`objExists ($nameSpace+"FKHead_M")`)
	error ("Object \""+$nameSpace+"FKHead_M\" not found, you might not be using a AdvancedSkeleton body rig.\nManually constraint head rotation.");
if (!`objExists $arHeadRotObject`)
	error ("Object:\""+$arHeadRotObject+"\" not found, Choose the object that has the head rotation animation.");

if ($autoKeyState) autoKeyframe -state 0;
if (`objExists asARKHeadOrientation`) delete asARKHeadOrientation;
if (`objExists asARKHeadOrientation_orientConstraint`) delete asARKHeadOrientation_orientConstraint;
createNode -n asARKHeadOrientation -p ($nameSpace+"FKHead_M") transform;
setAttr ($arHeadRotObject+".r") -type float3 0 0 0;
parent asARKHeadOrientation $arHeadRotObject;
$tempString=`orientConstraint asARKHeadOrientation ($nameSpace+"FKHead_M")`;
rename asARKHeadOrientation_orientConstraint;
if ($autoKeyState) autoKeyframe -state 1;
dgdirty -a;
print ("// Head rotation connected.\n");
select -cl;
}

global proc asARKDisconnectHeadRotation (string $nameSpace)
{
if (`objExists asARKHeadOrientation_orientConstraint`) delete asARKHeadOrientation_orientConstraint;
setAttr ($nameSpace+"FKHead_M.r") -type float3 0 0 0;
}

global proc asConnectARKit (string $nameSpace)
{
int $autoKeyState=`autoKeyframe -q -state`;
float $posA[],$posB[];
string $name,$altName,$side,$leftRight,$oppositeSide;
string $bs=`optionMenu -q -v asConnectARKitBSOptionMenu`;

if (`attributeExists RigType ($nameSpace+"FaceFitSkeleton")`)
	if (`getAttr ($nameSpace+"FaceFitSkeleton.RigType")`==1)
		error "Face Rig Type \"BlendShapes\" detected. To use Connect ARKit, you must build Face Rig Type \"Joints\".";

if (!`objExists $bs`)
	error "Not a valid blendShape node";
//ARKit shapes from https://developer.apple.com/documentation/arkit/arfaceanchor/blendshapelocation
string $arShapes[]={"eyeBlinkLeft","eyeLookDownLeft","eyeLookInLeft","eyeLookOutLeft","eyeLookUpLeft","eyeSquintLeft","eyeWideLeft",
									"eyeBlinkRight","eyeLookDownRight","eyeLookInRight","eyeLookOutRight","eyeLookUpRight","eyeSquintRight","eyeWideRight",
									"jawForward","jawLeft","jawRight","jawOpen","mouthClose","mouthFunnel","mouthPucker","mouthLeft","mouthRight",
									"mouthSmileLeft","mouthSmileRight","mouthFrownLeft","mouthFrownRight","mouthDimpleLeft","mouthDimpleRight",
									"mouthStretchLeft","mouthStretchRight","mouthRollLower","mouthRollUpper","mouthShrugLower","mouthShrugUpper",
									"mouthPressLeft","mouthPressRight","mouthLowerDownLeft","mouthLowerDownRight","mouthUpperUpLeft","mouthUpperUpRight",
									"browDownLeft","browDownRight","browInnerUp","browOuterUpLeft","browOuterUpRight","cheekPuff","cheekSquintLeft",
								"cheekSquintRight","noseSneerLeft","noseSneerRight","tongueOut"};
//App mismatch names - FaceCap


currentTime 0;
if ($autoKeyState) autoKeyframe -state 0;
for ($i=0;$i<size($arShapes);$i++)
	{
	$name=$arShapes[$i];
	$attr=$name;
	$altName=`substitute "Right" $name "_R"`;
	$altName=`substitute "Left" $altName "_L"`;

	//some Apps rename the "Right" suffix to "_R" for some attributes (e.g FaceCap)
	if ((catchQuiet (`getAttr ($bs+"."+$name)`)) && (!catchQuiet (`getAttr ($bs+"."+$altName)`)))
		$attr=$altName;

	if ((catchQuiet (`getAttr ($bs+"."+$name)`)) && (catchQuiet (`getAttr ($bs+"."+$altName)`)))
		{
		print ("Missing ARKit BlendShapes target: "+($bs+"."+$altName)+"\n");
		continue;
		}

	setAttr ($bs+"."+$attr) 0;

	for ($b=1;$b>-2;$b=$b-2)
		{
		if ($b==1)  {$side="_R";$leftRight="Right";$oppositeSide="_L";}
		if ($b==-1) {$side="_L";$leftRight="Left";$oppositeSide="_R";}

		if ($arShapes[$i]=="eyeBlink"+$leftRight) asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlEye"+$side+".blink") 10;
		if ($arShapes[$i]=="eyeLookDown"+$leftRight) asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlEye"+$side+".ty") -1;
		if ($arShapes[$i]=="eyeLookIn"+$leftRight) asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlEye"+$side+".tx") (1*$b);
		if ($arShapes[$i]=="eyeLookOut"+$leftRight) asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlEye"+$side+".tx") (-1*$b);
		if ($arShapes[$i]=="eyeLookUp"+$leftRight) asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlEye"+$side+".ty") 1;
		if ($arShapes[$i]=="eyeSquint"+$leftRight) asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlEye"+$side+".squint") 10;
		if ($arShapes[$i]=="eyeWide"+$leftRight) asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlEye"+$side+".blink") -2;
		if ($arShapes[$i]=="mouthDimple"+$leftRight) asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouthCorner"+$side+".tx")  0.3;
		if ($arShapes[$i]=="browOuterUp"+$leftRight) asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlBrow"+$side+".outerUpDown") 7.5;
		if ($arShapes[$i]=="noseSneer"+$leftRight) asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlNose"+$side+".ty") 0.5;

		//AdvSkel has Center ctrl for these side attrs
		if (`attributeExists lipSide ctrlMouth_M`)//backwards compataqbility for rigs<v5.750
			{
			if ($arShapes[$i]=="mouthRight") asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.lipSide") -10;
			if ($arShapes[$i]=="mouthLeft")  asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.lipSide") 10;
			}

		//Drive multiple attr
		if ($arShapes[$i]=="mouthSmile"+$leftRight)
			{
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouthCorner"+$side+".tx") 0.75;
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouthCorner"+$side+".ty") 0.75;
			}
		if ($arShapes[$i]=="mouthFrown"+$leftRight)
			{
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouthCorner"+$side+".tx")  0.25;
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouthCorner"+$side+".ty") -0.25;
			}
		if ($arShapes[$i]=="mouthStretch"+$leftRight)
			{
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouthCorner"+$side+".tx")  0.5;
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouthCorner"+$side+".ty") -0.5;
			}
		if ($arShapes[$i]=="mouthPressRight" || $arShapes[$i]=="mouthPressLeft")//common for l/r first, then one by one
			{
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.upperSqueeze")  1.5;
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.lowerSqueeze")  1.5;
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"LipRegion_M.ty")  0.1;
			}
		if ($arShapes[$i]=="mouthPress"+$leftRight) asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouthCorner"+$side+".ty") 0.25; 
		if ($arShapes[$i]=="mouthLowerDown"+$leftRight)
			{
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"lowerLipA"+$side+".ty") -0.4;
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"lowerLip_M.ty") -0.3;
			}
		if ($arShapes[$i]=="mouthUpperUp"+$leftRight)
			{
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"upperLipA"+$side+".ty") 0.4;
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"upperLip_M.ty") 0.3;
			}
		if ($arShapes[$i]=="browDown"+$leftRight)
			{
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlBrow"+$side+".ty") -0.75;
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlBrow"+$side+".squeeze") 7.5;
			}
		if ($arShapes[$i]=="cheekSquint"+$leftRight)
			{
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlEye"+$side+".squint") 3;
			asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"Cheek"+$side+".ty") 0.1;
			}
		}

	//Middle shapes
	if (`attributeExists jawForward ctrlMouth_M`)//backwards compataqbility for rigs<v5.750
		{
		if ($arShapes[$i]=="jawForward") asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.jawForward") 5;
		if ($arShapes[$i]=="jawRight") asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.jawSide") 5;
		if ($arShapes[$i]=="jawLeft")  asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.jawSide") -5;
		}
	if ($arShapes[$i]=="jawOpen") asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.ty") -1;
	if ($arShapes[$i]=="mouthClose")//mixed with the opposite amount of JawOpen gives stickylips 
		{
		setAttr ($nameSpace+"ctrlMouth_M.ty") 0;
		$posA=`xform -q -ws -t ($nameSpace+"lowerLip_M")`;
		setAttr ($nameSpace+"ctrlMouth_M.ty") -1;
		$posB=`xform -q -ws -t ($nameSpace+"lowerLip_M")`;
		xform -ws -t $posB[0] ($posB[1]+($posA[1]-$posB[1])) $posB[2] ($nameSpace+"lowerLip_M");
		$posA=`getAttr ($nameSpace+"lowerLip_M.t")`;
		setAttr ($nameSpace+"ctrlMouth_M.ty") 0;
		asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"lowerLip_M.ty") ($posA[1]/2.0);
		asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"upperLip_M.ty") ($posA[1]/-2.0);
		}
	if ($arShapes[$i]=="mouthPucker")
		{
		asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.upperPucker") 10;
		asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.lowerPucker") 10;
		}
	if ($arShapes[$i]=="mouthFunnel")
		{
		asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.upperPucker") 10;
		asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.lowerPucker") 10;
		asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"upperLip_M.ty") 0.3;
		asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"lowerLip_M.ty") -0.3;
		}
	if ($arShapes[$i]=="mouthRollUpper") asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.upperRoll") -5;
	if ($arShapes[$i]=="mouthRollLower") asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.lowerRoll") -5;
	if ($arShapes[$i]=="mouthShrugUpper") asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"upperLip_M.ty") 0.3;
	if ($arShapes[$i]=="mouthShrugLower") asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"lowerLip_M.ty") 0.3;
	if ($arShapes[$i]=="browInnerUp")
		{
		asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlBrow_R.tx") 1;
		asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlBrow_L.tx") 1;
		}
	if ($arShapes[$i]=="cheekPuff")
		{
		asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlCheek_R.tx") 0.4;
		asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlCheek_L.tx") 0.4;
		asFaceBsSdk ($bs+"."+$attr) 1 ($nameSpace+"ctrlMouth_M.tx") -0.2;
		}
	}
if ($autoKeyState) autoKeyframe -state 0;
dgdirty -a;
print ("// ARKit connected.\n");
select -cl;
}

global proc asDisconnectARKit (string $nameSpace)
{
string $controls[]=`sets -q ($nameSpace+"FaceControlSet")`;
string $tempString[];

for ($i=0;$i<size($controls);$i++)
	{
	$tempString=`listConnections -s 1 -d 0 -type animCurve $controls[$i]`;
	if (size($tempString)) delete $tempString;
	$tempString=`listConnections -s 1 -d 0 -type blendWeighted $controls[$i]`;
	if (size($tempString)) delete $tempString;
	}
asGoToBuildPoseOptions $nameSpace "FaceControlSet";
}

global proc asBakeARKit (string $nameSpace)
{
string $controls[]=`sets -q ($nameSpace+"FaceControlSet")`;
if (`objExists ($nameSpace+"FKHead_M")`)
	$controls[size($controls)]=($nameSpace+"FKHead_M");
select -cl;
bakeResults -simulation true -t (`playbackOptions -q -min`+":"+`playbackOptions -q -max`) -sampleBy 1 -disableImplicitControl 0 -preserveOutsideKeys 0 -sparseAnimCurveBake false -controlPoints true -shape false $controls;
select $controls;
evalEcho "delete -staticChannels -unitlessAnimationCurves false -hierarchy none -controlPoints 0 -shape 1";
select -cl;
print ("// ARKit bake complete.\n");
}


global proc asAutoLipSyncDownload (string $uiName)
{
string $zipFileUrl="https://github.com/MontrealCorpusTools/Montreal-Forced-Aligner/releases/download/v1.1.0-beta.2/montreal-forced-aligner_win64.zip";
if (`about -mac`)
	$zipFileUrl="https://github.com/MontrealCorpusTools/Montreal-Forced-Aligner/releases/download/v1.1.0-beta.2/montreal-forced-aligner_macosx.zip";
string $libriSpeechLexiconUrl="http://www.openslr.org/resources/11/librispeech-lexicon.txt";
string $sLoc=`asGetScriptLocation`;
string $curl=$sLoc+"/AdvancedSkeleton5Files/bin/curl.e";
string $zip=$sLoc+"/AdvancedSkeleton5Files/bin/7za.e";
string $binDir=$sLoc+"/AdvancedSkeleton5Files/bin";
string $downloadDir=$sLoc+"/AdvancedSkeleton5Files/bin/download";
string $tempString[];
tokenize $zipFileUrl "/" $tempString;
string $downloadedZipFile=$tempString[size($tempString)-1];
string $downloadedZipFilePath=$downloadDir+"/"+$downloadedZipFile;
string $cmd;

if (!`file -q -ex $downloadDir`) sysFile -md $downloadDir;

//download Montreal-Forced-Aligner
if (`about -mac` || `about -linux`)
	{
	$cmd="\"curl -L -o "+$downloadDir+"/"+$downloadedZipFile+" "+$zipFileUrl+"\"";
	evalEcho ("system("+$cmd+")");
	}
else
	{
	$cmd="start\/wait/I \"Downloading\"  \""+$curl+"\" -L -o \""+$downloadDir+"/"+$downloadedZipFile+"\" "+$zipFileUrl;
	print ("// Starting Download:"+$cmd+"\n");
	system ($cmd);
	}

//download librispeech-lexicon
if (`about -mac` || `about -linux`)
	{
	$cmd="\"curl -L -o "+$downloadDir+"/librispeech-lexicon.txt "+$libriSpeechLexiconUrl+"\"";
	evalEcho ("system("+$cmd+")");
	}
else
	{
	$cmd="start\/wait/I \"Downloading\"  \""+$curl+"\" -L -o \""+$downloadDir+"/librispeech-lexicon.txt\" "+$libriSpeechLexiconUrl;
	print ("// Starting Download:"+$cmd+";\n");
	system ($cmd);
	}

//confirm downloads
if (`file -q -ex $downloadedZipFilePath`)
	print ("// Downloaded sucessfully:"+$downloadedZipFilePath+";\n");
else
	error ("// Download failed, could not find:"+$downloadedZipFilePath+";\n");
if (`file -q -ex ($downloadDir+"/librispeech-lexicon.txt")`)
	print ("// Downloaded sucessfully:"+$downloadDir+"/librispeech-lexicon.txt;\n");
else
	error ("// Download failed, could not find:"+$downloadDir+"/librispeech-lexicon.txt;\n");

//unzip
if (`about -mac` || `about -linux`)
  $cmd="unzip "+$downloadedZipFilePath+" -d "+$binDir;
else
	$cmd="start\/wait/I \"Unzipping\"  \""+$zip+"\" x \""+$downloadedZipFilePath+"\" -o\""+$binDir+"\"";
print ("// Starting Unzip:"+$cmd+";\n");
system($cmd);

//confirm unzip
if (`file -q -ex ($binDir+"/montreal-forced-aligner")`)
	print ("// Unzipped sucessfully:"+$downloadedZipFile+";\n");
else
	error ("// Unzipp failed, could not find:"+$binDir+"/montreal-forced-aligner\n");

//Delete download
if (`filetest -f $downloadedZipFilePath`)
	sysFile -del $downloadedZipFilePath;
//Delete montreal-forced-aligner_win64.zip (1kb file that comes with the zip)
if (`filetest -f ($binDir+"/montreal-forced-aligner_win64.zip")`)
	sysFile -del ($binDir+"/montreal-forced-aligner_win64.zip");

//Move librispeech-lexicon.txt
sysFile -ren ($binDir+"/montreal-forced-aligner/bin/librispeech-lexicon.txt") ($downloadDir+"/librispeech-lexicon.txt");

asAutoLipSyncUI $uiName;
}

global proc asAutoLipSyncFfmpegDownload ()
{
//ffmpeg for $segmentReplacing
string $zipFileUrl="http://www.advancedskeleton.com/download/div/ffmpeg.7z";
string $ffmpegFile="ffmpeg.e";
if (`about -mac`)
	{
	$zipFileUrl="http://www.advancedskeleton.com/download/div/ffmpeg_mac.7z";
	$ffmpegFile="ffmpeg";
	}
string $sLoc=`asGetScriptLocation`;
string $curl=$sLoc+"/AdvancedSkeleton5Files/bin/curl.e";
string $zip=$sLoc+"/AdvancedSkeleton5Files/bin/7za.e";
string $forceAlignerBinDir=$sLoc+"/AdvancedSkeleton5Files/bin/montreal-forced-aligner/bin";
string $downloadDir=$sLoc+"/AdvancedSkeleton5Files/bin/download";
string $tempString[];
tokenize $zipFileUrl "/" $tempString;
string $downloadedZipFile=$tempString[size($tempString)-1];
string $downloadedZipFilePath=$downloadDir+"/"+$downloadedZipFile;
string $cmd;

if (!`file -q -ex $downloadDir`) sysFile -md $downloadDir;

//download
if (`about -mac` || `about -linux`)
	{
	$cmd="\"curl -k -o "+$downloadedZipFilePath+" "+$zipFileUrl+"\"";
	evalEcho ("system("+$cmd+")");
	}
else
	{
	$cmd="start\/wait/I \"Downloading\"  \""+$curl+"\" -k -o \""+$downloadedZipFilePath+"\" "+$zipFileUrl;
	print ("// Starting Download:"+$cmd+";\n");
	system ($cmd);
	}

//confirm downloads
if (`file -q -ex $downloadedZipFilePath`)
	print ("// Downloaded sucessfully:"+$downloadedZipFilePath+";\n");
else
	error ("// Download failed, could not find:"+$downloadedZipFilePath+"\n");

//unzip
if (`about -mac` || `about -linux`)
  $cmd="unzip "+$downloadedZipFilePath+" -d "+$forceAlignerBinDir;
else
	$cmd="start\/wait/I \"Unzipping\"  \""+$zip+"\" x \""+$downloadedZipFilePath+"\" -o\""+$forceAlignerBinDir+"\"";
print ("// Starting Unzip:"+$cmd+";\n");
system($cmd);

//confirm unzip
if (`file -q -ex ($forceAlignerBinDir+"/"+$ffmpegFile)`)
	print ("// Unzipped sucessfully:"+$downloadedZipFile+";\n");
else
	error ("// Unzipp failed, could not find:"+$forceAlignerBinDir+"/"+$ffmpegFile+"\n");

//Delete download
if (`filetest -f $downloadedZipFilePath`)
	sysFile -del $downloadedZipFilePath;
}

global proc asAutoLipSyncUI (string $uiName)
{
asEnsureAsGetScriptLocation;
string $nameSpace;
string $alignerDir=`asGetScriptLocation`+"/AdvancedSkeleton5Files/bin/montreal-forced-aligner";
string $aligner=$alignerDir+"/bin/mfa_align";
if (`about -win`)
	$aligner=$alignerDir+"/bin/mfa_align.exe";
int $haveAligner=`file -q -ex $aligner`;

if ($uiName!="")
	if (`optionMenu -q -ex ($uiName+"OptionMenu")`)
		$nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
string $languages[]=`asAutoLipSyncLanguages`;
stringArrayInsertAtIndex 1 $languages "English";
string $language="English";
if (`optionVar -ex asAutoLipSyncLanguage`)
	$language=`optionVar -q asAutoLipSyncLanguage`;

if (`window -q -ex asAutoLipSyncUI`)
	deleteUI asAutoLipSyncUI;
window -t AutoLipSync -w 300 -h 200 asAutoLipSyncUI;
formLayout asAutoLipSyncUIFormLayout;
columnLayout asAutoLipSyncUIColumnLayout;
if (!$haveAligner)
	{
	separator -h 5 -st none;
	text -l "To run AutoLipSync,";
	text -l "first download \"Montreal-Forced-Aligner\"";
	separator -h 5 -st none;
	button -l "Download Montreal-Forced-Aligner" -c ("asAutoLipSyncDownload "+$uiName);
	showWindow;
	print ("// Montreal-Forced-Aligner tool must be downloaded first\n");
	print ("// If the automatic download does not work, you can manually download from:\n");
	print ("// https://github.com/MontrealCorpusTools/Montreal-Forced-Aligner/releases\n");
	print ("// download and extract the files, so you get this available file-path:\n");
	print ("// "+$aligner+"\n");
	print ("\n");
	return;
	}

text -m 0 -l $aligner asAutoLipSyncAlignerText;
separator -h 10 -st none;
columnLayout;
	text -l "Add Idle motions:";
	rowLayout -nc 5;
		checkBox -l "Blink" -v 1 asAutoLipSyncBlinkCheckBox;
		checkBox -l "EyeBrows" -v 1 asAutoLipSyncEyeBrowsCheckBox;
		checkBox -l "Head" -v 1 asAutoLipSyncHeadCheckBox;
		setParent..;
	setParent..;

separator -h 20 -st none;
setParent..;
scrollField -wordWrap false -text "Dialog text here" asAutoLipSyncUIScrollField;
button -m 0 -l "Open text editor, for non-latin alphabet" -c asAutoLipOpenTextEditor asAutoLipSyncUIButton;
frameLayout -w 300 -cll 1 -cl 0 -l "advanced options" asAutoLipSyncUIFrameLayout;
	columnLayout -adj 0;
		rowLayout -nc 3;
			optionMenu -l "language:" -cc asAutoLipSyncLanguageChanged asAutoLipSyncLanguageOptionMenu;
			for ($i=0;$i<size($languages);$i++)
				menuItem $languages[$i];
			separator -st none -w 20;
			checkBox -l "non-latin alphabet" -cc asAutoLipSyncLanguageChanged asAutoLipSyncNonLatinCheckBox;
			setParent..;
		if (`stringArrayCount $language $languages`)
			{
			optionMenu -e -v $language asAutoLipSyncLanguageOptionMenu;
			asAutoLipSyncLanguageChanged;
			}
		rowLayout -nc 3;
			text -l "phoneme  fadeIn   ";
			floatField -pre 3 -v 0.05 -min 0.001 asAutoLipSyncPhonemeInFloatField;
			text -l "seconds";
			setParent..;
		rowLayout -nc 3;
			text -l "phoneme  fadeOut";
			floatField -pre 3 -v 0.05 -min 0.001 asAutoLipSyncPhonemeOutFloatField;
			text -l "seconds";
			setParent..;
		rowLayout -nc 3;
			checkBox -l "always re-code audio file" asAutoLipSyncAlwaysReCodeCheckBox;		
			setParent..;
		rowLayout -nc 6;
			separator -w 5 -st none;
			button -l "import example voice" -c asAutoLipSyncImportExampleVoice;
			separator -w 5 -st none;
			button -l "visit ibm.com/watson" -c "showHelp -a \"https://www.ibm.com/watson/services/text-to-speech/\"";
			separator -w 5 -st none;
			button -l "enable mp3" -c "asAutoLipSyncEnableMp3";
			setParent..;
		separator;
		setParent..;
	setParent..;
columnLayout asAutoLipSyncUIColumnLayout2;
separator -h 5 -st none;

rowLayout -nc 3;
	separator -w 50 -st none;
	button -w 100 -l "Run Auto LipSync" -c ("asAutoLipSyncForceAlign \""+$nameSpace+"\"");

formLayout -e 
	-ac asAutoLipSyncUIScrollField "top" 0 asAutoLipSyncUIColumnLayout
	-af asAutoLipSyncUIScrollField "left" 0
	-af asAutoLipSyncUIScrollField "right" 0
	-ac asAutoLipSyncUIScrollField "bottom" 0 asAutoLipSyncUIFrameLayout

	-ac asAutoLipSyncUIFrameLayout "bottom" 0 asAutoLipSyncUIColumnLayout2

	-ac asAutoLipSyncUIButton "top" 0 asAutoLipSyncUIColumnLayout
	-af asAutoLipSyncUIButton "left" 0
	-af asAutoLipSyncUIButton "right" 0
	-ac asAutoLipSyncUIButton "bottom" 0 asAutoLipSyncUIFrameLayout

	-af asAutoLipSyncUIColumnLayout2 "bottom" 5
	-af asAutoLipSyncUIFrameLayout "left" 0
	-af asAutoLipSyncUIFrameLayout "right" 0
	asAutoLipSyncUIFormLayout;

showWindow;
}

global proc asAutoLipSyncForceAlign (string $nameSpace)
{
global string $gPlayBackSlider;
int $segmentReplacing=0;
int $alwaysReCode=`checkBox -q -v asAutoLipSyncAlwaysReCodeCheckBox`;
float $timeRangeArray[]=`timeControl -q -rangeArray $gPlayBackSlider`;
if (($timeRangeArray[1]-$timeRangeArray[0])>1)
	$segmentReplacing=1;
float $currentTimeUnitToFPS=`currentTimeUnitToFPS`;
if ($currentTimeUnitToFPS<1) $currentTimeUnitToFPS=1;
string $mfaDir,$cmd,$genDictCmd,$ffMpegCmd,$pythonCmd,$uniDecoded;
string $tempString[];
string $languageMenuValue=`optionMenu -q -v asAutoLipSyncLanguageOptionMenu`;
string $language=`tolower $languageMenuValue`;
string $aligner=`text -q -l asAutoLipSyncAlignerText`;
$mfaDir=`asStripPath $aligner 2`;
string $lexicon=$mfaDir+"bin/librispeech-lexicon.txt";
string $ffmpeg=$mfaDir+"bin/ffmpeg.e";
if (`about -mac`)
	$ffmpeg=$mfaDir+"bin/ffmpeg";
string $mfaGD=$mfaDir+"bin/mfa_generate_dictionary";
string $g2pFile=$mfaDir+"pretrained_models/"+$language+"_g2p.zip"; 
string $tempDir=`asGetTempDirectory`+"AdvancedSkeleton/autoLipSync/";
string $audioNode = `timeControl -q -s $gPlayBackSlider`;
if (!`objExists $audioNode`)
	error "No sound found. Make sure to import a sound file first";
string $soundFile=`getAttr ($audioNode+".filename")`;
tokenize $soundFile "/" $tempString;
string $soundFileBaseName=$tempString[size($tempString)-1];
tokenize $soundFile "." $tempString;
string $soundFileExt=$tempString[size($tempString)-1];
string $forceSoundFile=$tempDir+"input/align."+$soundFileExt;
string $forceTextFile=$tempDir+"input/align.txt";
string $textGridFile=$tempDir+"output/align.TextGrid";
string $forceText=`scrollField -q -tx asAutoLipSyncUIScrollField`;

//non-english needs to mfa_generate_dictionary (lexicon) first
//use the g2p to make the dict, and then the zip (Pretrained acoustic models) to align
if ($language!="english")
	{
	$lexicon=$tempDir+"sessionLexicon.txt";
	if (`about -mac` || `about -linux`)
		$genDictCmd="\""+$mfaGD+"\" \""+$g2pFile+"\" \""+$tempDir+"input/\" \""+$lexicon+"\"";
	else
		$genDictCmd="start\/wait/I \"Generating Dictionary\" \""+$mfaGD+"\" \""+$g2pFile+"\" \""+$tempDir+"input/\" \""+$lexicon+"\"";
	}

if (`about -mac` || `about -linux`)
	{
	$cmd="\""+$aligner+"\" -c \""+$tempDir+"input/\" \""+$lexicon+"\" "+$language+" \""+$tempDir+"output/\"";
	$ffMpegCmd="start\/wait/I \"Coding Audio\" \""+$ffmpeg+"\" -y -i \""+$soundFile+"\"";
	}
else
	{
	$cmd="start\/wait/I \"Force Aligning\" \""+$aligner+"\" -c \""+$tempDir+"input/\" \""+$lexicon+"\" "+$language+" \""+$tempDir+"output/\"";
	$ffMpegCmd="start\/wait/I \"Coding Audio\" \""+$ffmpeg+"\" -y -i \""+$soundFile+"\"";
	}

if (!`file -q -ex $soundFile`)
	error "Not a valid sound file";

//Remove old results
$tempString=`getFileList -fld ($tempDir+"output")`;
for ($i=0;$i<size($tempString);$i++)
	sysFile -del ($tempDir+"output/"+$tempString[$i]);
if (`checkBox -q -v asAutoLipSyncNonLatinCheckBox` && !$segmentReplacing)
	{
	$tempString=`getFileList -fld ($tempDir+"render")`;
	for ($i=0;$i<size($tempString);$i++)
		sysFile -del ($tempDir+"render/"+$tempString[$i]);
	}

if (!`file -q -ex ($tempDir+"input")`) sysFile -md ($tempDir+"input");
if (!`file -q -ex ($tempDir+"output")`) sysFile -md ($tempDir+"output");
sysFile -cp $forceSoundFile $soundFile;

if ($segmentReplacing || $alwaysReCode)
	if (!`file -q -ex $ffmpeg`)
		asAutoLipSyncFfmpegDownload;
	
if ($segmentReplacing)
	{//If range in the timeLine is highlighted, then we assume to run "Correction" for this segment
	print ("// Segment selection found on Timeline-slider, trimming audio to selection.\n");
	$ffMpegCmd+=" -ss "+($timeRangeArray[0]/$currentTimeUnitToFPS)+" -to "+($timeRangeArray[1]/$currentTimeUnitToFPS)+" "+$forceSoundFile;
	print ("// Starting Trimming Audio:"+$ffMpegCmd+"\n");
	system ($ffMpegCmd);
	}
else if ($alwaysReCode)
	{
	$ffMpegCmd+=" "+$forceSoundFile;
	print ("// Starting ReCode Audio:"+$ffMpegCmd+"\n");
	system ($ffMpegCmd);	
	}

if (`latinWriting $languageMenuValue`)
	{
	//Unidecode to transliterates any unicode string into the closest possible representation in ascii text.
	//open file > strip_accents > save file, in Py, so we do not need to pass the text as variable

	$forceText=`substituteAllString $forceText "`" ""`;
	$forceText=`substituteAllString $forceText "'" " "`;
	$forceText=`substituteAllString $forceText "\n" " "`;
	$pythonCmd+="import unicodedata\n";
	$pythonCmd+="def strip_accents(s):\n";
	$pythonCmd+="	return ''.join(c for c in unicodedata.normalize('NFD', s)\n";
	$pythonCmd+="		if unicodedata.category(c) != 'Mn')\n";
	python ($pythonCmd);
	$uniDecoded=`python ("strip_accents(u'"+$forceText+"')")`;

	$fileId=`fopen $forceTextFile "w"`;
	fprint $fileId $uniDecoded;
	fclose $fileId;

	if ($uniDecoded!=$forceText)
		scrollField -e -tx $uniDecoded asAutoLipSyncUIScrollField;
	}

//non-english needs to mfa_generate_dictionary (lexicon) first
//use the g2p to make the dict, and then the zip (Pretrained acoustic models) to align
if ($language!="english")
	{
	print ("// Starting Generating Dictionary:"+$genDictCmd+"\n");
	if (`file -q -ex $lexicon`)
		sysFile -del $lexicon;
	system ($genDictCmd);
	if (!`file -q -ex $lexicon`)
		error ("Failed to create dictionary:\""+$lexicon+"\"");
	}

if ($forceText!="")
	{
	print ("// Starting Force Aligning:"+$cmd+"\n");
	system ($cmd);
	}
else
	{
	$fileId=`fopen $textGridFile "w"`;
	fprint $fileId "";
	fclose $fileId;
	}

if (`file -q -ex $textGridFile`)
	print ("// Successfully created:\""+$textGridFile+"\".\n");
else
	{
	if (`file -q -ex ($tempDir+"output/unaligned.txt")`)
		{
		$fileId=`fopen ($tempDir+"output/unaligned.txt") "r"`;
		$tempString[0]=`fread $fileId $tempString[0]`;
		print ($tempString[0]+"\n");
		fclose $fileId;
		}
	//Try with ffmpeg
	if (!$alwaysReCode)
		{
		if (`confirmDialog -title "Confirm" -message "Force Align Failed.\nThis could be a compatibility issues with the sound-file.\nTry to re-code sound-file?"
  	  -button "Yes" -button "No" -defaultButton "Yes"
    	-cancelButton "No" -dismissString "No"`=="Yes")
    		{
    		checkBox -e -v 1 asAutoLipSyncAlwaysReCodeCheckBox;
    		asAutoLipSyncForceAlign $nameSpace;
    		return;
    		}
		}
	error ("Failed to create:\""+$textGridFile+"\".\n");
	}

asAutoLipSyncImport $nameSpace;
}

global proc asAutoLipSyncImport (string $nameSpace)
{
global string $gMainProgressBar;
global string $gPlayBackSlider;
int $segmentReplacing=0;
float $timeRangeArray[]=`timeControl -q -rangeArray $gPlayBackSlider`;
int $nonLatin=`checkBox -q -v asAutoLipSyncNonLatinCheckBox`;
int $wordsSection,$phonesSection,$fileId;
int $autoBlink=`checkBox -q -v asAutoLipSyncBlinkCheckBox`;
int $autoEyeBrows=`checkBox -q -v asAutoLipSyncEyeBrowsCheckBox`;
int $autoHead=`checkBox -q -v asAutoLipSyncHeadCheckBox`;
int $lastKeyFrameNr,$nextBlinkTriggFrameNr,$nextHeadTriggFrameNr,$wordNr;
if (!`objExists ($nameSpace+"FKHead_M")`) $autoHead=0;
float $prePhoneTime=`floatField -q -v asAutoLipSyncPhonemeInFloatField`;
float $postPhoneTime=`floatField -q -v asAutoLipSyncPhonemeOutFloatField`;
float $sequenceStart=-1;
float $sequenceEnd=-1;
float $wordSpace=0.25;
float $scale=1.5;
float $currentTimeUnitToFPS=`currentTimeUnitToFPS`;
if ($currentTimeUnitToFPS<1) $currentTimeUnitToFPS=1;
float $xMin,$xMax,$segRepOffset,$keyValue,$previousKeyTime;
float $wordStarts[],$wordEnds[],$phoneStarts[],$phoneEnds[],$bb[],$keyTimes[],$pos[],$pos1[],$pos2[];
string $aligner=`text -q -l asAutoLipSyncAlignerText`;
string $tempDir=`asGetTempDirectory`+"AdvancedSkeleton/autoLipSync/";
string $textGridFile=$tempDir+"output/align.TextGrid";
string $nextLine,$para,$value,$imConvertCmd,$pythonCmd,$fileRead,$font;
string $tempString[],$words[],$phones[],$missingPhoneParts[],$letters[],$deleteObjs[],$texts[],$chars[];
string $currentTimeUnit=`currentUnit -q -t`;
string $audioNode = `timeControl -q -s $gPlayBackSlider`;
string $languageMenuValue=`optionMenu -q -v asAutoLipSyncLanguageOptionMenu`;
string $language=`tolower $languageMenuValue`;
float $offset=`getAttr ($audioNode+".offset")`/$currentTimeUnitToFPS;

if (!`objExists ($nameSpace+"ctrlPhonemes_M")`)
	error ($nameSpace+"ctrlPhonemes_M not found");

if (`objExists ($nameSpace+"FaceFitSkeletonHeightShape")`)
    {
    $pos1=`xform -q -ws -t ($nameSpace+"FaceFitSkeletonHeightShape.cv[5]")`;
    $pos2=`xform -q -ws -t ($nameSpace+"FaceFitSkeletonShape.cv[5]")`;
    $scale=$pos1[1]-$pos2[1];
   	}

if (($timeRangeArray[1]-$timeRangeArray[0])>1 && `objExists ($nameSpace+"subTitles")`)
	$segmentReplacing=1;

if ($segmentReplacing)
	{
	//Remove all subtitles in segment (and onwards
	select -cl;
	$pos=`xform -q -ws -t ($nameSpace+"subTitlesArrowShape.cv[3]")`;
	$texts=`listRelatives -c ($nameSpace+"subTitlesRoller")`;
	currentTime $timeRangeArray[0];
	$previousKeyTime=`findKeyframe -which previous ($nameSpace+"subTitlesRoller.tx")`;

	for ($i=0;$i<size($texts);$i++)
		{
		$chars=`listRelatives -c -type transform $texts[$i]`;
		if ($nonLatin) $chars[0]=$texts[$i];
		for ($y=0;$y<size($chars);$y++)
			if ((`getAttr -t $previousKeyTime ($nameSpace+"subTitlesRoller.tx")`*-1)<=`getAttr ($texts[$i]+".tx")`)
				select -add $texts[$i];
		}

	if (size(`ls -sl`))
		delete;

	//Remove subTitlesRoller keyframes as well
	if (`objExists ($nameSpace+"subTitlesRoller")`)
		cutKey -time ($timeRangeArray[0]+":99999") -attribute translateX -option keys ($nameSpace+"subTitlesRoller");
	cutKey -time ($timeRangeArray[0]+":99999") -option keys ($nameSpace+"ctrlPhonemes_M");
	}

currentUnit -t "sec";
evalDeferred ("catchQuiet(`currentUnit -t "+$currentTimeUnit+"`)");

if ($segmentReplacing)
	$segRepOffset=`currentTime -q`;

if ($segmentReplacing)
	{
	if (`objExists subTitles_parentConstraint1`)
		delete subTitles_parentConstraint1;
	if (`objExists ($nameSpace+"subTitles")`)
		xform -ws -t 0 0 0 -ro 0 0 0 -s 1 1 1 ($nameSpace+"subTitles");
	}
else
	{
	$deleteObjs={"subTitles","subTitlesExp","subTitlesBox","subTitlesArrow","subTitlesBoxMPD"};
	for ($i=0;$i<size($deleteObjs);$i++)
		if (`objExists ($nameSpace+$deleteObjs[$i])`)
			delete ($nameSpace+$deleteObjs[$i]);

	createNode -n ($nameSpace+"subTitles") transform;
	createNode -n ($nameSpace+"subTitlesRoller") -p ($nameSpace+"subTitles") transform;
	$tempString[0]=`curve -d 1 -p -4 1.25 0 -p -4 -0.5 0 -p 4 -0.5 0 -p 4 1.25 0 -p -4 1.25 0 -k 0 -k 1 -k 2 -k 3 -k 4`;
	rename $tempString[0] ($nameSpace+"subTitlesBox");
	setAttr ($nameSpace+"subTitlesBoxShape.overrideEnabled") 1;
	setAttr ($nameSpace+"subTitlesBoxShape.overrideColor") 13;
	$tempString[0]=`curve -d 1 -p 0 0.65 0 -p 0.127 1.2 0 -p -0.127 1.2 0 -p 0 0.65 0 -k 0 -k 1 -k 2 -k 3`;
	parent ($nameSpace+"subTitlesBox") ($nameSpace+"subTitles");
	addAttr -k 1 -ln textVisCenter -at double -dv 4.5 ($nameSpace+"subTitlesBox");
	addAttr -k 1 -ln textVisLenght -at double -dv 8 ($nameSpace+"subTitlesBox");
	createNode -n ($nameSpace+"subTitlesBoxMPD") multiplyDivide;
	connectAttr ($nameSpace+"subTitlesBox.sx") ($nameSpace+"subTitlesBoxMPD.input1X");
	connectAttr ($nameSpace+"subTitlesBox.sx") ($nameSpace+"subTitlesBoxMPD.input1Y");
	setAttr ($nameSpace+"subTitlesBoxMPD.input2") -type float3 4 8 0;
	connectAttr ($nameSpace+"subTitlesBoxMPD.outputX") ($nameSpace+"subTitlesBox.textVisCenter");
	connectAttr ($nameSpace+"subTitlesBoxMPD.outputY") ($nameSpace+"subTitlesBox.textVisLenght");

	rename $tempString[0] ($nameSpace+"subTitlesArrow");
	setAttr ($nameSpace+"subTitlesArrowShape.overrideEnabled") 1;
	setAttr ($nameSpace+"subTitlesArrowShape.overrideColor") 13;
	parent -add -s ($nameSpace+"subTitlesArrowShape") ($nameSpace+"subTitlesBox");
	delete ($nameSpace+"subTitlesArrow");

	$tempString=`listConnections -s 1 -d 0 -type animCurve ($nameSpace+"ctrlPhonemes_M")`;
	if (size($tempString)) delete $tempString;
	$tempString=`listAttr -ud ($nameSpace+"ctrlPhonemes_M")`;
	for ($i=0;$i<size($tempString);$i++)
		if (!`getAttr -l ($nameSpace+"ctrlPhonemes_M."+$tempString[$i])`)
			setAttr ($nameSpace+"ctrlPhonemes_M."+$tempString[$i]) 0;
	if (`attributeExists jaw ($nameSpace+"ctrlPhonemes_M")`)//could have converted to SimplifiedFaceSetup
		{
		setAttr ($nameSpace+"ctrlPhonemes_M.jaw") 0.25;
		setAttr ($nameSpace+"ctrlPhonemes_M.lip") 0.25;
		}
	}

$fileId=`fopen $textGridFile "r"`;
$nextLine = `fgetline $fileId`;
while (size($nextLine)>0)
	{
	$line=`strip $nextLine`;
	tokenize $line " = " $tempString;
	$para=$tempString[0];
	$value=$tempString[1];
	if ($sequenceStart==-1 && $para=="xmin") $sequenceStart=$value;
	if ($sequenceEnd==-1 && $para=="xmax") $sequenceEnd=$value;
	if ($para=="name" && $value=="\"words\"") $wordsSection=1;
	if ($para=="name" && $value=="\"phones\"") {$phonesSection=1;$wordsSection=0;}
	if ($para=="xmin") $xMin=$value;
	if ($para=="xmax") $xMax=$value;
	if ($wordsSection && $para=="text")
		{
		$tempString[0]=`substituteAllString $value "\"" ""`;
		if ($tempString[0]!="")
			{
			$words[size($words)]=$tempString[0];
			$wordStarts[size($wordStarts)]=$xMin+$segRepOffset+$offset;
			$wordEnds[size($wordEnds)]=$xMax+$segRepOffset+$offset;
			}
		}
	if ($phonesSection && $para=="text")
		{
		$tempString[0]=`substituteAllString $value "\"" ""`;
		if ($tempString[0]!="")
			{
			$phones[size($phones)]=$tempString[0];
			$phoneStarts[size($phoneStarts)]=$xMin+$segRepOffset+$offset;
			$phoneEnds[size($phoneEnds)]=$xMax+$segRepOffset+$offset;
			}
		}
	$nextLine=`fgetline $fileId`;
	}
fclose $fileId;

for ($i=0;$i<size($words);$i++)
	if ($words[$i]=="<unk>")
		$words[$i]="***";

if (!$segmentReplacing)
	playbackOptions -min ($sequenceStart+$offset) -ast ($sequenceStart+$offset) -aet ($sequenceEnd+$offset) -max ($sequenceEnd+$offset);

//ensure key of value 0 at frame 0
setKeyframe -itt linear -ott linear -t 0 -v 0 ($nameSpace+"subTitlesRoller.tx");

for ($i=0;$i<9999;$i++)
	if (!`objExists ($nameSpace+"text"+$i)`)
		break;
$wordNr=$i;

//render non supported symbols, from utf-8 format text files
if ($nonLatin)
	{
	if (!`file -q -ex ($tempDir+"render")`)
		sysFile -md ($tempDir+"render");
	$pythonCmd="";
	$pythonCmd+="wordsSection = 0\n";
	$pythonCmd+="wordNr = "+$wordNr+"\n";
	$pythonCmd+="searchfile = open('"+$textGridFile+"','r')\n";
	$pythonCmd+="for line in searchfile:\n";
	$pythonCmd+="	if line.find('name = \"words\"') != -1:\n";
	$pythonCmd+="		wordsSection = 1\n";
	$pythonCmd+="	if line.find('name = \"phones\"') != -1:\n";
	$pythonCmd+="		wordsSection = 0\n";
	$pythonCmd+="	if wordsSection == 1:\n";
	$pythonCmd+="		if line.find('text = ') != -1:\n";
	$pythonCmd+="			word = line.split()[2].replace('\"','')\n";
	$pythonCmd+="			if word == '':\n";
	$pythonCmd+="				continue\n";
	$pythonCmd+="			wordFile = open ('"+$tempDir+"render/word'+str(wordNr)+'.txt','w')\n";
	$pythonCmd+="			wordFile.write (word)\n";
	$pythonCmd+="			wordFile.close ()\n";
	$pythonCmd+="			wordNr = wordNr + 1\n";
	$pythonCmd+="searchfile.close()\n";
	python ($pythonCmd);
	}

evalDeferred ("progressBar -e -ep "+$gMainProgressBar);
progressBar -e -st "Reading Data" -bp -ii 1 -min 0 -max (size($words)+1) $gMainProgressBar;

for ($i=0;$i<size($words);$i++)
	{
	progressBar -e -s 1 $gMainProgressBar;
	if (`progressBar -q -ic $gMainProgressBar`)
		error "Interrupted";
	currentTime $wordStarts[$i];
	if ($nonLatin)
		{
		$font="arial";
		if ($language=="thai") $font="tahoma";
		if ($language=="korean") $font="Malgun-Gothic";
		$imConvertCmd="imconvert -background black -fill white -font "+$font+" -pointsize 72 label:@"+$tempDir+"render/word"+$wordNr+".txt "+$tempDir+"render/word"+$wordNr+".png";
		system ($imConvertCmd);
		$tempString=`ls ($nameSpace+"text*"+$wordNr)`;
		for ($y=0;$y<size($tempString);$y++)
			if (`objExists $tempString[$y]`) delete $tempString[$y];
		polyPlane -n ($nameSpace+"text"+$wordNr) -w 1 -h 1 -sx 1 -sy 1 -ax 0 0 1 -cuv 2 -ch 0;
		shadingNode -n ($nameSpace+"textShader"+$wordNr) -asShader surfaceShader;
		sets -renderable true -noSurfaceShader true -empty -name ($nameSpace+"textSG"+$wordNr);
		connectAttr -f ($nameSpace+"textShader"+$wordNr+".outColor") ($nameSpace+"textSG"+$wordNr+".surfaceShader");
		sets -e -forceElement ($nameSpace+"textSG"+$wordNr) ($nameSpace+"text"+$wordNr);
		createNode -n ($nameSpace+"textFile"+$wordNr) file;
		connectAttr -force ($nameSpace+"textFile"+$wordNr+".outColor") ($nameSpace+"textShader"+$wordNr+".outColor");
		setAttr -type "string" ($nameSpace+"textFile"+$wordNr+".fileTextureName") ($tempDir+"render/word"+$wordNr+".png");
		move -r -ws -wd 0.5 0 0 ($nameSpace+"text"+$wordNr+".vtx[0:99]");
		scale -r -p 0 0 0 ((size($words[$i])/2.25)) 1 1 ($nameSpace+"text"+$wordNr+".vtx[0:99]");

//		setAttr ($nameSpace+"text"+$wordNr+".sx") ((size($words[$i])/2.0));
		}
	else
		{
		$tempString=`textCurves -f "Times New Roman|wt:50|sz:28" -t $words[$i]`;
		delete `ls -type makeTextCurves`;
		rename $tempString[0] ($nameSpace+"text"+$wordNr);
		}

	setAttr ($nameSpace+"text"+$wordNr+".tx") $wordSpace;//word spacing
	parent ($nameSpace+"text"+$wordNr) ($nameSpace+"subTitlesRoller");
	setKeyframe -itt linear -ott linear -t $wordStarts[$i] -v (`getAttr ($nameSpace+"subTitlesRoller.tx")`-$wordSpace) ($nameSpace+"subTitlesRoller.tx");
	$bb=`xform -q -ws -bb ($nameSpace+"text"+$wordNr)`;
	$keyValue=`getAttr ($nameSpace+"subTitlesRoller.tx")`-($bb[0]+$bb[3]);
	setKeyframe -itt linear -ott linear -t $wordEnds[$i] -v $keyValue ($nameSpace+"subTitlesRoller.tx");

	$wordNr++;
	}

for ($i=0;$i<size($phones);$i++)
	{
	if ($phones[$i]=="sil" || $phones[$i]=="sp" || $phones[$i]=="spn") 
		continue;
//	if ($language=="english")
		$phoneme=`asAutoLipSyncPhonemeMapping $phones[$i]`;
//	else
//		$phoneme=`substring $language 1 3`+"_"+`asPhonemeTranslate $phones[$i] 0`;
	if ($phoneme=="" || !`attributeExists $phoneme ($nameSpace+"ctrlPhonemes_M")`)
		{
		if (!`stringArrayCount $phoneme $missingPhoneParts`)
			$missingPhoneParts[size($missingPhoneParts)]="Phoneme:\""+$phoneme+"\", from the phone:	\""+$phones[$i]+"\"";
		continue;
		}

	setKeyframe -v 0 -t ($phoneStarts[$i]-$prePhoneTime) ($nameSpace+"ctrlPhonemes_M."+$phoneme);
	setKeyframe -v 10 -t (($phoneStarts[$i]+$phoneEnds[$i])/2.0) ($nameSpace+"ctrlPhonemes_M."+$phoneme);
	setKeyframe -v 0 -t ($phoneEnds[$i]+$postPhoneTime) ($nameSpace+"ctrlPhonemes_M."+$phoneme);
	}

if (size($missingPhoneParts))
	{
	print "Found the following phonemes, that are not mapped to the current FaceSetup:\n";
	print $missingPhoneParts;
	}
progressBar -e -ep $gMainProgressBar;

currentTime 0;
currentUnit -t $currentTimeUnit;

//Find each letter`s offset, and animate visibility to be visible only inside box
//currentUnit -t "pal";
currentTime 0 ;
$tempString=`listRelatives -ad -type transform ($nameSpace+"subTitlesRoller")`;
for ($i=0;$i<size($tempString);$i++)
	{
	if (!`gmatch $tempString[$i] "Char_*"` && !$nonLatin)
		continue;
	$letters[size($letters)]=$tempString[$i];
	}
$bb=`xform -q -ws -bb ($nameSpace+"subTitlesBox")`;
$boxWidth=($bb[3]-$bb[0])/`getAttr ($nameSpace+"subTitles.sx")`;
if ($nameSpace!="" && `objExists subTitlesRoller_translateX`)
	rename subTitlesRoller_translateX ($nameSpace+"subTitlesRoller_translateX");
$keyTimes=`keyframe -q -tc ($nameSpace+"subTitlesRoller_translateX")`;
$lastKeyFrameNr=$keyTimes[size($keyTimes)-1];
progressBar -e -st "Animating text visibility" -bp -ii 1 -min 0 -max ($lastKeyFrameNr+1) $gMainProgressBar;
for ($y=0;$y<size($letters);$y++)
	{
	if (`objExists ($letters[$y]+"PMA")`)
		continue;
	createNode -n ($letters[$y]+"PMA") plusMinusAverage;
	connectAttr -f ($nameSpace+"subTitlesRoller_translateX.output") ($letters[$y]+"PMA.input1D[1]");
	connectAttr -f ($nameSpace+"subTitlesBox.textVisCenter") ($letters[$y]+"PMA.input1D[2]");
	$bb=`xform -q -ws -bb $letters[$y]`;
	setAttr ($letters[$y]+"PMA.input1D[0]") ($bb[0]+0.5);
	createNode -n ($letters[$y]+"Clamp") clamp;
	connectAttr -f ($letters[$y]+"PMA.output1D") ($letters[$y]+"Clamp.inputR");
	setAttr ($letters[$y]+"Clamp.maxR") 1000;
	createNode -n ($letters[$y]+"Condition") condition;
	setAttr ($letters[$y]+"Condition.operation") 2;
	connectAttr -f ($letters[$y]+"Clamp.outputR") ($letters[$y]+"Condition.firstTerm");
	connectAttr -f ($letters[$y]+"Clamp.outputR") ($letters[$y]+"Condition.colorIfFalseR");
	connectAttr -f ($nameSpace+"subTitlesBox.textVisLenght") ($letters[$y]+"Condition.secondTerm");
	connectAttr -f ($letters[$y]+"Condition.outColorR") ($letters[$y]+".v");
	}

//Place subTitles
$bb=`xform -q -ws -bb ($nameSpace+"subTitles")`;
setAttr ($nameSpace+"subTitles.s") -type float3 ($scale/10.0) ($scale/10.0) ($scale/10.0);
$bb=`xform -q -ws -bb ($nameSpace+"ctrlBox")`;
$bb2=`xform -q -ws -bb ($nameSpace+"ctrlPhonemes_M")`;
$bb3=`xform -q -ws -bb ($nameSpace+"subTitlesBox")`;
xform -ws -t ($bb[3]+$bb3[3]*1.2) $bb2[1] $bb[5] ($nameSpace+"subTitles");
parentConstraint -mo ($nameSpace+"ctrlBox") ($nameSpace+"subTitles");

if ($segmentReplacing)
	{
	select -r ($nameSpace+"subTitles");
	currentTime $timeRangeArray[0];
	dgdirty -a;
	return;
	}

//Remove existing animation, if Idle animations is to be added
if ($autoBlink)
	{
	$tempString=`listConnections -s 1 -d 0 -type animCurve ($nameSpace+"ctrlEye_R") ($nameSpace+"ctrlEye_L")`;
	if (size($tempString)) delete $tempString;
	}
if ($autoEyeBrows)
	{
	$tempString=`listConnections -s 1 -d 0 -type animCurve ($nameSpace+"ctrlBrow_R") ($nameSpace+"ctrlBrow_L")`;
	if (size($tempString)) delete $tempString;
	}
if ($autoHead)
	{
	$tempString=`listConnections -s 1 -d 0 -type animCurve ($nameSpace+"FKHead_M")`;
	if (size($tempString)) delete $tempString;
	}


//Add Idle animations
if ($autoHead)
	setAttr ($nameSpace+"AimEye_M.follow") 0;
$nextBlinkTriggFrameNr=$keyTimes[0]+`rand 25 40`;
$nextHeadTriggFrameNr=$keyTimes[0]+`rand 80 100`;
for ($i=1;$i<`playbackOptions -q -max`;$i++)
	{
	if ($i<$keyTimes[0])
		continue;//no Idle-anim before first word
	if ($autoBlink && $i==$nextBlinkTriggFrameNr)
		{
		$nextBlinkTriggFrameNr=$i+`rand 50 250`;
		setKeyframe -t $i -v 1.5 ($nameSpace+"ctrlEye_L.blink") ($nameSpace+"ctrlEye_R.blink");
		setKeyframe -t ($i+1) -v 10 ($nameSpace+"ctrlEye_L.blink") ($nameSpace+"ctrlEye_R.blink");
		setKeyframe -t ($i+3) -v 10 ($nameSpace+"ctrlEye_L.blink") ($nameSpace+"ctrlEye_R.blink");
		setKeyframe -t ($i+6) -v 1.5 ($nameSpace+"ctrlEye_L.blink") ($nameSpace+"ctrlEye_R.blink");

		setKeyframe -t ($i+(($nextBlinkTriggFrameNr-$i)*1/4)) -v 2.5 ($nameSpace+"ctrlEye_L.blink") ($nameSpace+"ctrlEye_R.blink");
		setKeyframe -t ($i+(($nextBlinkTriggFrameNr-$i)*3.8/4)) -v 2.5 ($nameSpace+"ctrlEye_L.blink") ($nameSpace+"ctrlEye_R.blink");
		//squint
		$tempFloat[0]=`rand 1 5`;
		setKeyframe -t ($i+(($nextBlinkTriggFrameNr-$i)*1.5/4)) -v 0 ($nameSpace+"ctrlEye_L.squint") ($nameSpace+"ctrlEye_R.squint");
		setKeyframe -t ($i+(($nextBlinkTriggFrameNr-$i)*2/4)) -v $tempFloat[0] ($nameSpace+"ctrlEye_L.squint") ($nameSpace+"ctrlEye_R.squint");
		setKeyframe -t ($i+(($nextBlinkTriggFrameNr-$i)*3.8/4)) -v $tempFloat[0] ($nameSpace+"ctrlEye_L.squint") ($nameSpace+"ctrlEye_R.squint");
		setKeyframe -t ($i+(($nextBlinkTriggFrameNr-$i)*4/4)) -v 0 ($nameSpace+"ctrlEye_L.squint") ($nameSpace+"ctrlEye_R.squint");

		}
	if ($autoHead && $i==$nextHeadTriggFrameNr)
		{
		$nextHeadTriggFrameNr=$i+`rand 25 300`;
		$moveDur=`rand 5 20`;
		$tempFloat=`getAttr -t $i ($nameSpace+"FKHead_M.r")`;
		setKeyframe -t $i -v $tempFloat[0] ($nameSpace+"FKHead_M.rx");
		setKeyframe -t $i -v $tempFloat[1] ($nameSpace+"FKHead_M.ry");
		setKeyframe -t $i -v $tempFloat[2] ($nameSpace+"FKHead_M.rz");
			setKeyframe -t ($i+($moveDur/2.0)) -v ($tempFloat[2]-4) ($nameSpace+"FKHead_M.rz");
		setKeyframe -t ($i+$moveDur) -v `rand -8 8` ($nameSpace+"FKHead_M.rx");
		setKeyframe -t ($i+$moveDur) -v `rand -4 4` ($nameSpace+"FKHead_M.ry");
		setKeyframe -t ($i+$moveDur) -v `rand -2 2` ($nameSpace+"FKHead_M.rz");
		if ($autoEyeBrows)
			{
			$tempFloat=`getAttr -t $i ($nameSpace+"ctrlBrow_R.t")`;
			setKeyframe -t ($i+($moveDur/2.0)) -v $tempFloat[0] ($nameSpace+"ctrlBrow_R.tx") ($nameSpace+"ctrlBrow_L.tx");
			setKeyframe -t ($i+($moveDur/2.0)) -v $tempFloat[1] ($nameSpace+"ctrlBrow_R.ty") ($nameSpace+"ctrlBrow_L.ty");
			setKeyframe -t ($i+$moveDur) -v `rand -0.8 0.8` ($nameSpace+"ctrlBrow_R.tx") ($nameSpace+"ctrlBrow_L.tx");
			setKeyframe -t ($i+$moveDur) -v `rand -0.8 0.8` ($nameSpace+"ctrlBrow_R.ty") ($nameSpace+"ctrlBrow_L.ty");
			}
		}
	}

if ($nonLatin && !$segmentReplacing)
	setAttr ($nameSpace+"subTitlesBox.sx") 4;

select -r ($nameSpace+"subTitles");
dgdirty -a;
}

global proc string asAutoLipSyncPhonemeMapping (string $phone)
{
//We will do all upperCase, since the English "meta.yaml" is all upperCase
$phone=`toupper $phone`;

string $phoneme;
//english
if ($phone=="EY1") $phoneme="aaa";
if ($phone=="Z") $phoneme="sss";
if ($phone=="B") $phoneme="mbp";
if ($phone=="AA2") $phoneme="aaa";
if ($phone=="D") $phoneme="lntd";
if ($phone=="IY0") $phoneme="tth";//
if ($phone=="K") $phoneme="gk";
if ($phone=="AO2") $phoneme="ohh";
if ($phone=="R") $phoneme="rrr";
if ($phone=="T") $phoneme="lntd";
if ($phone=="G") $phoneme="gk";
if ($phone=="AH0") $phoneme="ahh";//
if ($phone=="OY1") $phoneme="ohh";
if ($phone=="N") $phoneme="lntd";
if ($phone=="L") $phoneme="lntd";
if ($phone=="M") $phoneme="mbp";
if ($phone=="AY1") $phoneme="ahh";//
if ($phone=="OW2") $phoneme="ohh";
if ($phone=="S") $phoneme="sss";
if ($phone=="P") $phoneme="mbp";
if ($phone=="EH2") $phoneme="eh";
if ($phone=="IY1") $phoneme="iee";
if ($phone=="AY2") $phoneme="aaa";
if ($phone=="OW1") $phoneme="ohh";
if ($phone=="ER0") $phoneme="schwa";//
if ($phone=="UW1") $phoneme="ohh";
if ($phone=="W") $phoneme="www";
if ($phone=="DH") $phoneme="lntd";
if ($phone=="JH") $phoneme="ssh";//
if ($phone=="IH1") $phoneme="iee";
if ($phone=="IH0") $phoneme="iee";
if ($phone=="NG") $phoneme="gk";
if ($phone=="IH2") $phoneme="eh";
if ($phone=="V") $phoneme="schwa";//
if ($phone=="AA1") $phoneme="ohh";
if ($phone=="TH") $phoneme="lntd";
if ($phone=="ZH") $phoneme="ssh";
if ($phone=="AE2") $phoneme="aaa";
if ($phone=="SH") $phoneme="ssh";
if ($phone=="EH1") $phoneme="eh";
if ($phone=="AA0") $phoneme="aaa";
if ($phone=="F") $phoneme="fff";
if ($phone=="AW1") $phoneme="www";
if ($phone=="AE1") $phoneme="aaa";
if ($phone=="OW0") $phoneme="ohh";
if ($phone=="AW2") $phoneme="aaa";
if ($phone=="EY2") $phoneme="eh";
if ($phone=="EY0") $phoneme="aaa";
if ($phone=="AE0") $phoneme="aaa";
if ($phone=="IY2") $phoneme="iee";
if ($phone=="CH") $phoneme="ssh";
if ($phone=="ER1") $phoneme="ahh";
if ($phone=="EH0") $phoneme="ahh";//
if ($phone=="UW2") $phoneme="ohh";
if ($phone=="AH1") $phoneme="ohh";
if ($phone=="HH") $phoneme="schwa";//
if ($phone=="AH2") $phoneme="ahh";
if ($phone=="AO0") $phoneme="ahh";
if ($phone=="OY2") $phoneme="ohh";
if ($phone=="OY0") $phoneme="ohh";
if ($phone=="UH1") $phoneme="ohh";
if ($phone=="AO1") $phoneme="ohh";
if ($phone=="UW0") $phoneme="ohh";
if ($phone=="Y") $phoneme="uuu";//
if ($phone=="AW0") $phoneme="aaa";
if ($phone=="AY0") $phoneme="ahh";
if ($phone=="ER2") $phoneme="ohh";
if ($phone=="UH0") $phoneme="uuu";
if ($phone=="UH2") $phoneme="ohh";

//bulgarian
if ($phone=="A") $phoneme="ahh";
if ($phone=="BJ") $phoneme="mbp";
if ($phone=="DZ") $phoneme="lntd";
if ($phone=="DJ") $phoneme="lntd";
if ($phone=="E") $phoneme="eh";
if ($phone=="FJ") $phoneme="fff";
if ($phone=="GJ") $phoneme="gk";
if ($phone=="I") $phoneme="iee";
if ($phone=="J") $phoneme="ssh";
if ($phone=="JA") $phoneme="tth";
if ($phone=="JU") $phoneme="tth";
if ($phone=="KJ") $phoneme="gk";
if ($phone=="LJ") $phoneme="lntd";
if ($phone=="MJ") $phoneme="mbp";
if ($phone=="NJ") $phoneme="lntd";
if ($phone=="O") $phoneme="ohh";
if ($phone=="PJ") $phoneme="mbp";
if ($phone=="RJ") $phoneme="rrr";
if ($phone=="SJ") $phoneme="ssh";
if ($phone=="TS") $phoneme="ssh";
if ($phone=="TJ") $phoneme="tth";
if ($phone=="U") $phoneme="uuu";
if ($phone=="VJ") $phoneme="schwa";
if ($phone=="X") $phoneme="ohh";
if ($phone=="ZJ") $phoneme="sss";

//Croatian
if ($phone=="DZP") $phoneme="lntd";
if ($phone=="tcp") $phoneme="sss";
if ($phone=="x") $phoneme="";//covered in bulgarian

//Czech
if ($phone=="AA") $phoneme="aaa";
if ($phone=="AW") $phoneme="ahh";
if ($phone=="C") $phoneme="ssh";
if ($phone=="EE") $phoneme="eh";
if ($phone=="EW") $phoneme="eh";
if ($phone=="H") $phoneme="schwa";
if ($phone=="II") $phoneme="iee";
if ($phone=="MG") $phoneme="mbp";
if ($phone=="OO") $phoneme="ohh";
if ($phone=="OW") $phoneme="ohh";
if ($phone=="RSH") $phoneme="rrr";
if ($phone=="RZH") $phoneme="rrr";
if ($phone=="UU") $phoneme="uuu";
 
//French
if ($phone=="AE") $phoneme="eh";
if ($phone=="AX") $phoneme="aaa";
if ($phone=="A~") $phoneme="ahh";
if ($phone=="EU") $phoneme="uuu";
if ($phone=="E~") $phoneme="eh";
if ($phone=="OE") $phoneme="ohh";
if ($phone=="OE~") $phoneme="ohh";
if ($phone=="o~") $phoneme="ohh";

//german
if ($phone=="+hGH") $phoneme="gk";
if ($phone=="C") $phoneme="ssh";
if ($phone=="AI") $phoneme="aaa";
if ($phone=="AU") $phoneme="aaa";
if ($phone=="AL") $phoneme="aaa";
if ($phone=="ATU") $phoneme="lntd";
if ($phone=="EL") $phoneme="eh";
if ($phone=="ETU") $phoneme="lntd";
if ($phone=="IL") $phoneme="lntd";
if ($phone=="OEL") $phoneme="lntd";
if ($phone=="OL") $phoneme="ohh";
if ($phone=="UE") $phoneme="uuu";
if ($phone=="UEL") $phoneme="lntd";
if ($phone=="UL") $phoneme="uuu";

//Hausa
if ($phone=="KR") $phoneme="gk";
if ($phone=="Q") $phoneme="uuu";
if ($phone=="AI") $phoneme="";//covered in german
if ($phone=="AU") $phoneme="";//covered in german
if ($phone=="A_L") $phoneme="aaa";
if ($phone=="A_S") $phoneme="aaa";
if ($phone=="A_T1") $phoneme="aaa";;
if ($phone=="A_T2") $phoneme="aaa";;
if ($phone=="A_T3") $phoneme="aaa";;
if ($phone=="E_L") $phoneme="eh";
if ($phone=="E_S") $phoneme="eh";
if ($phone=="E_T1") $phoneme="eh";
if ($phone=="E_T2") $phoneme="eh";
if ($phone=="I_L") $phoneme="iee";
if ($phone=="I_S") $phoneme="iee";
if ($phone=="I_T1") $phoneme="iee";
if ($phone=="I_T2") $phoneme="iee";
if ($phone=="I_T3") $phoneme="iee";
if ($phone=="O_L") $phoneme="ohh";
if ($phone=="O_S") $phoneme="ohh";
if ($phone=="O_T1") $phoneme="ohh";
if ($phone=="O_T2") $phoneme="ohh";
if ($phone=="U_L") $phoneme="uuu";
if ($phone=="U_S") $phoneme="uuu";
if ($phone=="U_T1") $phoneme="uuu";
if ($phone=="U_T2") $phoneme="uuu";

//Korean
if ($phone=="BB") $phoneme="mbp";
if ($phone=="CHH") $phoneme="ssh";
if ($phone=="DD") $phoneme="lntd";
if ($phone=="EO") $phoneme="eh";
if ($phone=="GG") $phoneme="gk";
if ($phone=="JJ") $phoneme="ssh";
if ($phone=="KH") $phoneme="gk";
if ($phone=="PH") $phoneme="mbp";
if ($phone=="SS") $phoneme="sss";
if ($phone=="EUI") $phoneme="eh";
if ($phone=="IA") $phoneme="ahh";
if ($phone=="IE") $phoneme="iee";
if ($phone=="IEO") $phoneme="";
if ($phone=="IO") $phoneme="ohh";
if ($phone=="IU") $phoneme="uuu";
if ($phone=="OA") $phoneme="ohh";
if ($phone=="UEO") $phoneme="ohh";

//Mandarin (pinyin)
if ($phone=="A1") $phoneme="ahh";
if ($phone=="A2") $phoneme="ahh";
if ($phone=="A3") $phoneme="ahh";
if ($phone=="A4") $phoneme="ahh";
if ($phone=="A5") $phoneme="ahh";
if ($phone=="AI1") $phoneme="ahh";
if ($phone=="AI2") $phoneme="ahh";
if ($phone=="AI3") $phoneme="ahh";
if ($phone=="AI4") $phoneme="ahh";
if ($phone=="AI5") $phoneme="ahh";
if ($phone=="AO1") $phoneme="ohh";
if ($phone=="AO2") $phoneme="ohh";
if ($phone=="AO3") $phoneme="ohh";
if ($phone=="AO4") $phoneme="ohh";
if ($phone=="AO5") $phoneme="ohh";
if ($phone=="E1") $phoneme="eh";
if ($phone=="E2") $phoneme="eh";
if ($phone=="E3") $phoneme="eh";
if ($phone=="E4") $phoneme="eh";
if ($phone=="E5") $phoneme="eh";
if ($phone=="EI1") $phoneme="eh";
if ($phone=="EI2") $phoneme="eh";
if ($phone=="EI3") $phoneme="eh";
if ($phone=="EI4") $phoneme="eh";
if ($phone=="I1") $phoneme="iee"; 
if ($phone=="I2") $phoneme="iee";
if ($phone=="I3") $phoneme="iee";
if ($phone=="I4") $phoneme="iee";
if ($phone=="I5") $phoneme="iee";
if ($phone=="IA1") $phoneme="iee";
if ($phone=="IA2") $phoneme="iee";
if ($phone=="IA3") $phoneme="iee";
if ($phone=="IA4") $phoneme="iee";
if ($phone=="IA5") $phoneme="iee";
if ($phone=="IAO1") $phoneme="iee";
if ($phone=="IAO2") $phoneme="iee";
if ($phone=="IAO3") $phoneme="iee";
if ($phone=="IAO4") $phoneme="iee";
if ($phone=="IE1") $phoneme="iee";
if ($phone=="IE2") $phoneme="iee";
if ($phone=="IE3") $phoneme="iee";
if ($phone=="IE4") $phoneme="iee";
if ($phone=="IE5") $phoneme="iee";
if ($phone=="II1") $phoneme="iee";
if ($phone=="II2") $phoneme="iee";
if ($phone=="II3") $phoneme="iee";
if ($phone=="II4") $phoneme="iee";
if ($phone=="II5") $phoneme="iee";
if ($phone=="IO1") $phoneme="iee";
if ($phone=="IO2") $phoneme="iee";
if ($phone=="IO3") $phoneme="iee";
if ($phone=="IO4") $phoneme="iee";
if ($phone=="IOU1") $phoneme="iee";
if ($phone=="IOU2") $phoneme="iee";
if ($phone=="IOU3") $phoneme="iee";
if ($phone=="IOU4") $phoneme="iee";
if ($phone=="IU1") $phoneme="iee";
if ($phone=="IU2") $phoneme="iee";
if ($phone=="IU3") $phoneme="iee";
if ($phone=="IU4") $phoneme="iee";
if ($phone=="IU5") $phoneme="iee";
if ($phone=="O1") $phoneme="ohh";
if ($phone=="O2") $phoneme="ohh";
if ($phone=="O3") $phoneme="ohh";
if ($phone=="O4") $phoneme="ohh";
if ($phone=="O5") $phoneme="ohh";
if ($phone=="OU1") $phoneme="ohh";
if ($phone=="OU2") $phoneme="ohh";
if ($phone=="OU3") $phoneme="ohh";
if ($phone=="OU4") $phoneme="ohh";
if ($phone=="OU5") $phoneme="ohh";
if ($phone=="U1") $phoneme="uuu";
if ($phone=="U2") $phoneme="uuu";
if ($phone=="U3") $phoneme="uuu";
if ($phone=="U4") $phoneme="uuu";
if ($phone=="U5") $phoneme="uuu";
if ($phone=="UA1") $phoneme="uuu";
if ($phone=="UA2") $phoneme="uuu";
if ($phone=="UA3") $phoneme="uuu";
if ($phone=="UA4") $phoneme="uuu";
if ($phone=="UA5") $phoneme="uuu";
if ($phone=="UAI1") $phoneme="uuu";
if ($phone=="UAI2") $phoneme="uuu";
if ($phone=="UAI3") $phoneme="uuu";
if ($phone=="UAI4") $phoneme="uuu";
if ($phone=="UE1") $phoneme="uuu";
if ($phone=="UE2") $phoneme="uuu";
if ($phone=="UE3") $phoneme="uuu";
if ($phone=="UE4") $phoneme="uuu";
if ($phone=="UE5") $phoneme="uuu";
if ($phone=="UEI1") $phoneme="uuu";
if ($phone=="UEI2") $phoneme="uuu";
if ($phone=="UEI3") $phoneme="uuu";
if ($phone=="UEI4") $phoneme="uuu";
if ($phone=="UEI5") $phoneme="uuu";
if ($phone=="UO1") $phoneme="uuu";
if ($phone=="UO2") $phoneme="uuu";
if ($phone=="UO3") $phoneme="uuu";
if ($phone=="UO4") $phoneme="uuu";
if ($phone=="UO5") $phoneme="uuu";
if ($phone=="V1") $phoneme="www";
if ($phone=="V2") $phoneme="www";
if ($phone=="V3") $phoneme="www";
if ($phone=="V4") $phoneme="www";
if ($phone=="V5") $phoneme="www";
if ($phone=="VA1") $phoneme="www";
if ($phone=="VA2") $phoneme="www";
if ($phone=="VA3") $phoneme="www";
if ($phone=="VA4") $phoneme="www";
if ($phone=="VE1") $phoneme="www";
if ($phone=="VE2") $phoneme="www";
if ($phone=="VE3") $phoneme="www";
if ($phone=="VE4") $phoneme="www";

//Polish
if ($phone=="DZJ") $phoneme="sss";
if ($phone=="EO5") $phoneme="iee";
if ($phone=="N~") $phoneme="lntd";
if ($phone=="OC5") $phoneme="ohh";
if ($phone=="TSJ") $phoneme="sss";

//Portuguese
if ($phone=="A+") $phoneme="aaa";
if ($phone=="A~+") $phoneme="aaa";
if ($phone=="E+") $phoneme="eh";
if ($phone=="E~+") $phoneme="eh";
if ($phone=="I+") $phoneme="iee";
if ($phone=="IX") $phoneme="iee";
if ($phone=="I~") $phoneme="iee";
if ($phone=="I~+") $phoneme="iee";
if ($phone=="O+") $phoneme="ohh";
if ($phone=="O~+") $phoneme="ohh";
if ($phone=="RR") $phoneme="rrr";
if ($phone=="SCH") $phoneme="sss";
if ($phone=="U+") $phoneme="uuu"; 
if ($phone=="UX") $phoneme="uuu";
if ($phone=="U~") $phoneme="uuu";
if ($phone=="U~+") $phoneme="uuu";
if ($phone=="W~") $phoneme="www";

//Russian
if ($phone=="STS") $phoneme="sss";
if ($phone=="STSJ") $phoneme="sss";
if ($phone=="HRD") $phoneme="rrr";
if ($phone=="jE") $phoneme="eh";
if ($phone=="JO") $phoneme="ohh";

//Spanish
if ($phone=="EI") $phoneme="";
if ($phone=="OI") $phoneme="ohh";
if ($phone=="RF") $phoneme="rrr";

//Swahili
if ($phone=="GH") $phoneme="gk";
if ($phone=="MB") $phoneme="mbp"; 
if ($phone=="MV") $phoneme="mbp";
if ($phone=="ND") $phoneme="lntd";
if ($phone=="NG~") $phoneme="gk";
if ($phone=="NY") $phoneme="lntd";
if ($phone=="NZ") $phoneme="lntd";

//Swedish
if ($phone=="ABL") $phoneme="mbp";
if ($phone=="AEL") $phoneme="iee";
if ($phone=="ALE") $phoneme="lntd";
if ($phone=="ALEL") $phoneme="lntd";
if ($phone=="DR") $phoneme="lntd";
if ($phone=="KS") $phoneme="gk";
if ($phone=="LR") $phoneme="lntd";
if ($phone=="NR") $phoneme="lntd";
if ($phone=="OC") $phoneme="ohh";
if ($phone=="OLE") $phoneme="lntd";
if ($phone=="OLEL") $phoneme="lntd";
if ($phone=="OX") $phoneme="ohh";
if ($phone=="SR") $phoneme="sss";
if ($phone=="TR") $phoneme="rrr";
if ($phone=="UXL") $phoneme="uuu";

//Thai
if ($phone=="IIA") $phoneme="iee";
if ($phone=="KHW") $phoneme="gk";
if ($phone=="KW") $phoneme="gk";
if ($phone=="QQ") $phoneme="gk";
if ($phone=="UUA") $phoneme="uuu";
if ($phone=="VV") $phoneme="www";
if ($phone=="VVA") $phoneme="www";
if ($phone=="XX") $phoneme="ohh";
if ($phone=="YY") $phoneme="uuu";

//Turkish
if ($phone=="AB") $phoneme="mbp";
if ($phone=="SFT") $phoneme="sss";

//Ukrainian
if ($phone=="DZH") $phoneme="lntd";
if ($phone=="HJ") $phoneme="eh"; 
if ($phone=="KJ") $phoneme="gk";
if ($phone=="SHJ") $phoneme="sss"; 
if ($phone=="TSH") $phoneme="sss";
if ($phone=="TSHJ") $phoneme="sss";
if ($phone=="WJ") $phoneme="www";
if ($phone=="XJ") $phoneme="sss";
if ($phone=="ZHJ") $phoneme="ssh";

return $phoneme;
}

global proc asAutoLipSyncImportExampleVoice ()
{
string $soundFile,$text;
string $asScriptLocation=`asGetScriptLocation`;

$soundFile=$asScriptLocation+"/AdvancedSkeleton5Files/div/sound/exampleVoice.wav";
if (!`file -q -ex $soundFile`)
	error ("Unable to find the file:\""+$soundFile+"\"");
if (`objExists exampleVoice`)
	delete exampleVoice;
file -import -type "audio" -ignoreVersion -mergeNamespacesOnClash false -rpr "exampleVoice" -options "o=0" $soundFile;

$text="Conscious of its spiritual and moral heritage, the Union is founded on the indivisible, universal values of human dignity,\n";
$text+="freedom, equality and solidarity; it is based on the principles of democracy and the rule of law. It places the individual\n";
$text+="at the heart of its activities, by establishing the citizenship of the Union and by creating an area of freedom, security and justice.";
scrollField -e -tx $text asAutoLipSyncUIScrollField;
optionMenu -e -v "English" asAutoLipSyncLanguageOptionMenu;
if (`checkBox -q -v asAutoLipSyncNonLatinCheckBox`)
	{
	checkBox -e -v 0 asAutoLipSyncNonLatinCheckBox;
	asAutoLipSyncLanguageChanged;
	}

print ("// Example voice imported, you can now Run Auto LipSync.\n");
}

global proc asAutoLipSyncEnableMp3 ()
{
string $cmd;
if (`whatIs performFileDropAction`=="Unknown")
	error "\"performFileDropAction\" function not avaiable in this version of Maya. A newer version of Maya is reuired for this to work.";

$cmd+="global proc int performFileDropAction (string $theFile)\n";
$cmd+="{\n";
$cmd+="if (`gmatch $theFile \"*.mp3\"`)\n";
$cmd+="	{\n";
$cmd+="	asAutoLipSyncConvertMp3 $theFile;\n";
$cmd+="	return 0;\n";
$cmd+="	}\n";

$cmd+="return( performFileImportAction( $theFile ) );\n";
$cmd+="}\n";
evalEcho ($cmd);
print ("// mp3 files will not automatically get converted to wav, when dropped into Maya.\n");
}

global proc asAutoLipSyncConvertMp3 (string $theFile)
{
string $aligner=`text -q -l asAutoLipSyncAlignerText`;
string $ffmpeg=`substitute "mfa_align.exe" $aligner "ffmpeg.e"`;
if (`about -mac`)
	$ffmpeg=`substitute "mfa_align" $aligner "ffmpeg"`;
string $convertedFile=`substitute ".mp3" $theFile ".wav"`;
string $ffMpegCmd;
if (`about -mac` || `about -linux`)
	$ffMpegCmd="\""+$ffmpeg+"\" -y -i \""+$theFile+"\" \""+$convertedFile+"\"";
else
	$ffMpegCmd="start\/wait/I \"Converting Audio\" \""+$ffmpeg+"\" -y -i \""+$theFile+"\" \""+$convertedFile+"\"";
print ("// Starting Converting Audio:"+$ffMpegCmd+"\n");
system ($ffMpegCmd);
if (!`file -q -ex $convertedFile`)
	error ("Failed to create:\""+$convertedFile+"\"");
file -import -type "audio" -ignoreVersion -mergeNamespacesOnClash false -options "o=0" $convertedFile;
}

global proc asAutoLipSyncLanguageChanged ()
{
int $haveLanguage=1;
string $languageMenuValue=`optionMenu -q -v asAutoLipSyncLanguageOptionMenu`;
string $language=`tolower $languageMenuValue`;
string $languageZip=$language+".zip";
string $sLoc=`asGetScriptLocation`;
string $curl=$sLoc+"/AdvancedSkeleton5Files/bin/curl.e";
string $zip=$sLoc+"/AdvancedSkeleton5Files/bin/7za.e";
string $binDir=$sLoc+"/AdvancedSkeleton5Files/bin";
string $aligner=`text -q -l asAutoLipSyncAlignerText`;
string $zipFileUrl;
string $tempString[],$languageZipFiles[];
string $pretrainedModelsDir=`asStripPath $aligner 2`+"pretrained_models/";
string $phonemes[]=`asAutoLipSyncLanguagePhonemes $language`;

//latinWriting
if (`latinWriting $languageMenuValue`)
	{
	button -e -m 0 asAutoLipSyncUIButton;
	scrollField -e -m 1 asAutoLipSyncUIScrollField;
	}
else
	{
	button -e -m 1 asAutoLipSyncUIButton;
	scrollField -e -m 0 asAutoLipSyncUIScrollField;
	}

//ensure we have the Phonemes for the language
$languageZipFiles=`getFileList -fld $pretrainedModelsDir`;

if (!`stringArrayCount $languageZip $languageZipFiles`)
	{
	if (`confirmDialog -title "Confirm" -message ("Download files for \""+$language+"\ language?")
    -button "Yes" -button "No" -defaultButton "Yes"
    -cancelButton "No" -dismissString "No"`=="Yes")
		$haveLanguage=0;
	else
		optionMenu -e -v "English" asAutoLipSyncLanguageOptionMenu;
	}

//save this as the new `default` language
optionVar -sv asAutoLipSyncLanguage $languageMenuValue;

if ($haveLanguage)
	return;

if (!`file -q -ex $pretrainedModelsDir`)
	error ("Folder not found:"+$pretrainedModelsDir);

//download language files
for ($a=0;$a<2;$a++)
	{
	if ($a==0) {$languageZip=$language+".zip";		$zipFileUrl="http://mlmlab.org/mfa/mfa-models/"+$languageZip;}
	if ($a==1) {$languageZip=$language+"_g2p.zip";$zipFileUrl="http://mlmlab.org/mfa/mfa-models/g2p/"+$languageZip;}

	if ($language=="mandarin")
		$zipFileUrl="http://mlmlab.org/mfa/mfa-models/g2p/"+$language+"_pinyin_g2p.zip";//_pinyin variant

	if (`about -mac` || `about -linux`)
		{
		$cmd="\"curl -L -o "+$pretrainedModelsDir+$languageZip+" "+$zipFileUrl+"\"";
		evalEcho ("system("+$cmd+")");
		}
	else
		{
		$cmd="start\/wait/I \"Downloading\"  \""+$curl+"\" -L -o \""+$pretrainedModelsDir+$languageZip+"\" "+$zipFileUrl;
		print ("// Starting Download:"+$cmd+"\n");
		system ($cmd);
		}
	}
}

global proc string[] asAutoLipSyncLanguages ()
{
string $languages[]={"Bulgarian","Croatian","Czech","French","German","Hausa",
	"Korean","Mandarin","Polish","Portuguese","Russian","Spanish","Swahili","Swedish",
	"Thai","Turkish","Ukrainian","Vietnamese"};
//Maya appears to be the lower ASCII range, we need utf-8 for these languages
$languages=`stringArrayRemove {"Vietnamese"} $languages`;
return $languages;
}

global proc int latinWriting (string $language)
{
int $latin=1;
//Bulgarian: Cyrillic alphabet
//Korean: Hangul
//Mandarin: Hanzi 
//Russian: Cyrillic alphabet
//Thai: Thai script
//Ukrainian: Cyrillic alphabet
//Vietnamese: Vietnamese alphabet
string $latinWritingLaunguages[]={"Bulgarian","Korean","Mandarin","Russian","Thai","Ukrainian","Vietnamese"};
if (`stringArrayCount $language $latinWritingLaunguages`)
	$latin=0;

//proceed with assuming all languages should be Romanized
//$latin=1;

//checkBox can now choose this
$latin=!`checkBox -q -v asAutoLipSyncNonLatinCheckBox`;

return $latin;
}

global proc asAutoLipOpenTextEditor ()
{
string $tempDir=`asGetTempDirectory`+"AdvancedSkeleton/autoLipSync/";
string $forceTextFile=$tempDir+"input/align.txt";
string $pythonCmd;

if (!`file -q -ex ($tempDir+"input")`)
	sysFile -md ($tempDir+"input");

//utf-8 format
$pythonCmd+="txt = u'$\\u2026\\n'\n";
$pythonCmd+="with open('"+$forceTextFile+"', 'wb') as fp:\n";
$pythonCmd+="    fp.write(txt.encode('utf-8'))\n";
print $pythonCmd;
python ($pythonCmd);

system ("load \""+$forceTextFile+"\"");
}

global proc string asPhonemeTranslate (string $phonemes, int $toMaya)
{
//as some phoneme symbols are not valid in Maya-attribute-names, translate to valid symbols:
string $return;
if ($toMaya)
	{
	$return=`substitute "[~]" $phonemes "Tilde"`;
	$return=`substitute "[+]" $return "Plus"`;
	}
else
	{
	$return=`substitute "Tilde" $phonemes "~"`;
	$return=`substitute "Plus" $return "+"`;
	}
return $return;
}

global proc string[] asAutoLipSyncLanguagePhonemes (string $language)
{
string $phonemes[];

if ($language=="bulgarian")
	$phonemes={"S","Y","Z","a","b","bj","d","dZ","dj","dz","e","f","fj","g","gj","i","j","ja","ju","k","kj","l",
		"lj","m","mj","n","nj","o","p","pj","r","rj","s","sj","t","tS","tj","ts","u","v","vj","x","z","zj"};

if ($language=="croatian")
	$phonemes={"L","S","Z","a","b","d","dZ","dzp","e","f","g","i","j","k","l","m","n","nj","o","p","r","s","t",
		"tS","tcp","ts","u","v","x","z"};

if ($language=="czech")
	$phonemes={"a","aa","aw","b","c","ch","d","dj","e","ee","ew","f","g","h","i","ii","j","k","l","m","mg","n",
  "ng","nj","o","oo","ow","p","r","rsh","rzh","s","sh","t","tj","u","uu","v","x","z","zh"};
 
//if ($language=="english")
//	$phonemes={"AA0","AA1","AA2","AE0","AE1","AE2","AH0","AH1","AH2","AO0","AO1","AO2,AW0","AW1","AW2","AY0",
//	"AY1","AY2","EH0","EH1","EH2","ER0","ER1","ER2,EY0","EY1","EY2","IH0","IH1","IH2","IY0","IY1","IY2","OW0",
//	"OW1","OW2,OY0","OY1","OY2","UH0","UH1","UH2","UW0","UW1","UW2,B","CH","D","DH","F","G","HH","JH","K","L",
//	"M","N","NG","P","R,S","SH","T","TH","V","W","Y","Z","ZH"};

if ($language=="french")
	$phonemes={"AE","AX","A~","B","D","E","EU","E~","F","G","H","J","K","L","M","N","NG","NJ","O","OE","OE~",
  "P","R","S","SH","T","V","W","Z","ZH","a","e","h","i","o","o~","u","y"};

if ($language=="german")
	$phonemes={"+hGH","C","S","a","aI","aU","ae","al","atu","b","d","e","eU","el","etu","f","g","h","i","il","j",
		"k","l","m","n","ng","o","oe","oel","ol","p","r","s","t","ts","u","ue","uel","ul","v","x","z"};

if ($language=="hausa")
	$phonemes={"B","D","DZ","F","K","KR","Q","R","S","TS","a","aI","aU","a_L","a_S","a_T1","a_T2","a_T3","b","c",
		"d","e","e_L","e_S","e_T1","e_T2","g","h","i","i_L","i_S","i_T1","i_T2","i_T3","j","k","l","m","n","o","o_L",
		"o_S","o_T1","o_T2","p","r","s","t","u","u_L","u_S","u_T1","u_T2","w","z"};

if ($language=="korean")
	$phonemes={"A","AE","B","BB","CHh","D","DD","E","EO","EU","G","GG","H","I","J","JJ","Kh","L","M","N","NG","O",
		"OE","Ph","R","S","SS","Th","U","UE","euI","iA","iE","iEO","iO","iU","k","oA","p","t","uEO"};

if ($language=="mandarin")
	$phonemes={"a1","a2","a3","a4","a5","ai1","ai2","ai3","ai4","ai5","ao1","ao2","ao3","ao4","ao5","b",
  "c","ch","d","e1","e2","e3","e4","e5","ei1","ei2","ei3","ei4","f","g","h","i1","i2","i3","i4","i5","ia1",
  "ia2","ia3","ia4","ia5","iao1","iao2","iao3","iao4","ie1","ie2","ie3","ie4","ie5","ii1","ii2","ii3",
  "ii4","ii5","io1","io2","io3","io4","iou1","iou2","iou3","iou4","iu1","iu2","iu3","iu4","iu5","j",
  "k","l","m","n","ng","o1","o2","o3","o4","o5","ou1","ou2","ou3","ou4","ou5","p","q","r","s","sh","t",
  "u1","u2","u3","u4","u5","ua1","ua2","ua3","ua4","ua5","uai1","uai2","uai3","uai4","ue1","ue2","ue3",
  "ue4","ue5","uei1","uei2","uei3","uei4","uei5","uo1","uo2","uo3","uo4","uo5","v1","v2","v3","v4",
  "v5","va1","va2","va3","va4","ve1","ve2","ve3","ve4","x","z","zh"};

if ($language=="polish")
	$phonemes={"S","Z","a","b","c","d","dZ","dz","dzj","e","eo5","f","g","h","i","i2","j","k","l","m","n","n~","o",
		"oc5","p","r","s","sj","t","tS","tsj","u","v","w","z","zj"};

if ($language=="portuguese")
	$phonemes={"A","A+","AX","A~","A~+","B","D","DJ","E","E+","E~","E~+","F","G","I","I+","IX","I~","I~+","K","L",
		"LJ","M","N","NJ","O","O+","O~","O~+","P","R","RR","S","SCH","T","TJ","U","U+","UX","U~","U~+","V","W","W~","Z"};

if ($language=="russian")
	$phonemes={"S","Sj","StS","StSj","Z","Zj","a","b","bj","d","dj","e","f","g","hrd","i","i2","j","jA","jE",
  "jO","jU","k","l","lj","m","mj","n","nj","o","p","pj","r","rj","s","sj","t","tS","tSj","tj","ts","u","v",
  "vj","x","z","zj"};

if ($language=="spanish")
	$phonemes={"D","G","L","T","V","a","a+","aI","aU","b","d","e","e+","eI","eU","f","g","i","i+","j","k","l",
  "m","n","ng","n~","o","o+","oI","p","r","rf","s","t","tS","u","u+","w","x","z"};

if ($language=="swahili")
	$phonemes={"a","b","ch","d","dh","e","f","g","gh","h","i","j","k","kh","l","m","mb","mv","n","nd","ng","ng~",
		"nj","ny","nz","o","p","r","s","sh","t","th","u","v","w","y","z"};

if ($language=="swedish")
	$phonemes={"C","S","a","abl","ae","ael","al","ale","alel","b","d","dr","e","el","etu","f","g","h","i","il","j",
		"k","ks","l","lr","m","n","ng","nr","o","oc","oe","oel","ol","ole","olel","ox","p","r","s","sr","t","tr","u",
		"ue","uel","ul","uxl","v"};

if ($language=="thai")
	$phonemes={"a","aa","b","c","ch","d","e","ee","f","h","i","ii","iia","j","k","kh","khw","kw","l","m","n","ng",
		"o","oo","p","ph","q","qq","r","s","t","th","u","uu","uua","v","vv","vva","w","x","xx","y","yy","z"};

if ($language=="turkish")
	$phonemes={"S","Z","ab","b","d","dZ","e","f","g","h","i","i2","j","k","l","m","n","o","oe","p","r","s","sft",
  "t","tS","u","ue","v","z"};

if ($language=="ukrainian")
	$phonemes={"a","b","bj","d","dj","dz","dzh","dzj","e","f","fj","g","h","hj","i","j","k","kj","l","lj","m","mj",
		"n","nj","o","p","pj","r","rj","s","sh","shj","sj","t","tj","ts","tsh","tshj","tsj","u","w","wj","x","xj",
		"y","z","zh","zhj","zj"};

if ($language=="vietnamese")
	$phonemes={"WB","a1_T1","a1_T2","a1_T3","a1_T4","a1_T5","a1_T6","a2_T1","a2_T2","a2_T3","a2_T4",
  "a2_T5","a2_T6","a3_T1","a3_T2","a3_T3","a3_T4","a3_T5","a3_T6","ai_T1","ai_T2","ai_T3","ai_T4",
  "ai_T5","ai_T6","ao_T1","ao_T2","ao_T3","ao_T4","ao_T5","ao_T6","au3_T1","au3_T2","au3_T3",
  "au3_T4","au3_T5","au3_T6","au_T1","au_T2","au_T3","ay3_T1","ay3_T2","ay3_T3","ay3_T4","ay3_T5",
  "ay3_T6","ay_T1","ay_T2","ay_T3","ay_T4","ay_T5","ay_T6","b","ch","d1","d2","e1_T1","e1_T2","e1_T3",
  "e1_T4","e1_T5","e1_T6","e2_T1","e2_T2","e2_T3","e2_T4","e2_T5","e2_T6","eo_T1","eo_T2","eo_T3",
  "eo_T4","eo_T5","eo_T6","eu_T2","eu_T3","eu_T4","eu_T6","g","h","i_T1","i_T2","i_T3","i_T4","i_T5",
  "i_T6","ie2_T1","ie2_T2","ie2_T3","ie2_T4","ie2_T5","ie2_T6","ieu_T1","ieu_T2","ieu_T3","ieu_T4",
  "ieu_T6","iu_T1","iu_T2","iu_T3","iu_T4","iu_T5","iu_T6","j","k","kh","l","m","n","ng","nh","o1_T1",
  "o1_T2","o1_T3","o1_T4","o1_T5","o1_T6","o2_T1","o2_T2","o2_T3","o2_T4","o2_T5","o2_T6","o3_T1",
  "o3_T2","o3_T3","o3_T4","o3_T5","o3_T6","oa_T1","oa_T2","oa_T3","oa_T4","oa_T5","oa_T6","oe_T1",
  "oe_T2","oe_T3","oe_T4","oe_T6","oi2_T1","oi2_T2","oi2_T3","oi2_T4","oi2_T5","oi2_T6","oi3_T1",
  "oi3_T2","oi3_T3","oi3_T4","oi3_T5","oi3_T6","oi_T1","oi_T2","oi_T3","oi_T4","oi_T5","oi_T6",
  "p","ph","r","s","t","th","tr","u1_T1","u1_T2","u1_T3","u1_T4","u1_T5","u1_T6","u2_T1","u2_T2",
  "u2_T3","u2_T4","u2_T5","u2_T6","ua2_T1","ua2_T2","ua2_T3","ua2_T4","ua2_T5","ua2_T6","ua_T1",
  "ua_T2","ua_T3","ua_T4","ua_T5","ua_T6","ui2_T4","ui_T1","ui_T2","ui_T3","ui_T4","ui_T5","ui_T6",
  "uoi2_T1","uoi2_T3","uoi2_T4","uoi2_T5","uoi2_T6","uoi3_T1","uoi3_T2","uoi3_T3","uoi3_T4",
  "uoi3_T5","uou_T6","uu2_T1","uu2_T2","uu2_T3","uu2_T4","uu2_T5","uu2_T6","uy_T1","uy_T2","uy_T3",
  "uy_T4","uy_T5","uy_T6","v","x"};

return $phonemes;
}

global proc asDynBake (string $uiName)
{
float $pos[],$rot[];
string $tempString[],$dynJoints[],$fkCtrls[];

if (`confirmDialog -title "Confirm" -message "Bake all dynamics ?"
    -button "Yes" -button "No" -defaultButton "Yes"
    -cancelButton "No" -dismissString "No"`!="Yes")
	return;

if (!`objExists Dynamics`)
	error "Dynamics not found";
$dynJoints=`listRelatives -type joint -ad Dynamics`;
	string $softDynCurves[]=`ls -type nurbsCurve "DynCurveSoft*"`;
bakeResults -simulation true -t (`playbackOptions -q -min`+":"+`playbackOptions -q -max`) -sampleBy 1 -disableImplicitControl 0 -preserveOutsideKeys 0 -sparseAnimCurveBake false -controlPoints true -shape false $softDynCurves;

for ($y=0;$y<size($dynJoints);$y++)
	{
	$tempString=`listConnections ($dynJoints[$y]+".r")`;
	if (`objectType $tempString[0]`=="pairBlend")
		$tempString=`listConnections ($tempString[0]+".outRotate.outRotateX")`;
	$fkCtrl="FK"+$tempString[0];
	if (!`objExists $fkCtrl`)
		continue;
	parentConstraint $dynJoints[$y] $fkCtrl;
	$fkCtrls[size($fkCtrls)]=$fkCtrl;
	}

bakeResults -simulation true -t (`playbackOptions -q -min`+":"+`playbackOptions -q -max`) -sampleBy 1 -disableImplicitControl 0 -preserveOutsideKeys 1 -sparseAnimCurveBake false -controlPoints false -shape false $fkCtrls;
delete -staticChannels -unitlessAnimationCurves false -hierarchy none -controlPoints 0 -shape 0 $fkCtrls;
delete Dynamics;
select -cl;
print ("// Dynamics bake complete.\n");
}

global proc asControlsVisibilityToggle ()
{
int $vis;
string $motionSystems[]=`ls -r 1 MotionSystem FaceMotionSystem`;
for ($i=0;$i<size($motionSystems);$i++)
    {
    if ($i==0)
        $vis=!(`getAttr ($motionSystems[$i]+".v")`);
    setAttr ($motionSystems[$i]+".v") $vis;
    }
}

global proc asSetupControlVisibilityHotKeyDialog ()
{
if (`confirmDialog -title "Confirm Control Visibility HotKey"
	-message ("Add toggle of control-visibility to the \"~\" hotkey.\n"
	+"For easy toggling visibility of controls.\n"
	+"Holding down the \"~\" key (next to the \"number 1\" on the keyboard) to see and select controls.\n"
	+"As you let go, controls will again be hidden.\n"
	+"The idea is to work without the `visual clutter` of the controls.\n"
	+"Tip: Ctrl+\"\~\" will leave the controls visible")
	-button "Confirm" -button "Cancel" -defaultButton "Confirm"
	-cancelButton "Cancel" -dismissString "Cancel"`!="Confirm")
	return;
asSetupControlVisibilityHotKey;
}

global proc asSetupControlVisibilityHotKey ()
{
if (!`runTimeCommand -q -ex advancedSkeletonVisibilitySwitch`)
	{
	nameCommand -ann "advancedSkeletonVisibilitySwitch" -c "advancedSkeletonVisibilitySwitch" advancedSkeletonVisibilitySwitchNameCommand;
	runTimeCommand -annotation "switches the visibility of controls" -category "User"
		-command ("int $vis;\nstring $motionSystems[]=`ls -r 1 MotionSystem FaceMotionSystem`;\nfor ($i=0;$i<size($motionSystems);$i++)\n    {\n    if ($i==0)\n        $vis=!(`getAttr ($motionSystems[$i]+\".v\")`);\n    setAttr ($motionSystems[$i]+\".v\") $vis;\n    }")
		advancedSkeletonVisibilitySwitch;
	}
hotkey -keyShortcut "`"  -name "advancedSkeletonVisibilitySwitchNameCommand" -releaseName "advancedSkeletonVisibilitySwitchNameCommand";
hotkey -keyShortcut "\`" -name "advancedSkeletonVisibilitySwitchNameCommand" -releaseName "advancedSkeletonVisibilitySwitchNameCommand";
hotkey -keyShortcut "`"  -ctrlModifier -name "advancedSkeletonVisibilitySwitchNameCommand";
hotkey -keyShortcut "\`" -ctrlModifier -name "advancedSkeletonVisibilitySwitchNameCommand";
}

global proc asVisualizeGimbalLock (string $uiName)
{
string $sel[]=`ls -sl`;
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
int $fromSelection;
int $buildGimbal[];
float $scale;
float $bb[];
string $name,$lookForVisConnectionOnObject;
string $tempString[];
string $controlSets[]=`asGetControlSetsFromUI $uiName`;
string $controls[]=`sets -q $controlSets`;

if (`objExists GimbalLockVisualizers`)
	{
	delete GimbalLockVisualizers;
	return;
	}
createNode -n GimbalLockVisualizers transform;
if ($sel[0]!="")
	if (`stringArrayCount $sel[0] $controls`)
		$fromSelection=1;
for ($i=0;$i<size($controls);$i++)
	{
	if ($controls[$i]==$nameSpace+"Main")
		continue;
	$tempString=`listRelatives -s $controls[$i]`;
	if ($tempString[0]!="")
		if (`objectType $tempString[0]`=="nurbsCurve")
			{
			if ($fromSelection)
				if (`stringArrayCount $controls[$i] $sel`)
					$buildGimbal[$i]=1;
			if (!$fromSelection)
				$buildGimbal[$i]=1;
			}
	}
for ($i=0;$i<size($controls);$i++)
	{
	if (!$buildGimbal[$i])
		continue;
	$name=$controls[$i];
	createNode -n ($name+"Constraint") -p GimbalLockVisualizers transform;
	createNode -n ($name+"Offset") -p ($name+"Constraint") transform;

	polyCylinder -n ($name+"Cones") -r 0.3 -h 2.5 -sx 10 -sy 2 -sz 1 -ax 0 1 0 -rcp 0 -cuv 3 -ch 0;
	scale -r -p 0 0 0 0 0 0 ($name+"Cones.vtx[10:19]");
	parent ($name+"Cones") ($name+"Offset");
	if (`objExists asGreenSG`)
		sets -e -forceElement asGreenSG ($name+"Cones");

	polyTorus -n ($name+"Torus") -r 1 -sr 0.03 -tw 0 -sx 30 -sy 6 -ax 0 1 0 -cuv 1 -ch 0;
	parent ($name+"Torus") ($name+"Offset");
	if (`objExists asRedSG`)
		sets -e -forceElement asRedSG ($name+"Torus");

	polyCylinder -n ($name+"Arrow") -r 0.06 -h 2 -sx 10 -sy 3 -sz 1 -ax 0 1 0 -rcp 0 -cuv 3 -ch 0;
	rotate -r 0 0 90 ($name+"Arrow.vtx[0:41]");
	move -r 1 0 0 ($name+"Arrow.vtx[0:41]");
	scale -r -p 0 0 0 1 0 0 ($name+"Arrow.vtx[0:9]") ($name+"Arrow.vtx[40]");
	move -r 0.35 0 0 ($name+"Arrow.vtx[10:19]");
	scale -r -p 0 0 0 1 1.75 1.75 ($name+"Arrow.vtx[10:19]");
	move -r 1 0 0 ($name+"Arrow.vtx[20:29]");
	parent ($name+"Arrow") ($name+"Offset");
	if (`objExists asGreen2SG`)
		sets -e -forceElement asGreen2SG ($name+"Arrow");
	orientConstraint $name ($name+"Arrow");

	$tempString=`listRelatives -p $name`;
	parentConstraint $tempString[0] ($name+"Constraint");
	$tempString=`listRelatives -s $name`;
	$bb=`xform -q -bb ($tempString[0]+".cv[0:999]")`;
	$scale=(($bb[3]-$bb[0])+($bb[4]-$bb[1])+($bb[5]-$bb[2]))/5.0;
	setAttr -type float3 ($name+"Offset.s") $scale $scale $scale;
	$lookForVisConnectionOnObject=$name;
	$tempString=`listConnections -p 1 ($lookForVisConnectionOnObject+".v")`;
	for ($y=0;$y<20;$y++)
		{
		if ($tempString[0]!="")
			{
			connectAttr $tempString[0] ($name+"Offset.v");
			break;
			}
		$tempString=`listRelatives -p $lookForVisConnectionOnObject`;
		if ($tempString[0]=="")
			break;
		$lookForVisConnectionOnObject=$tempString[0];
		$tempString=`listConnections -p 1 ($lookForVisConnectionOnObject+".v")`;
		}
	setDrivenKeyframe -itt "linear" -ott "linear" -v 90 -dv 0 -cd ($name+".rotateOrder") ($name+"Cones.rx");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 1 -cd ($name+".rotateOrder") ($name+"Cones.rx");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 2 -cd ($name+".rotateOrder") ($name+"Cones.rx");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 3 -cd ($name+".rotateOrder") ($name+"Cones.rx");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 4 -cd ($name+".rotateOrder") ($name+"Cones.rx");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 90 -dv 5 -cd ($name+".rotateOrder") ($name+"Cones.rx");

	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 0 -cd ($name+".rotateOrder") ($name+"Cones.rz");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 1 -cd ($name+".rotateOrder") ($name+"Cones.rz");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 90 -dv 2 -cd ($name+".rotateOrder") ($name+"Cones.rz");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 3 -cd ($name+".rotateOrder") ($name+"Cones.rz");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 90 -dv 4 -cd ($name+".rotateOrder") ($name+"Cones.rz");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 5 -cd ($name+".rotateOrder") ($name+"Cones.rz");

	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 0 -cd ($name+".rotateOrder") ($name+"Torus.rx");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 1 -cd ($name+".rotateOrder") ($name+"Torus.rx");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 90 -dv 2 -cd ($name+".rotateOrder") ($name+"Torus.rx");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 3 -cd ($name+".rotateOrder") ($name+"Torus.rx");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 4 -cd ($name+".rotateOrder") ($name+"Torus.rx");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 90 -dv 5 -cd ($name+".rotateOrder") ($name+"Torus.rx");

	setDrivenKeyframe -itt "linear" -ott "linear" -v 90 -dv 0 -cd ($name+".rotateOrder") ($name+"Torus.rz");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 1 -cd ($name+".rotateOrder") ($name+"Torus.rz");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 2 -cd ($name+".rotateOrder") ($name+"Torus.rz");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 90 -dv 3 -cd ($name+".rotateOrder") ($name+"Torus.rz");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 4 -cd ($name+".rotateOrder") ($name+"Torus.rz");
	setDrivenKeyframe -itt "linear" -ott "linear" -v 0 -dv 5 -cd ($name+".rotateOrder") ($name+"Torus.rz");
	}
select $sel;
}

global proc asFaceCtrlsDetach (string $uiName)
{
int $wasConnected;
string $nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";
string $objA=$nameSpace+"SkinAttachCtrlsWrap";
string $objB=$nameSpace+"AttacherCurveShape";
string $plugA=$objA+".outputGeometry[0]";
string $plugB=$objB+".create";

if (`objExists outerLidCurveFromMeshEdge_R`)
	{
	$objA=$nameSpace+"SkinAttachMeshBS";
	$objB=$nameSpace+"SkinAttachMeshShapeDeformed";
	$plugA=$objA+".outputGeometry[0]";
	$plugB=$objB+".inMesh";
	}

if (!`objExists $objA` || !`objExists $objB`)
	return;

if (`isConnected $plugA $plugB`)
	{
	$wasConnected=1;
	disconnectAttr $plugA $plugB;
	}
else
	{
	connectAttr $plugA $plugB;
	dgdirty -a;
	}

setAttr ($nameSpace+"ctrlBox.ACtrlVis") (!$wasConnected);
setAttr ($nameSpace+"ctrlBox.BCtrlVis") (!$wasConnected);
setAttr ($nameSpace+"ctrlBox.CCtrlVis") (!$wasConnected);

if ($wasConnected)
	print ("// Detached On-Face controllers.\n");
else
	print ("// Attached On-Face controllers.\n");
}

global proc int asHaveAnimation (string $node)
{
int $haveAnimation=0;
string $nodeType;
string $tempString[]=`listConnections -s 1 -d 0 $node`;

for ($i=0;$i<size($tempString);$i++)
	{
	$nodeType=`objectType $tempString[$i]`;
	if (`gmatch $nodeType "animCurve*"`)
		{
		$haveAnimation=1;
		break;
		}
	}
return $haveAnimation;
}

global proc asNameMatcherUI ()
{
asMappingUI NameMatcher "";
}

global proc asMoCapMatcherUI (string $uiName)
{
asMappingUI moCapMatcher $uiName;
}


global proc asMappingUI (string $tool, string $uiName)
{
int $cw=150;
int $sep=10;
string $name,$nameSpace;
string $tempString[];
if ($uiName!="")
	if (`optionMenu -q -ex ($uiName+"OptionMenu")`)
		$nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";

if (`window -q -ex asMappingUI`)
    deleteUI asMappingUI;
window -t $tool asMappingUI;
formLayout asMappingUIFormLayout;
scrollLayout asMappingUIScrollLayout;
columnLayout asMappingUIColumnLayout1;

frameLayout -p asMappingUIColumnLayout1 -w 320 -cll 1 -cl 1 -cc asFL -ec asFL -l "Templates" asMappingUITemplatesFrameLayout;
	columnLayout;
		rowLayout -nc 4 -cw4 10 25 200 100;
			separator -st none;
			text -l "files:";
			optionMenu -cc ("asMappingUIFileOptionMenuChanged "+$tool) asMappingUIFiles;
//			button -w 80 -l "New" -c asMappingUIClearAll;
			setParent..;
		rowLayout -nc 2 -cw2 40 200;
			separator -st none;
			text -en 0 -l "" -fn smallBoldLabelFont asMappingUIWWW;
			setParent..;
		separator -h 15 -st none;
		rowLayout -nc 3 -cw3 86 150 200;
			separator -st none;
			text -al right -l "save current configutation:";
			button -h 15 -w 80 -l "Export" -c ("asMappingUIExport "+$tool);
			setParent..;		

frameLayout -p asMappingUIColumnLayout1 -w 320 -cll 1 -cl 1 -cc asFL -ec asFL -l "Side" asMappingUISideFrameLayout;
	columnLayout;
		textFieldGrp -cw2 45 60 -l "Right:" -ed 1 -tx "Right" -cc asMappingUISideOptionChanged asMappingUISideRightTextFieldGrp;
		textFieldGrp -cw2 45 60 -l "Left:" -ed 1 -tx "Left" asMappingUISideLeftTextFieldGrp;
		textFieldGrp -cw2 45 60 -l "Middle:" -ed 1 -tx "" asMappingUISideMiddleTextFieldGrp;
		separator -st none -h 5;
		rowLayout -nc 2 -cw2 10 50;
			separator -st none;
			checkBox -l "%Side% before name" -v 1 -cc asMappingUISideOptionChanged asMappingUISideBeforeNameCheckBox;
			setParent..;
		rowLayout -nc 2 -cw2 10 50;
			separator -st none;
			checkBox -l "Use _ (underscore)" -cc asMappingUISideOptionChanged asMappingUISideUnderScoreCheckBox;
			setParent..;
		separator -h 10 -st none;
		rowLayout -nc 3 -cw3 10 100 100;
			separator -st none;
			text -l "Result example:";
			text -fn boldLabelFont -l "RightArm" asMappingUISideResultExampleText;

frameLayout -p asMappingUIColumnLayout1 -w 320 -cll 1 -cl 1 -cc asFL -ec asFL -l "Joints" asMappingUIJointsFrameLayout;
	formLayout asMappingUIJointsFormLayout;
		columnLayout -adj 1 asMappingUIJointsColumnLayout1;
			rowLayout -nc 3 -cw3 $cw $sep $cw;
				text -l "   AdvancedSkeleton:";
				text -l "|";
				text -l "   Other:";
				setParent..;
			separator -w ($cw*2);
			columnLayout asMappingUIJointsColumnLayout;
				setParent..;
			setParent..;

		columnLayout asMappingUIJointsColumnLayout2;
			separator -w 320 -h 10;
			separator -w 320 -h 5 -st none;
			rowLayout -nc 3 -cw3 10 80 80;
				separator -st none;
				button -l "Add joint" -c asMappingUIAddJoint;
				button -l "Clear all" -c asMappingUIClearAll;
				setParent..;

frameLayout -p asMappingUIColumnLayout1 -w 320 -cll 1 -cl 1 -cc asFL -ec asFL -l "NameSpaces" asMappingUINameSpacesFrameLayout;
	formLayout asMappingUINameSpacesFormLayout;
		columnLayout -adj 1 asMappingUINameSpacesColumnLayout1;
			rowLayout -nc 3 -cw3 $cw $sep $cw;
				text -l "   AdvancedSkeleton:";
				text -l "|";
				text -l "   Other:";
				setParent..;
			separator -w ($cw*2);
			columnLayout asMappingUINameSpacesColumnLayout;
				rowLayout -nc 3 -cw3 $cw $sep $cw;
					textField -w ($cw-$sep-10) asMappingUINameSpacesTextFieldA1;
					if ($nameSpace!="")
						textField -e -tx $nameSpace asMappingUINameSpacesTextFieldA1;
					text -l "|";
					textField -w ($cw-$sep-10) asMappingUINameSpacesTextFieldB1;
				setParent..;
				rowLayout -nc 3 -cw3 $cw $sep $cw;
					separator -st none;
					separator -st none;
					button -h 15 -l "detect from selected" -c asMoCapMatcherNameSpaceDetect;

formLayout -e
	-ac asMappingUIJointsColumnLayout2 "top" 0 asMappingUIJointsColumnLayout1
	asMappingUIJointsFormLayout;

frameLayout -p asMappingUIColumnLayout1 -w 320 -cll 1 -cl 1 -cc asFL -ec asFL -l "Functions" asMappingUIFunctionsFrameLayout;
	columnLayout;
		if ($tool=="NameMatcher")
			{
			separator -st none -h 10;
			rowLayout -adj 2 -nc 2 -cw2 80 150;
				separator -st none;
				button -w 150 -l "Create + Place FitSkeleton" -c asNameMatcherAutoRigFit;
				setParent..;

			separator -st none -h 10;
			rowLayout -adj 2 -nc 2 -cw2 80 150;
				separator -st none;
				button -w 150 -l "Build AdvancedSkeleton" -c asReBuildAdvancedSkeleton;
				setParent..;

			separator -st none -h 10;
			rowLayout -adj 2 -nc 2 -cw2 80 150;
				separator -st none;
				button -w 150 -l "Constraint to Joints" -c "asMappingUIFunction AutoRigConstraint";
				setParent..;

			if (size(`ls -type blendShape`))
				{
				separator -st none -h 10;
				rowLayout -adj 2 -nc 2 -cw2 80 150;
					separator -st none;
					button -w 150 -l "Create BlendShapes Control" -c "asMappingUIFunction CreateBSControl";
					setParent..;
				}
			}
		if ($tool=="moCapMatcher")
			{
			separator -st none -h 10;
			rowLayout -adj 2 -nc 2 -cw2 80 150;
				separator -st none;
				columnLayout;
					text -l "1: Import MoCap-skeleton.";
					separator -st none -h 10;
					text -l "2: Select MoCap-skeleton";
					button -w 150 -l "Scan MoCap-skeleton" -c asMoCapMatcherScan;
					separator -st none -h 10;
					text -al "left" -l "3: Scale the MoCap-skeleton,\n    to match your character.";
					separator -st none -h 10;
					text -al "left" -l "4: Go to start of animation,\n    and `zero-out` MoCap-joints.";
					button -w 150 -l "`zero-out` MoCap-joints" -c "asMappingUIFunction MoCapZeroOut";
					text -al "left" -l "And align skeletons (if needed)";
					separator -st none -h 20;
					button -w 150 -l "Set rig to all FK" -c ("asSetAllFK \""+$nameSpace+"\"");
					rowLayout -nc 2 -cw2 160 10;
						button -w 150 -l "Connect MoCap Skeleton" -c "asMappingUIFunction MoCapConnect";
						checkBox -l "FKExtra" asMappingUIFKExtraCheckBox;
						setParent..;
					button -w 150 -l "Disconnect MoCap Skeleton" -c asMoCapMatcherDisconnect;
					button -w 150 -l "Bake" -c asMoCapMatcherBake;
			}

formLayout -e
	-af asMappingUIScrollLayout "top" 0
	-af asMappingUIScrollLayout "bottom" 0
	-af asMappingUIScrollLayout "left" 0
	-af asMappingUIScrollLayout "right" 0
asMappingUIFormLayout;

showWindow;

//populate $tool`s optionMenu
string $asScriptLocation=`asGetScriptLocationFromSelector`;
string $toolDir=$asScriptLocation+"/AdvancedSkeleton5Files/"+$tool+"s/";
string $toolFiles[]=`getFileList -fld $toolDir`;
setParent -menu asMappingUIFiles;
for ($i=0;$i<size($toolFiles);$i++)
	{
	if (`gmatch $toolFiles[$i] "[.]*"` || $toolFiles[$i]=="incrementalSave")
		continue;
	tokenize $toolFiles[$i] "." $tempString;
	$name=$tempString[0];
	menuItem -l $name;
	}
//defaults to "MotionBuilder"
if (`stringArrayCount "MotionBuilder.txt" $toolFiles`)
	optionMenu -e -v MotionBuilder asMappingUIFiles;
menuItem -l "*New";

//update UI from optionVars
string $framLayouts[]=`lsUI -type frameLayout`;
for ($i=0;$i<size($framLayouts);$i++)
	if (`gmatch $framLayouts[$i] "asMappingUI*FrameLayout"`)
		if (`optionVar -ex $framLayouts[$i]`)
			frameLayout -e -cl `optionVar -q $framLayouts[$i]` $framLayouts[$i];

//auto-guess source App from scene nodes
if (size(`ls |master`)) catchQuiet(`optionMenu -e -v AutodeskCharacterGenerator asMappingUIFiles`);
if (size(`ls "*BoneRoot"`)) catchQuiet(`optionMenu -e -v CharacterCreator asMappingUIFiles`);
if (size(`ls "*BoneRoot"`) && size(`ls "*_Thumb1"`)) catchQuiet(`optionMenu -e -v CharacterCreator3 asMappingUIFiles`);
if (size(`ls ik_foot_root`)) catchQuiet(`optionMenu -e -v Unreal asMappingUIFiles`);
if (size(`ls Abdomen`)) catchQuiet(`optionMenu -e -v Poser asMappingUIFiles`);
if (size(`ls abdomenLower`)) catchQuiet(`optionMenu -e -v Daz3d asMappingUIFiles`);
if (size(`ls RightFinger5Proximal`)) catchQuiet(`optionMenu -e -v Rokoko asMappingUIFiles`);

asMappingUIFileOptionMenuChanged $tool;
}

global proc asMappingUIFileOptionMenuChanged (string $tool)
{
string $text="";
string $file=`optionMenu -q -v asMappingUIFiles`;
if ($file=="AutodeskCharacterGenerator") $text="charactergenerator.autodesk.com";
if ($file=="CharacterCreator") $text="www.reallusion.com/character-creator";
if ($file=="Daz3d") $text="www.daz3d.com";
if ($file=="iPiSoft") $text="www.ipisoft.com";
if ($file=="Mixamo") $text="www.mixamo.com";
if ($file=="MotionBuilder") $text="www.autodesk.com";
if ($file=="Poser") $text="smithmicro.com/poser";
if ($file=="Unreal") $text="www.unrealengine.com";
if ($file=="Rokoko") $text="www.rokoko.com";

text -e -l $text asMappingUIWWW;

asMappingUIClearAll;
if ($file!="*New")
	asMappingUILoad $tool;
}

global proc asMappingUISideOptionChanged ()
{
string $joint="Arm";
string $side=`textFieldGrp -q -tx asMappingUISideRightTextFieldGrp`;
string $underS="";
if (`checkBox -q -v asMappingUISideUnderScoreCheckBox`)
	$underS="_";
string $text=$text=$joint+$underS+$side;
if (`checkBox -q -v asMappingUISideBeforeNameCheckBox`)
	$text=$side+$underS+$joint;
text -e -l $text asMappingUISideResultExampleText;
}

global proc asMappingUIAddJoint ()
{
int $cw=150;
int $sep=10;
string $rowLayouts[]=`columnLayout -q -ca asMappingUIJointsColumnLayout`;
int $nr=size($rowLayouts)+1;

setParent asMappingUIJointsColumnLayout;
rowLayout -nc 3 -cw3 $cw $sep $cw ("asMappingUIJointsRowLayout"+$nr);
	textField -w ($cw-$sep-10) ("asMappingUIJointsTextFieldA"+$nr);
	text -l "|";
	textField -w ($cw-$sep-10) ("asMappingUIJointsTextFieldB"+$nr);
}

global proc asMappingUIClearAll ()
{
string $rowLayouts[]=`columnLayout -q -ca asMappingUIJointsColumnLayout`;
for ($i=0;$i<size($rowLayouts);$i++)
	deleteUI $rowLayouts[$i];

textFieldGrp -e -tx "Right" asMappingUISideRightTextFieldGrp;
textFieldGrp -e -tx "Left"  asMappingUISideLeftTextFieldGrp;
textFieldGrp -e -tx "" 			asMappingUISideMiddleTextFieldGrp;
checkBox -e -v 1 asMappingUISideBeforeNameCheckBox;
checkBox -e -v 0 asMappingUISideUnderScoreCheckBox;
}

global proc asMappingUILoad (string $tool)
{
int $section=1;
int $nr=1;
string $line,$para,$value;
string $asScriptLocation=`asGetScriptLocationFromSelector`;
string $file=$asScriptLocation+"/AdvancedSkeleton5Files/"+$tool+"s/"+`optionMenu -q -v asMappingUIFiles`+".txt";
string $tempString[];

if (!`file -q -ex $file`)
	error ("Unable to find the file:\""+$file+"\".\n");

print ("// Loading:\""+$file+"\".\n");
asMappingUIClearAll;
int $fileId=`fopen $file "r"`;
string $nextLine = `fgetline $fileId`;
while (size($nextLine)>0)
	{
	$line=`strip $nextLine`;
	tokenize $line "=" $tempString;
	$para=$tempString[0];
	$value=$tempString[1];

	if ($para=="sideRight")
		textFieldGrp -e -tx $value asMappingUISideRightTextFieldGrp;
	if ($para=="sideLeft")
		textFieldGrp -e -tx $value asMappingUISideLeftTextFieldGrp;
	if ($para=="sideMiddle")
		textFieldGrp -e -tx $value asMappingUISideMiddleTextFieldGrp;
	if ($para=="sideBeforeName")
		eval ("checkBox -e -v "+$value+" asMappingUISideBeforeNameCheckBox");
	if ($para=="sideUnderScore")
		eval ("checkBox -e -v "+$value+" asMappingUISideUnderScoreCheckBox");


	if ($section==2)
		{
		if ($para=="")
			{
			$nextLine=`fgetline $fileId`;
			continue;
			}

		asMappingUIAddJoint;
		textField -e -tx $para ("asMappingUIJointsTextFieldA"+$nr);
		textField -e -tx $value ("asMappingUIJointsTextFieldB"+$nr);
		$nr++;
		}

	if ($para=="" && $value=="")
		$section++;
	$nextLine=`fgetline $fileId`;
	}
fclose $fileId;
asMappingUISideOptionChanged;
}

global proc asMappingUIExport (string $tool)
{
string $fDet,$a,$b;
string $asScriptLocation=`asGetScriptLocationFromSelector`;
string $tempString[],$existingMenuItems[],$existingFiles[];

string $return[] = `fileDialog2 -fileFilter "*.txt" -dialogStyle 2 -dir ($asScriptLocation+"/AdvancedSkeleton5Files/"+$tool+"s/")`;
string $file=$return[0];
if ($file=="")
	return;

$fDet+="sideRight="+`textFieldGrp -q -tx asMappingUISideRightTextFieldGrp`+"\n";
$fDet+="sideLeft="+`textFieldGrp -q -tx asMappingUISideLeftTextFieldGrp`+"\n";
$fDet+="sideMiddle="+`textFieldGrp -q -tx asMappingUISideMiddleTextFieldGrp`+"\n";
$fDet+="sideBeforeName="+`checkBox -q -v asMappingUISideBeforeNameCheckBox`+"\n";
$fDet+="sideUnderScore="+`checkBox -q -v asMappingUISideUnderScoreCheckBox`+"\n";
$fDet+="\n";
for ($nr=1;$nr<999;$nr++)
	{
	if (!`rowLayout -q -ex ("asMappingUIJointsRowLayout"+$nr)`)
		break;
	$a=`textField -q -tx ("asMappingUIJointsTextFieldA"+$nr)`;
	$b=`textField -q -tx ("asMappingUIJointsTextFieldB"+$nr)`;
	$fDet+=$a+"="+$b+"\n";
	}

int $fileId=`fopen $file "w"`;
fprint $fileId $fDet;
fclose $fileId;

tokenize $return[0] "/" $tempString;
tokenize $tempString[size($tempString)-1] "." $tempString;

$existingMenuItems=`optionMenu -q -ill asMappingUIFiles`;
for ($i=0;$i<size($existingMenuItems);$i++)
	$existingFiles[$i]=`menuItem -q -l $existingMenuItems[$i]`;
if (!`stringArrayCount $tempString[0] $existingFiles`)
	{
	setParent -menu asMappingUIFiles;
	menuItem -l $tempString[0];
	optionMenu -e -v $tempString[0] asMappingUIFiles;
	}
}

global proc asNameMatcherAutoRigFit ()
{
int $isMiddleJoint,$numChilJoInts,$maxNumChildJoints,$numMissing;
int $sideBeforeName=`checkBox -q -v asMappingUISideBeforeNameCheckBox`;
int $sideUnderScore=`checkBox -q -v asMappingUISideUnderScoreCheckBox`;
float $scale;
float $pos[],$bb[];
textField -e -ebg 1 asMappingUIJointsTextFieldA1;
float $bgc[]=`textField -q -bgc asMappingUIJointsTextFieldA1`;
textField -e -ebg 0 asMappingUIJointsTextFieldA1;
frameLayout -e -ebg 1 asMappingUITemplatesFrameLayout;
float $bgc2[]=`frameLayout -q -bgc asMappingUITemplatesFrameLayout`;
frameLayout -e -ebg 0 asMappingUITemplatesFrameLayout;
string $a,$b,$aLong,$bResolved,$otherTopNode,$underS,$bs,$suffix,$part,$longHipToKeep,$baseName;
string $sideRight=`textFieldGrp -q -tx asMappingUISideRightTextFieldGrp`;
string $sideLeft=`textFieldGrp -q -tx asMappingUISideLeftTextFieldGrp`;
string $sideMiddle=`textFieldGrp -q -tx asMappingUISideMiddleTextFieldGrp`;
string $nameSpaceB=`textField -q -tx asMappingUINameSpacesTextFieldB1`;
string $tempString[],$tempString2[],$tempString3[],$tempString4[],$tempString5[],$fitJoints[],$allReservedNames[];
string $reservedNames[]={"Root","Spine1","Spine2","Chest","Scapula","Shoulder","Elbow","Wrist","Cup",
	"ThumbFinger1","ThumbFinger2","ThumbFinger3","ThumbFinger4",
	"IndexFinger1","IndexFinger2","IndexFinger3","IndexFinger4",
	"MiddleFinger1","MiddleFinger2","MiddleFinger3","MiddleFinger4",
	"RingFinger1","RingFinger2","RingFinger3","RingFinger4",
	"PinkyFinger1","PinkyFinger2","PinkyFinger3","PinkyFinger4",
	"Neck","Head","HeadEnd","Eye","EyeEnd","Jaw","JawEnd",
	"Hip","Knee","Ankle","Toes","ToesEnd"};

for ($i=0;$i<size($reservedNames);$i++)
	for ($p=0;$p<3;$p++)
		{
		if ($p==0) $part="";
		if ($p==1) $part="Part1";
		if ($p==2) $part="Part2";
		$allReservedNames[size($allReservedNames)]=$reservedNames[$i]+$part;
		}

//Remove any animation that might be on joints (iclone seem to have test-animation by default)
$tempString=`ls -type joint`;
for ($i=0;$i<size($tempString);$i++)
	{
	$tempString2=`listConnections -type animCurve -s 1 -d 0 $tempString[$i]`;
	for ($y=0;$y<size($tempString2);$y++)
		delete $tempString2[$y];
	}

//remove existing FitSkeleton
if (`objExists |FitSkeleton`)
	delete |FitSkeleton;

//resolve Daz non-unique names
$longHipToKeep="hip";
$tempString=`ls hip`;
if (size($tempString)>1)
	{
	warning "Found multiple objects called \"hip\", this is probably a export from DAZ, now attemting to merge all the DAZ skeletons..";
	for ($i=0;$i<size($tempString);$i++)
		{
		$tempString2=`listRelatives -ad -c $tempString[$i]`;
		$numChilJoints=size($tempString2);
		if ($numChilJoints>$maxNumChildJoints)
			{
			$longHipToKeep=$tempString[$i];
			$maxNumChildJoints=$numChilJoints;
			}
		}
	}
//rename to unique names
for ($i=0;$i<size($tempString);$i++)
	{
	if ($tempString[$i]==$longHipToKeep)
		continue;
	$tempString2=`listRelatives -p $tempString[$i]`;
	$baseName=$tempString2[0];
	$tempString2=`listRelatives -ad -c -f $baseName`;
	$tempString3=`listRelatives -ad -c $baseName`;
	$tempString4=`listRelatives -ad -c -f $longHipToKeep`;
	$tempString5=`listRelatives -ad -c $longHipToKeep`;
	for ($y=0;$y<size($tempString2);$y++)
		{
		rename $tempString2[$y] ($baseName+"_"+$tempString3[$y]);
		for ($z=0;$z<size($tempString5);$z++)//constraint to `keepJoint`
			if ($tempString5[$z]==$tempString3[$y])
				{
				parentConstraint $tempString4[$z] ($baseName+"_"+$tempString3[$y]);
				scaleConstraint $tempString4[$z] ($baseName+"_"+$tempString3[$y]);
				}
		}
	}

//turning Off "segmentScaleCompensate" for all joints, for better scale behaviour
$tempString=`ls -type joint`;
for ($i=0;$i<size($tempString);$i++)
	setAttr ($tempString[$i]+".segmentScaleCompensate") 0;


//Unique naming requirements workaround, rename the "Other"
for ($nr=1;$nr<999;$nr++)
	{
	if (!`rowLayout -q -ex ("asMappingUIJointsRowLayout"+$nr)`)
		break;
	$b=`textField -q -tx ("asMappingUIJointsTextFieldB"+$nr)`;

	for ($z=0;$z<4;$z++)
		{
		if ($z==0) $suffix="_R";
		if ($z==1) $suffix="_L";
		if ($z==2) $suffix="_M";
		if ($z==3) $suffix="";

		$bs=$b+$suffix;

		if (`objExists $bs`)
			for ($i=0;$i<size($allReservedNames);$i++)
				if ($bs==$allReservedNames[$i]+$suffix)
					{
					print ("Renaming \""+$bs+"\" to \""+$b+"TMP"+$suffix+"\", to avoid name clashing.\n");
					rename $bs ($b+"TMP"+$suffix);
					textField -e -tx ($b+"TMP") ("asMappingUIJointsTextFieldB"+$nr);
					}

		}
	}

//find $otherTopNode
for ($nr=1;$nr<999;$nr++)
	{
	if (!`rowLayout -q -ex ("asMappingUIJointsRowLayout"+$nr)`)
		break;
	$b=`textField -q -tx ("asMappingUIJointsTextFieldB"+$nr)`;
	$bResolved=$b;
	if ($nameSpaceB!="")
		$bResolved=$nameSpaceB+":"+$b;
	$tempString=`ls $bResolved`;
	if (size($tempString)!=1)
		continue;
	$tempString=`ls -l $bResolved`;
	tokenize $tempString[0] "|" $tempString;
	$otherTopNode="|"+$tempString[0];
	break;
	}

//find $otherTopNode. part2, migh require sidePrefix (checking for middle only)
if ($otherTopNode=="")
	for ($nr=1;$nr<999;$nr++)
		{
		if (!`rowLayout -q -ex ("asMappingUIJointsRowLayout"+$nr)`)
			break;
		$b=`textField -q -tx ("asMappingUIJointsTextFieldB"+$nr)`;
		$underS="";
		if ($sideUnderScore)
			$underS="_";
		if ($sideBeforeName)
			$bResolved=$sideMiddle+$underS+$b;
		else
			$bResolved=$b+$underS+$sideMiddle;
		$tempString=`ls $bResolved`;
		if (size($tempString)!=1)
			continue;
		$tempString=`ls -l $bResolved`;
		tokenize $tempString[0] "|" $tempString;
		$otherTopNode="|"+$tempString[0];
		break;
		}

if ($otherTopNode=="")
	error ("Unable to find Top-Node of the \"Other\" Skeleton,\n"
		+"Possible reasones:\n"
		+"-No joints in the list in the \"joints\" section.\n"
		+"-None of the joints in the list have unique names, meaning they have same name as other objects in the scene.\n");

if (!`optionMenu -q -ex asFitFiles`)
	AdvancedSkeleton5;
optionMenu -e -v "biped.ma" asFitFiles;
asFitSkeletonImport;

$bb=`xform -q -bb $otherTopNode`;
$scale=$bb[4]/17.0;
setAttr FitSkeleton.s -type float3 $scale $scale $scale;

for ($nr=1;$nr<999;$nr++)
	{
	if (!`rowLayout -q -ex ("asMappingUIJointsRowLayout"+$nr)`)
		break;
	$a=`textField -q -tx ("asMappingUIJointsTextFieldA"+$nr)`;
	$b=`textField -q -tx ("asMappingUIJointsTextFieldB"+$nr)`;

	if (!`objExists $a`)
		{
		warning ("AdvancedSkeleton FitJoint \""+$a+"\" not found");
		continue;
		}

	//find $aLong
	$fitJoints=`listRelatives -ad -type joint FitSkeleton`;
	$tempString=`listRelatives -fullPath -ad -type joint FitSkeleton`;
	for ($i=0;$i<size($fitJoints);$i++)
		if ($fitJoints[$i]==$a)
			$aLong=$tempString[$i];

	$isMiddleJoint=1;
	$pos=`xform -q -ws -t $aLong`;
	if ($pos[0]>0.001 || $pos[0]<-0.001)
		$isMiddleJoint=0;

	//find $bResolved
	$underS="";
	if ($sideUnderScore)
		$underS="_";
	if ($isMiddleJoint && $sideMiddle=="")//special case, no middleSuffix, so no underscore
		$underS="";

	if ($sideBeforeName)
		{
		if ($isMiddleJoint) $bResolved=$sideMiddle+$underS+$b;
		else 								$bResolved=$sideRight+$underS+$b;
		}
	else
		{
		if ($isMiddleJoint) $bResolved=$b+$underS+$sideMiddle;
		else 								$bResolved=$b+$underS+$sideRight;
		}

	if ($nameSpaceB!="")
		$bResolved=$nameSpaceB+":"+$bResolved;

	if (!`objExists $bResolved`)
		{
		warning ("Other Joint \""+$bResolved+"\" not found");
		textField -e -bgc 1.0 0.5 0.5 ("asMappingUIJointsTextFieldB"+$nr);
		$numMissing++;
		continue;
		}
	else
		textField -e -bgc $bgc[0] $bgc[1] $bgc[2] ("asMappingUIJointsTextFieldB"+$nr);

	$pos=`xform -q -ws -t $bResolved`;
	xform -ws -t $pos[0] $pos[1] $pos[2] $aLong;
	}

if ($numMissing)
	frameLayout -e -cl 1 -bgc 1.0 0.5 0.5 asMappingUIJointsFrameLayout;
else
	frameLayout -e -bgc $bgc2[0] $bgc2[1] $bgc2[2] asMappingUIJointsFrameLayout;

asFitModeManualUpdate;
select FitSkeleton;
}

global proc asMappingUIFunction (string $function)
{
int $sideBeforeName=`checkBox -q -v asMappingUISideBeforeNameCheckBox`;
int $sideUnderScore=`checkBox -q -v asMappingUISideUnderScoreCheckBox`;
int $numChar,$numChar2,$numConnectedJoints;
float $height=10;
if (`objExists "Main"`)
	$height=`getAttr "Main.height"`;
string $fk="FK";
if (`checkBox -q -ex asMappingUIFKExtraCheckBox`)
	if (`checkBox -q -v asMappingUIFKExtraCheckBox`)
		$fk="FKExtra";
string $target,$source,$dest;
string $underS="";
string $sideRight=`textFieldGrp -q -tx asMappingUISideRightTextFieldGrp`;
string $sideLeft=`textFieldGrp -q -tx asMappingUISideLeftTextFieldGrp`;
string $sideMiddle=`textFieldGrp -q -tx asMappingUISideMiddleTextFieldGrp`;
string $nameSpaceA=`textField -q -tx asMappingUINameSpacesTextFieldA1`;
string $nameSpaceB=`textField -q -tx asMappingUINameSpacesTextFieldB1`;
string $tempString[],$as[],$bs[];
string $deformJoints[]=`listRelatives -type joint -ad ($nameSpaceA+"DeformationSystem")`;

if ($function=="MoCapConnect" && `objExists MoCapConstraints`)
	error "MoCapConstraints object already exists, which means MoCap skeleton is already connected";

for ($nr=1;$nr<999;$nr++)
	{
	if (!`rowLayout -q -ex ("asMappingUIJointsRowLayout"+$nr)`)
		break;
	$as[$nr-1]=`textField -q -tx ("asMappingUIJointsTextFieldA"+$nr)`;
	$bs[$nr-1]=`textField -q -tx ("asMappingUIJointsTextFieldB"+$nr)`;
	}

for ($i=0;$i<size($deformJoints);$i++)
	{
	$target="";
	if ($sideUnderScore) $underS="_";
	$numChar=size($deformJoints[$i]);
	for ($y=0;$y<size($as);$y++)
		if (`substring $deformJoints[$i] 1 ($numChar-2)`==$nameSpaceA+$as[$y])
			{
			if (`gmatch $deformJoints[$i] "*_R"`)
				{
				if ($sideBeforeName)
					$target=$sideRight+$underS+$bs[$y];
				else
					$target=$bs[$y]+$underS+$sideRight;
				}
			if (`gmatch $deformJoints[$i] "*_L"`)
				{
				if ($sideBeforeName)
					$target=$sideLeft+$underS+$bs[$y];
				else
					$target=$bs[$y]+$underS+$sideLeft;
				}
			if (`gmatch $deformJoints[$i] "*_M"`)
				{
				if ($sideMiddle=="")//special case, no middleSuffix, so no underscore
					$underS="";
				if ($sideBeforeName)
					$target=$sideMiddle+$underS+$bs[$y];
				else
					$target=$bs[$y]+$underS+$sideMiddle;
				}
			if ($nameSpaceB!="")
				$target=$nameSpaceB+":"+$target;
			}
	if ($target=="")
		continue;

	//AutoRigConstraint
	if ($function=="AutoRigConstraint")
		{
		//some packages makes locked or limited atrributes, so open up these first
		transformLimits -erx 0 0 -ery 0 0 -erz 0 0 -etx 0 0 -ety 0 0 -etz 0 0 -esx 0 0 -esy 0 0 -esz 0 0 $target;
		asLockAttr $target 0 0 0 0;

		print ($deformJoints[$i]+" -> "+$target+"\n");
		select $target;
		if (`objExists ($target+"_parentConstraint1")`) delete ($target+"_parentConstraint1");
		if (`objExists ($target+"_scaleConstraint1")`) delete ($target+"_scaleConstraint1");
		parentConstraint -mo $deformJoints[$i] $target;
		scaleConstraint $deformJoints[$i] $target;
		}

	//MoCapConnect
	if ($function=="MoCapConnect")
		{
		if ($nameSpaceA!="")
			$dest=`substitute $nameSpaceA $deformJoints[$i] ($nameSpaceA+$fk)`;
		else
			$dest=$fk+$deformJoints[$i];
		if (`gmatch $dest "*Root_M"`)
			{//Use RootX instead of Root, as Root may have `inbetween` joint
			$dest=`substitute "FKRoot_M" $dest "RootX_M"`;
			if ($fk=="FKExtra")
				$dest=`substitute "FKExtraRoot_M" $dest "RootExtraX_M"`;
			}
		if (!`objExists $dest`)
			continue;
		if (!`objExists $target`)
			{
			if (!`objExists MoCapConstraints`)
				error ("Unable to find \""+$target+"\", Maybe the NameSpace is not defined ?\n");
			else
				{
				print ("\""+$target+"\" not found, Skipping.\n");
				continue;
				}
			}

		print ($target+" -> "+$dest+"\n");

		//if MoCap-joint-name clashes with FitSkeleton-joint-name (e.g Head, Neck)
		//we will update $target to have `fullPath`
		$tempString=`ls -l $target`;
		if (size($tempString)>1)
			{
			for ($y=0;$y<size($tempString);$y++)
				{
				if (`gmatch $tempString[$y] "*|Group|*"`)
					continue;
				$target=$tempString[$y];
				}
			print ("non-unique name update:"+$target+" -> "+$dest+"\n");
			}

		if (!`objExists MoCapConstraints`)
			{
			createNode -n MoCapConstraints transform;
			asLockAttr MoCapConstraints 1 1 1 1;
			}
		if (!`attributeExists disableConstraints MoCapConstraints`)
			addAttr -k 1 -ln "disableConstraints"  -at bool MoCapConstraints;
		if ($deformJoints[$i]==$nameSpaceA+"Root_M")
			{
			$tempString=`pointConstraint -mo $target $dest`;
//			parent $tempString[0] MoCapConstraints;
			connectAttr MoCapConstraints.disableConstraints ($tempString[0]+".nodeState");
			}
		$tempString=`orientConstraint -mo $target $dest`;
//		parent $tempString[0] MoCapConstraints;
		connectAttr MoCapConstraints.disableConstraints ($tempString[0]+".nodeState");
		$numConnectedJoints++;
		}

	//MoCapZeroOut
	if ($function=="MoCapZeroOut")
		{
		if (!`objExists $target`)
			continue;
		catchQuiet (`setAttr ($target+".r") -type float3 0 0 0`);
		$numConnectedJoints++;
		}
	}

//blendShapes
if ($function=="CreateBSControl")
	{
	$tempString=`ls -type blendShape`;
	for ($i=0;$i<size($tempString);$i++)
		{
		circle -n ($tempString[$i]+"Ctrl") -c 0 0 0 -nr 0 1 0 -sw 360 -r 1 -d 3 -ut 0 -tol 0.01 -s 8 -ch 0;
		parentConstraint Head_M ($tempString[$i]+"Ctrl");
		setAttr ($tempString[$i]+"Ctrl_parentConstraint1.target[0].targetOffsetTranslateX") (($height/40.0)*$i);
		setAttr ($tempString[$i]+"Ctrl_parentConstraint1.target[0].targetOffsetTranslateZ") ($height/-10.0);
		setAttr ($tempString[$i]+"CtrlShape.overrideEnabled") 1;
		setAttr ($tempString[$i]+"CtrlShape.overrideColor") 17;
		asLockAttr ($tempString[$i]+"Ctrl") 1 1 1 1;
		$tempString2=`listAttr -m ($tempString[$i]+".weight")`;
		if (size($tempString2)==0)
			delete ($tempString[$i]+"Ctrl");
		for ($y=0;$y<size($tempString2);$y++)
			{
			addAttr -k 1 -ln $tempString2[$y] -at double ($tempString[$i]+"Ctrl");
			connectAttr -f ($tempString[$i]+"Ctrl."+$tempString2[$y]) ($tempString[$i]+"."+$tempString2[$y]);
			}
		}
	}

select -cl;
if ($function=="MoCapConnect")
	print ("// "+$numConnectedJoints+" joints connected.\n");
if ($function=="MoCapZeroOut")
	print ("// "+$numConnectedJoints+" joints `zeroed out`.\n");
}

global proc asMoCapMatcherNameSpaceDetect ()
{
string $sel[]=`ls -sl`;
string $tempString[];
string $nameSpace;
for ($i=0;$i<size($sel);$i++)
	{
	tokenize $sel[$i] ":" $tempString;
	if (size($tempString)>1)
		$nameSpace=$tempString[0];
	}
print ("// nameSpace \""+$nameSpace+"\" detected, and updated in the  \"NameSpaces\" section.\n");
textField -e -tx $nameSpace asMappingUINameSpacesTextFieldB1;
}

global proc asMoCapMatcherScan ()
{
asMoCapMatcherNameSpaceDetect;
}

global proc asMoCapMatcherDisconnect ()
{
string $tempString[];

if (`objExists MoCapConstraints`)
	{
	if (`attributeExists disableConstraints MoCapConstraints`)
		$tempString=`listConnections -s 0 -d 1 MoCapConstraints.disableConstraints`;
	for ($i=0;$i<size($tempString);$i++)
		delete $tempString[$i];
	delete MoCapConstraints;
	}
print ("// MoCap Skeleton disconnected.\n");
}

global proc asMoCapMatcherBake ()
{
string $tempString[],$bakeControls[];

if (!`objExists MoCapConstraints`) error "MoCapConstraints object not found";
if (!`attributeExists disableConstraints MoCapConstraints`) error "MoCapConstraints.disableConstraints attribute not found";

string $moCapConstraints[]=`listConnections -s 0 -d 1 MoCapConstraints.disableConstraints`;
for ($i=0;$i<size($moCapConstraints);$i++)
	{
	$tempString=`listConnections ($moCapConstraints[$i]+".constraintParentInverseMatrix")`;
	$bakeControls[size($bakeControls)]=$tempString[0];
	}
select $bakeControls;
bakeResults -simulation true -t (`playbackOptions -q -min`+":"+`playbackOptions -q -max`) -sampleBy 1 -disableImplicitControl true -preserveOutsideKeys false -sparseAnimCurveBake false -removeBakedAttributeFromLayer false 
	-bakeOnOverrideLayer false -controlPoints false -shape false $bakeControls;
select $bakeControls;
evalEcho "delete -staticChannels -unitlessAnimationCurves false -hierarchy none -controlPoints 0 -shape 1";
}

global proc asMoCapLibraryUI (string $uiName)
{
asEnsureAsGetScriptLocation;
string $frameLayouts[]={"asMCLBody","asMCLMoCapMaya","asMCLCNU","asMCLMoCapCustom","asMCLFace"};
for ($i=0;$i<size($frameLayouts);$i++)
	if (!`optionVar -ex ($frameLayouts[$i]+"FrameLayout")`)
		optionVar -iv ($frameLayouts[$i]+"FrameLayout") 1;

string $nameSpace;
if ($uiName!="")
	if (`optionMenu -q -ex ($uiName+"OptionMenu")`)
		$nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";

if (`window -q -ex asMoCapLibraryUI`)
    deleteUI asMoCapLibraryUI;
window -t "MoCapLibrary" asMoCapLibraryUI;

columnLayout -adj 1;
	text -l "Motion Capture Library:";
	separator -h 10;
	frameLayout -mw 10 -cll 1 -cl `optionVar -q asMCLBodyFrameLayout` -cc asFL -ec asFL -l "Body" asMCLBodyFrameLayout;
		frameLayout -cll 1 -cl `optionVar -q asMCLMoCapMayaFrameLayout` -cc asFL -ec asFL -l " Motion Capture (Maya)" asMCLMoCapMayaFrameLayout;
			text -l "Maya Example Motion Capture files:";
			text -al "center" -fn "smallBoldLabelFont" -l "* only for Maya versions that include MoCap files";
			rowLayout -nc 2;
				optionMenu asMCLMoCapsOptionMenu;
				button -l "apply" -c ("asMCLMayaApply \""+$nameSpace+"\"");
				setParent..;
			setParent..;

		frameLayout -cll 1 -cl `optionVar -q asMCLMoCapCustomFrameLayout` -cc asFL -ec asFL -l " Motion Capture (Custom)" asMCLMoCapCustomFrameLayout;
			rowLayout -nc 3;
				text -w 100 -l "MoCap library path:";
				textField -ed 0 -w 300 asMCLMoCapCustomPathTextField;
				if (`optionVar -q asMCLMoCapCustomPathTextField`!=0)
					textField -e -tx `optionVar -q asMCLMoCapCustomPathTextField` asMCLMoCapCustomPathTextField;
				button -l "browse" -c "asMCLMoCapCustomBrowse asMCLMoCapCustomPathTextField";
				setParent..;
/*
			rowLayout -nc 3;
				text -w 100 -l "Attach pose file:";
				textField -ed 0 -w 300 asMCLMoCapCustomAttachPoseFileTextField;
				if (`optionVar -q asMCLMoCapCustomAttachPoseFileTextField`!=0)
					textField -e -tx `optionVar -q asMCLMoCapCustomAttachPoseFileTextField` asMCLMoCapCustomAttachPoseFileTextField;
				button -l "browse" -c "asMCLMoCapCustomBrowse asMCLMoCapCustomAttachPoseFileTextField";
				setParent..;
*/
			rowLayout -nc 2;
				optionMenu asMCLMoCapCustomOptionMenu;
				button -l "apply" -c ("asMCLCustomApply \""+$nameSpace+"\"");
				setParent..;
			setParent..;

		frameLayout -cll 1 -cl `optionVar -q asMCLCNUFrameLayout` -cc asFL -ec asFL -l " Motion Capture (CMU Database)" asMCLCNUFrameLayout;
			text -l "Carnegie Mellon University MoCap Database files";
			text -al "center" -fn "smallBoldLabelFont" -l "http://mocap.cs.cmu.edu";
			optionMenu -cc asPopulateMCLCNU2OptionMenu asMCLCNU1OptionMenu;
			optionMenu -cc asUpdateMoCapCNUApplyButton asMCLCNU2OptionMenu;
			separator -h 5;
			rowLayout -nc 2;
				separator -w 200 -st none;
				button -w 100 -l "apply" -c ("asMCLCNUApply \""+$nameSpace+"\"") asMCLCNUApplyButton;
				setParent..;
			setParent..;

		separator -h 20 -st none;
		rowLayout -nc 2;
			separator -w 50 -st none;
			button -l "Remove Body animation" -c ("asMCLRemoveBody \""+$nameSpace+"\"");
			setParent..;
		setParent..;

	frameLayout -cll 1 -cl `optionVar -q asMCLFaceFrameLayout` -cc asFL -ec asFL -l "Face" asMCLFaceFrameLayout;
		columnLayout;
			rowLayout -nc 2;
				separator -h 20 -w 50 -st none;
				button -l "import Face test-animation" -c ("asMCLTestFace \""+$nameSpace+"\"");
			setParent..;
		setParent..;
		rowLayout -nc 2;
			separator -h 20 -w 50 -st none;
			button -l "Remove Face animation" -c ("asMCLRemoveFace \""+$nameSpace+"\"");
			setParent..;

asPopulateMCLMoCapsOptionMenu;
asPopulateMCLMoCapCustomOptionMenu;
asPopulateMCLCNU1OptionMenu;

showWindow asMoCapLibraryUI;
}

global proc asMCLMoCapCustomBrowse (string $textField)
{
int $fileMode=1;
if ($textField=="asMCLMoCapCustomPathTextField")
	$fileMode=3;
string $result[]=`fileDialog2 -fm $fileMode -cap folder -okc Choose`;
textField -e -tx $result[0] $textField;
optionVar -sv $textField $result[0];
asPopulateMCLMoCapCustomOptionMenu;
}

global proc asPopulateMCLMoCapsOptionMenu ()
{
string $mayaLocation=`getenv MAYA_LOCATION`;
string $mocapFbxLocation=$mayaLocation+"/Examples/Animation/Motion_Capture/FBX";
if (!`file -q -ex $mocapFbxLocation`)
	return;
string $files[]=`getFileList -fs "*.fbx" -fld $mocapFbxLocation`;
setParent -menu asMCLMoCapsOptionMenu;
for ($i=0;$i<size($files);$i++)
	menuItem -l $files[$i];
}

global proc asPopulateMCLMoCapCustomOptionMenu ()
{
string $path=`textField -q -tx asMCLMoCapCustomPathTextField`;
string $tempString[];

if ($path=="")
	return;

$tempString=`optionMenu -q -ill asMCLMoCapCustomOptionMenu`;
for ($y=0;$y<size($tempString);$y++)
	deleteUI $tempString[$y];

string $files[]=`getFileList -fs "*.*" -fld $path`;
setParent -menu asMCLMoCapCustomOptionMenu;
for ($i=0;$i<size($files);$i++)
	menuItem -l $files[$i];
}

global proc asPopulateMCLCNU1OptionMenu ()
{
string $line;
string $subjects[],$tempString[];
string $file=`asGetScriptLocation`+"/AdvancedSkeleton5Files/moCapCNUlib/moCapCNUlib.txt"; 

if (!`file -q -ex $file`)
	error ("Unable to find file:"+$file);

int $fileId=`fopen $file "r"`;
string $nextLine = `fgetline $fileId`;
while (size($nextLine)>0)
	{
	$line=`strip $nextLine`;
	tokenize $line $tempString;
	if (`gmatch $line "Subject #*"`)
		$subjects[size($subjects)]=$line;
	$nextLine=`fgetline $fileId`;
	}
fclose $fileId;

setParent -menu asMCLCNU1OptionMenu;
for ($i=0;$i<size($subjects);$i++)
	menuItem -l $subjects[$i];
asPopulateMCLCNU2OptionMenu;
//asUpdateMoCapCNUApplyButton;
}

global proc asPopulateMCLCNU2OptionMenu ()
{
int $subjectMatch;
string $line;
string $tempString[],$clips[];
string $file=`asGetScriptLocation`+"/AdvancedSkeleton5Files/moCapCNUlib/moCapCNUlib.txt";
string $subject=`optionMenu -q -v asMCLCNU1OptionMenu`;

//remove existing items
$tempString=`optionMenu -q -ill asMCLCNU2OptionMenu`;
for ($y=0;$y<size($tempString);$y++)
	deleteUI $tempString[$y];

int $fileId=`fopen $file "r"`;
string $nextLine = `fgetline $fileId`;
while (size($nextLine)>0)
	{
	$line=`strip $nextLine`;
	tokenize $line $tempString;
	if (`gmatch $line "Subject #*"`)
		{
		if ($line==$subject)
			$subjectMatch=1;
		else
			$subjectMatch=0;
		}
	if ($subjectMatch && `gmatch $line "[0-9][0-9]*"`)
		$clips[size($clips)]=$line;

	$nextLine=`fgetline $fileId`;
	}
fclose $fileId;

setParent -menu asMCLCNU2OptionMenu;
for ($i=0;$i<size($clips);$i++)
	menuItem -l $clips[$i];

asUpdateMoCapCNUApplyButton;
}

global proc asUpdateMoCapCNUApplyButton ()
{
string $label="apply";
string $asScriptLocation=`asGetScriptLocation`;
string $moCapCNUlibDir=$asScriptLocation+"/AdvancedSkeleton5Files/moCapCNUlib/downloads";
string $clipLongName=`optionMenu -q -v asMCLCNU2OptionMenu`;
string $files[]=`getFileList -fld $moCapCNUlibDir`;
string $tempString[];
tokenize $clipLongName $tempString;
string $clipShortName=$tempString[0];
string $mayaFile=$moCapCNUlibDir+"/"+$clipShortName+".ma";

if (!`file -q -ex $mayaFile`)
	$label="download && apply";
button -e -l $label asMCLCNUApplyButton;
}

global proc asMCLMayaApply (string $nameSpace)
{
int $lastKeyFrameNr;
float $posA[],$posB[],$keyTimes[],$rotA[],$rotB[];
string $mayaLocation=`getenv MAYA_LOCATION`;
string $mocapFbxLocation=$mayaLocation+"/Examples/Animation/Motion_Capture/FBX";
string $fileName=`optionMenu -q -v asMCLMoCapsOptionMenu`;
string $file=$mocapFbxLocation+"/"+$fileName;
string $basename=`basenameEx $file`;
string $ref="MotionCaptureLibrary:"+$basename+":Reference";
string $hipsJoint,$side,$rightLeft,$mpd,$moCapNs;
string $constraintObjs[],$constraintToObjs[],$tempString[];
string $fingers[]={"Thumb","Index","Middle","Ring","Pinky"};

if ($fileName=="")
	error "No MotionCapture Example choosen";

asMCLRemoveBody $nameSpace;

file -r -type "FBX" -ignoreVersion -gl -mergeNamespacesOnClash false -namespace "MotionCaptureLibrary" -options "v=0;" $file;
$tempString=`ls -r 1 Reference`;
if (!`size($tempString)`)
	error "Top node named Reference, not found in this file";
$moCapNs=`substitute ":Reference" $tempString[0] ""`;

$ref=$moCapNs+":Reference";

currentTime 0;
$tempString=`listRelatives -type joint -c $ref`;
$hipsJoint=$tempString[0];

createNode -n MotionCaptureLibraryScale transform;
parent $ref MotionCaptureLibraryScale;

$posA=`xform -q -ws -t ($nameSpace+"Root_M")`;
$posB=`xform -q -ws -t $hipsJoint`;
setAttr MotionCaptureLibraryScale.s -type float3 ($posA[1]/$posB[1]) ($posA[1]/$posB[1]) ($posA[1]/$posB[1]);

$keyTimes=`keyframe -q -tc ($hipsJoint+".tx")`;
$lastKeyFrameNr=$keyTimes[size($keyTimes)-1];

playbackOptions -min 0 -ast 0 -max $lastKeyFrameNr -aet $lastKeyFrameNr;
//asSetAllFK "";
if (`objExists ($nameSpace+"FKIKLeg_R")`) setAttr ($nameSpace+"FKIKLeg_R.FKIKBlend") 10;
if (`objExists ($nameSpace+"FKIKLeg_L")`) setAttr ($nameSpace+"FKIKLeg_L	.FKIKBlend") 10;
if (`objExists ($nameSpace+"FKIKSpine_M")`) setAttr ($nameSpace+"FKIKSpine_M.FKIKBlend") 0;
if (`objExists ($nameSpace+"FKIKArm_R")`) setAttr ($nameSpace+"FKIKArm_R.FKIKBlend") 0;
if (`objExists ($nameSpace+"FKIKArm_L")`) setAttr ($nameSpace+"FKIKArm_L.FKIKBlend") 0;

$tempString=`listRelatives -type joint -ad $ref`;
for ($i=0;$i<size($tempString);$i++)
	setAttr ($tempString[$i]+".r") -type float3 0 0 0;
setAttr ($hipsJoint+".tx") 0;
setAttr ($hipsJoint+".tz") 0;

xform -ws -ro -180 0 180 ($moCapNs+":RightShoulder");
xform -ws -ro 0 0 0 ($moCapNs+":LeftShoulder");
xform -ws -ro 90 0 -180 ($moCapNs+":RightArm");
xform -ws -ro 90 0 0 ($moCapNs+":LeftArm");


for ($b=1;$b>-2;$b=$b-2)
	{
	if ($b==1)  {$side="_R";$rightLeft="Right";$rotA[0]=0;$rotA[1]=0;$rotA[2]=180;$rotB[0]=-90;$rotB[1]=0;$rotB[2]=-180;}
	if ($b==-1) {$side="_L";$rightLeft="Left";$rotA[0]=0;$rotA[1]=0;$rotA[2]=0;$rotB[0]=90;$rotB[1]=0;$rotB[2]=-180;}

	xform -ws -ro $rotA[0] $rotA[1] $rotA[2] ($moCapNs+":"+$rightLeft+"Arm");
	xform -ws -ro $rotA[0] $rotA[1] $rotA[2] ($moCapNs+":"+$rightLeft+"Hand");

	if (`objExists ("FKShoulder"+$side)`)
		xform -ws -ro $rotB[0] $rotB[1] $rotB[2] ("FKShoulder"+$side);
	if (`objExists ("FKWrist"+$side)`)
		xform -ws -ro $rotB[0] $rotB[1] $rotB[2] ("FKWrist"+$side);
	}

$constraintObjs={"RootX_M","FKRoot_M","FKSpine1_M","FKChest_M","FKNeck_M","FKHead_M"};
$constraintToObjs={"Hips","Spine","Spine1","Spine3","Neck","Head"};

for ($b=1;$b>-2;$b=$b-2)
	{
	if ($b==1)  {$side="_R";$rightLeft="Right";}
	if ($b==-1) {$side="_L";$rightLeft="Left";}

	$tempString={("IKLeg"+$side),("IKToes"+$side),("FKScapula"+$side),("FKShoulder"+$side),("FKElbow"+$side),("FKWrist"+$side)};
	$constraintObjs=`stringArrayCatenate $tempString $constraintObjs`;
	$tempString={($rightLeft+"Foot"),($rightLeft+"ToeBase"),($rightLeft+"Shoulder"),($rightLeft+"Arm"),($rightLeft+"ForeArm"),($rightLeft+"Hand")};
	$constraintToObjs=`stringArrayCatenate $tempString $constraintToObjs`;
	}

for ($i=0;$i<size($constraintObjs);$i++)
	{
	if (!`objExists ($moCapNs+":"+$constraintToObjs[$i])` || !`objExists ($nameSpace+$constraintObjs[$i])`)
		continue;
	if (`gmatch $constraintObjs[$i] "FK*"` || `gmatch $constraintObjs[$i] "IKToes_*"`)
		orientConstraint -mo ($moCapNs+":"+$constraintToObjs[$i]) ($nameSpace+$constraintObjs[$i]);
	else
		parentConstraint -mo ($moCapNs+":"+$constraintToObjs[$i]) ($nameSpace+$constraintObjs[$i]);
	}

for ($b=1;$b>-2;$b=$b-2)
	{
	if ($b==1)  {$side="_R";$rightLeft="Right";}
	if ($b==-1) {$side="_L";$rightLeft="Left";}


	for ($y=0;$y<size($fingers);$y++)
		for ($i=1;$i<4;$i++)
			{
			$mpd="MotionCaptureLibrary"+$fingers[$y]+"FingerMPD"+$i+$side;
			if (`objExists $mpd`)
				delete $mpd;
			if (!`objExists ($moCapNs+":"+$rightLeft+"Hand"+$fingers[$y]+$i)` || !`objExists ($nameSpace+"FK"+$fingers[$y]+"Finger"+$i+$side)`)
				continue;
			createNode -n $mpd multiplyDivide;
			connectAttr ($moCapNs+":"+$rightLeft+"Hand"+$fingers[$y]+$i+".ry") ($mpd+".input1Y");
			connectAttr ($moCapNs+":"+$rightLeft+"Hand"+$fingers[$y]+$i+".rz") ($mpd+".input1Z");
			setAttr ($mpd+".input2Y") -1;
			if ($side=="_L")
				setAttr ($mpd+".input2Z") -1;
			connectAttr ($mpd+".outputZ") ($nameSpace+"FK"+$fingers[$y]+"Finger"+$i+$side+".ry");
			connectAttr ($mpd+".outputY") ($nameSpace+"FK"+$fingers[$y]+"Finger"+$i+$side+".rz");
			}
	}

setAttr MotionCaptureLibraryScale.v 0;
select -cl;
currentTime 0;
}

global proc asMCLCustomApply (string $nameSpace)
{
int $autoKeyState=`autoKeyframe -q -state`;
int $lastKeyFrameNr;
float $posA[],$posB[],$keyTimes[];
string $topNode,$hipsJoint,$side,$rightLeft;
string $path=`textField -q -tx asMCLMoCapCustomPathTextField`;
string $file=$path+"/"+`optionMenu -q -v asMCLMoCapCustomOptionMenu`;
string $moCapNs="MotionCaptureLibrary";
string $tempString[],$topNodesBefore[],$topNodesAfter[];

asMCLRemoveBody $nameSpace;

$topNodesBefore=`ls -as`;
file -r -namespace $moCapNs $file;
$topNodesAfter=`ls -as`;
for ($i=0;$i<size($topNodesAfter);$i++)
	if (!`stringArrayCount $topNodesAfter[$i] $topNodesBefore`)
		$topNode=$topNodesAfter[$i];

$tempString=`listRelatives -type joint -ad $topNode`;
$hipsJoint=$tempString[size($tempString)-1];

createNode -n MotionCaptureLibraryScale transform;
setAttr ($topNode+".s") -type float3 1 1 1;

parent $topNode MotionCaptureLibraryScale;

$posA=`xform -q -ws -t ($nameSpace+"Root_M")`;
$posB=`xform -q -ws -t $hipsJoint`;
setAttr MotionCaptureLibraryScale.s -type float3 ($posA[1]/$posB[1]) ($posA[1]/$posB[1]) ($posA[1]/$posB[1]);

$keyTimes=`keyframe -q -tc ($hipsJoint+".tx")`;
$lastKeyFrameNr=$keyTimes[size($keyTimes)-1];

playbackOptions -min 0 -ast 0 -max $lastKeyFrameNr -aet $lastKeyFrameNr;
if ($autoKeyState) autoKeyframe -state 0;

if (`objExists ($nameSpace+"FKIKLeg_R")`) setAttr ($nameSpace+"FKIKLeg_R.FKIKBlend") 10;
if (`objExists ($nameSpace+"FKIKLeg_L")`) setAttr ($nameSpace+"FKIKLeg_L	.FKIKBlend") 10;
if (`objExists ($nameSpace+"FKIKSpine_M")`) setAttr ($nameSpace+"FKIKSpine_M.FKIKBlend") 0;
if (`objExists ($nameSpace+"FKIKArm_R")`) setAttr ($nameSpace+"FKIKArm_R.FKIKBlend") 0;
if (`objExists ($nameSpace+"FKIKArm_L")`) setAttr ($nameSpace+"FKIKArm_L.FKIKBlend") 0;

select -cl;
dgdirty -a;

//Go to T-pose, asuming this is at rot==0 for all joints
$tempString=`listRelatives -ad -type joint $topNode`;
for ($i=0;$i<size($tempString);$i++)
	setAttr ($tempString[$i]+".r") -type float3 0 0 0;
setAttr ($hipsJoint+".tx") 0;
setAttr ($hipsJoint+".tz") 0;

$constraintObjs={"RootX_M","FKRoot_M","FKSpine1_M","FKChest_M","FKNeck_M","FKHead_M"};
$constraintToObjs={"Hips","LowerBack","Spine","Spine1","Neck1","Head"};

for ($b=1;$b>-2;$b=$b-2)
	{
	if ($b==1)  {$side="_R";$rightLeft="Right";}
	if ($b==-1) {$side="_L";$rightLeft="Left";}

//	$tempString={("IKLeg"+$side),("IKToes"+$side),("FKScapula"+$side),("FKShoulder"+$side),("FKElbow"+$side),("FKWrist"+$side)};
//	$tempString={("FKHip"+$side),("FKKnee"+$side),("FKAnkle"+$side),("FKToes"+$side),("FKScapula"+$side),("FKShoulder"+$side),("FKElbow"+$side),("FKWrist"+$side)};
	$tempString={("IKLeg"+$side),("IKToes"+$side),("FKHip"+$side),("FKKnee"+$side),("FKAnkle"+$side),("FKToes"+$side),("FKScapula"+$side),("FKShoulder"+$side),("FKElbow"+$side),("FKWrist"+$side)};
	$constraintObjs=`stringArrayCatenate $tempString $constraintObjs`;
//	$tempString={($rightLeft+"Foot"),($rightLeft+"ToeBase"),($rightLeft+"Shoulder"),($rightLeft+"Arm"),($rightLeft+"ForeArm"),($rightLeft+"Hand")};
//	$tempString={($rightLeft+"UpLeg"),($rightLeft+"Leg"),($rightLeft+"Foot"),($rightLeft+"ToeBase"),($rightLeft+"Shoulder"),($rightLeft+"Arm"),($rightLeft+"ForeArm"),($rightLeft+"Hand")};
	$tempString={($rightLeft+"Foot"),($rightLeft+"ToeBase"),($rightLeft+"UpLeg"),($rightLeft+"Leg"),($rightLeft+"Foot"),($rightLeft+"ToeBase"),($rightLeft+"Shoulder"),($rightLeft+"Arm"),($rightLeft+"ForeArm"),($rightLeft+"Hand")};
	$constraintToObjs=`stringArrayCatenate $tempString $constraintToObjs`;
	}

for ($i=0;$i<size($constraintObjs);$i++)
	{
	if (!`objExists ($moCapNs+":"+$constraintToObjs[$i])` || !`objExists ($nameSpace+$constraintObjs[$i])`)
		continue;
	if (`gmatch $constraintObjs[$i] "FK*"` || `gmatch $constraintObjs[$i] "IKToes_*"`)
		orientConstraint -mo ($moCapNs+":"+$constraintToObjs[$i]) ($nameSpace+$constraintObjs[$i]);
	else
		parentConstraint -mo ($moCapNs+":"+$constraintToObjs[$i]) ($nameSpace+$constraintObjs[$i]);
	}

setAttr MotionCaptureLibraryScale.v 0;
select -cl;
currentTime 1;
if ($autoKeyState) autoKeyframe -state 1;
}

global proc asMCLCNUApply (string $nameSpace)
{
float $posA[],$posB[];
string $side,$rightLeft;
string $asScriptLocation=`asGetScriptLocation`;
string $moCapCNUlibDir=$asScriptLocation+"/AdvancedSkeleton5Files/moCapCNUlib/downloads";
string $clipLongName=`optionMenu -q -v asMCLCNU2OptionMenu`;
string $files[]=`getFileList -fld $moCapCNUlibDir`;
string $tempString[];
tokenize $clipLongName $tempString;
string $clipShortName=$tempString[0];
string $mayaFile=$moCapCNUlibDir+"/"+$clipShortName+".ma";
string $moCapNs="MotionCaptureLibrary";
string $ref=$moCapNs+":reference";

if (`button -q -l asMCLCNUApplyButton`=="download && apply")
	asMCLMoCapCNUDownload;

asMCLRemoveBody $nameSpace;

file -r -type "mayaAscii" -ignoreVersion -gl -mergeNamespacesOnClash false -namespace $moCapNs -options "v=0;" $mayaFile;
currentTime 0;
$hipsJoint="MotionCaptureLibrary:Hips";

createNode -n MotionCaptureLibraryScale transform;
setAttr ($ref+".s") -type float3 1 1 1;

parent $ref MotionCaptureLibraryScale;

$posA=`xform -q -ws -t ($nameSpace+"Root_M")`;
$posB=`xform -q -ws -t $hipsJoint`;
setAttr MotionCaptureLibraryScale.s -type float3 ($posA[1]/$posB[1]) ($posA[1]/$posB[1]) ($posA[1]/$posB[1]);

$keyTimes=`keyframe -q -tc ($hipsJoint+".tx")`;
$lastKeyFrameNr=$keyTimes[size($keyTimes)-1];

playbackOptions -min 0 -ast 0 -max $lastKeyFrameNr -aet $lastKeyFrameNr;
//asSetAllFK "";
if (`objExists ($nameSpace+"FKIKLeg_R")`) setAttr ($nameSpace+"FKIKLeg_R.FKIKBlend") 10;
if (`objExists ($nameSpace+"FKIKLeg_L")`) setAttr ($nameSpace+"FKIKLeg_L	.FKIKBlend") 10;
if (`objExists ($nameSpace+"FKIKSpine_M")`) setAttr ($nameSpace+"FKIKSpine_M.FKIKBlend") 0;
if (`objExists ($nameSpace+"FKIKArm_R")`) setAttr ($nameSpace+"FKIKArm_R.FKIKBlend") 0;
if (`objExists ($nameSpace+"FKIKArm_L")`) setAttr ($nameSpace+"FKIKArm_L.FKIKBlend") 0;


//Go to T-pose, as some rigs might not be rigged in T-pose (CNU shoulders 8 degree, head 16 degree)
if (`objExists ($nameSpace+"FKShoulder_R")`) xform -ws -ro 90 188 0 ($nameSpace+"FKShoulder_R");
if (`objExists ($nameSpace+"FKShoulder_L")`) xform -ws -ro -90 188 0 ($nameSpace+"FKShoulder_L");
if (`objExists ($nameSpace+"FKHead_M")`) setAttr ($nameSpace+"FKHead_M.rotateZ") 16;

//line up root & upleg
$posA=`xform -q -ws -t $hipsJoint`;
xform -ws -t ($posA[0]*-1) 0 ($posA[2]*-1) $ref;

select -cl;
dgdirty -a;
$constraintObjs={"RootX_M","FKRoot_M","FKSpine1_M","FKChest_M","FKNeck_M","FKHead_M"};
$constraintToObjs={"Hips","LowerBack","Spine","Spine1","Neck1","Head"};

for ($b=1;$b>-2;$b=$b-2)
	{
	if ($b==1)  {$side="_R";$rightLeft="Right";}
	if ($b==-1) {$side="_L";$rightLeft="Left";}

//	$tempString={("IKLeg"+$side),("IKToes"+$side),("FKScapula"+$side),("FKShoulder"+$side),("FKElbow"+$side),("FKWrist"+$side)};
//	$tempString={("FKHip"+$side),("FKKnee"+$side),("FKAnkle"+$side),("FKToes"+$side),("FKScapula"+$side),("FKShoulder"+$side),("FKElbow"+$side),("FKWrist"+$side)};
	$tempString={("IKLeg"+$side),("IKToes"+$side),("FKHip"+$side),("FKKnee"+$side),("FKAnkle"+$side),("FKToes"+$side),("FKScapula"+$side),("FKShoulder"+$side),("FKElbow"+$side),("FKWrist"+$side)};
	$constraintObjs=`stringArrayCatenate $tempString $constraintObjs`;
//	$tempString={($rightLeft+"Foot"),($rightLeft+"ToeBase"),($rightLeft+"Shoulder"),($rightLeft+"Arm"),($rightLeft+"ForeArm"),($rightLeft+"Hand")};
//	$tempString={($rightLeft+"UpLeg"),($rightLeft+"Leg"),($rightLeft+"Foot"),($rightLeft+"ToeBase"),($rightLeft+"Shoulder"),($rightLeft+"Arm"),($rightLeft+"ForeArm"),($rightLeft+"Hand")};
	$tempString={($rightLeft+"Foot"),($rightLeft+"ToeBase"),($rightLeft+"UpLeg"),($rightLeft+"Leg"),($rightLeft+"Foot"),($rightLeft+"ToeBase"),($rightLeft+"Shoulder"),($rightLeft+"Arm"),($rightLeft+"ForeArm"),($rightLeft+"Hand")};
	$constraintToObjs=`stringArrayCatenate $tempString $constraintToObjs`;
	}

for ($i=0;$i<size($constraintObjs);$i++)
	{
	if (!`objExists ($moCapNs+":"+$constraintToObjs[$i])` || !`objExists ($nameSpace+$constraintObjs[$i])`)
		continue;
	if (`gmatch $constraintObjs[$i] "FK*"` || `gmatch $constraintObjs[$i] "IKToes_*"`)
		orientConstraint -mo ($moCapNs+":"+$constraintToObjs[$i]) ($nameSpace+$constraintObjs[$i]);
	else
		parentConstraint -mo ($moCapNs+":"+$constraintToObjs[$i]) ($nameSpace+$constraintObjs[$i]);
	}

setAttr MotionCaptureLibraryScale.v 0;
select -cl;
currentTime 1;
}

global proc asMCLMoCapCNUDownload ()
{
string $asScriptLocation=`asGetScriptLocation`;
string $moCapCNUlibDir=$asScriptLocation+"/AdvancedSkeleton5Files/moCapCNUlib/downloads";
string $clipLongName=`optionMenu -q -v asMCLCNU2OptionMenu`;
string $tempString[];
tokenize $clipLongName $tempString;
string $clipShortName=$tempString[0];
string $zipFileUrl="http://www.advancedskeleton.com/download/moCapCNUlib/downloads/"+$clipShortName+".7z";
string $curl=$asScriptLocation+"/AdvancedSkeleton5Files/bin/curl.e";
string $zip=$asScriptLocation+"/AdvancedSkeleton5Files/bin/7za.e";
string $downloadDir=$asScriptLocation+"/AdvancedSkeleton5Files/moCapCNUlib/downloads";
tokenize $zipFileUrl "/" $tempString;
string $downloadedZipFile=$tempString[size($tempString)-1];
string $downloadedZipFilePath=$downloadDir+"/"+$downloadedZipFile;
string $downloadedMaFilePath=$downloadDir+"/"+`substitute "[.]7z" $downloadedZipFile ".ma"`;
string $cmd;

if (!`file -q -ex $downloadDir`) sysFile -md $downloadDir;

//download
if (`about -mac` || `about -linux`)
	{
	$cmd="\"curl -L -o "+$downloadDir+"/"+$downloadedZipFile+" "+$zipFileUrl+"\"";
	evalEcho ("system("+$cmd+")");
	}
else
	{
	$cmd="start\/wait/I \"Downloading\"  \""+$curl+"\" -L -o \""+$downloadDir+"/"+$downloadedZipFile+"\" "+$zipFileUrl;
	print ("// Starting Download:"+$cmd+"\n");
	system ($cmd);
	}

//confirm downloads
if (`file -q -ex $downloadedZipFilePath`)
	print ("// Downloaded sucessfully:"+$downloadedZipFilePath+";\n");
else
	error ("// Download failed, could not find:"+$downloadedZipFilePath+";\n");

//unzip
if (`about -mac` || `about -linux`)
  $cmd="unzip "+$downloadedZipFilePath+" -d "+$moCapCNUlibDir;
else
	$cmd="start\/wait/I \"Unzipping\"  \""+$zip+"\" x \""+$downloadedZipFilePath+"\" -o\""+$moCapCNUlibDir+"\"";
print ("// Starting Unzip:"+$cmd+";\n");
system($cmd);

//confirm unzip
if (`file -q -ex $downloadedMaFilePath`)
	print ("// Unzipped sucessfully:"+$downloadedMaFilePath+";\n");
else
	error ("// Unzipp failed, could not find:"+$downloadedMaFilePath);

//Delete download
if (`filetest -f $downloadedZipFilePath`)
	sysFile -del $downloadedZipFilePath;

asUpdateMoCapCNUApplyButton;
}

global proc asMCLRemoveBody (string $nameSpace)
{
$controlSetMembers=`sets -q ($nameSpace+"ControlSet")`;
string $refFiles[]=`file -q -r`;
string $sel[]=`ls -sl`;
string $tempString[],$tempString2[];

select $controlSetMembers;
string $animCurves[]=`listConnections -type animCurve -s 1 -d 0`;
if (size($animCurves))
	delete $animCurves;

//remove constraint
for ($i=0;$i<size($controlSetMembers);$i++)
	{
	$tempString=`listConnections -type constraint -s 1 -d 0 $controlSetMembers[$i]`;
	for ($y=0;$y<size($tempString);$y++)
		{
		if (!`objExists $tempString[$y]`)
			continue;
		$tempString2=`listConnections -s 1 -d 0 ($tempString[$y]+".target[0].targetRotate")`;
		if (`gmatch $tempString2[0] "MotionCaptureLibrary:*"`)
			delete $tempString[$y];
		}
	}

//remove testMoCap as well
for ($i=0;$i<size($refFiles);$i++)
	if (`file -q -ns $refFiles[$i]`=="MotionCaptureLibrary")
		file -rr $refFiles[$i];
if (`objExists MotionCaptureLibraryScale`)
	delete MotionCaptureLibraryScale;

$tempString=`ls "MotionCaptureLibrary*MPD*"`;
if (size($tempString))
	delete $tempString;

if (`objExists ($nameSpace+"ControlSet")`) asGoToBuildPoseOptions $nameSpace "ControlSet";

catchQuiet (`select $sel`);
print ("// Body Test animations removed.\n");
}

global proc asMCLRemoveFace (string $nameSpace)
{
string $refFiles[]=`file -q -r`;
string $sel[]=`ls -sl`;
string $tempString[],$tempString2[];
select -cl;
if (`objExists ($nameSpace+"FaceControlSet")`) select -add `sets -q ($nameSpace+"FaceControlSet")`;
if (`objExists ($nameSpace+"FKHead_M")`) select -add ($nameSpace+"FKHead_M");
if (`objExists ($nameSpace+"FKNeck_M")`) select -add ($nameSpace+"FKNeck_M");
	
string $animCurves[]=`listConnections -type animCurve -s 1 -d 0`;
if (size($animCurves))
	delete $animCurves;

if (`objExists ($nameSpace+"ControlSet")`) asGoToBuildPoseOptions $nameSpace "ControlSet";
if (`objExists ($nameSpace+"FaceControlSet")`) asGoToBuildPoseOptions $nameSpace "FaceControlSet";

catchQuiet (`select $sel`);
print ("// Face Test animations removed.\n");
}

global proc asMCLTestFace (string $nameSpace)
{
string $ctrl,$attr;
string $tempString[],$tempString2[],$animCurves[];
string $asMotionFaceFile=`asGetScriptLocation`+"/AdvancedSkeleton5Files/div/asMotionFace.ma";

if (`objExists animationTestFaceFromMoCapLibrary`)
	{
	$nameSpace=`getAttr animationTestFaceFromMoCapLibrary.nameSpace`;
	delete animationTestFaceFromMoCapLibrary;
	}
file -r -ignoreVersion -gl -mergeNamespacesOnClash false -namespace "MotionCaptureLibraryFace" -options "v=0;" $asMotionFaceFile;

$animCurves=`ls -type animCurve "MotionCaptureLibraryFace:*"`;
for ($i=0;$i<size($animCurves);$i++)
	{
	$tempString=`duplicate $animCurves[$i]`;
	tokenize $tempString[0] "_" $tempString2;
	$ctrl=$nameSpace+$tempString2[0]+"_"+$tempString2[1];
	$attr=$tempString2[2];
	if (!`attributeExists $attr $ctrl`)
		{
		print ("//Not found:"+$ctrl+"."+$attr+"\n");
		continue;
		}
	 catchQuiet (`connectAttr ($tempString[0]+".output") ($ctrl+"."+$attr)`);
	}
file -rr $asMotionFaceFile;
playbackOptions -min 0 -ast 0 -max 330 -aet 330;
currentTime 70;
print "// Test animation applied.\n";
}

global proc int asIsMayaLT ()
{
int $isMayaLT=0;
if (!`exists CreateWrap`)
	$isMayaLT=1;
return $isMayaLT;
}

global proc asReferenceBrowser (int $model)
{
global string $selectedNamespaceRadioButton;
string $sNRB=$selectedNamespaceRadioButton;
string $referenceOptionsRenamePrefix=`optionVar -q referenceOptionsRenamePrefix`;
int $referenceUseNamespacesDuringFileIO=`optionVar -q referenceUseNamespacesDuringFileIO`;
int $referenceOptionsUseRenamePrefix=`optionVar -q referenceOptionsUseRenamePrefix`;
string $ReferenceMergeOptionValue=`optionVar -q ReferenceMergeOptionValue`;
string $defaultFileReferenceType=`optionVar -q defaultFileReferenceType`;
string $tempString[],$topNodesBefore[],$topNodesAfter[];

string $renamePrefix="model";
string $mergeOptionValue="radioNamespaceOnString";
if (!$model)
	{
	$renamePrefix="anim";
	$mergeOptionValue="radioNamespaceOnFileName";
	}

optionVar -sv referenceOptionsRenamePrefix $renamePrefix;
optionVar -iv referenceUseNamespacesDuringFileIO 1;
optionVar -iv referenceOptionsUseRenamePrefix 1;
optionVar -sv ReferenceMergeOptionValue $mergeOptionValue;
optionVar -sv defaultFileReferenceType "mayaBinary";
if ($selectedNamespaceRadioButton!="")
	$selectedNamespaceRadioButton=$mergeOptionValue;

if ($model)
	$topNodesBefore=`ls -as`;

if (`asIsMayaLT`)
	error ("Maya LT does not have Reference, Import the model instead");
CreateReferenceOptions;

//Something in here Ran again, makes it work
global string $selectedNamespaceRadioButton;
$sNRB=$selectedNamespaceRadioButton;
$referenceOptionsRenamePrefix=`optionVar -q referenceOptionsRenamePrefix`;
$referenceUseNamespacesDuringFileIO=`optionVar -q referenceUseNamespacesDuringFileIO`;
$referenceOptionsUseRenamePrefix=`optionVar -q referenceOptionsUseRenamePrefix`;
$ReferenceMergeOptionValue=`optionVar -q ReferenceMergeOptionValue`;
$defaultFileReferenceType=`optionVar -q defaultFileReferenceType`;

optionVar -sv referenceOptionsRenamePrefix $renamePrefix;
optionVar -iv referenceUseNamespacesDuringFileIO 1;
optionVar -iv referenceOptionsUseRenamePrefix 1;
optionVar -sv ReferenceMergeOptionValue $mergeOptionValue;
optionVar -sv defaultFileReferenceType "mayaBinary";
if ($selectedNamespaceRadioButton!="")
	$selectedNamespaceRadioButton=$mergeOptionValue;
CreateReference;

optionVar -sv referenceOptionsRenamePrefix $referenceOptionsRenamePrefix;
optionVar -iv referenceUseNamespacesDuringFileIO $referenceUseNamespacesDuringFileIO;
optionVar -iv referenceOptionsUseRenamePrefix $referenceOptionsUseRenamePrefix;
optionVar -sv ReferenceMergeOptionValue $ReferenceMergeOptionValue;
optionVar -sv defaultFileReferenceType $defaultFileReferenceType;
if ($sNRB!="")
	$selectedNamespaceRadioButton=$sNRB;

if ($model)
	$topNodesAfter=`ls -as`;
select -cl;
for ($i=0;$i<size($topNodesAfter);$i++)
	if (!`stringArrayCount $topNodesAfter[$i] $topNodesBefore`)
		select -add $topNodesAfter[$i];

if ($model)
	{
	//Hi layer
	if (`objExists Hi`)
		{
		if (`objectType Hi`=="displayLayer")
			editDisplayLayerMembers -noRecurse Hi `ls -selection`;
		}
	else
		{
		createDisplayLayer -name Hi -number 1 -nr;
		setAttr Hi.displayType 1;
		}
	}

if (!$model)
	{
	//refresh character-chooser-dropdowns
	$tempString=`lsUI -type optionMenu`;
	for ($i=0;$i<size($tempString);$i++)
		if (`gmatch $tempString[$i] "asSelector*OptionMenu"` || `gmatch $tempString[$i] "asPicker*OptionMenu"`)
			asPopulateNameSpaceMenu `substitute "OptionMenu" $tempString[$i] ""`;
	}
select -cl;
}

global proc asExportFbxBrowser (string $uiName)
{
string $sel[]=`ls -sl`;
string $nameSpace,$deformationSystem,$exportFbxPre,$exportFbxPost;

string $plugins[]=`pluginInfo -q -ls`;
for ($plug in $plugins)
	if (!`stringArrayCount "fbxmaya" $plugins`)
	error "You Must Load the \"fbxmaya\" plugin !";

if ($uiName!="")
	$nameSpace=`optionMenu -q -v ($uiName+"OptionMenu")`;
if ($nameSpace==":")
	$nameSpace="";

//if (`objExists ($nameSpace+"FaceMotionSystem")` && `objExists ($nameSpace+"DeformationLayers")`)
//	warning "This FaceRig seem to have not yet been \"Converted to SimplifiedFaceSet\", so this facerig will not work in a GameEngine.";

$deformationSystem=$nameSpace+":DeformationSystem";
if (`objExists ($nameSpace+"|root")`) //Unreal Skeleton
	$deformationSystem=$nameSpace+"|root";
if (`objExists ($nameSpace+"|GameSkeletonRoot_M")`) //Custom oriented GameSkeletonRoot
	$deformationSystem=$nameSpace+"|GameSkeletonRoot_M";

select $deformationSystem ($nameSpace+":Geometry");
refresh;

//custom code can be added
if (`attributeExists exportFbxPre fbxExportOptions`) $exportFbxPre=`getAttr fbxExportOptions.exportFbxPre`;
if (`attributeExists exportFbxPost fbxExportOptions`) $exportFbxPost=`getAttr fbxExportOptions.exportFbxPost`;
if ($exportFbxPre!="") evalEcho ($exportFbxPre);

optionVar -sv "defaultFileExportActiveType" "FBX export";
ExportSelection;

if ($exportFbxPost!="") evalEcho ($exportFbxPost);
print "// Fbx exported.\n";
}

global proc asFaceBsSdk (string $ctrl, float $driveValue, string $bs, float $drivenValue)
{
setDrivenKeyframe -itt spline -ott spline -dv 0 -v 0 -currentDriver $ctrl $bs;
setDrivenKeyframe -itt spline -ott spline -dv $driveValue -v $drivenValue -currentDriver $ctrl $bs;
if ($driveValue>0)
	keyTangent -in 0 -itt linear -ott linear $bs;

//pre post infinity
string $nodes[]=`listConnections -s 1 -d 0 -scn 1 $bs`;
for ($i=0;$i<size($nodes);$i++)
	{
	$tempString=`nodeType -inherited $nodes[$i]`;
	if ($tempString[0]=="animCurve")
		{
		setAttr ($nodes[$i]+".preInfinity") 4;
		setAttr ($nodes[$i]+".postInfinity") 4;
		}
	}
}

global proc string asGetTempDirectory ()
{
string $tempDirectory=`internalVar -utd`;
string $folders[];

//resolve userNames that has beeen `truncated` with Tilde symbol
if (`gmatch $tempDirectory "*~*"`)
	{
	tokenize $tempDirectory "/" $folders;
	$tempDirectory="";
	for ($i=0;$i<size($folders);$i++)
		{
		if (`gmatch $folders[$i] "*~*"`)
			$folders[$i]=`getenv USERNAME`;
		$tempDirectory+=$folders[$i];
//		if ($i<(size($folders)-1))
		if ($i<size($folders))
			$tempDirectory+="/";
		}
	}
return $tempDirectory;
}

global proc string asStripPath (string $path, int $numStrip)
{
string $return;
string $tempString[];

//mac/linux path could start with /
if (`gmatch $path "//*"`) $return="//";
else if (`gmatch $path "/*"`) $return="/";

tokenize $path "/" $tempString;
for ($i=0;$i<size($tempString)-$numStrip;$i++)
	$return+=$tempString[$i]+"/";
return $return;
}

global proc asEnsureAsGetScriptLocation  ()
{
string $asSelectorScriptLocation,$AdvancedSkeletonMelFile;
string $folders[];
if (`exists asGetScriptLocation`)
	return;
else if (`exists asSelectorScriptLocation`)
	{
	$asSelectorScriptLocation=`asSelectorScriptLocation`;
	//Could be that we `have` AdvancedSkeleton, it was just not sourced, so we source it
	if (`gmatch $asSelectorScriptLocation "*/AdvancedSkeleton5Files/*"`) 
		{
		$AdvancedSkeletonMelFile=`asStripPath $asSelectorScriptLocation 2`+"AdvancedSkeleton5.mel";
		if (`file -q -ex $AdvancedSkeletonMelFile`)
			{
			evalDeferred ("source \""+$AdvancedSkeletonMelFile+"\"");
			print ("source \""+$AdvancedSkeletonMelFile+"\";\n");
			error "AdvancedSkeleton5.mel was not sourced. Sourcing this now. Try running this function again";
			}
		else
			error ("Expected file not found:\""+$AdvancedSkeletonMelFile+"\"");
		}
	else
		error "Seems AdvancedSkeleton is not installed (only running Selector or Picker). This function requires AdvancedSkeleton installed.\n";
	}
else
	error "Unable to run `asGetScriptLocation`, and unable to run `asSelectorScriptLocation`, so unable to determine directory for force-aligner.";
}

global proc string asGetScriptLocationFromSelector ()
{
//simulates `asGetScriptLocation`, but can run in `standalone` mode.
int $numChar;
string $asScriptLocation;
string $tempString[];
if (`exists asGetScriptLocation`)
	$asScriptLocation=`asGetScriptLocation`;
else//can run `standalone`
	{
	$tempString[0]=`asSelectorScriptLocation`;
	$asScriptLocation=`asStripPath $tempString[0] 2`;
	if (`gmatch $asScriptLocation "*/"`)//remove last slash
		{
		$numChar=size($asScriptLocation);
		$asScriptLocation=`substring $asScriptLocation 1 ($numChar-1)`;
		}
	}
return $asScriptLocation;
}

global proc string asSelectorScriptLocation ()
{
string $whatIs=`whatIs asSelectorScriptLocation`;
string $fullPath=`substring $whatIs 25 999`;
string $buffer[];
int $numTok=`tokenize $fullPath "/" $buffer`;
if ($numTok<2)
	if (`about -win`)
		$numTok=`tokenize $fullPath "\\" $buffer`;
int $numLetters=size($fullPath);
int $numLettersLastFolder=size($buffer[$numTok-1]);
string $scriptLocation=`substring $fullPath 1 ($numLetters-$numLettersLastFolder)`;
return $scriptLocation;
}
//-- ASTools Procedures Ends Here --//
