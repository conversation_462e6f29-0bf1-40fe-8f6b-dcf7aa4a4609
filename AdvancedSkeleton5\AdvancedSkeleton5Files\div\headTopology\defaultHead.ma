//Maya ASCII 2012 scene
//Name: defaultHead.ma
//Last modified: Wed, Nov 08, 2017 05:03:09 PM
//Codeset: 1252
requires maya "2012";
requires "stereoCamera" "10.0";
currentUnit -l centimeter -a degree -t pal;
fileInfo "application" "maya";
fileInfo "product" "Maya 2012";
fileInfo "version" "2012 x64";
fileInfo "cutIdentifier" "201201172029-821146";
fileInfo "osv" "Microsoft Business Edition, 64-bit  (Build 9200)\n";
fileInfo "comment" "";
fileInfo "incPath" "C:/Users/<USER>/Documents/maya/2015-x64/scripts/AdvancedSkeleton5/AdvancedSkeleton5Files/div/headTopology/incrementalSave/defaultHead.ma/defaultHead.0003.ma";
fileInfo "path" "C:/Users/<USER>/Documents/maya/2015-x64/scripts/AdvancedSkeleton5/AdvancedSkeleton5Files/div/headTopology/defaultHead.ma";
fileInfo "user" "me";
fileInfo "date" "2017/11/08";
fileInfo "time" "17:03:09";
fileInfo "shotInfo" "";
createNode transform -s -n "persp";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -0.36699478436797744 0.42497430127048974 1.7758352900309171 ;
	setAttr ".r" -type "double3" 175.46164742526256 -156.19999999996142 179.99999999998244 ;
	setAttr ".rp" -type "double3" -2.7755575615628914e-017 1.1102230246251565e-016 0 ;
	setAttr ".rpt" -type "double3" -3.9075188777972731e-018 -1.7481387370475386e-018 
		4.0206580339147164e-017 ;
createNode camera -s -n "perspShape" -p "persp";
	setAttr -k off ".v" no;
	setAttr ".fl" 34.999999999999979;
	setAttr ".ncp" 0.001;
	setAttr ".coi" 1.5154364023480766;
	setAttr ".imn" -type "string" "persp";
	setAttr ".den" -type "string" "persp_depth";
	setAttr ".man" -type "string" "persp_mask";
	setAttr ".tp" -type "double3" 0 0.32919086515903473 0.36916229128837585 ;
	setAttr ".hc" -type "string" "viewSet -p %camera";
createNode transform -s -n "top";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -0.25209160198782088 1000.1014840896139 0.1985592057615812 ;
	setAttr ".r" -type "double3" -89.999999999999986 0 0 ;
createNode camera -s -n "topShape" -p "top";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 999.708760133434;
	setAttr ".ow" 1.0145055408649828;
	setAttr ".imn" -type "string" "top";
	setAttr ".den" -type "string" "top_depth";
	setAttr ".man" -type "string" "top_mask";
	setAttr ".tp" -type "double3" 3.5017728805541992e-006 0.39124151319265366 0.057378515601158142 ;
	setAttr ".hc" -type "string" "viewSet -t %camera";
	setAttr ".o" yes;
createNode transform -s -n "front";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -0.16263261691411002 0.58213451923238946 1000.3290506972049 ;
createNode camera -s -n "frontShape" -p "front";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 999.97916768601044;
	setAttr ".ow" 6.7598380783323337;
	setAttr ".imn" -type "string" "front";
	setAttr ".den" -type "string" "front_depth";
	setAttr ".man" -type "string" "front_mask";
	setAttr ".tp" -type "double3" 0.048586874767294619 0.57313127403378339 0.34988301119449261 ;
	setAttr ".hc" -type "string" "viewSet -f %camera";
	setAttr ".o" yes;
createNode transform -s -n "side";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 1000.3282139861501 0.30622436341403375 0.10647663242854377 ;
	setAttr ".r" -type "double3" 0 89.999999999999972 0 ;
	setAttr ".rpt" -type "double3" 5.5466782398352393e-032 0 -6.1629758220391547e-033 ;
createNode camera -s -n "sideShape" -p "side";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 1000.3282140457547;
	setAttr ".ow" 0.70904938320721356;
	setAttr ".imn" -type "string" "side";
	setAttr ".den" -type "string" "side_depth";
	setAttr ".man" -type "string" "side_mask";
	setAttr ".tp" -type "double3" -5.9604644775390625e-008 -0.046261796626903627 0.41401045097348615 ;
	setAttr ".hc" -type "string" "viewSet -s %camera";
	setAttr ".o" yes;
createNode transform -n "headTopology";
createNode mesh -n "headTopologyShape" -p "headTopology";
	setAttr -k off ".v";
	setAttr -s 2 ".iog[0].og";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcol" yes;
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
createNode mesh -n "headTopologyShapeOrig" -p "headTopology";
	setAttr -k off ".v";
	setAttr ".io" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr -s 785 ".uvst[0].uvsp";
	setAttr ".uvst[0].uvsp[0:249]" -type "float2" 0.52051216 0.45185 0.52059788
		 0.42921394 0.51592684 0.4177579 0.53275496 0.41967896 0.51848799 0.406147 0.51307797
		 0.41048297 0.52058786 0.41809198 0.52885973 0.41855791 0.51944596 0.41249099 0.51605493
		 0.41572896 0.52139497 0.42361489 0.52711689 0.42241913 0.52004194 0.41895697 0.51786995
		 0.42216396 0.51773602 0.47018301 0.51313502 0.49432498 0.53217602 0.48172399 0.53445101
		 0.45734096 0.55138987 0.46103483 0.57926798 0.38459796 0.58953196 0.36360499 0.59345394
		 0.3443211 0.5494439 0.33521003 0.55142099 0.33590496 0.5512867 0.33589867 0.54892689
		 0.33523396 0.54659361 0.33506688 0.5464251 0.33480421 0.54020387 0.335419 0.53824586
		 0.334562 0.52145696 0.33808997 0.54231286 0.33718696 0.54865205 0.33624405 0.55109477
		 0.33879787 0.55291802 0.336299 0.55571479 0.33707881 0.55107468 0.33455294 0.55279785
		 0.33287087 0.52916408 0.31808296 0.52838796 0.32593 0.52105999 0.34470084 0.5442394
		 0.34017208 0.52145594 0.33298999 0.52046323 0.33098567 0.5731349 0.3393361 0.56078899
		 0.33684099 0.55675739 0.32739735 0.57011288 0.35367608 0.55643493 0.34283608 0.56319994
		 0.37155113 0.55107659 0.35428381 0.52089894 0.36100924 0.55361003 0.39746025 0.55355591
		 0.42602217 0.54481786 0.39464793 0.52347487 0.39205799 0.56946099 0.40994796 0.56646097
		 0.43561497 0.535586 0.42831001 0.54264295 0.41794896 0.54742986 0.44203413 0.59189802
		 0.254002 0.54149735 0.21944883 0.50076902 0.19492599 0.62891495 0.33291945 0.63631016
		 0.28421363 0.66265392 0.312646 0.683788 0.37806994 0.69243091 0.340516 0.69277596
		 0.32167199 0.66465771 0.99772567 0.92500782 0.9760164 0.75689101 0.67340297 0.74885988
		 0.58638197 0.79923803 0.66838092 0.76279193 0.81373394 0.72210908 0.80809343 0.76310807
		 0.75322396 0.80654401 0.75313908 0.81710798 0.82164401 0.86320919 0.80203491 0.79148698
		 0.55438799 0.79845411 0.36550489 0.824507 0.41011116 0.85853696 0.46902665 0.87705374
		 0.48384902 0.84676594 0.47165099 0.80727077 0.56349409 0.81547797 0.56149787 0.80041569
		 0.40057376 0.84284002 0.47109413 0.78191197 0.38469699 0.76257414 0.37983355 0.75570118
		 0.42465526 0.84765399 0.51850808 0.80072874 0.46046183 0.78408301 0.47231692 0.8139751
		 0.55406493 0.80449206 0.55398047 0.78966385 0.54287988 0.74776286 0.45838806 0.74811184
		 0.47562006 0.81706917 0.56849056 0.82151377 0.56683517 0.82208931 0.5598945 0.82108033
		 0.55135053 0.83762115 0.54032266 0.81844324 0.54497653 0.77541065 0.52044773 0.78034312
		 0.5442065 0.77297598 0.54845774 0.76096189 0.4028177 0.77147794 0.39911401 0.78160495
		 0.40357801 0.83312386 0.4645389 0.84310895 0.507559 0.83600295 0.53177696 0.76449996
		 0.49116388 0.76429564 0.48487014 0.78380394 0.42904201 0.81407094 0.44542998 0.81381196
		 0.44806698 0.77361298 0.41884097 0.820382 0.51310402 0.81226569 0.5434165 0.80813032
		 0.50887412 0.81690496 0.502909 0.82337981 0.49255252 0.82913703 0.49368396 0.8679828
		 0.54446685 0.83372986 0.57357502 0.83614099 0.55972803 0.85360193 0.53235996 0.83498293
		 0.55295795 0.85242498 0.53243399 0.78955573 0.43748739 0.81294399 0.44901901 0.81237388
		 0.44968146 0.78802395 0.44197896 0.77643257 0.42545173 0.78617793 0.43466297 0.7856046
		 0.44055739 0.78237212 0.44023299 0.81068271 0.48814324 0.81421584 0.49891278 0.82177222
		 0.4908472 0.82052296 0.48946199 0.80274999 0.54143292 0.80295765 0.50870621 0.78551394
		 0.44394201 0.79518592 0.45364493 0.77301216 0.4289026 0.77757746 0.45024648 0.76893771
		 0.42420307 0.76381499 0.47122297 0.768013 0.45139396 0.78026915 0.46515331 0.77072406
		 0.45093766 0.77289701 0.45574927 0.76985109 0.46328485 0.77326095 0.46274391 0.77203703
		 0.458958 0.77130228 0.45889702 0.77106994 0.45541599 0.77255803 0.45887622 0.77099997
		 0.45964596 0.77118802 0.460475 0.77051902 0.46136397 0.77011704 0.46046856 0.77155995
		 0.46009496 0.77238196 0.46115887 0.79046869 0.53214604 0.7834419 0.51761007 0.78370601
		 0.49065697 0.78958273 0.47965562 0.84443802 0.40280706 0.86533302 0.41535899 0.76633465
		 0.41926184 0.75733894 0.44770056 0.75510454 0.35964209 0.70908695 0.39943096 0.71455789
		 0.34727094 0.709086 0.43674898 0.80475634 0.4954901 0.79770589 0.50163454 0.79663682
		 0.49044761 0.723382 0.52223098 0.77229702 0.57659 0.99087399 0.84378302 0.86443299
		 0.75707382 0.99209356 0.73243868 0.85974497 0.67338973 0.99209797 0.65921301 0.8517459
		 0.60885322 0.98965055 0.59744751 0.89265394 0.57318473 0.98964804 0.53829306 0.89651966
		 0.50379765 0.98965007 0.468402 0.86505401 0.37525898 0.98965007 0.39821067 0.88283801
		 0.40808696 0.99036956 0.28778741 0.98965007 0.32311001 0.56324899 0.096202798 0.56110692
		 0.146466 0.817873 0.34902194 0.71961695 0.32736599 0.67842352 0.1760855 0.61318392
		 0.16183597 0.66993695 0.29172102 0.68109995 0.27275181 0.64204651 0.26546857 0.64966977
		 0.24792485 0.59548903 0.23316599 0.55046892 0.192176 0.60052592 0.21370111 0.54484779
		 0.20374991 0.8032853 0.31371781 0.72597432 0.24474949 0.80734897 0.57879597 0.80362594
		 0.56950283 0.75746202 0.095896997 0.75537795 0.14415801 0.68119425 0.10990082 0.67255449
		 0.044527993 0.84842771 0.16981585 0.84861135 0.22678338 0.97164959 0.22224738 0.98965001
		 0.22446528 0.90032899 0.2051366 0.89712209 0.26661408 0.93394732 0.22009318 0.58875048
		 0.028244663 0.579009 0.064160995 0.73288095 0.47742498 0.52991796 0.30687597 0.54701543
		 0.43157536 0.53523803 0.44406199 0.5686959 0.31352219 0.58674014 0.30458954 0.53808498
		 0.57055193 0.60859215 0.5639165 0.60311377 0.57800037 0.56913674 0.59052587 0.54006392
		 0.56662792 0.60048157 0.56728202 0.60264701 0.56701303 0.60084891 0.57620895 0.56881893
		 0.58878893;
	setAttr ".uvst[0].uvsp[250:499]" 0.53730524 0.55064082 0.53253198 0.55967891
		 0.54016525 0.556831 0.53675294 0.55893701 0.56350285 0.54019016 0.59205097 0.54833412
		 0.56311733 0.55399966 0.59027499 0.55851609 0.56405729 0.55752558 0.54678845 0.56248873
		 0.56417698 0.55516517 0.5885793 0.56061381 0.59059709 0.55923176 0.6018126 0.57681906
		 0.60324365 0.57501119 0.56997967 0.58673006 0.5697962 0.58828026 0.54478151 0.56961697
		 0.5406341 0.56528574 0.54420602 0.56338513 0.52815533 0.56174159 0.53571993 0.57284492
		 0.5639962 0.5262875 0.53413868 0.54183382 0.61515296 0.55992103 0.59529895 0.536744
		 0.60613292 0.57991695 0.56905198 0.5936029 0.52262497 0.53244406 0.56833154 0.50248379
		 0.51593125 0.56663871 0.50906253 0.54076308 0.52819508 0.58778805 0.56675744 0.60700762
		 0.61513495 0.58874661 0.50811702 0.56559336 0.67067111 0.70623517 0.58332163 0.73453224
		 0.53488696 0.73518103 0.69715708 0.77241594 0.6071226 0.81178945 0.54030114 0.82838696
		 0.62518281 0.55106789 0.60151768 0.52135503 0.58923668 0.46719897 0.60861081 0.4330503
		 0.61359715 0.4948824 0.62160599 0.38962203 0.61608672 0.40966287 0.62468797 0.37109607
		 0.65221399 0.35135099 0.62898397 0.46632996 0.67019892 0.51173776 0.63963592 0.53724098
		 0.63701391 0.40464097 0.67640197 0.43537197 0.67123497 0.46194601 0.63532603 0.43201998
		 0.64249802 0.38770297 0.67776179 0.414397 0.66430104 0.61170995 0.63000214 0.6082148
		 0.77817494 0.21123698 0.78082794 0.26033893 0.85405904 0.26719394 0.85255295 0.31430101
		 0.89578944 0.30802444 0.89559591 0.36076587 0.70869094 0.46435398 0.71846586 0.50413871
		 0.63556194 0.87608093 0.55414182 0.90362829 0.66576731 0.90958095 0.55985719 0.93383801
		 0.76873308 0.91991103 0.75906193 0.95599693 0.88380909 0.88928777 0.89670694 0.94972986
		 0.74540365 0.41400966 0.74132299 0.41824496 0.74077296 0.50358093 0.52729726 0.33188695
		 0.52773309 0.33058804 0.80627394 0.47412896 0.80187398 0.48800501 0.78101188 0.49812114
		 0.789837 0.49599898 0.79232621 0.51002246 0.53331697 0.27413157 0.53898007 0.25154907
		 0.93180799 0.81067574 0.96725202 0.83797586 0.9446649 0.71640295 0.98126394 0.73174995
		 0.94550097 0.64571196 0.98221701 0.65889293 0.94432986 0.60385484 0.97969794 0.59716696
		 0.93977892 0.53193587 0.979137 0.53867102 0.93535691 0.44620785 0.97737002 0.47022298
		 0.93433195 0.38318279 0.97582597 0.39881301 0.93623394 0.32383201 0.97384393 0.326334
		 0.93200517 0.27644926 0.97035575 0.29168689 0.52122694 0.4333407 0.52817792 0.43099701
		 0.529827 0.44550201 0.51983696 0.42592981 0.51682097 0.430085 0.52142292 0.446116
		 0.52150089 0.44176954 0.51626092 0.444051 0.74418896 0.51808202 0.75079298 0.48972297
		 0.54135597 0.55881095 0.54073828 0.56112319 0.51531398 0.61776692 0.525738 0.67571706
		 0.56737298 0.64780098 0.5709371 0.68061095 0.7406615 0.30418682 0.74271905 0.31917825
		 0.50076878 0.45052415 0.48102698 0.45184997 0.5007689 0.42283309 0.48094097 0.42921397
		 0.50076902 0.407132 0.50076842 0.40536711 0.48561099 0.41775799 0.48304999 0.406147
		 0.48846099 0.41048297 0.47267896 0.41855797 0.48095 0.41809201 0.48209196 0.41249099
		 0.48548299 0.41572896 0.47442099 0.42241898 0.48014298 0.42361501 0.48149601 0.41895697
		 0.48366901 0.42216396 0.50076878 0.47112918 0.5007689 0.49739105 0.48380196 0.47018301
		 0.48840299 0.49432498 0.46936196 0.48172399 0.45014796 0.46103498 0.41879898 0.386675
		 0.41000098 0.36468598 0.40738001 0.34365597 0.45025 0.335899 0.45011696 0.33590496
		 0.45511299 0.334804 0.45494297 0.33506697 0.50076866 0.34038401 0.45044297 0.33879799
		 0.45288596 0.33624399 0.44582298 0.33707896 0.44861996 0.336299 0.45046297 0.334553
		 0.44873998 0.33287096 0.50076866 0.33506101 0.48008099 0.33808997 0.46133399 0.335419
		 0.463292 0.334562 0.48047796 0.34470099 0.45729798 0.34017196 0.459225 0.33718696
		 0.473151 0.32593 0.47237498 0.31808296 0.50076866 0.31110397 0.50076854 0.32151696
		 0.42872897 0.33916998 0.44047797 0.338669 0.43142599 0.35367599 0.44390896 0.343916
		 0.43584299 0.374044 0.45355198 0.35561201 0.48063898 0.36100897 0.50077379 0.35774356
		 0.45671996 0.39464793 0.47806296 0.39205799 0.50076902 0.38877499 0.50076866 0.40021807
		 0.44792801 0.39745998 0.44798198 0.42602199 0.43207696 0.40994796 0.435078 0.43561497
		 0.46878296 0.41967896 0.50076902 0.211317 0.40963998 0.254002 0.36522797 0.28421399
		 0.33888397 0.312646 0.30910701 0.340516 0.50078112 0.93937987 0.33684775 0.99771923
		 0.24464697 0.67340297 0.2023 0.66838092 0.23874599 0.81373394 0.23843099 0.75322396
		 0.18443 0.82164401 0.19499399 0.75313902 0.177031 0.41011098 0.21005099 0.55438799
		 0.194267 0.56349397 0.18618698 0.56133503 0.15570399 0.47335798 0.201122 0.40057397
		 0.21962598 0.38469699 0.16050299 0.47203594 0.20080899 0.46046197 0.21745497 0.47231698
		 0.187739 0.55300897 0.19704598 0.55397993 0.180025 0.566836 0.17988101 0.56035399
		 0.18084 0.55129594 0.164223 0.53874898 0.22878398 0.51557302 0.23005998 0.39911401
		 0.21993299 0.40357801 0.16659699 0.46471396 0.16033198 0.51013899 0.18280201 0.54510593
		 0.166723 0.53138399 0.237242 0.48486999 0.23703797 0.49116397 0.21773499 0.42904201
		 0.227926 0.41884097 0.18463199 0.502909 0.18115599 0.51310402 0.17558199 0.49347696
		 0.19225998 0.44436696 0.19251899 0.44700399 0.18133798 0.49234501 0.13355599 0.54446691
		 0.16780898 0.57357502 0.14793599 0.53235996 0.16519 0.55993497 0.167725 0.55482 0.15399499
		 0.53275198 0.15670398 0.52069598 0.18732199 0.49891296 0.19338699 0.447956 0.193955
		 0.44861796 0.184195 0.489254 0.18294498 0.49064201 0.18901598 0.54330993;
	setAttr ".uvst[0].uvsp[500:749]" 0.193406 0.50887299 0.19861799 0.54222894
		 0.21916699 0.44023296 0.225106 0.42545199 0.21351399 0.44197896 0.21602498 0.44394201
		 0.20635299 0.45364496 0.22852498 0.42890197 0.23772298 0.47122297 0.22396098 0.45024696
		 0.221269 0.46515396 0.230814 0.45093697 0.23352498 0.45139396 0.23168798 0.46328497
		 0.22864099 0.45574901 0.22827798 0.46274397 0.230349 0.460475 0.23101899 0.46136397
		 0.229978 0.46009496 0.22915699 0.46115896 0.22897999 0.45887601 0.22950099 0.458958
		 0.21187396 0.54287994 0.19858 0.50870597 0.24057698 0.40281901 0.245837 0.42465496
		 0.21198098 0.43748796 0.21536 0.43466297 0.21593501 0.440557 0.124484 0.48385 0.13620399
		 0.41535899 0.15710101 0.402807 0.14300101 0.46902695 0.29245001 0.39943096 0.25267801
		 0.58638197 0.076524004 0.97602302 0.13833 0.80203402 0.010663 0.84378302 0.13710499
		 0.75707394 0.0094404686 0.73248166 0.14179298 0.67338991 0.0094405012 0.65921301
		 0.149792 0.608854 0.011889309 0.59743392 0.10888399 0.57318503 0.011887 0.53830367
		 0.105018 0.50379997 0.011888926 0.46840221 0.13554201 0.37461501 0.1187 0.40808696
		 0.50076878 0.14027293 0.50076866 0.086514115 0.43828997 0.096202999 0.20308399 0.36550501
		 0.31492698 0.19116698 0.30876201 0.32167199 0.33159998 0.29172099 0.32043701 0.27275196
		 0.35949197 0.26546901 0.35186797 0.24792498 0.40604898 0.23316599 0.45668998 0.20374997
		 0.50076866 0.18253499 0.23023701 0.45889699 0.230468 0.45541599 0.23142199 0.46046898
		 0.23053898 0.45964596 0.184469 0.56849098 0.22062199 0.54518598 0.194189 0.57879597
		 0.19847099 0.56899798 0.244077 0.095896997 0.24616098 0.14415801 0.011164 0.28778699
		 0.011887627 0.22446419 0.153098 0.169825 0.152927 0.22678299 0.101209 0.20513099
		 0.104417 0.26662099 0.50076795 0.047321014 0.50076902 0.016294001 0.32799 0.050243001
		 0.41278899 0.028243 0.42252898 0.064160995 0.32222897 0.11307699 0.29245198 0.43674898
		 0.50076866 0.300035 0.45877799 0.41796497 0.46595293 0.42831001 0.45421794 0.44218796
		 0.454521 0.43158001 0.46629998 0.44406199 0.46708596 0.45734096 0.47161996 0.30687597
		 0.44478098 0.32739696 0.46003997 0.21944898 0.432401 0.59052598 0.46345398 0.57055193
		 0.46147397 0.56662792 0.43271899 0.58878893 0.46423301 0.55064094 0.46900496 0.55967891
		 0.40940899 0.54826891 0.43803498 0.54018992 0.41125798 0.55856299 0.43841898 0.55399996
		 0.40126094 0.56728148 0.39922398 0.57612592 0.40142101 0.57690698 0.43173999 0.58828199
		 0.431559 0.58672702 0.41286099 0.56063098 0.41100097 0.559255 0.43735796 0.55516499
		 0.43747899 0.55753499 0.460904 0.56528699 0.45675299 0.56962103 0.38638496 0.55992103
		 0.39519197 0.58009595 0.43248698 0.59360296 0.46581796 0.57284492 0.47338399 0.56174099
		 0.46739897 0.54183292 0.43754098 0.52628797 0.40619996 0.53676993 0.47891298 0.532444
		 0.485607 0.56663799 0.47334301 0.58778793 0.43478096 0.60700792 0.49247596 0.54076302
		 0.50076902 0.53980339 0.49342093 0.565593 0.50076896 0.56473303 0.33086699 0.70623493
		 0.41821799 0.73453093 0.46665001 0.73518693 0.50077814 0.73684156 0.30438098 0.77241492
		 0.27942699 0.808092 0.50069904 0.83251619 0.50081986 0.91007566 0.38621399 0.588516
		 0.37635395 0.55106795 0.40001997 0.52135497 0.43320596 0.50248402 0.41230097 0.46719998
		 0.38545197 0.40966296 0.392928 0.43305099 0.37993097 0.389624 0.37262401 0.33291897
		 0.37685001 0.37109798 0.387941 0.49488196 0.36621201 0.43201998 0.372554 0.46632996
		 0.33133897 0.51173693 0.36452398 0.40464097 0.32513601 0.43537197 0.31774998 0.37806994
		 0.349325 0.35135099 0.35903999 0.38770297 0.32377601 0.41439694 0.36190194 0.53724098
		 0.398891 0.56701303 0.40007201 0.57599193 0.39854699 0.57788599 0.39294598 0.56391603
		 0.48008198 0.33298999 0.48107502 0.33098501 0.50076914 0.33019707 0.5007689 0.33158004
		 0.27556297 0.24474899 0.22336298 0.21123698 0.22070999 0.26033896 0.14748 0.26719397
		 0.148985 0.31430101 0.10574699 0.30802599 0.105942 0.36076596 0.011887147 0.398211
		 0.01188862 0.32311037 0.33030298 0.46194601 0.29284701 0.46435398 0.26865599 0.47742498
		 0.39431199 0.81176293 0.46098498 0.82867199 0.36597601 0.87608093 0.447395 0.90362889
		 0.33577001 0.90958095 0.441681 0.93383795 0.232805 0.91991103 0.24247599 0.95599693
		 0.44043097 0.146466 0.45106897 0.192176 0.40101197 0.21370099 0.388354 0.161836 0.25377601
		 0.45838797 0.28307199 0.50413895 0.27815697 0.52223098 0.45209298 0.33520997 0.452611
		 0.33523399 0.47424001 0.33188701 0.47380501 0.33058801 0.50076902 0.32669699 0.50076962
		 0.32925004 0.24419999 0.44770101 0.23260099 0.42420298 0.235203 0.41926196 0.256134
		 0.41400996 0.23896301 0.37983301 0.245791 0.36050296 0.26021501 0.41824496 0.21195598
		 0.47965595 0.19085498 0.48814401 0.19526498 0.47412896 0.199664 0.48800501 0.19678098
		 0.49548998 0.220073 0.51615292 0.21097298 0.532646 0.21783198 0.49065697 0.22052598
		 0.49812099 0.20490098 0.49044901 0.21170199 0.49599898 0.20911598 0.51072896 0.20383199
		 0.501634 0.43609598 0.31435299 0.41479799 0.304589 0.46822199 0.27413097 0.46255797
		 0.25154901 0.50076896 0.24459416 0.50076866 0.27271301 0.117729 0.88928795 0.10483199
		 0.94972986 0.069729999 0.81067592 0.034285996 0.83797598 0.056873001 0.71640295 0.020274
		 0.73174995 0.056038 0.64571202 0.019321 0.65889293 0.057207998 0.60385495 0.021838998
		 0.59716696 0.061758999 0.53193599 0.022400998 0.53867102 0.066179998 0.44620797 0.024168998
		 0.47022298 0.067206003 0.38318297 0.025711998 0.39881301 0.065304004 0.32383201 0.027693998
		 0.326334 0.067589 0.220074 0.06953299 0.27645496 0.031184001 0.29168901;
	setAttr ".uvst[0].uvsp[750:784]" 0.029888 0.22222599 0.47335994 0.43099701
		 0.48031199 0.433341 0.48170096 0.42592898 0.48471698 0.430085 0.48003796 0.44176999
		 0.48527801 0.444051 0.48011598 0.446116 0.47171101 0.44550201 0.25342599 0.47561997
		 0.260764 0.50358093 0.250745 0.48972297 0.25734898 0.51808196 0.22924198 0.57659
		 0.228562 0.54845792 0.46137401 0.556831 0.46478501 0.55893701 0.45475101 0.56248999
		 0.46018201 0.55881095 0.46079996 0.56112295 0.457324 0.56339097 0.50079495 0.61276698
		 0.50076878 0.6583423 0.48612899 0.61775595 0.47579998 0.67571694 0.434165 0.64780098
		 0.43060997 0.68061692 0.33723596 0.61170995 0.37153396 0.60821396 0.28191999 0.32736596
		 0.28698 0.34727097 0.19825199 0.31371698 0.26087701 0.304187 0.25881901 0.319179
		 0.18366499 0.34902197;
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcol" yes;
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr -s 469 ".pt";
	setAttr ".pt[3:33]" -type "float3" 0 0.00030238746 -4.6521141e-005  0 0.0026853501 
		-0.00038914976  0 0.0067933151 -0.00094380148  0 0.010889092 -0.0014855436  9.3132257e-010 
		0.021683713 -0.0029301506  0 0.022111945 -0.0029756688  0 0.021829624 -0.0029192113  
		0 0.02034452 -0.002756739  0 0.015520535 -0.0021106168  0 0 0  0 0 0  0 0 0  0 0 
		0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 
		0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0.02135108 -0.0028451327  0 -0.031815168 0.001786723 ;
	setAttr ".pt[41:59]" -type "float3" 0 0.00040256078 -6.193242e-005  -1.1384775e-005 
		0.0011534999 -0.0001755047  -0.0001395127 0.0026319129 -0.0003809288  0 3.290996e-005 
		-5.0630715e-006  -8.26096e-005 0.002517153 -0.00037305488  -0.00053251081 0.0062976778 
		-0.00087734091  -0.00076268945 0.0093453247 -0.001306646  -0.00017711382 0.0029793708 
		-0.00042792063  -0.00018127158 0.0036430773 -0.00052931532  -0.00033806352 0.0048548453 
		-0.00068879029  -0.00055984099 0.0073412601 -0.0010331955  -0.00010469869 0.0025598316 
		-0.00037582332  -0.00016561551 0.0032888283 -0.00047750576  -0.00022017535 0.0039068358 
		-0.00056320621  -0.00039122492 0.0055364948 -0.00078452204  -2.9962834e-005 0.0013507386 
		-0.00020265575  -6.3789099e-005 0.0018127225 -0.00026791592  -8.7537024e-005 0.0021485828 
		-0.00031550444  -0.00020023575 0.0033774525 -0.00048519016 ;
	setAttr ".pt[66:67]" -type "float3" 0 0.00024537632 -3.7750193e-005  0 0.00063618389 
		-9.7874414e-005 ;
	setAttr ".pt[69]" -type "float3" 0 0.00099124142 -0.0001524988 ;
	setAttr ".pt[71]" -type "float3" 0 0.00075066526 -0.00011548703 ;
	setAttr ".pt[73]" -type "float3" 0 -9.0463393e-005 -1.5058326e-005 ;
	setAttr ".pt[75:112]" -type "float3" 0 -0.0015159004 0.00010242571  0 -0.00084062613 
		-6.9274654e-005  0 -0.0017018709 -8.8557252e-005  -4.503584e-006 0.0016253607 -0.00035368802  
		-4.2219428e-005 0.0018910677 -0.00046634435  0 -0.0043385909 0.00015349558  -0.00026729912 
		-0.023797082 0.0015605212  -9.7811513e-005 0.0054479428 -0.00082133268  -0.00020682759 
		0.0068182172 -0.0010184929  0 -0.034923341 0.002110654  0 -0.0041838163 0.00011676511  
		0 -0.0020654807 -9.2200346e-005  -4.503584e-006 -0.001755601 -0.0001219486  -7.343242e-005 
		0.0020343487 -0.00051304279  -6.3789099e-005 0.0018812731 -0.00049263414  0 -0.0037321798 
		7.1828661e-005  -0.00026350765 0.0070735691 -0.001066872  -0.0013680739 0.017192315 
		-0.002409817  -0.0014989797 0.016858479 -0.0023359568  -0.00031573573 0.0071043051 
		-0.0010785244  -0.00033446416 -0.022340512 0.0014501951  -0.00035141956 -0.020854698 
		0.0013467956  0 -0.032838631 0.0018685525  0 -0.034210578 0.0020005489  0 -0.0042689769 
		0.00023796984  -0.00016748399 -0.022442443 0.0014866863  -4.503584e-006 -0.0015545688 
		-0.00013224191  -7.5727017e-005 0.0021070973 -0.00051502587  -0.0003262565 0.007076439 
		-0.0010724287  -0.0010164088 0.015535966 -0.0022154411  -0.0011666046 0.016504603 
		-0.0023386418  -0.0015027458 0.016292298 -0.0022482013  -1.1384775e-005 0.0032738645 
		-0.00050171447  -0.00079030369 0.012187525 -0.0017391574  0 0.0017832101 -0.00027433998  
		0 0.00018804282 -2.8929691e-005  0 -0.033094611 0.0020449373  0 -0.0064259027 0.00043418273 ;
	setAttr ".pt[114:115]" -type "float3" 0 -0.014260541 0.00091320853  0 -0.00041865674 
		2.8287632e-005 ;
	setAttr ".pt[317:318]" -type "float3" 0 0 0  0 0 0 ;
	setAttr ".pt[329:331]" -type "float3" 0 0 0  -3.7542217e-005 -0.016050324 
		0.0010778256  0 -0.027013969 0.0017001089 ;
	setAttr ".pt[404:772]" -type "float3" 0 -0.0035642432 6.5429398e-005  -0.00036763016 
		-0.020196026 0.0012994176  0 0.00037404298 -5.7545076e-005  0 0.00057016127 -8.7717148e-005  
		-6.8612353e-006 0.00072487438 -0.00011033969  -1.8946619e-005 0.00092896353 -0.0001396609  
		0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  2.2100721e-010 0.00040256316 -6.1917439e-005  
		1.1386204e-005 0.0011534904 -0.00017551643  0.00013951198 0.0026318929 -0.00038093489  
		1.0913936e-009 3.2896904e-005 -5.0526473e-006  8.2609011e-005 0.0025171735 -0.0003730554  
		0.00053251104 0.0062976852 -0.00087736076  0.00076269009 0.0093453098 -0.001306667  
		0.00017711404 0.0029793652 -0.00042793909  0.00018127408 0.003643055 -0.00052931893  
		0.00033806369 0.0048548626 -0.00068880484  0.00055984105 0.0073412554 -0.0010332003  
		0.00010469893 0.002559846 -0.0003758402  0.00016561378 0.0032888274 -0.00047748812  
		0.00022017543 0.0039068311 -0.00056319428  0.00039122486 0.0055364929 -0.00078453118  
		2.996299e-005 0.0013507259 -0.00020264224  6.3786807e-005 0.001812719 -0.00026792468  
		8.753757e-005 0.0021485549 -0.00031552077  0.0002002354 0.0033774623 -0.00048519313  
		-5.2386895e-010 2.7794158e-009 -7.8780431e-009  -3.4333425e-009 -1.0275926e-008 1.4588267e-008  
		0 0 0  2.4374458e-009 -4.6493369e-009 -1.0400072e-008  6.344635e-009 1.481385e-008 
		1.4610123e-008  0 0 0  1.0477379e-009 0.00024537352 -3.7764239e-005  3.0267984e-009 
		0.00063620135 -9.7858487e-005  2.6193447e-010 1.3882527e-008 -3.5797711e-009  2.2118911e-009 
		0.00099126156 -0.00015249348  -1.1350494e-009 -7.8289304e-009 6.5083441e-009  -3.608875e-009 
		0.00075065123 -0.00011549502  -6.9849193e-009 -9.1094989e-009 3.9726729e-009  -2.3283064e-009 
		-9.0459645e-005 -1.5058002e-005  0 5.4278644e-009 2.910383e-010  -4.6566129e-010 
		-0.0015159035 0.00010243536  4.6566129e-010 -0.00084064319 -6.9245987e-005  -1.3969839e-009 
		-0.0017018681 -8.8539906e-005  4.4992194e-006 0.0016253544 -0.00035370706  4.2223837e-005 
		0.0018910696 -0.00046634144  2.3283064e-009 -0.0043385844 0.0001534927  0.00026730238 
		-0.023797108 0.0015605181  9.7809359e-005 0.005447967 -0.00082130986  0.00020683184 
		0.0068182098 -0.0010185092  0 -0.0041838316 0.00011676733  -3.4924597e-009 -0.0020654728 
		-9.2200702e-005  4.5020133e-006 -0.0017556082 -0.00012196106  7.3431758e-005 0.0020343426 
		-0.0005130482  6.3793734e-005 0.0018812669 -0.00049262308  -3.4924597e-009 -0.003732177 
		7.1835209e-005  0.00026350818 0.0070735747 -0.0010668776  0.0013680768 0.017192319 
		-0.0024098149  0.0014989795 0.016858483 -0.0023359659  0.00031573325 0.0071043205 
		-0.0010785194  0.00033446401 -0.022340497 0.0014501823  0.00035141665 -0.020854698 
		0.0013468047  -4.6566129e-010 -0.0042689592 0.00023796674  0.00016748463 -0.022442438 
		0.0014866997  4.5031775e-006 -0.0015545709 -0.00013222301  7.5723976e-005 0.0021070966 
		-0.00051502849  0.00032625534 0.0070764562 -0.001072432  0.0010164101 0.015535936 
		-0.0022154492  0.0011666042 0.016504612 -0.0023386483  0.0015027442 0.016292306 -0.0022482041  
		1.1386233e-005 0.0032738652 -0.00050171767  0.00079030218 0.012187524 -0.0017391359  
		2.3283064e-010 0.0017832224 -0.00027434033  -2.9976945e-009 0.00018806057 -2.8946695e-005  
		-2.910383e-010 -0.0064259018 0.0004341926  -9.094947e-010 1.3969839e-009 7.2213879e-009  
		-3.1513991e-009 7.0976967e-009 7.6470315e-009  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  
		0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 
		0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 
		0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  
		0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 
		0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 
		0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  
		0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 
		0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 
		0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  
		0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 
		0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 
		0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  
		0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 
		0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 
		0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  
		0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 
		0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 
		0 0  0 0 0  0 0 0  0 0 0  3.7544873e-005 -0.016050329 0.001077831  0 0 0  0 0 0  
		0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 
		0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 
		0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  
		0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 
		0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 
		0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  0 0 0  
		6.9849193e-010 -0.0035642264 6.5401546e-005  0.00036762911 -0.020196026 0.0012994114  
		5.5297278e-010 0.00037406205 -5.7551322e-005  -1.1277734e-009 0.00057015079 -8.7708977e-005  
		6.8617228e-006 0.00072488229 -0.00011032653  1.8946157e-005 0.00092895736 -0.00013964641 ;
	setAttr -s 773 ".vt";
	setAttr ".vt[0:165]"  0 0.48418969 0.43330681 0 0.46065456 0.44973677 0 0.51601332 0.41233236
		 0 0.43391693 0.4506239 0 0.41721815 0.43753347 0 0.41160488 0.418194 0 0.40454251 0.40325812
		 -9.3132257e-010 0.34947449 0.41062447 0 0.33946627 0.40764534 0 0.33399796 0.39612439
		 0 0.36152941 0.40502417 0 0.38635385 0.39762175 0 0.99108207 0.12990727 0 0.94751775 0.23805511
		 0 0.99771941 0.011805417 0 0.95829749 -0.14435531 0 0.86943752 -0.2611194 0 0.74851131 -0.31603917
		 0 0.63901281 -0.33586687 0 0.57645309 -0.33466029 0 0.48369494 -0.31169376 0 0.38046348 -0.27130353
		 0 0.27420041 -0.25219762 0 0.16429058 -0.25426102 0 0.090681829 -0.26456228 0 0.039238244 -0.27519855
		 0 0.5632686 0.39077061 0 0.58619416 0.38456333 0 0.6463275 0.38922495 0 0.70018804 0.37959611
		 0 0.77645236 0.36317438 0 0.87572432 0.31759435 0 0.33442873 0.38991708 0 0.33476785 0.38666272
		 0.020582266 0.46007133 0.44056913 0.020399602 0.48197719 0.42469376 0.047043189 0.46773344 0.39295277
		 0.022686593 0.51143289 0.40479237 0.019352764 0.55967337 0.3840743 0.048213713 0.49388885 0.38079453
		 0.047241412 0.54231858 0.36059171 0.020603558 0.43636283 0.4423342 0.048923895 0.4350538 0.40640929
		 0.018106062 0.42110342 0.43092713 0.049715161 0.45191944 0.40102765 0.042921364 0.42452469 0.40807047
		 0.0078449175 0.41427916 0.41733804 0.016466176 0.40854537 0.39966047 0.022017369 0.42313632 0.41963649
		 0.035855617 0.42342806 0.40650186 0.013704918 0.41996795 0.41513214 0.019052964 0.41614038 0.40158466
		 0.024728663 0.42963535 0.41502139 0.03405273 0.42815101 0.40476614 0.017738272 0.42758754 0.41285253
		 0.021271145 0.42373934 0.40093997 0.026504518 0.44096863 0.41559738 0.036070224 0.43841457 0.40224904
		 0.018945662 0.43857852 0.4133718 0.022561263 0.43324593 0.39599335 0.066826627 0.44800738 0.36074984
		 0.079962067 0.46493042 0.35846692 0.12020144 0.51365757 0.33638829 0.09415745 0.44494474 0.35185659
		 0.1293928 0.36662096 0.32789397 0.16442995 0.41982067 0.29815164 0.072150916 0.42874324 0.35777393
		 0.10345121 0.35383227 0.3486194 0.11863692 0.39259875 0.33523989 0.092484802 0.37810019 0.35170093
		 0.10702586 0.4196167 0.34354821 0.081907481 0.4052549 0.3552469 0.13712695 0.34254387 0.32138932
		 0.11027122 0.33425754 0.34466228 0.1274949 0.29568589 0.32258937 0.10384193 0.30317843 0.34679523
		 0.09694083 0.33181646 0.34973508 0.088761076 0.33006382 0.3500357 0.08955235 0.34260505 0.35712188
		 0.083563738 0.33483511 0.35871428 0.083950371 0.32527006 0.35309261 0.047748446 0.31707719 0.38528332
		 0.077057429 0.35843641 0.36970359 0.075734571 0.34340459 0.37329382 0 0.31395718 0.40020117
		 0.081003167 0.32886702 0.35125697 0.084762126 0.32977051 0.34836218 0.08159294 0.32984975 0.34499809
		 0.075174287 0.33006683 0.35166818 0.079566419 0.33140457 0.35638145 0.078047782 0.3305167 0.34792149
		 0.072322123 0.33610222 0.37008202 0.038819201 0.3419877 0.39699924 0.03629221 0.33516032 0.38584113
		 0.066641495 0.33196974 0.36274028 0.049196653 0.32956553 0.38263512 0.048439801 0.33542171 0.37620339
		 0 0.33539277 0.3915799 0 0.32874233 0.39784616 0.089789897 0.31519157 0.35270193
		 0.047497794 0.29984057 0.37991562 0.079921082 0.33011377 0.34233755 0.072540008 0.33016858 0.34800428
		 0.062717907 0.33156675 0.35732013 0.037578233 0.3663007 0.3980698 0.040403381 0.35328996 0.40084255
		 0.034721136 0.33428231 0.37895888 0.066843823 0.39852929 0.36624122 0.029603258 0.39051902 0.39222786
		 0.05742966 0.42198303 0.36629283 0.066372 0.43711963 0.36312118 0 0.29653615 0.39334512
		 0.060749553 0.24503805 0.37113458 0.063197091 0.20181125 0.35606435 0 0.24110389 0.39131287
		 0 0.19225377 0.37481087 0.1224483 0.2431957 0.30097356 0.17875065 0.2988503 0.23074201
		 0.055807792 0.18031748 0.29808366 0.11349447 0.21490344 0.26173604 0.17707063 0.26802579 0.20435727
		 0.20381987 0.34440166 0.17928641 0.20842983 0.31944466 0.15684573 0.21954049 0.35627711 0.13287903
		 0.056497507 0.94484264 0.23462793 0.056773543 0.98888254 0.12798724 0.13616696 0.92626655 0.21588725
		 0.22380564 0.8908633 0.093974829 0.13971291 0.96808761 0.11821429 0.22039729 0.37845504 0.13275781
		 0.27185208 0.63594109 0.10320389 0.2393436 0.54038882 0.19889866 0.26728454 0.5577485 0.10682441
		 0.23529139 0.5015099 0.18368182 0.2600264 0.52844846 0.10554002 0.24333844 0.47762355 0.098475903
		 0.24946219 0.50160342 0.10286149 0.22019576 0.4146682 0.14168379 0.23867187 0.44111457 0.090879366
		 0.23269229 0.39568907 0.089630209 0.22477958 0.35847661 0.097911537 0.21629101 0.87317193 0.16093643
		 0.058161952 0.9967283 0.010530041 0.23291509 0.89475733 0.0056669256 0.14360213 0.97146565 0.0073723267
		 0.15477735 0.92832911 -0.12964219 0.062209237 0.95433861 -0.14221494 0.24077788 0.87120926 -0.10265216
		 0.27043661 0.73186022 0.092674069 0.25851923 0.81342995 0.087648749 0.28501207 0.72393841 0.0068128649
		 0.27358535 0.81282133 0.0052958522 0.27840832 0.62437344 0.035686102 0.26361743 0.81165826 -0.11304435
		 0.23518488 0.50376016 -0.17284188 0.26230112 0.51433992 -0.10020955 0.24774612 0.46759751 -0.082817107
		 0.25048447 0.45681128 -0.010981733 0.26098561 0.48090091 -0.035452187 0.27446178 0.518601 -0.058286875
		 0.2739498 0.47122374 -0.0035162633 0.26190054 0.4690915 0.029848222 0.28007442 0.48864084 -0.024174515
		 0.29580486 0.52063644 -0.046424337 0.28956965 0.63640636 0.0054703206 0.29542801 0.63899064 -0.024340812;
	setAttr ".vt[166:331]" 0.30099806 0.63163489 -0.054780208 0.30480701 0.62504506 0.012467263
		 0.31467247 0.63273507 -0.015156815 0.32027042 0.62699455 -0.042630158 0.24016675 0.43208209 -0.032825269
		 0.25131586 0.47135067 -0.054970212 0.26471347 0.51888925 -0.066387162 0.2756184 0.56545728 -0.10367441
		 0.27651617 0.55764693 -0.07374125 0.27830675 0.60129714 -0.072302863 0.28136778 0.58667654 -0.065909609
		 0.29711211 0.58363193 -0.072370842 0.28106391 0.61897862 -0.024210563 0.28417903 0.61240768 -0.049681164
		 0.28746864 0.609164 -0.056850947 0.30156493 0.61408681 -0.065482035 0.2887702 0.55310643 -0.073193237
		 0.31972072 0.60918111 -0.052001741 0.29065505 0.52644813 -0.038206909 0.25591201 0.50144774 0.045183003
		 0.27635065 0.49908859 -0.016639426 0.27029166 0.48959583 -0.001918152 0.26544079 0.49208266 0.019046456
		 0.26606005 0.50756252 0.031706817 0.2614525 0.51890969 0.047343031 0.27108207 0.51632541 -0.022573957
		 0.27186352 0.51451683 -0.016705746 0.26392463 0.51381487 -0.019313034 0.26455289 0.51005256 -0.014109962
		 0.26704058 0.51069808 -0.019276999 0.26721829 0.51322907 -0.023873052 0.26019022 0.51233226 0.00010426063
		 0.21716091 0.51427925 -0.016917096 0.21593471 0.53645545 -0.013649615 0.22364593 0.52259248 0.013598477
		 0.22246803 0.50531566 0.0031079734 0.22539146 0.540555 0.0086427834 0.26581842 0.53902173 0.046628751
		 0.30964962 0.55169928 -0.058145963 0.30074167 0.55327201 -0.049972773 0.27751464 0.52901369 -0.030137736
		 0.283373 0.53087634 -0.028154636 0.29247427 0.54960072 -0.037787519 0.28512105 0.54706371 -0.038555805
		 0.273895 0.54213268 -0.029657727 0.26681525 0.5260362 -0.025810309 0.27145067 0.52602136 -0.031135419
		 0.27920291 0.54426575 -0.037074886 0.26323491 0.53043401 -0.0063848197 0.27358124 0.53705007 0.028489299
		 0.27111155 0.54436225 -0.0099357162 0.2681177 0.55930471 0.045344368 0.27624178 0.55687928 0.026813373
		 0.31656468 0.57997292 -0.057871565 0.28902662 0.5638231 -0.038397755 0.29607674 0.56684566 -0.037341092
		 0.27533501 0.5566262 -0.031036312 0.30777794 0.60028791 -0.04365816 0.30883217 0.61074317 -0.036091927
		 0.29321477 0.60400027 0.02892179 0.28072149 0.61930627 0.014832074 0.25506416 0.51347899 0.067172341
		 0.26164088 0.53668469 0.066992946 0.27845013 0.57586581 0.015285874 0.28944355 0.5971778 0.015065521
		 0.27428567 0.61588073 -0.031546619 0.30504292 0.57671845 -0.04986465 0.29702285 0.58055687 -0.033124246
		 0.28947794 0.57832652 -0.027478857 0.28093037 0.57040465 -0.030498153 0.28100908 0.58034807 -0.004922688
		 0.29471296 0.60516751 0.0028802687 0.28056997 0.57528746 0.031699434 0.27288741 0.60778612 0.018743381
		 0.27257454 0.50774354 -0.003066564 0.26889014 0.5087887 0.010109834 0.27680531 0.51202172 -0.012042509
		 0.30052626 0.55128968 -0.039182484 0.28997758 0.53017426 -0.028319282 0.30339158 0.57030231 -0.038962133
		 0.30349231 0.61444896 -0.018815007 0.27399158 0.56516153 0.0064115147 0.26815128 0.5113157 -0.0084506609
		 0.26426372 0.51213568 0.0051762625 0.25878868 0.51771629 -0.0049579237 0.27368194 0.5678457 -0.015884183
		 0.27885807 0.57483625 -0.014375368 0.28141531 0.55998147 -0.037922677 0.27186522 0.55712676 -0.014332777
		 0.2713286 0.52082574 0.032344904 0.25379941 0.51583064 0.012241188 0.25040087 0.52019912 0.0054957168
		 0.25828415 0.52016968 0.018449329 0.25719801 0.52860564 0.017305821 0.25216198 0.52763748 0.0081848949
		 0.24006361 0.50939238 0.016449837 0.23368835 0.51694787 0.00079725124 0.24448273 0.51994997 0.027113996
		 0.24351783 0.53374904 0.020022031 0.23363759 0.53201985 0.0027860608 0.27341986 0.57175303 -0.00094257388
		 0.23313788 0.41244537 0.037543051 0.24455482 0.45385501 0.038541563 0.24993318 0.49269536 0.064690396
		 0.2765514 0.56653845 -0.029607762 0.26878369 0.56020004 0.066481821 0.06448555 0.86574411 -0.25714105
		 0.15778603 0.84108347 -0.22285096 0.27261549 0.72181118 -0.12103901 0.15546457 0.73260063 -0.26984641
		 0.061877497 0.74496531 -0.31051257 0.060679104 0.63699448 -0.33304825 0.26769257 0.63446331 -0.116918
		 0.15256087 0.63185364 -0.29443729 0.058992542 0.57548743 -0.33121997 0.24848168 0.57575762 -0.18556124
		 0.14922456 0.57429713 -0.29622874 0.14314671 0.48907983 -0.27463341 0.056045339 0.48408476 -0.30808961
		 0.05626896 0.38183719 -0.2691125 0.22001313 0.41844952 -0.13007307 0.13755046 0.3893894 -0.23836678
		 0.22456005 0.3692866 0.0456753 0.23127601 0.38497448 -0.024072135 0.21969935 0.28579491 -0.0031755064
		 0.19869874 0.27541909 0.066971652 0.11985824 0.076076671 0.10882142 0.18338032 0.092378229 0.065521806
		 0.20235611 0.17641032 0.02891688 0.22645223 0.18100558 -0.042019069 0.14926797 0.1779152 0.106079
		 0.21952248 0.3811999 -0.096537396 0.19901322 0.2876929 -0.15108521 0.1455328 0.28264639 -0.21850142
		 0.06047925 0.27573711 -0.25043243 0.21673907 0.28743154 -0.074161448 0.06987457 0.16744092 -0.25120074
		 0.081902564 0.094569057 -0.26614973 0.24679181 0.10567083 -0.0037988629 0.30295417 0.11862674 -0.11619307
		 0.23598763 0.18264222 -0.13493294 0.17302826 0.17753106 -0.21742508 0.21894738 0.1109511 -0.22738479
		 0.047320869 0.16530633 0.23384155 0.0454631 0.11421406 0.18723187 0.099430136 0.19419979 0.21042451
		 0 0.15426879 0.24277157 0.087556966 0.13724728 0.15460871 0 0.10617031 0.20844716
		 0.043611888 0.059872031 0.1661616 0.052356016 0.03947334 0.15056388 0 0.047138646 0.18522257
		 0 0.019075587 0.17367786 0.16471164 0.24447702 0.16903591 0.20727906 0.30020744 0.12632851
		 0 0.17014074 0.30918112 0.10475361 0.022719175 0.12050702 0.1674566 0.031400889 0.092223547
		 0.091175035 0.043747634 -0.28234139 0.26541376 0.050317049 0.026956715 0.36659908 0.073796242 -0.10313217
		 0.2602869 0.064230576 -0.23886889 0.048352964 0.0082486123 0.14661901 0 0.001017347 0.16480842
		 0.051246103 0.27519071 0.37217408 0 0.274333 0.38580182;
	setAttr ".vt[332:497]" 0.068479612 0.58760786 0.32846984 0.05695048 0.57318157 0.32678568
		 0.067955375 0.56277454 0.32821426 0.11284734 0.55276489 0.32872784 0.15662533 0.56174499 0.31042457
		 0.17389981 0.57695937 0.28878993 0.17037922 0.59271842 0.30278379 0.12331248 0.60667473 0.32894325
		 0.065095 0.59355474 0.34366539 0.047705859 0.57518095 0.33617526 0.064170197 0.55377561 0.33563226
		 0.11451428 0.5375644 0.33019656 0.16078074 0.54905677 0.30656323 0.18132105 0.57163346 0.28221646
		 0.17767349 0.59468269 0.30510604 0.12728684 0.60905582 0.34060919 0.063673824 0.57211095 0.32262045
		 0.071985453 0.58227873 0.32542679 0.071801148 0.57049406 0.32401711 0.11218797 0.56617564 0.32716578
		 0.15337752 0.5722248 0.31133136 0.16782601 0.58105087 0.29338518 0.1649206 0.589378 0.30444908
		 0.12259491 0.59966052 0.32816842 0.072133712 0.58020723 0.32054892 0.072154202 0.57137859 0.31913257
		 0.11038918 0.56785566 0.31866658 0.15211116 0.57429713 0.30845049 0.16653222 0.58204073 0.29353672
		 0.16218472 0.58894384 0.30256999 0.12091489 0.59834003 0.32143521 0.069417514 0.57430124 0.3185674
		 0.10917442 0.56611168 0.31437027 0.078915223 0.57220519 0.31560636 0.15005597 0.57293165 0.30411977
		 0.16121161 0.58953291 0.30038378 0.11967554 0.6001085 0.31751734 0.079418175 0.58636129 0.31848225
		 0.07474263 0.57759666 0.31616253 0.035068437 0.58000726 0.35834074 0.01822729 0.58493191 0.37894166
		 0.038948622 0.64398408 0.3868413 0.058144741 0.6084137 0.36902374 0.13389963 0.63233197 0.36364213
		 0.13604407 0.67422515 0.3632915 0.19467275 0.60929823 0.30910006 0.21056946 0.63320351 0.28677654
		 0.055050109 0.77595919 0.35679093 0.054534063 0.70209742 0.37605017 0.13659126 0.70713747 0.34817135
		 0.13786581 0.77304071 0.32515484 0.22835176 0.75055498 0.21698651 0.1378265 0.86049187 0.28130725
		 0.057161804 0.87154025 0.31200546 0.22308072 0.82957578 0.19154701 0.19561549 0.56253219 0.2737934
		 0.21425714 0.54739141 0.25838819 0.18734726 0.50403202 0.29440907 0.17079288 0.53117114 0.30166733
		 0.14026095 0.47586733 0.33183721 0.15435226 0.44753715 0.31754851 0.17117949 0.39423275 0.28094438
		 0.17498761 0.37061751 0.26700372 0.17804368 0.33446068 0.2490281 0.19833177 0.45103234 0.2555311
		 0.19610865 0.4787342 0.27801928 0.19836369 0.42756853 0.23495802 0.1973955 0.40664405 0.21787114
		 0.19880684 0.3754988 0.19723581 0.22921267 0.47237551 0.16866814 0.22379664 0.44869462 0.15623794
		 0.23541012 0.64598686 0.21739116 0.076116286 0.33064371 0.34534782 0.046050407 0.33474493 0.37114286
		 0.027637366 0.45247248 0.41043445 0.03639378 0.45034593 0.39555919 0.019694339 0.45158035 0.40817535
		 0.026133461 0.44868398 0.38929129 -0.020582266 0.46007133 0.44056913 -0.020399602 0.48197719 0.42469376
		 -0.047043189 0.46773344 0.39295277 -0.022686593 0.51143289 0.40479237 -0.019352764 0.55967337 0.3840743
		 -0.048213713 0.49388885 0.38079453 -0.047241412 0.54231858 0.36059171 -0.020603558 0.43636283 0.4423342
		 -0.048923895 0.4350538 0.40640929 -0.018106062 0.42110342 0.43092713 -0.049715161 0.45191944 0.40102765
		 -0.042921364 0.42452469 0.40807047 -0.0078449175 0.41427916 0.41733804 -0.016466176 0.40854537 0.39966047
		 -0.022017369 0.42313632 0.41963649 -0.035855617 0.42342806 0.40650186 -0.013704918 0.41996795 0.41513214
		 -0.019052964 0.41614038 0.40158466 -0.024728663 0.42963535 0.41502139 -0.03405273 0.42815101 0.40476614
		 -0.017738272 0.42758754 0.41285253 -0.021271145 0.42373934 0.40093997 -0.026504518 0.44096863 0.41559738
		 -0.036070224 0.43841457 0.40224904 -0.018945662 0.43857852 0.4133718 -0.022561263 0.43324593 0.39599335
		 -0.066826627 0.44800738 0.36074984 -0.079962067 0.46493042 0.35846692 -0.12020144 0.51365757 0.33638829
		 -0.09415745 0.44494474 0.35185659 -0.1293928 0.36662096 0.32789397 -0.16442995 0.41982067 0.29815164
		 -0.072150916 0.42874324 0.35777393 -0.10345121 0.35383227 0.3486194 -0.11863692 0.39259875 0.33523989
		 -0.092484802 0.37810019 0.35170093 -0.10702586 0.4196167 0.34354821 -0.081907481 0.4052549 0.3552469
		 -0.13712695 0.34254387 0.32138932 -0.11027122 0.33425754 0.34466228 -0.1274949 0.29568589 0.32258937
		 -0.10384193 0.30317843 0.34679523 -0.09694083 0.33181646 0.34973508 -0.088761076 0.33006382 0.3500357
		 -0.08955235 0.34260505 0.35712188 -0.083563738 0.33483511 0.35871428 -0.083950371 0.32527006 0.35309261
		 -0.047748446 0.31707719 0.38528332 -0.077057429 0.35843641 0.36970359 -0.075734571 0.34340459 0.37329382
		 -0.081003167 0.32886702 0.35125697 -0.084762126 0.32977051 0.34836218 -0.08159294 0.32984975 0.34499809
		 -0.075174287 0.33006683 0.35166818 -0.079566419 0.33140457 0.35638145 -0.078047782 0.3305167 0.34792149
		 -0.072322123 0.33610222 0.37008202 -0.038819201 0.3419877 0.39699924 -0.03629221 0.33516032 0.38584113
		 -0.066641495 0.33196974 0.36274028 -0.049196653 0.32956553 0.38263512 -0.048439801 0.33542171 0.37620339
		 -0.089789897 0.31519157 0.35270193 -0.047497794 0.29984057 0.37991562 -0.079921082 0.33011377 0.34233755
		 -0.072540008 0.33016858 0.34800428 -0.062717907 0.33156675 0.35732013 -0.037578233 0.3663007 0.3980698
		 -0.040403381 0.35328996 0.40084255 -0.034721136 0.33428231 0.37895888 -0.066843823 0.39852929 0.36624122
		 -0.029603258 0.39051902 0.39222786 -0.05742966 0.42198303 0.36629283 -0.066372 0.43711963 0.36312118
		 -0.060749553 0.24503805 0.37113458 -0.063197091 0.20181125 0.35606435 -0.1224483 0.2431957 0.30097356
		 -0.17875065 0.2988503 0.23074201 -0.055807792 0.18031748 0.29808366 -0.11349447 0.21490344 0.26173604
		 -0.17707063 0.26802579 0.20435727 -0.20381987 0.34440166 0.17928641 -0.20842983 0.31944466 0.15684573
		 -0.21954049 0.35627711 0.13287903 -0.056497507 0.94484264 0.23462793 -0.056773543 0.98888254 0.12798724
		 -0.13616696 0.92626655 0.21588725 -0.22380564 0.8908633 0.093974829;
	setAttr ".vt[498:663]" -0.13971291 0.96808761 0.11821429 -0.22039729 0.37845504 0.13275781
		 -0.27185208 0.63594109 0.10320389 -0.2393436 0.54038882 0.19889866 -0.26728454 0.5577485 0.10682441
		 -0.23529139 0.5015099 0.18368182 -0.2600264 0.52844846 0.10554002 -0.24333844 0.47762355 0.098475903
		 -0.24946219 0.50160342 0.10286149 -0.22019576 0.4146682 0.14168379 -0.23867187 0.44111457 0.090879366
		 -0.23269229 0.39568907 0.089630209 -0.22477958 0.35847661 0.097911537 -0.21629101 0.87317193 0.16093643
		 -0.058161952 0.9967283 0.010530041 -0.23291509 0.89475733 0.0056669256 -0.14360213 0.97146565 0.0073723267
		 -0.15477735 0.92832911 -0.12964219 -0.062209237 0.95433861 -0.14221494 -0.24077788 0.87120926 -0.10265216
		 -0.27043661 0.73186022 0.092674069 -0.25851923 0.81342995 0.087648749 -0.28501207 0.72393841 0.0068128649
		 -0.27358535 0.81282133 0.0052958522 -0.27840832 0.62437344 0.035686102 -0.26361743 0.81165826 -0.11304435
		 -0.23518488 0.50376016 -0.17284188 -0.26230112 0.51433992 -0.10020955 -0.24774612 0.46759751 -0.082817107
		 -0.25048447 0.45681128 -0.010981733 -0.26098561 0.48090091 -0.035452187 -0.27446178 0.518601 -0.058286875
		 -0.2739498 0.47122374 -0.0035162633 -0.26190054 0.4690915 0.029848222 -0.28007442 0.48864084 -0.024174515
		 -0.29580486 0.52063644 -0.046424337 -0.28956965 0.63640636 0.0054703206 -0.29542801 0.63899064 -0.024340812
		 -0.30099806 0.63163489 -0.054780208 -0.30480701 0.62504506 0.012467263 -0.31467247 0.63273507 -0.015156815
		 -0.32027042 0.62699455 -0.042630158 -0.24016675 0.43208209 -0.032825269 -0.25131586 0.47135067 -0.054970212
		 -0.26471347 0.51888925 -0.066387162 -0.2756184 0.56545728 -0.10367441 -0.27651617 0.55764693 -0.07374125
		 -0.27830675 0.60129714 -0.072302863 -0.28136778 0.58667654 -0.065909609 -0.29711211 0.58363193 -0.072370842
		 -0.28106391 0.61897862 -0.024210563 -0.28417903 0.61240768 -0.049681164 -0.28746864 0.609164 -0.056850947
		 -0.30156493 0.61408681 -0.065482035 -0.2887702 0.55310643 -0.073193237 -0.31972072 0.60918111 -0.052001741
		 -0.29065505 0.52644813 -0.038206909 -0.25591201 0.50144774 0.045183003 -0.27635065 0.49908859 -0.016639426
		 -0.27029166 0.48959583 -0.001918152 -0.26544079 0.49208266 0.019046456 -0.26606005 0.50756252 0.031706817
		 -0.2614525 0.51890969 0.047343031 -0.27108207 0.51632541 -0.022573957 -0.27186352 0.51451683 -0.016705746
		 -0.26392463 0.51381487 -0.019313034 -0.26455289 0.51005256 -0.014109962 -0.26704058 0.51069808 -0.019276999
		 -0.26721829 0.51322907 -0.023873052 -0.26019022 0.51233226 0.00010426063 -0.21716091 0.51427925 -0.016917096
		 -0.21593471 0.53645545 -0.013649615 -0.22364593 0.52259248 0.013598477 -0.22246803 0.50531566 0.0031079734
		 -0.22539146 0.540555 0.0086427834 -0.26581842 0.53902173 0.046628751 -0.30964962 0.55169928 -0.058145963
		 -0.30074167 0.55327201 -0.049972773 -0.27751464 0.52901369 -0.030137736 -0.283373 0.53087634 -0.028154636
		 -0.29247427 0.54960072 -0.037787519 -0.28512105 0.54706371 -0.038555805 -0.273895 0.54213268 -0.029657727
		 -0.26681525 0.5260362 -0.025810309 -0.27145067 0.52602136 -0.031135419 -0.27920291 0.54426575 -0.037074886
		 -0.26323491 0.53043401 -0.0063848197 -0.27358124 0.53705007 0.028489299 -0.27111155 0.54436225 -0.0099357162
		 -0.2681177 0.55930471 0.045344368 -0.27624178 0.55687928 0.026813373 -0.31656468 0.57997292 -0.057871565
		 -0.28902662 0.5638231 -0.038397755 -0.29607674 0.56684566 -0.037341092 -0.27533501 0.5566262 -0.031036312
		 -0.30777794 0.60028791 -0.04365816 -0.30883217 0.61074317 -0.036091927 -0.29321477 0.60400027 0.02892179
		 -0.28072149 0.61930627 0.014832074 -0.25506416 0.51347899 0.067172341 -0.26164088 0.53668469 0.066992946
		 -0.27845013 0.57586581 0.015285874 -0.28944355 0.5971778 0.015065521 -0.27428567 0.61588073 -0.031546619
		 -0.30504292 0.57671845 -0.04986465 -0.29702285 0.58055687 -0.033124246 -0.28947794 0.57832652 -0.027478857
		 -0.28093037 0.57040465 -0.030498153 -0.28100908 0.58034807 -0.004922688 -0.29471296 0.60516751 0.0028802687
		 -0.28056997 0.57528746 0.031699434 -0.27288741 0.60778612 0.018743381 -0.27257454 0.50774354 -0.003066564
		 -0.26889014 0.5087887 0.010109834 -0.27680531 0.51202172 -0.012042509 -0.30052626 0.55128968 -0.039182484
		 -0.28997758 0.53017426 -0.028319282 -0.30339158 0.57030231 -0.038962133 -0.30349231 0.61444896 -0.018815007
		 -0.27399158 0.56516153 0.0064115147 -0.26815128 0.5113157 -0.0084506609 -0.26426372 0.51213568 0.0051762625
		 -0.25878868 0.51771629 -0.0049579237 -0.27368194 0.5678457 -0.015884183 -0.27885807 0.57483625 -0.014375368
		 -0.28141531 0.55998147 -0.037922677 -0.27186522 0.55712676 -0.014332777 -0.2713286 0.52082574 0.032344904
		 -0.25379941 0.51583064 0.012241188 -0.25040087 0.52019912 0.0054957168 -0.25828415 0.52016968 0.018449329
		 -0.25719801 0.52860564 0.017305821 -0.25216198 0.52763748 0.0081848949 -0.24006361 0.50939238 0.016449837
		 -0.23368835 0.51694787 0.00079725124 -0.24448273 0.51994997 0.027113996 -0.24351783 0.53374904 0.020022031
		 -0.23363759 0.53201985 0.0027860608 -0.27341986 0.57175303 -0.00094257388 -0.23313788 0.41244537 0.037543051
		 -0.24455482 0.45385501 0.038541563 -0.24993318 0.49269536 0.064690396 -0.2765514 0.56653845 -0.029607762
		 -0.26878369 0.56020004 0.066481821 -0.06448555 0.86574411 -0.25714105 -0.15778603 0.84108347 -0.22285096
		 -0.27261549 0.72181118 -0.12103901 -0.15546457 0.73260063 -0.26984641 -0.061877497 0.74496531 -0.31051257
		 -0.060679104 0.63699448 -0.33304825 -0.26769257 0.63446331 -0.116918 -0.15256087 0.63185364 -0.29443729
		 -0.058992542 0.57548743 -0.33121997 -0.24848168 0.57575762 -0.18556124 -0.14922456 0.57429713 -0.29622874
		 -0.14314671 0.48907983 -0.27463341 -0.056045339 0.48408476 -0.30808961 -0.05626896 0.38183719 -0.2691125
		 -0.22001313 0.41844952 -0.13007307 -0.13755046 0.3893894 -0.23836678 -0.22456005 0.3692866 0.0456753
		 -0.23127601 0.38497448 -0.024072135 -0.21969935 0.28579491 -0.0031755064 -0.19869874 0.27541909 0.066971652
		 -0.11985824 0.076076671 0.10882142 -0.18338032 0.092378229 0.065521806;
	setAttr ".vt[664:772]" -0.20235611 0.17641032 0.02891688 -0.22645223 0.18100558 -0.042019069
		 -0.14926797 0.1779152 0.106079 -0.21952248 0.3811999 -0.096537396 -0.19901322 0.2876929 -0.15108521
		 -0.1455328 0.28264639 -0.21850142 -0.06047925 0.27573711 -0.25043243 -0.21673907 0.28743154 -0.074161448
		 -0.06987457 0.16744092 -0.25120074 -0.081902564 0.094569057 -0.26614973 -0.24679181 0.10567083 -0.0037988629
		 -0.30295417 0.11862674 -0.11619307 -0.23598763 0.18264222 -0.13493294 -0.17302826 0.17753106 -0.21742508
		 -0.21894738 0.1109511 -0.22738479 -0.047320869 0.16530633 0.23384155 -0.0454631 0.11421406 0.18723187
		 -0.099430136 0.19419979 0.21042451 -0.087556966 0.13724728 0.15460871 -0.043611888 0.059872031 0.1661616
		 -0.052356016 0.03947334 0.15056388 -0.16471164 0.24447702 0.16903591 -0.20727906 0.30020744 0.12632851
		 -0.10475361 0.022719175 0.12050702 -0.1674566 0.031400889 0.092223547 -0.091175035 0.043747634 -0.28234139
		 -0.26541376 0.050317049 0.026956715 -0.36659908 0.073796242 -0.10313217 -0.2602869 0.064230576 -0.23886889
		 -0.048352964 0.0082486123 0.14661901 -0.051246103 0.27519071 0.37217408 -0.068479612 0.58760786 0.32846984
		 -0.05695048 0.57318157 0.32678568 -0.067955375 0.56277454 0.32821426 -0.11284734 0.55276489 0.32872784
		 -0.15662533 0.56174499 0.31042457 -0.17389981 0.57695937 0.28878993 -0.17037922 0.59271842 0.30278379
		 -0.12331248 0.60667473 0.32894325 -0.065095 0.59355474 0.34366539 -0.047705859 0.57518095 0.33617526
		 -0.064170197 0.55377561 0.33563226 -0.11451428 0.5375644 0.33019656 -0.16078074 0.54905677 0.30656323
		 -0.18132105 0.57163346 0.28221646 -0.17767349 0.59468269 0.30510604 -0.12728684 0.60905582 0.34060919
		 -0.063673824 0.57211095 0.32262045 -0.071985453 0.58227873 0.32542679 -0.071801148 0.57049406 0.32401711
		 -0.11218797 0.56617564 0.32716578 -0.15337752 0.5722248 0.31133136 -0.16782601 0.58105087 0.29338518
		 -0.1649206 0.589378 0.30444908 -0.12259491 0.59966052 0.32816842 -0.072133712 0.58020723 0.32054892
		 -0.072154202 0.57137859 0.31913257 -0.11038918 0.56785566 0.31866658 -0.15211116 0.57429713 0.30845049
		 -0.16653222 0.58204073 0.29353672 -0.16218472 0.58894384 0.30256999 -0.12091489 0.59834003 0.32143521
		 -0.069417514 0.57430124 0.3185674 -0.10917442 0.56611168 0.31437027 -0.078915223 0.57220519 0.31560636
		 -0.15005597 0.57293165 0.30411977 -0.16121161 0.58953291 0.30038378 -0.11967554 0.6001085 0.31751734
		 -0.079418175 0.58636129 0.31848225 -0.07474263 0.57759666 0.31616253 -0.035068437 0.58000726 0.35834074
		 -0.01822729 0.58493191 0.37894166 -0.038948622 0.64398408 0.3868413 -0.058144741 0.6084137 0.36902374
		 -0.13389963 0.63233197 0.36364213 -0.13604407 0.67422515 0.3632915 -0.19467275 0.60929823 0.30910006
		 -0.21056946 0.63320351 0.28677654 -0.055050109 0.77595919 0.35679093 -0.054534063 0.70209742 0.37605017
		 -0.13659126 0.70713747 0.34817135 -0.13786581 0.77304071 0.32515484 -0.22835176 0.75055498 0.21698651
		 -0.1378265 0.86049187 0.28130725 -0.057161804 0.87154025 0.31200546 -0.22308072 0.82957578 0.19154701
		 -0.19561549 0.56253219 0.2737934 -0.21425714 0.54739141 0.25838819 -0.18734726 0.50403202 0.29440907
		 -0.17079288 0.53117114 0.30166733 -0.14026095 0.47586733 0.33183721 -0.15435226 0.44753715 0.31754851
		 -0.17117949 0.39423275 0.28094438 -0.17498761 0.37061751 0.26700372 -0.17804368 0.33446068 0.2490281
		 -0.19833177 0.45103234 0.2555311 -0.19610865 0.4787342 0.27801928 -0.19836369 0.42756853 0.23495802
		 -0.1973955 0.40664405 0.21787114 -0.19880684 0.3754988 0.19723581 -0.22921267 0.47237551 0.16866814
		 -0.22379664 0.44869462 0.15623794 -0.23541012 0.64598686 0.21739116 -0.076116286 0.33064371 0.34534782
		 -0.046050407 0.33474493 0.37114286 -0.027637366 0.45247248 0.41043445 -0.03639378 0.45034593 0.39555919
		 -0.019694339 0.45158035 0.40817535 -0.026133461 0.44868398 0.38929129;
	setAttr -s 1531 ".ed";
	setAttr ".ed[0:165]"  0 1 1 26 2 1 1 3 1 3 4 1 4 5 1 5 6 1 2 0 1 10 7 1 84 111 1
		 97 98 1 7 8 1 8 9 1 98 84 1 11 10 1 6 11 1 114 115 1 12 13 1 14 12 1 15 14 1 16 15 1
		 17 16 1 18 17 1 19 18 1 20 19 1 21 20 1 22 21 1 24 23 1 312 314 1 314 317 1 317 318 1
		 321 312 1 25 24 1 318 329 1 111 331 1 27 26 1 28 27 1 30 29 1 31 30 1 13 31 1 9 32 1
		 23 22 1 33 97 1 331 114 1 29 28 1 0 35 1 35 34 1 34 1 1 35 36 1 36 44 1 44 34 1 26 38 1
		 38 37 1 37 2 1 38 40 1 40 39 1 39 37 1 34 41 1 41 3 1 42 41 1 44 42 1 43 4 1 41 43 1
		 45 43 1 42 45 1 43 46 1 46 5 1 46 47 1 47 6 1 45 49 1 49 48 1 48 43 1 45 47 1 47 51 1
		 51 49 1 50 46 1 48 50 1 50 51 1 49 53 1 53 52 1 52 48 1 51 55 1 55 53 1 52 54 1 54 50 1
		 54 55 1 53 57 1 57 56 1 56 52 1 55 59 1 59 57 1 56 58 1 58 54 1 58 59 1 37 35 1 39 36 1
		 61 60 1 60 36 1 39 61 1 62 61 1 40 62 1 62 391 1 391 63 1 63 61 1 393 64 1 64 68 1
		 68 65 1 65 393 1 63 66 1 66 60 1 64 67 1 67 69 1 69 68 1 70 392 1 392 65 1 68 70 1
		 71 70 1 69 71 1 393 394 1 394 72 1 72 64 1 72 73 1 73 67 1 394 395 1 395 74 1 74 72 1
		 74 75 1 75 73 1 99 80 1 80 77 1 77 76 1 76 99 1 78 76 1 77 79 1 79 78 1 100 81 1
		 81 80 1 99 100 1 10 104 1 104 105 1 105 7 1 79 83 1 83 82 1 82 78 1 100 111 1 84 81 1
		 85 90 1 90 87 1 87 86 1 86 85 1 87 88 1 88 89 1 89 86 1 85 95 1 95 96 1 96 90 1 92 91 1
		 91 94 1 94 93 1 93 92 1 88 94 1 91 89 1 95 98 1 97 96 1 90 404 1 404 101 0 101 87 1;
	setAttr ".ed[166:331]" 101 102 0 102 88 1 102 103 0 103 94 1 105 92 1 92 8 1
		 79 89 1 91 83 1 77 86 1 80 85 1 81 95 1 93 9 1 104 82 1 83 105 1 103 106 0 106 93 1
		 75 99 1 76 73 1 78 67 1 82 69 1 107 71 1 82 107 1 108 107 1 104 108 1 11 108 1 107 109 1
		 109 66 1 66 71 1 108 47 1 47 109 1 63 70 1 391 392 1 42 109 1 110 60 1 109 110 1
		 112 113 1 113 115 1 114 112 1 395 117 1 117 116 1 116 74 1 118 113 1 113 116 1 116 119 1
		 119 118 1 321 115 1 118 321 1 117 120 1 120 119 1 395 400 1 400 121 1 121 117 1 121 122 1
		 122 120 1 400 137 1 137 129 1 129 121 1 123 122 1 129 123 1 12 125 1 125 124 1 124 13 1
		 126 128 1 128 127 1 127 141 1 141 126 1 403 130 1 130 132 1 132 131 1 131 403 1 132 134 1
		 134 133 1 133 131 1 135 402 1 402 401 1 401 136 1 136 135 1 135 138 1 138 137 1 137 402 1
		 138 139 1 139 129 1 140 123 1 139 140 1 14 142 1 142 125 1 144 143 1 143 127 1 128 144 1
		 15 146 1 146 142 1 144 145 1 145 147 1 147 143 1 403 383 1 383 148 1 148 130 1 149 148 1
		 383 386 1 386 149 1 151 150 1 150 148 1 149 151 1 150 152 1 152 130 1 153 274 1 274 150 1
		 151 153 1 274 278 1 278 152 1 127 149 1 386 141 1 143 151 1 147 153 1 155 154 1 154 286 1
		 286 156 1 156 155 1 161 160 1 160 157 1 157 268 1 268 161 1 160 162 1 162 158 1 158 157 1
		 162 163 1 163 159 1 159 158 1 164 167 1 167 225 1 225 226 1 226 164 1 165 168 1 168 167 1
		 164 165 1 165 166 1 166 169 1 169 168 1 181 183 1 183 169 1 166 181 1 158 171 1 171 170 1
		 170 157 1 159 172 1 172 171 1 281 154 1 155 173 1 173 281 1 172 174 1 174 173 1 155 172 1
		 159 182 1 182 174 1 178 165 1 226 178 1 179 166 1 178 179 1 180 181 1 179 180 1 163 204 1
		 204 182 1 162 186 1 186 184 1 184 163 1 160 187 1;
	setAttr ".ed[332:497]" 187 186 1 161 188 1 188 187 1 161 185 1 185 189 1 189 188 1
		 255 189 1 185 190 1 190 255 1 206 207 1 207 192 1 192 191 1 191 206 1 194 193 1 193 196 1
		 196 195 1 195 194 1 197 250 1 250 193 1 194 197 1 199 198 1 198 201 1 201 200 1 200 199 1
		 200 202 1 202 199 1 190 203 1 203 215 1 215 255 1 184 205 1 205 204 1 206 209 1 209 208 1
		 208 207 1 211 210 1 210 213 1 213 212 1 212 211 1 214 216 1 216 210 1 211 214 1 215 218 1
		 218 216 1 214 215 1 203 217 1 217 218 1 232 219 1 219 204 1 205 232 1 209 220 1 220 221 1
		 221 208 1 210 222 1 222 253 1 253 213 1 216 254 1 254 222 1 218 247 1 247 254 1 224 169 1
		 183 223 1 223 224 1 246 168 1 224 246 1 237 167 1 246 237 1 190 227 1 227 228 1 228 203 1
		 231 239 1 239 152 1 278 231 1 175 231 1 278 175 1 179 175 1 175 176 1 176 180 1 176 177 1
		 177 181 1 177 219 1 219 183 1 232 223 1 233 221 1 220 234 1 234 233 1 225 230 1 230 229 1
		 229 238 1 238 225 1 238 239 1 239 226 1 241 240 1 240 187 1 188 241 1 242 186 1 240 242 1
		 244 184 1 242 244 1 244 243 1 243 205 1 245 232 1 243 245 1 245 224 1 229 247 1 218 238 1
		 217 239 1 242 192 1 207 244 1 240 248 1 248 192 1 241 249 1 249 248 1 245 221 1 233 224 1
		 243 208 1 173 175 1 278 281 1 174 176 1 182 177 1 191 196 1 196 212 1 212 206 1 248 194 1
		 195 192 1 220 253 1 253 235 1 235 234 1 209 213 1 234 246 1 236 237 1 234 236 1 249 197 1
		 211 193 1 250 214 1 197 256 1 256 257 1 257 250 1 249 258 1 258 256 1 215 259 1 259 258 1
		 258 255 1 214 260 1 260 259 1 257 260 1 256 261 1 261 262 1 262 257 1 258 263 1 263 261 1
		 259 264 1 264 263 1 260 265 1 265 264 1 262 265 1 198 262 1 261 201 1 200 263 1 264 202 1
		 265 199 1 237 230 1 252 236 1 235 252 1 247 266 1 266 251 1 251 254 1;
	setAttr ".ed[498:663]" 171 156 1 286 170 1 255 241 1 267 139 1 138 268 1 268 267 1
		 135 269 1 269 268 1 270 251 1 251 252 1 235 270 1 152 271 1 271 132 1 272 146 1 16 272 1
		 145 273 1 273 153 1 276 272 1 17 276 1 273 275 1 275 274 1 277 276 1 18 277 1 275 279 1
		 279 278 1 280 277 1 19 280 1 279 282 1 282 281 1 284 280 1 20 284 1 282 283 1 283 154 1
		 285 284 1 21 285 1 283 287 1 287 286 1 286 297 1 297 289 1 289 170 1 301 290 1 290 289 1
		 297 301 1 293 292 1 292 296 1 296 294 1 294 293 1 304 293 1 294 295 1 295 304 1 300 285 1
		 22 300 1 298 301 1 286 298 1 299 298 1 287 299 1 303 302 1 302 23 1 24 303 1 295 306 1
		 306 305 1 305 304 1 308 305 1 306 307 1 307 308 1 310 314 1 312 309 1 309 310 1 310 315 1
		 315 317 1 316 318 1 315 316 1 310 313 1 313 316 1 170 267 1 288 291 1 291 320 1 320 140 1
		 140 288 1 291 296 1 296 319 1 319 320 1 292 316 1 313 296 1 313 311 1 311 319 1 320 122 1
		 319 120 1 311 119 1 309 118 1 311 309 1 290 291 1 288 289 1 178 231 1 323 322 0 322 292 1
		 293 323 1 325 323 0 304 325 1 324 303 1 25 324 0 326 325 0 305 326 1 327 326 0 308 327 1
		 316 328 1 328 329 0 322 328 0 227 269 1 136 227 1 100 330 1 330 331 1 44 110 1 75 330 1
		 112 74 1 341 340 1 340 332 1 332 333 1 333 341 1 334 335 1 335 343 1 343 342 1 342 334 1
		 345 344 1 344 336 1 336 337 1 337 345 1 346 345 1 337 338 1 338 346 1 338 339 1 339 347 1
		 347 346 1 339 332 1 340 347 1 332 349 1 349 348 1 348 333 1 350 351 1 351 335 1 334 350 1
		 336 352 1 352 353 1 353 337 1 354 355 1 355 339 1 338 354 1 355 349 1 363 348 1 349 356 1
		 356 363 1 357 358 1 358 351 1 350 357 1 359 360 1 360 353 1 352 359 1 361 354 1 354 353 1
		 360 361 1 361 362 1 362 355 1 362 356 1 342 341 1 333 334 1 348 350 1;
	setAttr ".ed[664:829]" 335 336 1 344 343 1 351 352 1 358 359 1 364 358 1 357 365 1
		 365 364 0 359 366 1 366 360 0 360 367 0 367 361 1 368 362 1 367 368 0 369 356 1 368 369 0
		 364 366 0 369 370 0 370 363 1 374 340 1 341 371 1 371 374 1 40 342 1 343 62 1 390 344 1
		 345 387 1 387 390 1 346 377 1 377 387 1 347 375 1 375 377 1 374 375 1 40 371 1 390 62 1
		 38 372 1 372 371 1 372 373 1 373 374 1 373 376 1 376 375 1 376 378 1 378 377 1 27 372 1
		 28 373 1 379 382 1 382 381 1 381 380 1 380 379 1 382 383 1 403 381 1 30 379 1 380 29 1
		 379 385 1 385 384 1 384 382 1 384 386 1 31 385 1 384 126 1 124 385 1 388 387 1 378 388 1
		 388 389 1 389 390 1 389 391 1 389 397 1 397 392 1 65 396 1 396 398 1 398 393 1 397 396 1
		 398 399 1 399 394 1 399 400 1 388 131 1 131 397 1 396 133 1 133 401 1 401 398 1 402 399 1
		 378 403 1 106 32 0 290 294 1 301 295 1 298 306 1 299 307 1 302 300 1 134 136 1 134 228 1
		 124 126 1 125 128 1 142 144 1 146 145 1 185 269 1 271 228 1 96 405 1 405 404 0 33 405 0
		 270 222 1 236 229 1 236 266 1 112 330 1 272 273 1 276 275 1 277 279 1 280 282 1 284 283 1
		 285 287 1 300 299 1 302 307 1 303 308 1 324 327 0 57 407 1 407 406 1 406 56 1 59 409 1
		 409 407 1 406 408 1 408 58 1 408 409 1 271 217 1 363 357 1 370 365 0 380 373 1 381 376 1
		 267 288 1 0 411 1 411 410 1 410 1 1 411 412 1 412 420 1 420 410 1 26 414 1 414 413 1
		 413 2 1 414 416 1 416 415 1 415 413 1 410 417 1 417 3 1 418 417 1 420 418 1 419 4 1
		 417 419 1 421 419 1 418 421 1 419 422 1 422 5 1 422 423 1 423 6 1 421 425 1 425 424 1
		 424 419 1 421 423 1 423 427 1 427 425 1 426 422 1 424 426 1 426 427 1 425 429 1 429 428 1
		 428 424 1 427 431 1 431 429 1 428 430 1 430 426 1 430 431 1 429 433 1;
	setAttr ".ed[830:995]" 433 432 1 432 428 1 431 435 1 435 433 1 432 434 1 434 430 1
		 434 435 1 413 411 1 415 412 1 437 436 1 436 412 1 415 437 1 438 437 1 416 438 1 438 754 1
		 754 439 1 439 437 1 756 440 1 440 444 1 444 441 1 441 756 1 439 442 1 442 436 1 440 443 1
		 443 445 1 445 444 1 446 755 1 755 441 1 444 446 1 447 446 1 445 447 1 756 757 1 757 448 1
		 448 440 1 448 449 1 449 443 1 757 758 1 758 450 1 450 448 1 450 451 1 451 449 1 472 456 1
		 456 453 1 453 452 1 452 472 1 454 452 1 453 455 1 455 454 1 473 457 1 457 456 1 472 473 1
		 10 477 1 477 478 1 478 7 1 455 459 1 459 458 1 458 454 1 473 111 1 84 457 1 460 465 1
		 465 462 1 462 461 1 461 460 1 462 463 1 463 464 1 464 461 1 460 470 1 470 471 1 471 465 1
		 467 466 1 466 469 1 469 468 1 468 467 1 463 469 1 466 464 1 470 98 1 97 471 1 465 767 1
		 767 474 0 474 462 1 474 475 0 475 463 1 475 476 0 476 469 1 478 467 1 467 8 1 455 464 1
		 466 459 1 453 461 1 456 460 1 457 470 1 468 9 1 477 458 1 459 478 1 476 479 0 479 468 1
		 451 472 1 452 449 1 454 443 1 458 445 1 480 447 1 458 480 1 481 480 1 477 481 1 11 481 1
		 480 482 1 482 442 1 442 447 1 481 423 1 423 482 1 439 446 1 754 755 1 418 482 1 483 436 1
		 482 483 1 484 485 1 485 115 1 114 484 1 758 487 1 487 486 1 486 450 1 488 485 1 485 486 1
		 486 489 1 489 488 1 488 321 1 487 490 1 490 489 1 758 763 1 763 491 1 491 487 1 491 492 1
		 492 490 1 763 507 1 507 499 1 499 491 1 493 492 1 499 493 1 12 495 1 495 494 1 494 13 1
		 496 498 1 498 497 1 497 511 1 511 496 1 766 500 1 500 502 1 502 501 1 501 766 1 502 504 1
		 504 503 1 503 501 1 505 765 1 765 764 1 764 506 1 506 505 1 505 508 1 508 507 1 507 765 1
		 508 509 1 509 499 1 510 493 1 509 510 1 14 512 1 512 495 1 514 513 1;
	setAttr ".ed[996:1161]" 513 497 1 498 514 1 15 516 1 516 512 1 514 515 1 515 517 1
		 517 513 1 766 746 1 746 518 1 518 500 1 519 518 1 746 749 1 749 519 1 521 520 1 520 518 1
		 519 521 1 520 522 1 522 500 1 523 644 1 644 520 1 521 523 1 644 648 1 648 522 1 497 519 1
		 749 511 1 513 521 1 517 523 1 525 524 1 524 656 1 656 526 1 526 525 1 531 530 1 530 527 1
		 527 638 1 638 531 1 530 532 1 532 528 1 528 527 1 532 533 1 533 529 1 529 528 1 534 537 1
		 537 595 1 595 596 1 596 534 1 535 538 1 538 537 1 534 535 1 535 536 1 536 539 1 539 538 1
		 551 553 1 553 539 1 536 551 1 528 541 1 541 540 1 540 527 1 529 542 1 542 541 1 651 524 1
		 525 543 1 543 651 1 542 544 1 544 543 1 525 542 1 529 552 1 552 544 1 548 535 1 596 548 1
		 549 536 1 548 549 1 550 551 1 549 550 1 533 574 1 574 552 1 532 556 1 556 554 1 554 533 1
		 530 557 1 557 556 1 531 558 1 558 557 1 531 555 1 555 559 1 559 558 1 625 559 1 555 560 1
		 560 625 1 576 577 1 577 562 1 562 561 1 561 576 1 564 563 1 563 566 1 566 565 1 565 564 1
		 567 620 1 620 563 1 564 567 1 569 568 1 568 571 1 571 570 1 570 569 1 570 572 1 572 569 1
		 560 573 1 573 585 1 585 625 1 554 575 1 575 574 1 576 579 1 579 578 1 578 577 1 581 580 1
		 580 583 1 583 582 1 582 581 1 584 586 1 586 580 1 581 584 1 585 588 1 588 586 1 584 585 1
		 573 587 1 587 588 1 602 589 1 589 574 1 575 602 1 579 590 1 590 591 1 591 578 1 580 592 1
		 592 623 1 623 583 1 586 624 1 624 592 1 588 617 1 617 624 1 594 539 1 553 593 1 593 594 1
		 616 538 1 594 616 1 607 537 1 616 607 1 560 597 1 597 598 1 598 573 1 601 609 1 609 522 1
		 648 601 1 545 601 1 648 545 1 549 545 1 545 546 1 546 550 1 546 547 1 547 551 1 547 589 1
		 589 553 1 602 593 1 603 591 1 590 604 1 604 603 1 595 600 1 600 599 1;
	setAttr ".ed[1162:1327]" 599 608 1 608 595 1 608 609 1 609 596 1 611 610 1 610 557 1
		 558 611 1 612 556 1 610 612 1 614 554 1 612 614 1 614 613 1 613 575 1 615 602 1 613 615 1
		 615 594 1 599 617 1 588 608 1 587 609 1 612 562 1 577 614 1 610 618 1 618 562 1 611 619 1
		 619 618 1 615 591 1 603 594 1 613 578 1 543 545 1 648 651 1 544 546 1 552 547 1 561 566 1
		 566 582 1 582 576 1 618 564 1 565 562 1 590 623 1 623 605 1 605 604 1 579 583 1 604 616 1
		 606 607 1 604 606 1 619 567 1 581 563 1 620 584 1 567 626 1 626 627 1 627 620 1 619 628 1
		 628 626 1 585 629 1 629 628 1 628 625 1 584 630 1 630 629 1 627 630 1 626 631 1 631 632 1
		 632 627 1 628 633 1 633 631 1 629 634 1 634 633 1 630 635 1 635 634 1 632 635 1 568 632 1
		 631 571 1 570 633 1 634 572 1 635 569 1 607 600 1 622 606 1 605 622 1 617 636 1 636 621 1
		 621 624 1 541 526 1 656 540 1 625 611 1 637 509 1 508 638 1 638 637 1 505 639 1 639 638 1
		 640 621 1 621 622 1 605 640 1 522 641 1 641 502 1 642 516 1 16 642 1 515 643 1 643 523 1
		 646 642 1 17 646 1 643 645 1 645 644 1 647 646 1 18 647 1 645 649 1 649 648 1 650 647 1
		 19 650 1 649 652 1 652 651 1 654 650 1 20 654 1 652 653 1 653 524 1 655 654 1 21 655 1
		 653 657 1 657 656 1 656 667 1 667 659 1 659 540 1 671 660 1 660 659 1 667 671 1 663 662 1
		 662 666 1 666 664 1 664 663 1 674 663 1 664 665 1 665 674 1 670 655 1 22 670 1 668 671 1
		 656 668 1 669 668 1 657 669 1 673 672 1 672 23 1 24 673 1 665 676 1 676 675 1 675 674 1
		 678 675 1 676 677 1 677 678 1 680 314 1 312 679 1 679 680 1 680 683 1 683 317 1 684 318 1
		 683 684 1 680 682 1 682 684 1 540 637 1 658 661 1 661 686 1 686 510 1 510 658 1 661 666 1
		 666 685 1 685 686 1 662 684 1 682 666 1 682 681 1 681 685 1 686 492 1;
	setAttr ".ed[1328:1493]" 685 490 1 681 489 1 679 488 1 681 679 1 660 661 1 658 659 1
		 548 601 1 688 687 0 687 662 1 663 688 1 690 688 0 674 690 1 689 673 1 25 689 0 691 690 0
		 675 691 1 692 691 0 678 692 1 684 693 1 693 329 0 687 693 0 597 639 1 506 597 1 473 694 1
		 694 331 1 420 483 1 451 694 1 484 450 1 704 703 1 703 695 1 695 696 1 696 704 1 697 698 1
		 698 706 1 706 705 1 705 697 1 708 707 1 707 699 1 699 700 1 700 708 1 709 708 1 700 701 1
		 701 709 1 701 702 1 702 710 1 710 709 1 702 695 1 703 710 1 695 712 1 712 711 1 711 696 1
		 713 714 1 714 698 1 697 713 1 699 715 1 715 716 1 716 700 1 717 718 1 718 702 1 701 717 1
		 718 712 1 726 711 1 712 719 1 719 726 1 720 721 1 721 714 1 713 720 1 722 723 1 723 716 1
		 715 722 1 724 717 1 717 716 1 723 724 1 724 725 1 725 718 1 725 719 1 705 704 1 696 697 1
		 711 713 1 698 699 1 707 706 1 714 715 1 721 722 1 727 721 1 720 728 1 728 727 0 722 729 1
		 729 723 0 723 730 0 730 724 1 731 725 1 730 731 0 732 719 1 731 732 0 727 729 0 732 733 0
		 733 726 1 737 703 1 704 734 1 734 737 1 416 705 1 706 438 1 753 707 1 708 750 1 750 753 1
		 709 740 1 740 750 1 710 738 1 738 740 1 737 738 1 416 734 1 753 438 1 414 735 1 735 734 1
		 735 736 1 736 737 1 736 739 1 739 738 1 739 741 1 741 740 1 27 735 1 28 736 1 742 745 1
		 745 744 1 744 743 1 743 742 1 745 746 1 766 744 1 30 742 1 743 29 1 742 748 1 748 747 1
		 747 745 1 747 749 1 31 748 1 747 496 1 494 748 1 751 750 1 741 751 1 751 752 1 752 753 1
		 752 754 1 752 760 1 760 755 1 441 759 1 759 761 1 761 756 1 760 759 1 761 762 1 762 757 1
		 762 763 1 751 501 1 501 760 1 759 503 1 503 764 1 764 761 1 765 762 1 741 766 1 479 32 0
		 660 664 1 671 665 1 668 676 1 669 677 1 672 670 1 504 506 1 504 598 1;
	setAttr ".ed[1494:1530]" 494 496 1 495 498 1 512 514 1 516 515 1 555 639 1 641 598 1
		 471 768 1 768 767 0 33 768 0 640 592 1 606 599 1 606 636 1 484 694 1 642 643 1 646 645 1
		 647 649 1 650 652 1 654 653 1 655 657 1 670 669 1 672 677 1 673 678 1 689 692 0 433 770 1
		 770 769 1 769 432 1 435 772 1 772 770 1 769 771 1 771 434 1 771 772 1 641 587 1 726 720 1
		 733 728 0 743 736 1 744 739 1 637 658 1;
	setAttr -s 756 ".fc";
	setAttr ".fc[0:499]" -type "polyFaces" 
		f 4 -47 -46 -45 0
		mu 0 4 376 0 14 393
		f 4 -50 -49 -48 45
		mu 0 4 0 238 17 14
		f 4 -53 -52 -51 1
		mu 0 4 394 15 281 630
		f 4 -56 -55 -54 51
		mu 0 4 15 16 278 281
		f 4 -58 -57 46 2
		mu 0 4 378 1 0 376
		f 4 -60 49 56 -59
		mu 0 4 58 238 0 1
		f 4 -62 57 3 -61
		mu 0 4 2 1 378 380
		f 4 -64 58 61 -63
		mu 0 4 3 58 1 2
		f 4 4 -66 -65 60
		mu 0 4 380 381 5 2
		f 4 5 -68 -67 65
		mu 0 4 381 435 4 5
		f 4 -71 -70 -69 62
		mu 0 4 2 6 7 3
		f 4 68 -74 -73 -72
		mu 0 4 3 7 8 4
		f 4 -76 70 64 -75
		mu 0 4 9 6 2 5
		f 4 -77 74 66 72
		mu 0 4 8 9 5 4
		f 4 -80 -79 -78 69
		mu 0 4 6 10 11 7
		f 4 77 -82 -81 73
		mu 0 4 7 11 12 8
		f 4 -84 -83 79 75
		mu 0 4 9 13 10 6
		f 4 -85 83 76 80
		mu 0 4 12 13 9 8
		f 4 -88 -87 -86 78
		mu 0 4 10 358 359 11
		f 4 85 -90 -89 81
		mu 0 4 11 359 361 12
		f 4 -92 -91 87 82
		mu 0 4 13 362 358 10
		f 4 88 -93 91 84
		mu 0 4 12 361 362 13
		f 4 -94 52 6 44
		mu 0 4 14 15 394 393
		f 4 -95 55 93 47
		mu 0 4 17 16 15 14
		f 4 -98 94 -97 -96
		mu 0 4 18 16 17 60
		f 4 -100 54 97 -99
		mu 0 4 279 278 16 18
		f 4 -103 -102 -101 98
		mu 0 4 18 57 294 279
		f 4 -107 -106 -105 -104
		mu 0 4 297 298 19 20
		f 4 -109 -108 102 95
		mu 0 4 60 53 57 18
		f 4 104 -112 -111 -110
		mu 0 4 20 19 49 47
		f 4 -115 105 -114 -113
		mu 0 4 56 19 298 295
		f 4 -117 111 114 -116
		mu 0 4 52 49 19 56
		f 4 -120 -119 -118 103
		mu 0 4 20 21 299 297
		f 4 -122 -121 119 109
		mu 0 4 47 44 21 20
		f 4 -125 -124 -123 118
		mu 0 4 21 240 64 299
		f 4 -127 -126 124 120
		mu 0 4 44 239 240 21
		f 4 -131 -130 -129 -128
		mu 0 4 46 45 35 37
		f 4 -134 -133 129 -132
		mu 0 4 48 33 35 45
		f 4 -137 127 -136 -135
		mu 0 4 236 46 37 38
		f 4 7 -140 -139 -138
		mu 0 4 431 406 40 51
		f 4 -143 -142 -141 133
		mu 0 4 48 50 41 33
		f 4 -145 8 -144 134
		mu 0 4 38 422 586 236
		f 4 -149 -148 -147 -146
		mu 0 4 36 34 23 22
		f 4 147 -152 -151 -150
		mu 0 4 23 34 32 26
		f 4 -155 -154 -153 145
		mu 0 4 22 332 39 36
		f 4 -159 -158 -157 -156
		mu 0 4 30 42 28 31
		f 4 -161 156 -160 150
		mu 0 4 32 31 28 26
		f 4 -163 9 -162 153
		mu 0 4 332 701 423 39
		f 4 146 -166 -165 -164
		mu 0 4 22 23 24 25
		f 4 165 149 -168 -167
		mu 0 4 24 23 26 27
		f 4 167 159 -170 -169
		mu 0 4 27 26 28 29
		f 4 10 -172 -171 139
		mu 0 4 406 413 30 40
		f 4 -174 160 -173 140
		mu 0 4 41 31 32 33
		f 4 172 151 -175 132
		mu 0 4 33 32 34 35
		f 4 174 148 -176 128
		mu 0 4 35 34 36 37
		f 4 135 175 152 -177
		mu 0 4 38 37 36 39
		f 4 11 -178 158 171
		mu 0 4 413 669 42 30
		f 4 138 -180 141 -179
		mu 0 4 51 40 41 50
		f 4 -182 -181 169 157
		mu 0 4 42 43 29 28
		f 4 170 155 173 179
		mu 0 4 40 30 31 41
		f 4 161 12 144 176
		mu 0 4 39 423 422 38
		f 4 -184 130 -183 126
		mu 0 4 44 45 46 239
		f 4 121 -185 131 183
		mu 0 4 44 47 48 45
		f 4 110 -186 142 184
		mu 0 4 47 49 50 48
		f 4 -188 185 116 -187
		mu 0 4 54 50 49 52
		f 4 -190 178 187 -189
		mu 0 4 55 51 50 54
		f 4 -191 13 137 189
		mu 0 4 55 434 431 51
		f 4 -194 -193 -192 186
		mu 0 4 52 53 59 54
		f 4 -196 -195 188 191
		mu 0 4 59 4 55 54
		f 4 194 67 14 190
		mu 0 4 55 4 435 434
		f 4 -197 107 193 115
		mu 0 4 56 57 53 52
		f 4 -198 101 196 112
		mu 0 4 295 294 57 56
		f 4 71 195 -199 63
		mu 0 4 3 4 59 58
		f 4 -201 192 108 -200
		mu 0 4 237 59 53 60
		f 4 -204 15 -203 -202
		mu 0 4 339 727 441 62
		f 4 123 -207 -206 -205
		mu 0 4 64 240 61 65
		f 4 -211 -210 -209 -208
		mu 0 4 217 214 61 62
		f 4 -213 207 202 -212
		mu 0 4 63 217 62 441
		f 4 209 -215 -214 205
		mu 0 4 61 214 212 65
		f 4 204 -218 -217 -216
		mu 0 4 64 65 66 300
		f 4 213 -220 -219 217
		mu 0 4 65 212 210 66
		f 4 216 -223 -222 -221
		mu 0 4 300 66 68 67
		f 4 -225 222 218 -224
		mu 0 4 69 68 66 210
		f 4 -228 -227 -226 16
		mu 0 4 640 321 323 446
		f 4 -232 -231 -230 -229
		mu 0 4 320 76 75 322
		f 4 -236 -235 -234 -233
		mu 0 4 310 302 186 73
		f 4 -239 -238 -237 234
		mu 0 4 302 306 319 186
		f 4 -243 -242 -241 -240
		mu 0 4 182 318 305 309
		f 4 -246 -245 -244 239
		mu 0 4 309 67 180 182
		f 4 221 -248 -247 244
		mu 0 4 67 68 181 180
		f 4 -250 247 224 -249
		mu 0 4 207 181 68 69
		f 4 225 -252 -251 17
		mu 0 4 446 323 325 70
		f 4 -255 229 -254 -253
		mu 0 4 324 322 75 79
		f 4 18 250 -257 -256
		mu 0 4 71 70 325 327
		f 4 252 -260 -259 -258
		mu 0 4 324 79 80 326
		f 4 -263 -262 -261 232
		mu 0 4 73 72 286 310
		f 4 -266 -265 261 -264
		mu 0 4 77 289 286 72
		f 4 -269 263 -268 -267
		mu 0 4 78 77 72 74
		f 4 267 262 -271 -270
		mu 0 4 74 72 73 187
		f 4 -274 266 -273 -272
		mu 0 4 189 78 74 191
		f 4 272 269 -276 -275
		mu 0 4 191 74 187 193
		f 4 230 -278 265 -277
		mu 0 4 75 76 289 77
		f 4 276 268 -279 253
		mu 0 4 75 77 78 79
		f 4 278 273 -280 259
		mu 0 4 79 78 189 80
		f 4 -284 -283 -282 -281
		mu 0 4 85 176 201 197
		f 4 -288 -287 -286 -285
		mu 0 4 92 179 82 91
		f 4 -291 -290 -289 285
		mu 0 4 82 83 89 91
		f 4 289 -294 -293 -292
		mu 0 4 89 83 86 90
		f 4 -298 -297 -296 -295
		mu 0 4 81 109 108 99
		f 4 -301 294 -300 -299
		mu 0 4 87 81 99 98
		f 4 298 -304 -303 -302
		mu 0 4 87 98 97 88
		f 4 -307 302 -306 -305
		mu 0 4 104 88 97 105
		f 4 -310 -309 -308 290
		mu 0 4 82 206 175 83
		f 4 293 307 -312 -311
		mu 0 4 86 83 175 84
		f 4 -315 -314 280 -313
		mu 0 4 195 129 85 197
		f 4 -318 313 -317 -316
		mu 0 4 84 85 129 132
		f 4 310 315 -320 -319
		mu 0 4 86 84 132 134
		f 4 -322 297 300 -321
		mu 0 4 221 109 81 87
		f 4 -324 320 301 -323
		mu 0 4 102 221 87 88
		f 4 -326 322 306 -325
		mu 0 4 103 102 88 104
		f 4 292 318 -328 -327
		mu 0 4 90 86 134 94
		f 4 291 -331 -330 -329
		mu 0 4 89 90 114 113
		f 4 288 328 -333 -332
		mu 0 4 91 89 113 112
		f 4 331 -335 -334 284
		mu 0 4 91 112 111 92
		f 4 333 -338 -337 -336
		mu 0 4 92 111 93 328
		f 4 -341 -340 336 -339
		mu 0 4 178 100 328 93
		f 4 -345 -344 -343 -342
		mu 0 4 136 135 140 121
		f 4 -349 -348 -347 -346
		mu 0 4 142 141 138 149
		f 4 -352 345 -351 -350
		mu 0 4 151 142 149 152
		f 4 -356 -355 -354 -353
		mu 0 4 169 165 162 161
		f 3 355 -358 -357
		mu 0 3 165 169 166
		f 4 340 -361 -360 -359
		mu 0 4 100 178 154 101
		f 4 330 326 -363 -362
		mu 0 4 114 90 94 115
		f 4 -366 -365 -364 341
		mu 0 4 121 127 145 136
		f 4 -370 -369 -368 -367
		mu 0 4 150 137 146 95
		f 4 -373 366 -372 -371
		mu 0 4 156 150 95 96
		f 4 -376 370 -375 -374
		mu 0 4 154 156 96 118
		f 4 359 373 -378 -377
		mu 0 4 101 154 118 367
		f 4 -381 362 -380 -379
		mu 0 4 116 115 94 106
		f 4 364 -384 -383 -382
		mu 0 4 145 127 126 144
		f 4 367 -387 -386 -385
		mu 0 4 95 146 143 333
		f 4 371 384 -389 -388
		mu 0 4 96 95 333 174
		f 4 374 387 -391 -390
		mu 0 4 118 96 174 173
		f 4 -394 -393 305 -392
		mu 0 4 124 107 105 97
		f 4 -396 391 303 -395
		mu 0 4 147 124 97 98
		f 4 -398 394 299 -397
		mu 0 4 171 147 98 99
		f 4 358 -401 -400 -399
		mu 0 4 100 101 330 235
		f 4 -404 275 -403 -402
		mu 0 4 220 193 187 110
		f 3 -406 403 -405
		mu 0 3 130 193 220
		f 4 325 -409 -408 -407
		mu 0 4 102 103 131 130
		f 4 324 -411 -410 408
		mu 0 4 103 104 133 131
		f 4 304 -413 -412 410
		mu 0 4 104 105 106 133
		f 4 -414 378 412 392
		mu 0 4 107 116 106 105
		f 4 -417 -416 382 -415
		mu 0 4 125 148 144 126
		f 4 -421 -420 -419 -418
		mu 0 4 108 117 335 172
		f 4 -423 -422 420 296
		mu 0 4 109 110 117 108
		f 4 -426 334 -425 -424
		mu 0 4 177 111 112 122
		f 4 -428 424 332 -427
		mu 0 4 119 122 112 113
		f 4 -430 426 329 -429
		mu 0 4 120 119 113 114
		f 4 428 361 -432 -431
		mu 0 4 120 114 115 128
		f 4 -434 431 380 -433
		mu 0 4 123 128 115 116
		f 4 -435 432 413 393
		mu 0 4 124 123 116 107
		f 4 419 -437 389 -436
		mu 0 4 335 117 118 173
		f 4 436 421 -438 377
		mu 0 4 118 117 110 367
		f 4 429 -440 342 -439
		mu 0 4 119 120 121 140
		f 4 427 438 -442 -441
		mu 0 4 122 119 140 139
		f 4 423 440 -444 -443
		mu 0 4 177 122 139 153
		f 4 434 -446 414 -445
		mu 0 4 123 124 125 126
		f 4 -447 433 444 383
		mu 0 4 127 128 123 126
		f 4 446 365 439 430
		mu 0 4 128 127 121 120
		f 4 314 -449 405 -448
		mu 0 4 129 195 193 130
		f 4 447 407 -450 316
		mu 0 4 129 130 131 132
		f 4 449 409 -451 319
		mu 0 4 132 131 133 134
		f 4 411 379 327 450
		mu 0 4 133 106 94 134
		f 4 344 -454 -453 -452
		mu 0 4 135 136 137 138
		f 4 441 -456 348 -455
		mu 0 4 139 140 141 142
		f 4 -459 -458 -457 415
		mu 0 4 148 183 143 144
		f 4 381 456 386 -460
		mu 0 4 145 144 143 146
		f 4 363 459 368 453
		mu 0 4 136 145 146 137
		f 4 416 445 395 -461
		mu 0 4 148 125 124 147
		f 4 -463 460 397 -462
		mu 0 4 337 148 147 171
		f 4 443 454 351 -464
		mu 0 4 153 139 142 151
		f 4 -466 350 -465 372
		mu 0 4 156 152 149 150
		f 4 369 464 346 452
		mu 0 4 137 150 149 138
		f 4 349 -469 -468 -467
		mu 0 4 151 152 158 157
		f 4 466 -471 -470 463
		mu 0 4 151 157 155 153
		f 4 360 -474 -473 -472
		mu 0 4 154 178 155 159
		f 4 375 471 -476 -475
		mu 0 4 156 154 159 160
		f 4 465 474 -477 468
		mu 0 4 152 156 160 158
		f 4 467 -480 -479 -478
		mu 0 4 157 158 164 163
		f 4 470 477 -482 -481
		mu 0 4 155 157 163 168
		f 4 472 480 -484 -483
		mu 0 4 159 155 168 167
		f 4 475 482 -486 -485
		mu 0 4 160 159 167 170
		f 4 476 484 -487 479
		mu 0 4 158 160 170 164
		f 4 353 -489 478 -488
		mu 0 4 161 162 163 164
		f 4 356 -491 483 -490
		mu 0 4 165 166 167 168
		f 4 357 -492 485 490
		mu 0 4 166 169 170 167
		f 4 352 487 486 491
		mu 0 4 169 161 164 170
		f 4 396 295 417 -493
		mu 0 4 171 99 108 172
		f 4 -495 458 462 -494
		mu 0 4 184 183 148 337
		f 4 390 -498 -497 -496
		mu 0 4 173 174 185 336
		f 4 308 -500 282 -499
		mu 0 4 175 206 201 176
		f 4 425 -501 338 337
		mu 0 4 111 177 178 93
		f 4 347 455 343 451
		mu 0 4 138 141 140 135
		f 4 317 311 498 283
		mu 0 4 85 84 175 176
		f 4 -504 -503 246 -502
		mu 0 4 375 179 180 181
		f 4 -506 -505 243 502
		mu 0 4 179 329 182 180
		f 4 -509 494 -508 -507
		mu 0 4 334 183 184 185
		f 4 233 -511 -510 270
		mu 0 4 73 186 366 187
		f 4 -513 19 255 -512
		mu 0 4 341 188 71 327
		f 4 258 279 -515 -514
		mu 0 4 326 80 189 340
		f 4 -517 20 512 -516
		mu 0 4 343 190 188 341
		f 4 514 271 -519 -518
		mu 0 4 340 189 191 342
		f 4 -521 21 516 -520
		mu 0 4 345 192 190 343
		f 4 -523 -522 518 274
		mu 0 4 193 344 342 191
		f 4 -525 22 520 -524
		mu 0 4 347 194 192 345
		f 4 -527 -526 522 448
		mu 0 4 195 346 344 193
		f 4 -529 23 524 -528
		mu 0 4 349 196 194 347
		f 4 -531 -530 526 312
		mu 0 4 197 348 346 195
		f 4 -533 24 528 -532
		mu 0 4 351 198 196 349
		f 4 530 281 -535 -534
		mu 0 4 348 197 201 350
		f 4 -538 -537 -536 499
		mu 0 4 206 218 199 201
		f 4 -541 536 -540 -539
		mu 0 4 315 199 218 313
		f 4 -545 -544 -543 -542
		mu 0 4 223 312 208 224
		f 4 -548 -547 544 -546
		mu 0 4 227 314 312 223
		f 4 -550 25 532 -549
		mu 0 4 353 200 198 351
		f 4 -552 535 540 -551
		mu 0 4 317 201 199 315
		f 4 -554 534 551 -553
		mu 0 4 352 350 201 317
		f 4 -557 26 -556 -555
		mu 0 4 357 202 203 355
		f 4 -560 -559 -558 547
		mu 0 4 227 231 316 314
		f 4 -563 -562 558 -561
		mu 0 4 356 354 316 231
		f 4 -566 -565 27 -564
		mu 0 4 205 215 562 550
		f 4 28 -568 -567 563
		mu 0 4 550 551 204 205
		f 4 -570 567 29 -569
		mu 0 4 234 204 551 579
		f 4 -572 -571 566 569
		mu 0 4 234 209 205 204
		f 4 286 503 -573 309
		mu 0 4 82 179 375 206
		f 4 -577 -576 -575 -574
		mu 0 4 374 207 211 219
		f 4 574 -580 -579 -578
		mu 0 4 219 211 213 208
		f 4 -582 571 -581 542
		mu 0 4 208 209 234 224
		f 4 -584 -583 581 578
		mu 0 4 213 216 209 208
		f 4 248 223 -585 575
		mu 0 4 207 69 210 211
		f 4 -586 579 584 219
		mu 0 4 212 213 211 210
		f 4 583 585 214 -587
		mu 0 4 216 213 212 214
		f 4 -589 586 210 -588
		mu 0 4 215 216 214 217
		f 4 30 564 587 212
		mu 0 4 63 562 215 217
		f 4 354 489 481 488
		mu 0 4 162 165 168 163
		f 4 539 -591 573 -590
		mu 0 4 313 218 374 219
		f 4 -592 323 406 404
		mu 0 4 220 221 102 130
		f 4 321 591 401 422
		mu 0 4 109 221 220 110
		f 4 -595 541 -594 -593
		mu 0 4 222 223 224 225
		f 4 -597 545 594 -596
		mu 0 4 226 227 223 222
		f 4 -599 31 556 -598
		mu 0 4 228 229 202 357
		f 4 -601 559 596 -600
		mu 0 4 230 231 227 226
		f 4 -603 560 600 -602
		mu 0 4 232 356 231 230
		f 4 32 -605 -604 568
		mu 0 4 579 580 233 234
		f 4 593 580 603 -606
		mu 0 4 225 224 234 233
		f 4 -608 242 504 -607
		mu 0 4 235 318 182 329
		f 4 33 -610 -609 143
		mu 0 4 586 728 338 236
		f 4 200 -611 59 198
		mu 0 4 59 237 238 58
		f 4 96 48 610 199
		mu 0 4 60 17 238 237
		f 4 608 -612 182 136
		mu 0 4 236 338 239 46
		f 4 208 206 -613 201
		mu 0 4 62 61 240 339
		f 4 -617 -616 -615 -614
		mu 0 4 270 251 241 271
		f 4 -621 -620 -619 -618
		mu 0 4 250 273 272 254
		f 4 -625 -624 -623 -622
		mu 0 4 274 242 255 275
		f 4 -628 -627 624 -626
		mu 0 4 276 243 242 274
		f 4 627 -631 -630 -629
		mu 0 4 243 276 277 244
		f 4 629 -633 614 -632
		mu 0 4 244 277 271 241
		f 4 -636 -635 -634 615
		mu 0 4 251 253 245 241
		f 4 -639 617 -638 -637
		mu 0 4 252 250 254 256
		f 4 623 -642 -641 -640
		mu 0 4 255 242 247 257
		f 4 -645 628 -644 -643
		mu 0 4 248 243 244 249
		f 4 631 633 -646 643
		mu 0 4 244 241 245 249
		f 4 -649 -648 634 -647
		mu 0 4 369 268 245 253
		f 4 -652 636 -651 -650
		mu 0 4 368 252 256 260
		f 4 -655 640 -654 -653
		mu 0 4 262 257 247 246
		f 4 -658 653 -657 -656
		mu 0 4 263 246 247 248
		f 4 655 642 -660 -659
		mu 0 4 263 248 249 266
		f 4 659 645 647 -661
		mu 0 4 266 249 245 268
		f 4 620 -663 616 -662
		mu 0 4 273 250 251 270
		f 4 638 -664 635 662
		mu 0 4 250 252 253 251
		f 4 618 -666 622 -665
		mu 0 4 254 272 275 255
		f 4 637 664 639 -667
		mu 0 4 256 254 255 257
		f 4 650 666 654 -668
		mu 0 4 260 256 257 262
		f 4 -671 -670 649 -669
		mu 0 4 258 259 368 260
		f 3 -673 -672 652
		mu 0 3 246 261 262
		f 3 657 -675 -674
		mu 0 3 246 263 264
		f 4 -677 674 658 -676
		mu 0 4 265 264 263 266
		f 4 -679 675 660 -678
		mu 0 4 267 265 266 268
		f 4 -680 668 667 671
		mu 0 4 261 258 260 262
		f 4 648 -682 -681 677
		mu 0 4 268 369 269 267
		f 4 -685 -684 613 -683
		mu 0 4 282 280 270 271
		f 4 99 -687 619 -686
		mu 0 4 278 279 272 273
		f 4 -690 -689 621 -688
		mu 0 4 293 292 274 275
		f 4 -692 -691 625 688
		mu 0 4 292 284 276 274
		f 4 -694 -693 630 690
		mu 0 4 284 283 277 276
		f 4 -695 682 632 692
		mu 0 4 283 282 271 277
		f 4 -696 685 661 683
		mu 0 4 280 278 273 270
		f 4 -697 687 665 686
		mu 0 4 279 293 275 272
		f 4 695 -699 -698 53
		mu 0 4 278 280 285 281
		f 4 684 -701 -700 698
		mu 0 4 280 282 370 285
		f 4 694 -703 -702 700
		mu 0 4 282 283 372 370
		f 4 693 -705 -704 702
		mu 0 4 283 284 311 372
		f 4 697 -706 34 50
		mu 0 4 281 285 632 630
		f 4 699 -707 35 705
		mu 0 4 285 370 771 632
		f 4 -711 -710 -709 -708
		mu 0 4 288 371 373 287
		f 4 -713 260 -712 708
		mu 0 4 373 310 286 287
		f 4 36 -715 710 -714
		mu 0 4 636 772 371 288
		f 4 -718 -717 -716 707
		mu 0 4 287 290 291 288
		f 4 264 -719 717 711
		mu 0 4 286 289 290 287
		f 4 -720 37 713 715
		mu 0 4 291 639 636 288
		f 4 277 231 -721 718
		mu 0 4 289 76 320 290
		f 4 -722 227 38 719
		mu 0 4 291 321 640 639
		f 4 -724 704 691 -723
		mu 0 4 303 311 284 292
		f 4 689 -726 -725 722
		mu 0 4 292 293 296 303
		f 4 696 100 -727 725
		mu 0 4 293 279 294 296
		f 4 -729 -728 726 197
		mu 0 4 295 301 296 294
		f 4 -732 -731 -730 106
		mu 0 4 297 304 307 298
		f 4 729 -733 728 113
		mu 0 4 298 307 301 295
		f 4 -735 -734 731 117
		mu 0 4 299 308 304 297
		f 4 -736 734 122 215
		mu 0 4 300 308 299 64
		f 4 -738 -737 724 727
		mu 0 4 301 302 303 296
		f 4 -741 -740 -739 730
		mu 0 4 304 305 306 307
		f 4 738 238 737 732
		mu 0 4 307 306 302 301
		f 4 -742 240 740 733
		mu 0 4 308 309 305 304
		f 4 245 741 735 220
		mu 0 4 67 309 308 300
		f 4 235 -743 723 736
		mu 0 4 302 310 311 303
		f 4 641 626 644 656
		mu 0 4 247 242 243 248
		f 4 177 39 -744 181
		mu 0 4 42 669 668 43
		f 4 543 -745 589 577
		mu 0 4 208 312 313 219
		f 4 546 -746 538 744
		mu 0 4 312 314 315 313
		f 4 557 -747 550 745
		mu 0 4 314 316 317 315
		f 4 561 -748 552 746
		mu 0 4 316 354 352 317
		f 4 549 -749 555 40
		mu 0 4 200 353 355 203
		f 4 -750 237 739 241
		mu 0 4 318 319 306 305
		f 4 607 399 -751 749
		mu 0 4 318 235 330 319
		f 4 -752 721 716 720
		mu 0 4 320 321 291 290
		f 4 -753 226 751 228
		mu 0 4 322 323 321 320
		f 4 -754 251 752 254
		mu 0 4 324 325 323 322
		f 4 257 -755 256 753
		mu 0 4 324 326 327 325
		f 4 570 582 588 565
		mu 0 4 205 209 216 215
		f 4 339 398 606 -756
		mu 0 4 328 100 235 329
		f 4 750 -757 510 236
		mu 0 4 319 330 366 186
		f 4 163 -759 -758 154
		mu 0 4 22 25 331 332
		f 4 -760 41 162 757
		mu 0 4 331 702 701 332
		f 4 500 442 469 473
		mu 0 4 178 177 153 155
		f 4 755 505 287 335
		mu 0 4 328 329 179 92
		f 4 -761 506 497 388
		mu 0 4 333 334 185 174
		f 4 457 508 760 385
		mu 0 4 143 183 334 333
		f 4 461 492 418 -762
		mu 0 4 337 171 172 335
		f 4 495 -763 761 435
		mu 0 4 173 336 337 335
		f 4 507 493 762 496
		mu 0 4 185 184 337 336
		f 4 -764 612 125 611
		mu 0 4 338 339 240 239
		f 4 609 42 203 763
		mu 0 4 338 728 727 339
		f 4 -765 511 754 513
		mu 0 4 340 341 327 326
		f 4 -766 515 764 517
		mu 0 4 342 343 341 340
		f 4 -767 519 765 521
		mu 0 4 344 345 343 342
		f 4 -768 523 766 525
		mu 0 4 346 347 345 344
		f 4 -769 527 767 529
		mu 0 4 348 349 347 346
		f 4 -770 531 768 533
		mu 0 4 350 351 349 348
		f 4 -771 548 769 553
		mu 0 4 352 353 351 350
		f 4 -772 748 770 747
		mu 0 4 354 355 353 352
		f 4 -773 554 771 562
		mu 0 4 356 357 355 354
		f 4 -774 597 772 602
		mu 0 4 232 228 357 356
		f 4 -777 -776 -775 86
		mu 0 4 358 363 360 359
		f 4 774 -779 -778 89
		mu 0 4 359 360 364 361
		f 4 -781 -780 776 90
		mu 0 4 362 365 363 358
		f 4 777 -782 780 92
		mu 0 4 361 364 365 362
		f 4 778 775 779 781
		mu 0 4 364 360 363 365
		f 4 756 400 376 -783
		mu 0 4 366 330 101 367
		f 4 437 402 509 782
		mu 0 4 367 110 187 366
		f 4 651 -784 646 663
		mu 0 4 252 368 369 253
		f 4 -785 681 783 669
		mu 0 4 259 269 369 368
		f 4 706 -786 714 43
		mu 0 4 771 370 371 772
		f 4 701 -787 709 785
		mu 0 4 370 372 373 371
		f 4 703 742 712 786
		mu 0 4 372 311 310 373
		f 4 -788 501 249 576
		mu 0 4 374 375 181 207
		f 4 537 572 787 590
		mu 0 4 218 206 375 374
		f 4 -1 788 789 790
		mu 0 4 376 393 395 377
		f 4 -790 791 792 793
		mu 0 4 377 395 592 591
		f 4 -2 794 795 796
		mu 0 4 394 630 629 396
		f 4 -796 797 798 799
		mu 0 4 396 629 625 397
		f 4 -3 -791 800 801
		mu 0 4 378 376 377 379
		f 4 802 -801 -794 803
		mu 0 4 588 379 377 591
		f 4 804 -4 -802 805
		mu 0 4 382 380 378 379
		f 4 806 -806 -803 807
		mu 0 4 440 382 379 588
		f 4 -805 808 809 -5
		mu 0 4 380 382 384 381
		f 4 -810 810 811 -6
		mu 0 4 381 384 383 435
		f 4 -807 812 813 814
		mu 0 4 382 440 385 386
		f 4 815 816 817 -813
		mu 0 4 440 383 387 385
		f 4 818 -809 -815 819
		mu 0 4 388 384 382 386
		f 4 -817 -811 -819 820
		mu 0 4 387 383 384 388
		f 4 -814 821 822 823
		mu 0 4 386 385 389 390
		f 4 -818 824 825 -822
		mu 0 4 385 387 391 389
		f 4 -820 -824 826 827
		mu 0 4 388 386 390 392
		f 4 -825 -821 -828 828
		mu 0 4 391 387 388 392
		f 4 -823 829 830 831
		mu 0 4 390 389 751 752
		f 4 -826 832 833 -830
		mu 0 4 389 391 753 751
		f 4 -827 -832 834 835
		mu 0 4 392 390 752 754
		f 4 -829 -836 836 -833
		mu 0 4 391 392 754 753
		f 4 -789 -7 -797 837
		mu 0 4 395 393 394 396
		f 4 -792 -838 -800 838
		mu 0 4 592 395 396 397
		f 4 839 840 -839 841
		mu 0 4 398 589 592 397
		f 4 842 -842 -799 843
		mu 0 4 644 398 397 625
		f 4 -843 844 845 846
		mu 0 4 398 644 645 439
		f 4 847 848 849 850
		mu 0 4 648 400 399 646
		f 4 -840 -847 851 852
		mu 0 4 589 398 439 437
		f 4 853 854 855 -849
		mu 0 4 400 426 428 399
		f 4 856 857 -850 858
		mu 0 4 438 647 646 399
		f 4 859 -859 -856 860
		mu 0 4 436 438 399 428
		f 4 -848 861 862 863
		mu 0 4 400 648 650 401
		f 4 -854 -864 864 865
		mu 0 4 426 400 401 424
		f 4 -863 866 867 868
		mu 0 4 401 650 649 724
		f 4 -865 -869 869 870
		mu 0 4 424 401 724 723
		f 4 871 872 873 874
		mu 0 4 594 412 409 425
		f 4 875 -874 876 877
		mu 0 4 427 425 409 407
		f 4 878 879 -872 880
		mu 0 4 593 421 412 594
		f 4 881 882 883 -8
		mu 0 4 431 430 417 406
		f 4 -878 884 885 886
		mu 0 4 427 407 418 429
		f 4 -879 887 -9 888
		mu 0 4 421 593 586 422
		f 4 889 890 891 892
		mu 0 4 411 697 403 410
		f 4 893 894 895 -892
		mu 0 4 403 405 408 410
		f 4 -890 896 897 898
		mu 0 4 697 411 420 700
		f 4 899 900 901 902
		mu 0 4 414 419 415 666
		f 4 -895 903 -901 904
		mu 0 4 408 405 415 419
		f 4 -898 905 -10 906
		mu 0 4 700 420 423 701
		f 4 907 908 909 -891
		mu 0 4 697 698 402 403
		f 4 910 911 -894 -910
		mu 0 4 402 404 405 403
		f 4 912 913 -904 -912
		mu 0 4 404 416 415 405
		f 4 -884 914 915 -11
		mu 0 4 406 417 414 413
		f 4 -885 916 -905 917
		mu 0 4 418 407 408 419
		f 4 -877 918 -896 -917
		mu 0 4 407 409 410 408
		f 4 -873 919 -893 -919
		mu 0 4 409 412 411 410
		f 4 920 -897 -920 -880
		mu 0 4 421 420 411 412
		f 4 -916 -903 921 -12
		mu 0 4 413 414 666 669
		f 4 922 -886 923 -883
		mu 0 4 430 429 418 417
		f 4 -902 -914 924 925
		mu 0 4 666 415 416 667
		f 4 -924 -918 -900 -915
		mu 0 4 417 418 419 414
		f 4 -921 -889 -13 -906
		mu 0 4 420 421 422 423
		f 4 -871 926 -875 927
		mu 0 4 424 723 594 425
		f 4 -928 -876 928 -866
		mu 0 4 424 425 427 426
		f 4 -929 -887 929 -855
		mu 0 4 426 427 429 428
		f 4 930 -861 -930 931
		mu 0 4 432 436 428 429
		f 4 932 -932 -923 933
		mu 0 4 433 432 429 430
		f 4 -934 -882 -14 934
		mu 0 4 433 430 431 434
		f 4 -931 935 936 937
		mu 0 4 436 432 587 437
		f 4 -936 -933 938 939
		mu 0 4 587 432 433 383
		f 4 -935 -15 -812 -939
		mu 0 4 433 434 435 383
		f 4 -860 -938 -852 940
		mu 0 4 438 436 437 439
		f 4 -857 -941 -846 941
		mu 0 4 647 438 439 645
		f 4 -808 942 -940 -816
		mu 0 4 440 588 587 383
		f 4 943 -853 -937 944
		mu 0 4 590 589 437 587
		f 4 945 946 -16 947
		mu 0 4 726 595 441 727
		f 4 948 949 950 -868
		mu 0 4 649 443 442 724
		f 4 951 952 953 954
		mu 0 4 561 595 442 560
		f 4 211 -947 -952 955
		mu 0 4 63 441 595 561
		f 4 -950 956 957 -954
		mu 0 4 442 443 558 560
		f 4 958 959 960 -949
		mu 0 4 649 658 444 443
		f 4 -961 961 962 -957
		mu 0 4 443 444 556 558
		f 4 963 964 965 -960
		mu 0 4 658 657 445 444
		f 4 966 -962 -966 967
		mu 0 4 555 556 444 445
		f 4 -17 968 969 970
		mu 0 4 640 446 687 685
		f 4 971 972 973 974
		mu 0 4 684 686 450 638
		f 4 975 976 977 978
		mu 0 4 777 534 696 654
		f 4 -978 979 980 981
		mu 0 4 654 696 695 679
		f 4 982 983 984 985
		mu 0 4 585 660 656 680
		f 4 -983 986 987 988
		mu 0 4 660 585 533 657
		f 4 -988 989 990 -965
		mu 0 4 657 533 780 445
		f 4 991 -968 -991 992
		mu 0 4 779 555 445 780
		f 4 -18 993 994 -969
		mu 0 4 446 447 689 687
		f 4 995 996 -973 997
		mu 0 4 688 452 450 686
		f 4 998 999 -994 -19
		mu 0 4 535 730 689 447
		f 4 1000 1001 1002 -996
		mu 0 4 688 729 536 452
		f 4 -976 1003 1004 1005
		mu 0 4 534 777 633 448
		f 4 1006 -1005 1007 1008
		mu 0 4 451 448 633 637
		f 4 1009 1010 -1007 1011
		mu 0 4 453 449 448 451
		f 4 1012 1013 -1006 -1011
		mu 0 4 449 763 534 448
		f 4 1014 1015 -1010 1016
		mu 0 4 538 540 449 453
		f 4 1017 1018 -1013 -1016
		mu 0 4 540 542 763 449
		f 4 1019 -1009 1020 -974
		mu 0 4 450 451 637 638
		f 4 -997 1021 -1012 -1020
		mu 0 4 450 452 453 451
		f 4 -1003 1022 -1017 -1022
		mu 0 4 452 536 538 453
		f 4 1023 1024 1025 1026
		mu 0 4 529 546 549 530
		f 4 1027 1028 1029 1030
		mu 0 4 707 460 553 708
		f 4 -1029 1031 1032 1033
		mu 0 4 553 460 459 454
		f 4 1034 1035 1036 -1033
		mu 0 4 459 461 458 454
		f 4 1037 1038 1039 1040
		mu 0 4 455 522 470 568
		f 4 1041 1042 -1038 1043
		mu 0 4 456 465 522 455
		f 4 1044 1045 1046 -1042
		mu 0 4 456 457 464 465
		f 4 1047 1048 -1046 1049
		mu 0 4 467 468 464 457
		f 4 -1034 1050 1051 1052
		mu 0 4 553 454 531 784
		f 4 1053 1054 -1051 -1037
		mu 0 4 458 532 531 454
		f 4 1055 -1024 1056 1057
		mu 0 4 544 546 529 487
		f 4 1058 1059 -1057 1060
		mu 0 4 532 489 487 529
		f 4 1061 1062 -1059 -1054
		mu 0 4 458 492 489 532
		f 4 1063 -1044 -1041 1064
		mu 0 4 570 456 455 568
		f 4 1065 -1045 -1064 1066
		mu 0 4 567 457 456 570
		f 4 1067 -1050 -1066 1068
		mu 0 4 466 467 457 567
		f 4 1069 1070 -1062 -1036
		mu 0 4 461 493 492 458
		f 4 1071 1072 1073 -1035
		mu 0 4 459 472 473 461;
	setAttr ".fc[500:755]"
		f 4 1074 1075 -1072 -1032
		mu 0 4 460 471 472 459
		f 4 -1028 1076 1077 -1075
		mu 0 4 460 707 524 471
		f 4 1078 1079 1080 -1077
		mu 0 4 707 706 525 524
		f 4 1081 -1080 1082 1083
		mu 0 4 703 525 706 694
		f 4 1084 1085 1086 1087
		mu 0 4 495 485 527 526
		f 4 1088 1089 1090 1091
		mu 0 4 502 505 504 528
		f 4 1092 1093 -1089 1094
		mu 0 4 507 509 505 502
		f 4 1095 1096 1097 1098
		mu 0 4 518 521 563 566
		f 3 1099 1100 -1099
		mu 0 3 566 516 518
		f 4 1101 1102 1103 -1084
		mu 0 4 694 759 508 703
		f 4 1104 1105 -1070 -1074
		mu 0 4 473 474 493 461
		f 4 -1085 1106 1107 1108
		mu 0 4 485 495 498 486
		f 4 1109 1110 1111 1112
		mu 0 4 506 462 497 496
		f 4 1113 1114 -1110 1115
		mu 0 4 510 463 462 506
		f 4 1116 1117 -1114 1118
		mu 0 4 508 477 463 510
		f 4 1119 1120 -1117 -1103
		mu 0 4 759 761 477 508
		f 4 1121 1122 -1106 1123
		mu 0 4 476 469 493 474
		f 4 1124 1125 1126 -1108
		mu 0 4 498 494 481 486
		f 4 1127 1128 1129 -1111
		mu 0 4 462 712 711 497
		f 4 1130 1131 -1128 -1115
		mu 0 4 463 710 712 462
		f 4 1132 1133 -1131 -1118
		mu 0 4 477 717 710 463
		f 4 1134 -1049 1135 1136
		mu 0 4 499 464 468 475
		f 4 1137 -1047 -1135 1138
		mu 0 4 501 465 464 499
		f 4 1139 -1043 -1138 1140
		mu 0 4 716 522 465 501
		f 4 1141 1142 1143 -1102
		mu 0 4 694 681 760 759
		f 4 1144 1145 -1019 1146
		mu 0 4 569 764 763 542
		f 3 1147 -1147 1148
		mu 0 3 488 569 542
		f 4 1149 1150 1151 -1069
		mu 0 4 567 488 490 466
		f 4 -1152 1152 1153 -1068
		mu 0 4 466 490 491 467
		f 4 -1154 1154 1155 -1048
		mu 0 4 467 491 469 468
		f 4 -1136 -1156 -1122 1156
		mu 0 4 475 468 469 476
		f 4 1157 -1126 1158 1159
		mu 0 4 500 481 494 523
		f 4 1160 1161 1162 1163
		mu 0 4 470 715 718 478
		f 4 -1040 -1164 1164 1165
		mu 0 4 568 470 478 764
		f 4 1166 1167 -1078 1168
		mu 0 4 705 480 471 524
		f 4 1169 -1076 -1168 1170
		mu 0 4 479 472 471 480
		f 4 1171 -1073 -1170 1172
		mu 0 4 484 473 472 479
		f 4 1173 1174 -1105 -1172
		mu 0 4 484 483 474 473
		f 4 1175 -1124 -1175 1176
		mu 0 4 482 476 474 483
		f 4 -1137 -1157 -1176 1177
		mu 0 4 499 475 476 482
		f 4 1178 -1133 1179 -1163
		mu 0 4 718 717 477 478
		f 4 -1121 1180 -1165 -1180
		mu 0 4 477 761 764 478
		f 4 1181 -1086 1182 -1173
		mu 0 4 479 527 485 484
		f 4 1183 1184 -1182 -1171
		mu 0 4 480 503 527 479
		f 4 1185 1186 -1184 -1167
		mu 0 4 705 704 503 480
		f 4 1187 -1158 1188 -1178
		mu 0 4 482 481 500 499
		f 4 -1127 -1188 -1177 1189
		mu 0 4 486 481 482 483
		f 4 -1174 -1183 -1109 -1190
		mu 0 4 483 484 485 486
		f 4 1190 -1149 1191 -1058
		mu 0 4 487 488 542 544
		f 4 -1060 1192 -1151 -1191
		mu 0 4 487 489 490 488
		f 4 -1063 1193 -1153 -1193
		mu 0 4 489 492 491 490
		f 4 -1194 -1071 -1123 -1155
		mu 0 4 491 492 493 469
		f 4 1194 1195 1196 -1088
		mu 0 4 526 504 496 495
		f 4 1197 -1092 1198 -1185
		mu 0 4 503 502 528 527
		f 4 -1159 1199 1200 1201
		mu 0 4 523 494 711 714
		f 4 1202 -1130 -1200 -1125
		mu 0 4 498 497 711 494
		f 4 -1197 -1112 -1203 -1107
		mu 0 4 495 496 497 498
		f 4 1203 -1139 -1189 -1160
		mu 0 4 523 501 499 500
		f 4 1204 -1141 -1204 1205
		mu 0 4 721 716 501 523
		f 4 1206 -1095 -1198 -1187
		mu 0 4 704 507 502 503
		f 4 -1116 1207 -1094 1208
		mu 0 4 510 506 505 509
		f 4 -1196 -1090 -1208 -1113
		mu 0 4 496 504 505 506
		f 4 1209 1210 1211 -1093
		mu 0 4 507 511 514 509
		f 4 -1207 1212 1213 -1210
		mu 0 4 507 704 512 511
		f 4 1214 1215 1216 -1104
		mu 0 4 508 513 512 703
		f 4 1217 1218 -1215 -1119
		mu 0 4 510 515 513 508
		f 4 -1212 1219 -1218 -1209
		mu 0 4 509 514 515 510
		f 4 1220 1221 1222 -1211
		mu 0 4 511 564 520 514
		f 4 1223 1224 -1221 -1214
		mu 0 4 512 565 564 511
		f 4 1225 1226 -1224 -1216
		mu 0 4 513 517 565 512
		f 4 1227 1228 -1226 -1219
		mu 0 4 515 519 517 513
		f 4 -1223 1229 -1228 -1220
		mu 0 4 514 520 519 515
		f 4 1230 -1222 1231 -1097
		mu 0 4 521 520 564 563
		f 4 1232 -1227 1233 -1100
		mu 0 4 566 565 517 516
		f 4 -1234 -1229 1234 -1101
		mu 0 4 516 517 519 518
		f 4 -1235 -1230 -1231 -1096
		mu 0 4 518 519 520 521
		f 4 1235 -1161 -1039 -1140
		mu 0 4 716 715 470 522
		f 4 1236 -1206 -1202 1237
		mu 0 4 722 721 523 714
		f 4 1238 1239 1240 -1134
		mu 0 4 717 720 719 710
		f 4 1241 -1026 1242 -1052
		mu 0 4 531 530 549 784
		f 4 -1081 -1082 1243 -1169
		mu 0 4 524 525 703 705
		f 4 -1195 -1087 -1199 -1091
		mu 0 4 504 526 527 528
		f 4 -1027 -1242 -1055 -1061
		mu 0 4 529 530 531 532
		f 4 1244 -990 1245 1246
		mu 0 4 783 780 533 708
		f 4 -1246 -987 1247 1248
		mu 0 4 708 533 585 709
		f 4 1249 1250 -1238 1251
		mu 0 4 713 719 722 714
		f 4 -1014 1252 1253 -977
		mu 0 4 534 763 762 696
		f 4 1254 -999 -20 1255
		mu 0 4 732 730 535 537
		f 4 1256 1257 -1023 -1002
		mu 0 4 729 731 538 536
		f 4 1258 -1256 -21 1259
		mu 0 4 734 732 537 539
		f 4 1260 1261 -1015 -1258
		mu 0 4 731 733 540 538
		f 4 1262 -1260 -22 1263
		mu 0 4 736 734 539 541
		f 4 -1018 -1262 1264 1265
		mu 0 4 542 540 733 735
		f 4 1266 -1264 -23 1267
		mu 0 4 738 736 541 543
		f 4 -1192 -1266 1268 1269
		mu 0 4 544 542 735 737
		f 4 1270 -1268 -24 1271
		mu 0 4 740 738 543 545
		f 4 -1056 -1270 1272 1273
		mu 0 4 546 544 737 739
		f 4 1274 -1272 -25 1275
		mu 0 4 742 740 545 547
		f 4 1276 1277 -1025 -1274
		mu 0 4 739 741 549 546
		f 4 -1243 1278 1279 1280
		mu 0 4 784 549 548 781
		f 4 1281 1282 -1280 1283
		mu 0 4 674 672 781 548
		f 4 1284 1285 1286 1287
		mu 0 4 572 584 554 671
		f 4 1288 -1288 1289 1290
		mu 0 4 576 572 671 673
		f 4 1291 -1276 -26 1292
		mu 0 4 744 742 547 677
		f 4 1293 -1284 -1279 1294
		mu 0 4 676 674 548 549
		f 4 1295 -1295 -1278 1296
		mu 0 4 743 676 549 741
		f 4 1297 1298 -27 1299
		mu 0 4 749 746 678 573
		f 4 -1291 1300 1301 1302
		mu 0 4 576 673 675 578
		f 4 1303 -1302 1304 1305
		mu 0 4 748 578 675 745
		f 4 1306 -28 1307 1308
		mu 0 4 690 550 562 691
		f 4 -1307 1309 1310 -29
		mu 0 4 550 690 552 551
		f 4 1311 -30 -1311 1312
		mu 0 4 583 579 551 552
		f 4 -1313 -1310 1313 1314
		mu 0 4 583 552 690 693
		f 4 -1053 1315 -1247 -1030
		mu 0 4 553 784 783 708
		f 4 1316 1317 1318 1319
		mu 0 4 782 670 557 779
		f 4 1320 1321 1322 -1318
		mu 0 4 670 554 559 557
		f 4 -1286 1323 -1315 1324
		mu 0 4 554 584 583 693
		f 4 -1322 -1325 1325 1326
		mu 0 4 559 554 693 692
		f 4 -1319 1327 -967 -992
		mu 0 4 779 557 556 555
		f 4 -963 -1328 -1323 1328
		mu 0 4 558 556 557 559
		f 4 1329 -958 -1329 -1327
		mu 0 4 692 560 558 559
		f 4 1330 -955 -1330 1331
		mu 0 4 691 561 560 692
		f 4 -956 -1331 -1308 -31
		mu 0 4 63 561 691 562
		f 4 -1232 -1225 -1233 -1098
		mu 0 4 563 564 565 566
		f 4 1332 -1317 1333 -1283
		mu 0 4 672 670 782 781
		f 4 -1148 -1150 -1067 1334
		mu 0 4 569 488 567 570
		f 4 -1166 -1145 -1335 -1065
		mu 0 4 568 764 569 570
		f 4 1335 1336 -1285 1337
		mu 0 4 571 581 584 572
		f 4 1338 -1338 -1289 1339
		mu 0 4 575 571 572 576
		f 4 1340 -1300 -32 1341
		mu 0 4 750 749 573 574
		f 4 1342 -1340 -1303 1343
		mu 0 4 577 575 576 578
		f 4 1344 -1344 -1304 1345
		mu 0 4 747 577 578 748
		f 4 -1312 1346 1347 -33
		mu 0 4 579 583 582 580
		f 4 1348 -1347 -1324 -1337
		mu 0 4 581 582 583 584
		f 4 1349 -1248 -986 1350
		mu 0 4 681 709 585 680
		f 4 -888 1351 1352 -34
		mu 0 4 586 593 725 728
		f 4 -943 -804 1353 -945
		mu 0 4 587 588 591 590
		f 4 -944 -1354 -793 -841
		mu 0 4 589 590 591 592
		f 4 -881 -927 1354 -1352
		mu 0 4 593 594 723 725
		f 4 -946 1355 -951 -953
		mu 0 4 595 726 724 442
		f 4 1356 1357 1358 1359
		mu 0 4 621 620 597 601
		f 4 1360 1361 1362 1363
		mu 0 4 600 603 623 622
		f 4 1364 1365 1366 1367
		mu 0 4 617 624 602 665
		f 4 1368 -1368 1369 1370
		mu 0 4 618 617 665 664
		f 4 1371 1372 1373 -1371
		mu 0 4 664 596 619 618
		f 4 1374 -1358 1375 -1373
		mu 0 4 596 597 620 619
		f 4 -1359 1376 1377 1378
		mu 0 4 601 597 598 766
		f 4 1379 1380 -1361 1381
		mu 0 4 765 605 603 600
		f 4 1382 1383 1384 -1367
		mu 0 4 602 604 662 665
		f 4 1385 1386 -1372 1387
		mu 0 4 663 599 596 664
		f 4 -1387 1388 -1377 -1375
		mu 0 4 596 599 598 597
		f 4 1389 -1378 1390 1391
		mu 0 4 769 766 598 615
		f 4 1392 1393 -1380 1394
		mu 0 4 768 613 605 765
		f 4 1395 1396 -1384 1397
		mu 0 4 612 606 662 604
		f 4 1398 1399 -1397 1400
		mu 0 4 607 663 662 606
		f 4 1401 1402 -1386 -1399
		mu 0 4 607 609 599 663
		f 4 1403 -1391 -1389 -1403
		mu 0 4 609 615 598 599
		f 4 1404 -1360 1405 -1364
		mu 0 4 622 621 601 600
		f 4 -1406 -1379 1406 -1382
		mu 0 4 600 601 766 765
		f 4 1407 -1366 1408 -1362
		mu 0 4 603 602 624 623
		f 4 1409 -1383 -1408 -1381
		mu 0 4 605 604 602 603
		f 4 1410 -1398 -1410 -1394
		mu 0 4 613 612 604 605
		f 4 1411 -1393 1412 1413
		mu 0 4 614 613 768 767
		f 3 -1396 1414 1415
		mu 0 3 606 612 611
		f 3 1416 1417 -1401
		mu 0 3 606 608 607
		f 4 1418 -1402 -1418 1419
		mu 0 4 610 609 607 608
		f 4 1420 -1404 -1419 1421
		mu 0 4 616 615 609 610
		f 4 -1415 -1411 -1412 1422
		mu 0 4 611 612 613 614
		f 4 -1421 1423 1424 -1392
		mu 0 4 615 616 770 769
		f 4 1425 -1357 1426 1427
		mu 0 4 627 620 621 626
		f 4 1428 -1363 1429 -844
		mu 0 4 625 622 623 644
		f 4 1430 -1365 1431 1432
		mu 0 4 643 624 617 642
		f 4 -1432 -1369 1433 1434
		mu 0 4 642 617 618 641
		f 4 -1434 -1374 1435 1436
		mu 0 4 641 618 619 628
		f 4 -1436 -1376 -1426 1437
		mu 0 4 628 619 620 627
		f 4 -1427 -1405 -1429 1438
		mu 0 4 626 621 622 625
		f 4 -1430 -1409 -1431 1439
		mu 0 4 644 623 624 643
		f 4 -798 1440 1441 -1439
		mu 0 4 625 629 631 626
		f 4 -1442 1442 1443 -1428
		mu 0 4 626 631 773 627
		f 4 -1444 1444 1445 -1438
		mu 0 4 627 773 775 628
		f 4 -1446 1446 1447 -1437
		mu 0 4 628 775 778 641
		f 4 -795 -35 1448 -1441
		mu 0 4 629 630 632 631
		f 4 -1449 -36 1449 -1443
		mu 0 4 631 632 771 773
		f 4 1450 1451 1452 1453
		mu 0 4 635 634 776 774
		f 4 -1452 1454 -1004 1455
		mu 0 4 776 634 633 777
		f 4 1456 -1454 1457 -37
		mu 0 4 636 635 774 772
		f 4 -1451 1458 1459 1460
		mu 0 4 634 635 683 682
		f 4 -1455 -1461 1461 -1008
		mu 0 4 633 634 682 637
		f 4 -1459 -1457 -38 1462
		mu 0 4 683 635 636 639
		f 4 -1462 1463 -975 -1021
		mu 0 4 637 682 684 638
		f 4 -1463 -39 -971 1464
		mu 0 4 683 639 640 685
		f 4 1465 -1435 -1448 1466
		mu 0 4 661 642 641 778
		f 4 -1466 1467 1468 -1433
		mu 0 4 642 661 651 643
		f 4 -1469 1469 -845 -1440
		mu 0 4 643 651 645 644
		f 4 -942 -1470 1470 1471
		mu 0 4 647 645 651 653
		f 4 -851 1472 1473 1474
		mu 0 4 648 646 652 655
		f 4 -858 -1472 1475 -1473
		mu 0 4 646 647 653 652
		f 4 -862 -1475 1476 1477
		mu 0 4 650 648 655 659
		f 4 -959 -867 -1478 1478
		mu 0 4 658 649 650 659
		f 4 -1471 -1468 1479 1480
		mu 0 4 653 651 661 654
		f 4 -1474 1481 1482 1483
		mu 0 4 655 652 679 656
		f 4 -1476 -1481 -982 -1482
		mu 0 4 652 653 654 679
		f 4 -1477 -1484 -984 1484
		mu 0 4 659 655 656 660
		f 4 -964 -1479 -1485 -989
		mu 0 4 657 658 659 660
		f 4 -1480 -1467 1485 -979
		mu 0 4 654 661 778 777
		f 4 -1400 -1388 -1370 -1385
		mu 0 4 662 663 664 665
		f 4 -926 1486 -40 -922
		mu 0 4 666 667 668 669
		f 4 -1321 -1333 1487 -1287
		mu 0 4 554 670 672 671
		f 4 -1488 -1282 1488 -1290
		mu 0 4 671 672 674 673
		f 4 -1489 -1294 1489 -1301
		mu 0 4 673 674 676 675
		f 4 -1490 -1296 1490 -1305
		mu 0 4 675 676 743 745
		f 4 -41 -1299 1491 -1293
		mu 0 4 677 678 746 744
		f 4 -985 -1483 -981 1492
		mu 0 4 680 656 679 695
		f 4 -1493 1493 -1143 -1351
		mu 0 4 680 695 760 681
		f 4 -1464 -1460 -1465 1494
		mu 0 4 684 682 683 685
		f 4 -972 -1495 -970 1495
		mu 0 4 686 684 685 687
		f 4 -998 -1496 -995 1496
		mu 0 4 688 686 687 689
		f 4 -1497 -1000 1497 -1001
		mu 0 4 688 689 730 729
		f 4 -1309 -1332 -1326 -1314
		mu 0 4 690 691 692 693
		f 4 1498 -1350 -1142 -1083
		mu 0 4 706 709 681 694
		f 4 -980 -1254 1499 -1494
		mu 0 4 695 696 762 760
		f 4 -899 1500 1501 -908
		mu 0 4 697 700 699 698
		f 4 -1501 -907 -42 1502
		mu 0 4 699 700 701 702
		f 4 -1217 -1213 -1186 -1244
		mu 0 4 703 512 704 705
		f 4 -1079 -1031 -1249 -1499
		mu 0 4 706 707 708 709
		f 4 -1132 -1241 -1250 1503
		mu 0 4 712 710 719 713
		f 4 -1129 -1504 -1252 -1201
		mu 0 4 711 712 713 714
		f 4 1504 -1162 -1236 -1205
		mu 0 4 721 718 715 716
		f 4 -1179 -1505 1505 -1239
		mu 0 4 717 718 721 720
		f 4 -1240 -1506 -1237 -1251
		mu 0 4 719 720 721 722
		f 4 -1355 -870 -1356 1506
		mu 0 4 725 723 724 726
		f 4 -1507 -948 -43 -1353
		mu 0 4 725 726 727 728
		f 4 -1257 -1498 -1255 1507
		mu 0 4 731 729 730 732
		f 4 -1261 -1508 -1259 1508
		mu 0 4 733 731 732 734
		f 4 -1265 -1509 -1263 1509
		mu 0 4 735 733 734 736
		f 4 -1269 -1510 -1267 1510
		mu 0 4 737 735 736 738
		f 4 -1273 -1511 -1271 1511
		mu 0 4 739 737 738 740
		f 4 -1277 -1512 -1275 1512
		mu 0 4 741 739 740 742
		f 4 -1297 -1513 -1292 1513
		mu 0 4 743 741 742 744
		f 4 -1491 -1514 -1492 1514
		mu 0 4 745 743 744 746
		f 4 -1306 -1515 -1298 1515
		mu 0 4 748 745 746 749
		f 4 -1346 -1516 -1341 1516
		mu 0 4 747 748 749 750
		f 4 -831 1517 1518 1519
		mu 0 4 752 751 758 757
		f 4 -834 1520 1521 -1518
		mu 0 4 751 753 755 758
		f 4 -835 -1520 1522 1523
		mu 0 4 754 752 757 756
		f 4 -837 -1524 1524 -1521
		mu 0 4 753 754 756 755
		f 4 -1525 -1523 -1519 -1522
		mu 0 4 755 756 757 758
		f 4 1525 -1120 -1144 -1500
		mu 0 4 762 761 759 760
		f 4 -1526 -1253 -1146 -1181
		mu 0 4 761 762 763 764
		f 4 -1407 -1390 1526 -1395
		mu 0 4 765 766 769 768
		f 4 -1413 -1527 -1425 1527
		mu 0 4 767 768 769 770
		f 4 -44 -1458 1528 -1450
		mu 0 4 771 772 774 773
		f 4 -1529 -1453 1529 -1445
		mu 0 4 773 774 776 775
		f 4 -1530 -1456 -1486 -1447
		mu 0 4 775 776 777 778
		f 4 -1320 -993 -1245 1530
		mu 0 4 782 779 780 783
		f 4 -1334 -1531 -1316 -1281
		mu 0 4 781 782 783 784;
	setAttr ".cd" -type "dataPolyComponent" Index_Data Edge 0 ;
	setAttr ".cvd" -type "dataPolyComponent" Index_Data Vertex 0 ;
	setAttr ".hfd" -type "dataPolyComponent" Index_Data Face 0 ;
createNode transform -n "guidesGroup";
createNode transform -n "eyeLidMainLocators" -p "guidesGroup";
createNode transform -n "eyeLidMainLoc0" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.063673800000000003 0.572111 0.32262 ;
createNode locator -n "eyeLidMainLocShape0" -p "eyeLidMainLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainLoc1" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.071985499999999994 0.582279 0.325427 ;
createNode locator -n "eyeLidMainLocShape1" -p "eyeLidMainLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainLoc2" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.122595 0.599661 0.328168 ;
createNode locator -n "eyeLidMainLocShape2" -p "eyeLidMainLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainLoc3" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.164921 0.589378 0.304449 ;
createNode locator -n "eyeLidMainLocShape3" -p "eyeLidMainLoc3";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainLoc4" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.167826 0.581051 0.293385 ;
createNode locator -n "eyeLidMainLocShape4" -p "eyeLidMainLoc4";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainLoc5" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.153378 0.572225 0.311331 ;
createNode locator -n "eyeLidMainLocShape5" -p "eyeLidMainLoc5";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainLoc6" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.112188 0.566176 0.327166 ;
createNode locator -n "eyeLidMainLocShape6" -p "eyeLidMainLoc6";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainLoc7" -p "eyeLidMainLocators";
	setAttr ".t" -type "double3" -0.071801100000000007 0.570494 0.324017 ;
createNode locator -n "eyeLidMainLocShape7" -p "eyeLidMainLoc7";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeLidMainCurve" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "eyeLidMainCurveShape" -p "eyeLidMainCurve";
	setAttr -k off ".v";
	setAttr -s 9 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 8 0 no 3
		9 0 1 2 3 4 5 6 7 8
		9
		-0.063673800000000003 0.57211100000000004 0.32262000000000002
		-0.071985499999999994 0.58227899999999999 0.32542700000000002
		-0.122595 0.599661 0.32816800000000002
		-0.16492100000000001 0.58937799999999996 0.30444900000000003
		-0.167826 0.58105099999999998 0.29338500000000001
		-0.15337799999999999 0.57222499999999998 0.31133100000000002
		-0.112188 0.56617600000000001 0.32716600000000001
		-0.071801100000000007 0.57049399999999995 0.324017
		-0.063673800000000003 0.57211100000000004 0.32262000000000002
		;
createNode transform -n "EyeBrowMainLocators" -p "guidesGroup";
createNode transform -n "eyeBrowMainLoc0" -p "EyeBrowMainLocators";
	setAttr ".t" -type "double3" -0.0389486 0.643984 0.386841 ;
createNode locator -n "eyeBrowMainLocShape0" -p "eyeBrowMainLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeBrowMainLoc1" -p "EyeBrowMainLocators";
	setAttr ".t" -type "double3" -0.136044 0.674225 0.363292 ;
createNode locator -n "eyeBrowMainLocShape1" -p "eyeBrowMainLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeBrowMainLoc2" -p "EyeBrowMainLocators";
	setAttr ".t" -type "double3" -0.210569 0.633204 0.286777 ;
createNode locator -n "eyeBrowMainLocShape2" -p "eyeBrowMainLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "eyeBrowMainLoc3" -p "EyeBrowMainLocators";
	setAttr ".t" -type "double3" -0.23541 0.645987 0.217391 ;
createNode locator -n "eyeBrowMainLocShape3" -p "eyeBrowMainLoc3";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "EyeBrowMainCurve2" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "EyeBrowMainCurve2Shape" -p "EyeBrowMainCurve2";
	setAttr -k off ".v";
	setAttr -s 4 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 3 0 no 3
		4 0 1 2 3
		4
		-0.0389486 0.643984 0.38684099999999999
		-0.136044 0.67422499999999996 0.363292
		-0.21056900000000001 0.63320399999999999 0.286777
		-0.23541000000000001 0.64598699999999998 0.217391
		;
createNode transform -n "lipMainLoc0" -p "guidesGroup";
	setAttr ".t" -type "double3" -0.081588400000000005 0.328094 0.344876 ;
createNode locator -n "lipMainLocShape0" -p "lipMainLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "upperLipMainLocators" -p "guidesGroup";
createNode transform -n "upperLipMainLoc0" -p "upperLipMainLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.355828 0.393205 ;
	setAttr -l on ".tx";
createNode locator -n "upperLipMainLocShape0" -p "upperLipMainLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "upperLipMainLoc1" -p "upperLipMainLocators";
	setAttr ".t" -type "double3" -0.052854499999999999 0.344604 0.370994 ;
createNode locator -n "upperLipMainLocShape1" -p "upperLipMainLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "upperLipMainCurve2" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "upperLipMainCurve2Shape" -p "upperLipMainCurve2";
	setAttr -k off ".v";
	setAttr -s 3 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 2 0 no 3
		3 0 1 2
		3
		-4.3166974595447286e-031 0.35582799999999998 0.39320500000000003
		-0.052854499999999999 0.34460400000000002 0.37099399999999999
		-0.081588400000000005 0.328094 0.34487600000000002
		;
createNode transform -n "lowerLipMainLocators" -p "guidesGroup";
createNode transform -n "lowerLipMainLoc0" -p "lowerLipMainLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.302554 0.393448 ;
	setAttr -l on ".tx";
createNode locator -n "lowerLipMainLocShape0" -p "lowerLipMainLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "lowerLipMainLoc1" -p "lowerLipMainLocators";
	setAttr ".t" -type "double3" -0.048088400000000003 0.314567 0.37755 ;
createNode locator -n "lowerLipMainLocShape1" -p "lowerLipMainLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "lowerLipMainCurve2" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "lowerLipMainCurve2Shape" -p "lowerLipMainCurve2";
	setAttr -k off ".v";
	setAttr -s 3 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 2 0 no 3
		3 2 3 4
		3
		-4.3166974595447286e-031 0.30255399999999999 0.39344800000000002
		-0.048088400000000003 0.31456699999999999 0.37755
		-0.081588400000000005 0.328094 0.34487600000000002
		;
createNode transform -n "JawMidLocators" -p "guidesGroup";
createNode transform -n "JawMidLoc1" -p "JawMidLocators";
	setAttr ".t" -type "double3" -0.17903 0.370618 0.278368 ;
createNode locator -n "JawMidLocShape1" -p "JawMidLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "JawMidLoc2" -p "JawMidLocators";
	setAttr ".t" -type "double3" -0.249933 0.492695 0.064690399999999995 ;
createNode locator -n "JawMidLocShape2" -p "JawMidLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "JawCurveMid" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "JawCurveMidShape" -p "JawCurveMid";
	setAttr -k off ".v";
	setAttr -s 3 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 2 0 no 3
		3 0 1 2
		3
		-0.081588400000000005 0.328094 0.34487600000000002
		-0.17902999999999999 0.370618 0.278368
		-0.24993299999999999 0.49269499999999999 0.064690399999999995
		;
createNode transform -n "earLocators" -p "guidesGroup";
createNode transform -n "EarLoc0" -p "earLocators";
	setAttr ".t" -type "double3" -0.298596 0.608014 0.016516900000000001 ;
createNode locator -n "EarLocShape0" -p "EarLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "EarLoc1" -p "earLocators";
	setAttr ".t" -type "double3" -0.321766 0.6272 -0.043368200000000003 ;
createNode locator -n "EarLocShape1" -p "EarLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "EarLoc2" -p "earLocators";
	setAttr ".t" -type "double3" -0.310416 0.552177 -0.057814 ;
createNode locator -n "EarLocShape2" -p "EarLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "EarLoc3" -p "earLocators";
	setAttr ".t" -type "double3" -0.283426 0.491894 -0.0282525 ;
createNode locator -n "EarLocShape3" -p "EarLoc3";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "EarLoc4" -p "earLocators";
	setAttr ".t" -type "double3" -0.272473 0.468755 -0.0012708000000000001 ;
createNode locator -n "EarLocShape4" -p "EarLoc4";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "EarLoc5" -p "earLocators";
	setAttr ".t" -type "double3" -0.26424 0.479167 0.032845699999999999 ;
createNode locator -n "EarLocShape5" -p "EarLoc5";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "EarCurve" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "EarCurveShape" -p "EarCurve";
	setAttr -k off ".v";
	setAttr -s 6 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 5 0 no 3
		6 0 1 2 3 4 5
		6
		-0.29859599999999997 0.60801400000000005 0.016516900000000001
		-0.321766 0.62719999999999998 -0.043368200000000003
		-0.31041600000000003 0.55217700000000003 -0.057813999999999997
		-0.28342600000000001 0.491894 -0.0282525
		-0.27247300000000002 0.46875499999999998 -0.0012708000000000001
		-0.26423999999999997 0.47916700000000001 0.032845699999999999
		;
createNode transform -n "HeadProfileSideLocators" -p "guidesGroup";
createNode transform -n "HeadProfileSideLoc0" -p "HeadProfileSideLocators";
	setAttr ".t" -type "double3" 0 0.880152 0.323072 ;
	setAttr -l on ".tx";
createNode locator -n "HeadProfileSideLoc0Shape" -p "HeadProfileSideLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideLoc1" -p "HeadProfileSideLocators";
	setAttr ".t" -type "double3" 0 0.99209 0.129988 ;
	setAttr -l on ".tx";
createNode locator -n "HeadProfileSideLoc1Shape" -p "HeadProfileSideLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideLoc2" -p "HeadProfileSideLocators";
	setAttr ".t" -type "double3" 0 0.959558 -0.142109 ;
	setAttr -l on ".tx";
createNode locator -n "HeadProfileSideLoc2Shape" -p "HeadProfileSideLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideLoc3" -p "HeadProfileSideLocators";
	setAttr ".t" -type "double3" 0 0.748149 -0.317261 ;
	setAttr -l on ".tx";
createNode locator -n "HeadProfileSideLoc3Shape" -p "HeadProfileSideLoc3";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideLoc4" -p "HeadProfileSideLocators";
	setAttr ".t" -type "double3" 0 0.51268099107915721 -0.316899 ;
	setAttr -l on ".tx";
createNode locator -n "HeadProfileSideLoc4Shape" -p "HeadProfileSideLoc4";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideLoc5" -p "HeadProfileSideLocators";
	setAttr ".t" -type "double3" 0 0.276872 -0.252859 ;
	setAttr -l on ".tx";
createNode locator -n "HeadProfileSideLoc5Shape" -p "HeadProfileSideLoc5";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideLoc6" -p "HeadProfileSideLocators";
	setAttr ".ove" yes;
	setAttr ".ovc" 24;
	setAttr ".t" -type "double3" 0 0.039238200000000001 -0.275199 ;
	setAttr -l on ".tx";
createNode locator -n "HeadProfileSideLoc6Shape" -p "HeadProfileSideLoc6";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileSideCurve" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "HeadProfileSideCurveShape" -p "HeadProfileSideCurve";
	setAttr -k off ".v";
	setAttr -s 14 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 6 0 no 3
		7 10 11 12 13 14 15 16
		7
		0 0.88015200000000005 0.32307200000000003
		0 0.99209000000000003 0.12998799999999999
		0 0.95955800000000002 -0.14210900000000001
		0 0.74814899999999995 -0.31726100000000002
		0 0.51268099107915721 -0.31689899999999999
		0 0.27687200000000001 -0.252859
		0 0.039238200000000001 -0.27519900000000003
		;
createNode transform -n "HeadProfileFrontLocators" -p "guidesGroup";
createNode transform -n "HeadProfileFrontLoc0" -p "HeadProfileFrontLocators";
	setAttr ".ove" yes;
	setAttr ".ovc" 24;
	setAttr ".t" -type "double3" -0.366599 0.073796200000000006 -0.103132 ;
createNode locator -n "HeadProfileFrontLocShape0" -p "HeadProfileFrontLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileFrontLoc1" -p "HeadProfileFrontLocators";
	setAttr ".t" -type "double3" -0.226817 0.22168673178114931 -0.08946714831203019 ;
createNode locator -n "HeadProfileFrontLocShape1" -p "HeadProfileFrontLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileFrontLoc2" -p "HeadProfileFrontLocators";
	setAttr ".t" -type "double3" -0.241509 0.416058 -0.089205183702067578 ;
createNode locator -n "HeadProfileFrontLocShape2" -p "HeadProfileFrontLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileFrontLoc3" -p "HeadProfileFrontLocators";
	setAttr ".t" -type "double3" -0.294712 0.71477004470314176 -0.087789228405209349 ;
createNode locator -n "HeadProfileFrontLocShape3" -p "HeadProfileFrontLoc3";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileFrontLoc4" -p "HeadProfileFrontLocators";
	setAttr ".t" -type "double3" -0.22774228320224379 0.89040590633812544 -0.066549898952336414 ;
createNode locator -n "HeadProfileFrontLocShape4" -p "HeadProfileFrontLoc4";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileFrontLoc5" -p "HeadProfileFrontLocators";
	setAttr ".t" -type "double3" 0.00016708899388481746 1.0022645921982931 0 ;
createNode locator -n "HeadProfileFrontLocShape5" -p "HeadProfileFrontLoc5";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "HeadProfileFrontCurve" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "HeadProfileFrontCurveShape" -p "HeadProfileFrontCurve";
	setAttr -k off ".v";
	setAttr -s 6 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 5 0 no 3
		6 0 1 2 3 4 5
		6
		-0.36659900000000001 0.073796200000000006 -0.103132
		-0.22681699999999999 0.22168673178114931 -0.08946714831203019
		-0.241509 0.41605799999999998 -0.089205183702067578
		-0.29471199999999997 0.71477004470314176 -0.087789228405209349
		-0.22774228320224379 0.89040590633812544 -0.066549898952336414
		0.00016708899388481746 1.0022645921982931 0
		;
createNode transform -n "noseProfileLocators" -p "guidesGroup";
createNode transform -n "noseProfileLoc0" -p "noseProfileLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.58714308785041625 0.38346369775815675 ;
	setAttr -l on ".tx";
createNode locator -n "noseProfileLocShape0" -p "noseProfileLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "noseProfileLoc1" -p "noseProfileLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.51690531051559518 0.41189327429844147 ;
	setAttr -l on ".tx";
createNode locator -n "noseProfileLocShape1" -p "noseProfileLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "noseProfileLoc2" -p "noseProfileLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.45753766538735352 0.45035681902941488 ;
	setAttr -l on ".tx";
createNode locator -n "noseProfileLocShape2" -p "noseProfileLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "noseProfileLoc3" -p "noseProfileLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.42325494073583364 0.45035681902941493 ;
	setAttr -l on ".tx";
createNode locator -n "noseProfileLocShape3" -p "noseProfileLoc3";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "noseProfileLoc4" -p "noseProfileLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.404023 0.400187 ;
	setAttr -l on ".tx";
createNode locator -n "noseProfileLocShape4" -p "noseProfileLoc4";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "noseProfileCurve" -p "guidesGroup";
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".ty";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr -l on -k off ".sx";
	setAttr -l on -k off ".sy";
	setAttr -l on -k off ".sz";
	setAttr ".it" no;
createNode nurbsCurve -n "noseProfileCurveShape" -p "noseProfileCurve";
	setAttr -k off ".v";
	setAttr -s 5 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 4 0 no 3
		5 0 1 2 3 4
		5
		-4.3166974595447286e-031 0.58714308785041625 0.38346369775815675
		-4.3166974595447286e-031 0.51690531051559518 0.41189327429844147
		-4.3166974595447286e-031 0.45753766538735352 0.45035681902941488
		-4.3166974595447286e-031 0.42325494073583364 0.45035681902941493
		-4.3166974595447286e-031 0.40402300000000002 0.40018700000000001
		;
createNode transform -n "chinProfileLocators" -p "guidesGroup";
createNode transform -n "chinProfileLoc0" -p "chinProfileLocators";
	setAttr ".ove" yes;
	setAttr ".ovc" 24;
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.00101735 0.164808 ;
	setAttr -l on ".tx";
createNode locator -n "chinProfileLocShape0" -p "chinProfileLoc0";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "chinProfileLoc1" -p "chinProfileLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.14812 0.228816 ;
	setAttr -l on ".tx";
createNode locator -n "chinProfileLocShape1" -p "chinProfileLoc1";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "chinProfileLoc2" -p "chinProfileLocators";
	setAttr ".t" -type "double3" -4.3166974595447286e-031 0.193064 0.3763 ;
	setAttr -l on ".tx";
createNode locator -n "chinProfileLocShape2" -p "chinProfileLoc2";
	setAttr -k off ".v";
	setAttr ".los" -type "double3" 0.02 0.02 0.02 ;
createNode transform -n "chinProfileCurve" -p "guidesGroup";
	setAttr ".it" no;
createNode nurbsCurve -n "chinProfileCurveShape" -p "chinProfileCurve";
	setAttr -k off ".v";
	setAttr -s 3 ".cp";
	setAttr ".cc" -type "nurbsCurve" 
		1 2 0 no 3
		3 0 1 2
		3
		-4.3166974595447286e-031 0.00101735 0.16480800000000001
		-4.3166974595447286e-031 0.14812 0.22881599999999999
		-4.3166974595447286e-031 0.19306400000000001 0.37630000000000002
		;
createNode transform -n "FaceGroup";
	setAttr -l on ".it" no;
createNode transform -n "FaceFitSkeleton" -p "FaceGroup";
	addAttr -ci true -sn "Geometry" -ln "Geometry" -dt "string";
	addAttr -ci true -sn "AllFaceGeo" -ln "AllFaceGeo" -dt "string";
	addAttr -ci true -sn "Eye_R" -ln "Eye_R" -dt "string";
	addAttr -ci true -sn "Eye_L" -ln "Eye_L" -dt "string";
	addAttr -ci true -sn "UpperTeeth" -ln "UpperTeeth" -dt "string";
	addAttr -ci true -sn "LowerTeeth" -ln "LowerTeeth" -dt "string";
	addAttr -ci true -sn "Tongue" -ln "Tongue" -dt "string";
	addAttr -ci true -sn "HeadJoint" -ln "HeadJoint" -dt "string";
	addAttr -ci true -sn "SkinCluster" -ln "SkinCluster" -dt "string";
	addAttr -ci true -sn "Pupil_R" -ln "Pupil_R" -dt "string";
	addAttr -ci true -sn "Pupil_L" -ln "Pupil_L" -dt "string";
	addAttr -ci true -sn "Iris_R" -ln "Iris_R" -dt "string";
	addAttr -ci true -sn "Iris_L" -ln "Iris_L" -dt "string";
	setAttr ".t" -type "double3" 0 0.19142804567901181 0 ;
	setAttr -l on -k off ".tx";
	setAttr -l on -k off ".tz";
	setAttr -l on -k off ".rx";
	setAttr -l on -k off ".ry";
	setAttr -l on -k off ".rz";
	setAttr ".s" -type "double3" 0.5373787352311199 0.5373787352311199 0.5373787352311199 ;
	setAttr -k off ".sx";
	setAttr ".Geometry" -type "string" "headTopology";
	setAttr ".AllFaceGeo" -type "string" "headTopology";
	setAttr ".Eye_R" -type "string" "";
	setAttr ".Eye_L" -type "string" "";
	setAttr ".UpperTeeth" -type "string" "";
	setAttr ".LowerTeeth" -type "string" "";
	setAttr ".Tongue" -type "string" "";
	setAttr ".HeadJoint" -type "string" "Head_M";
	setAttr ".SkinCluster" -type "string" "skinCluster1";
	setAttr ".Pupil_R" -type "string" "eyeball_R.e[942] eyeball_R.e[944] eyeball_R.e[946] eyeball_R.e[948] eyeball_R.e[950] eyeball_R.e[952] eyeball_R.e[954] eyeball_R.e[956] eyeball_R.e[958] eyeball_R.e[960] eyeball_R.e[962] eyeball_R.e[964] eyeball_R.e[966] eyeball_R.e[968] eyeball_R.e[970] eyeball_R.e[972] eyeball_R.e[974] eyeball_R.e[976] eyeball_R.e[978:979]";
	setAttr ".Pupil_L" -type "string" "eyeball_L.e[942] eyeball_L.e[944] eyeball_L.e[946] eyeball_L.e[948] eyeball_L.e[950] eyeball_L.e[952] eyeball_L.e[954] eyeball_L.e[956] eyeball_L.e[958] eyeball_L.e[960] eyeball_L.e[962] eyeball_L.e[964] eyeball_L.e[966] eyeball_L.e[968] eyeball_L.e[970] eyeball_L.e[972] eyeball_L.e[974] eyeball_L.e[976] eyeball_L.e[978:979]";
	setAttr ".Iris_R" -type "string" "eyeball_R.e[862] eyeball_R.e[864] eyeball_R.e[866] eyeball_R.e[868] eyeball_R.e[870] eyeball_R.e[872] eyeball_R.e[874] eyeball_R.e[876] eyeball_R.e[878] eyeball_R.e[880] eyeball_R.e[882] eyeball_R.e[884] eyeball_R.e[886] eyeball_R.e[888] eyeball_R.e[890] eyeball_R.e[892] eyeball_R.e[894] eyeball_R.e[896] eyeball_R.e[898:899]";
	setAttr ".Iris_L" -type "string" "eyeball_L.e[862] eyeball_L.e[864] eyeball_L.e[866] eyeball_L.e[868] eyeball_L.e[870] eyeball_L.e[872] eyeball_L.e[874] eyeball_L.e[876] eyeball_L.e[878] eyeball_L.e[880] eyeball_L.e[882] eyeball_L.e[884] eyeball_L.e[886] eyeball_L.e[888] eyeball_L.e[890] eyeball_L.e[892] eyeball_L.e[894] eyeball_L.e[896] eyeball_L.e[898:899]";
createNode nurbsCurve -n "FaceFitSkeletonShape" -p "FaceFitSkeleton";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 13;
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.39180581244561252 2.3991186704942341e-017 -0.39180581244561191
		-6.321585303914663e-017 3.3928661615554567e-017 -0.55409709377719396
		-0.39180581244561213 2.3991186704942356e-017 -0.39180581244561213
		-0.55409709377719396 9.8316773080939295e-033 -1.605634753618615e-016
		-0.39180581244561224 -2.3991186704942347e-017 0.39180581244561202
		-1.6696026817952597e-016 -3.3928661615554573e-017 0.55409709377719407
		0.39180581244561191 -2.399118670494236e-017 0.39180581244561219
		0.55409709377719396 -1.8223150339523961e-032 2.9760662996402926e-016
		0.39180581244561252 2.3991186704942341e-017 -0.39180581244561191
		-6.321585303914663e-017 3.3928661615554567e-017 -0.55409709377719396
		-0.39180581244561213 2.3991186704942356e-017 -0.39180581244561213
		;
createNode nurbsCurve -n "FaceFitSkeletonHeightShape" -p "FaceFitSkeleton";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 13;
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.39180581244561252 1.5 -0.39180581244561191
		-6.321585303914663e-017 1.5 -0.55409709377719396
		-0.39180581244561213 1.5 -0.39180581244561213
		-0.55409709377719396 1.5 -1.605634753618615e-016
		-0.39180581244561224 1.5 0.39180581244561202
		-1.6696026817952597e-016 1.5 0.55409709377719407
		0.39180581244561191 1.5 0.39180581244561219
		0.55409709377719396 1.5 2.9760662996402926e-016
		0.39180581244561252 1.5 -0.39180581244561191
		-6.321585303914663e-017 1.5 -0.55409709377719396
		-0.39180581244561213 1.5 -0.39180581244561213
		;
createNode transform -n "FaceFitEyeLidOuter" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr -k on ".radius";
	setAttr ".selection" -type "string" "headTopology.e[1356] headTopology.e[1362] headTopology.e[1364] headTopology.e[1368] headTopology.e[1373] headTopology.e[1375] headTopology.e[1404] headTopology.e[1408] ";
createNode transform -n "FaceFitEyeLidOuterGeo" -p "FaceFitEyeLidOuter";
	setAttr ".it" no;
createNode transform -n "upperEyeLidCylinderOuter" -p "FaceFitEyeLidOuterGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "upperEyeLidCylinderOuterShape" -p "upperEyeLidCylinderOuter";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "lowerEyeLidCylinderOuter" -p "FaceFitEyeLidOuterGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "lowerEyeLidCylinderOuterShape" -p "lowerEyeLidCylinderOuter";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "FaceFitEyeLidOuterCurve" -p "FaceFitEyeLidOuter";
	setAttr ".it" no;
createNode transform -n "upperEyeLidOuterCurve" -p "FaceFitEyeLidOuterCurve";
createNode nurbsCurve -n "upperEyeLidOuterCurveShape" -p "upperEyeLidOuterCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 14;
	setAttr ".cc" -type "nurbsCurve" 
		1 4 0 no 3
		5 0 1 2 3 4
		5
		-0.047705858949999999 0.57518094779999995 0.33617526289999999
		-0.065094999969999998 0.59355473520000002 0.3436653912
		-0.1272868365 0.60905581710000001 0.34060919280000002
		-0.17767348890000001 0.59468269350000003 0.3051060438
		-0.18132105470000001 0.57163345809999999 0.28221645950000002
		;
createNode transform -n "lowerEyeLidOuterCurve" -p "FaceFitEyeLidOuterCurve";
createNode nurbsCurve -n "lowerEyeLidOuterCurveShape" -p "lowerEyeLidOuterCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 14;
	setAttr ".cc" -type "nurbsCurve" 
		1 4 0 no 3
		5 0 1 2 3 4
		5
		-0.047705858949999999 0.57518094779999995 0.33617526289999999
		-0.064170196649999997 0.55377560849999996 0.33563226460000001
		-0.1145142764 0.53756439690000002 0.33019655939999998
		-0.16078074279999999 0.54905676839999995 0.3065632284
		-0.18132105470000001 0.57163345809999999 0.28221645950000002
		;
createNode transform -n "EyeLidProfileOuter" -p "FaceFitEyeLidOuterCurve";
createNode nurbsCurve -n "EyeLidProfileOuterShape" -p "EyeLidProfileOuter";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		-0.0022331999658692079 3.962500737768013e-035 -6.4712547985702658e-019
		-0.0015791108396116837 -9.6692651761466835e-020 0.0015791108396116828
		-6.7290673311121696e-019 -1.3674405950288523e-019 0.0022331999658692083
		0.0015791108396116824 -9.6692651761466883e-020 0.0015791108396116835
		0.0022331999658692079 -7.344550111034955e-035 1.1994560580484947e-018
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		;
createNode transform -n "FaceFitEyeLidOuterLoc" -p "FaceFitEyeLidOuter";
createNode transform -n "FaceFitEyeLidMain" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr -k on ".radius";
	setAttr ".selection" -type "string" "headTopology.e[1377] headTopology.e[1379] headTopology.e[1383] headTopology.e[1385] headTopology.e[1388] headTopology.e[1399] headTopology.e[1406] headTopology.e[1409] ";
createNode transform -n "FaceFitEyeLidMainGeo" -p "FaceFitEyeLidMain";
	setAttr ".it" no;
createNode transform -n "upperEyeLidCylinderMain" -p "FaceFitEyeLidMainGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "upperEyeLidCylinderMainShape" -p "upperEyeLidCylinderMain";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "lowerEyeLidCylinderMain" -p "FaceFitEyeLidMainGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "lowerEyeLidCylinderMainShape" -p "lowerEyeLidCylinderMain";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "FaceFitEyeLidMainCurve" -p "FaceFitEyeLidMain";
	setAttr ".it" no;
createNode transform -n "upperEyeLidMainCurve" -p "FaceFitEyeLidMainCurve";
createNode nurbsCurve -n "upperEyeLidMainCurveShape" -p "upperEyeLidMainCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 13;
	setAttr ".cc" -type "nurbsCurve" 
		1 4 0 no 3
		5 0 1 2 3 4
		5
		-0.063673824069999996 0.57211095089999997 0.32262045150000002
		-0.071985453369999994 0.58227872849999995 0.3254267871
		-0.12259490789999999 0.59966051580000002 0.32816842200000002
		-0.1649205983 0.58937799930000001 0.30444908139999999
		-0.16782601180000001 0.58105087280000001 0.29338517790000002
		;
createNode transform -n "lowerEyeLidMainCurve" -p "FaceFitEyeLidMainCurve";
createNode nurbsCurve -n "lowerEyeLidMainCurveShape" -p "lowerEyeLidMainCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 13;
	setAttr ".cc" -type "nurbsCurve" 
		1 4 0 no 3
		5 0 1 2 3 4
		5
		-0.063673824069999996 0.57211095089999997 0.32262045150000002
		-0.071801148359999997 0.5704940557 0.32401710750000001
		-0.1121879667 0.56617563959999995 0.3271657825
		-0.1533775181 0.57222479579999996 0.31133136150000001
		-0.16782601180000001 0.58105087280000001 0.29338517790000002
		;
createNode transform -n "EyeLidProfileMain" -p "FaceFitEyeLidMainCurve";
createNode nurbsCurve -n "EyeLidProfileMainShape" -p "EyeLidProfileMain";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		-0.0022331999658692079 3.962500737768013e-035 -6.4712547985702658e-019
		-0.0015791108396116837 -9.6692651761466835e-020 0.0015791108396116828
		-6.7290673311121696e-019 -1.3674405950288523e-019 0.0022331999658692083
		0.0015791108396116824 -9.6692651761466883e-020 0.0015791108396116835
		0.0022331999658692079 -7.344550111034955e-035 1.1994560580484947e-018
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		;
createNode transform -n "FaceFitEyeLidMainLoc" -p "FaceFitEyeLidMain";
createNode transform -n "FaceFitEyeLidInner" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr -k on ".radius";
	setAttr ".selection" -type "string" "headTopology.e[1413] headTopology.e[1415] headTopology.e[1416] headTopology.e[1419] headTopology.e[1421] headTopology.e[1422] headTopology.e[1423] headTopology.e[1527] ";
createNode transform -n "FaceFitEyeLidInnerGeo" -p "FaceFitEyeLidInner";
	setAttr ".t" -type "double3" 0 -0.35622556891217699 0 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode transform -n "upperEyeLidCylinderInner" -p "FaceFitEyeLidInnerGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "upperEyeLidCylinderInnerShape" -p "upperEyeLidCylinderInner";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "lowerEyeLidCylinderInner" -p "FaceFitEyeLidInnerGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "lowerEyeLidCylinderInnerShape" -p "lowerEyeLidCylinderInner";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "EyeLidInnerAreaMesh" -p "FaceFitEyeLidInnerGeo";
createNode mesh -n "EyeLidInnerAreaMeshShape" -p "EyeLidInnerAreaMesh";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr -s 39 ".uvst[0].uvsp[0:38]" -type "float2" 0.432401 0.59052598
		 0.46345398 0.57055193 0.46147397 0.56662792 0.43271899 0.58878893 0.46423301 0.55064094
		 0.46900496 0.55967891 0.40940899 0.54826891 0.43803498 0.54018992 0.41125798 0.55856299
		 0.43841898 0.55399996 0.40126094 0.56728148 0.39922398 0.57612592 0.40142101 0.57690698
		 0.43173999 0.58828199 0.431559 0.58672702 0.41286099 0.56063098 0.41100097 0.559255
		 0.43735796 0.55516499 0.43747899 0.55753499 0.460904 0.56528699 0.45675299 0.56962103
		 0.38638496 0.55992103 0.39519197 0.58009595 0.43248698 0.59360296 0.46581796 0.57284492
		 0.47338399 0.56174099 0.46739897 0.54183292 0.43754098 0.52628797 0.40619996 0.53676993
		 0.398891 0.56701303 0.40007201 0.57599193 0.39854699 0.57788599 0.39294598 0.56391603
		 0.46137401 0.556831 0.46478501 0.55893701 0.45475101 0.56248999 0.46018201 0.55881095
		 0.46079996 0.56112295 0.457324 0.56339097;
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr -s 39 ".vt[0:38]"  -0.068479612 0.58760786 0.32846984 -0.05695048 0.57318157 0.32678568
		 -0.067955375 0.56277454 0.32821426 -0.11284734 0.55276489 0.32872784 -0.15662533 0.56174499 0.31042457
		 -0.17389981 0.57695937 0.28878993 -0.17037922 0.59271842 0.30278379 -0.12331248 0.60667473 0.32894325
		 -0.065095 0.59355474 0.34366539 -0.047705859 0.57518095 0.33617526 -0.064170197 0.55377561 0.33563226
		 -0.11451428 0.5375644 0.33019656 -0.16078074 0.54905677 0.30656323 -0.18132105 0.57163346 0.28221646
		 -0.17767349 0.59468269 0.30510604 -0.12728684 0.60905582 0.34060919 -0.063673824 0.57211095 0.32262045
		 -0.071985453 0.58227873 0.32542679 -0.071801148 0.57049406 0.32401711 -0.11218797 0.56617564 0.32716578
		 -0.15337752 0.5722248 0.31133136 -0.16782601 0.58105087 0.29338518 -0.1649206 0.589378 0.30444908
		 -0.12259491 0.59966052 0.32816842 -0.072133712 0.58020723 0.32054892 -0.072154202 0.57137859 0.31913257
		 -0.11038918 0.56785566 0.31866658 -0.15211116 0.57429713 0.30845049 -0.16653222 0.58204073 0.29353672
		 -0.16218472 0.58894384 0.30256999 -0.12091489 0.59834003 0.32143521 -0.069417514 0.57430124 0.3185674
		 -0.10917442 0.56611168 0.31437027 -0.078915223 0.57220519 0.31560636 -0.15005597 0.57293165 0.30411977
		 -0.16121161 0.58953291 0.30038378 -0.11967554 0.6001085 0.31751734 -0.079418175 0.58636129 0.31848225
		 -0.07474263 0.57759666 0.31616253;
	setAttr -s 71 ".ed[0:70]"  9 8 0 8 0 1 0 1 1 1 9 1 2 3 1 3 11 1 11 10 0
		 10 2 1 13 12 0 12 4 1 4 5 1 5 13 1 14 13 0 5 6 1 6 14 1 6 7 1 7 15 1 15 14 0 7 0 1
		 8 15 0 0 17 1 17 16 1 16 1 1 18 19 1 19 3 1 2 18 1 4 20 1 20 21 1 21 5 1 22 23 1
		 23 7 1 6 22 1 23 17 1 31 16 1 17 24 1 24 31 1 25 26 1 26 19 1 18 25 1 27 28 1 28 21 1
		 20 27 1 29 22 1 22 21 1 28 29 1 29 30 1 30 23 1 30 24 1 10 9 0 1 2 1 16 18 1 3 4 1
		 12 11 0 19 20 1 26 27 1 32 26 1 25 33 1 33 32 0 27 34 1 34 28 0 28 35 0 35 29 1 36 30 1
		 35 36 0 37 24 1 36 37 0 32 34 0 37 38 0 38 31 1 31 25 1 38 33 0;
	setAttr -s 32 ".fc[0:31]" -type "polyFaces" 
		f 4 0 1 2 3
		mu 0 4 25 24 1 5
		f 4 4 5 6 7
		mu 0 4 4 7 27 26
		f 4 8 9 10 11
		mu 0 4 21 28 6 32
		f 4 12 -12 13 14
		mu 0 4 22 21 32 31
		f 4 15 16 17 -15
		mu 0 4 31 0 23 22
		f 4 18 -2 19 -17
		mu 0 4 0 1 24 23
		f 4 -3 20 21 22
		mu 0 4 5 1 2 34
		f 4 23 24 -5 25
		mu 0 4 33 9 7 4
		f 4 26 27 28 -11
		mu 0 4 6 8 29 32
		f 4 29 30 -16 31
		mu 0 4 30 3 0 31
		f 4 -31 32 -21 -19
		mu 0 4 0 3 2 1
		f 4 33 -22 34 35
		mu 0 4 37 34 2 19
		f 4 36 37 -24 38
		mu 0 4 36 17 9 33
		f 4 39 40 -28 41
		mu 0 4 16 10 29 8
		f 4 42 43 -41 44
		mu 0 4 11 30 29 10
		f 4 45 46 -30 -43
		mu 0 4 11 13 3 30
		f 4 47 -35 -33 -47
		mu 0 4 13 19 2 3
		f 4 48 -4 49 -8
		mu 0 4 26 25 5 4
		f 4 -50 -23 50 -26
		mu 0 4 4 5 34 33
		f 4 51 -10 52 -6
		mu 0 4 7 6 28 27
		f 4 53 -27 -52 -25
		mu 0 4 9 8 6 7
		f 4 54 -42 -54 -38
		mu 0 4 17 16 8 9
		f 4 55 -37 56 57
		mu 0 4 18 17 36 35
		f 3 -40 58 59
		mu 0 3 10 16 15
		f 3 60 61 -45
		mu 0 3 10 12 11
		f 4 62 -46 -62 63
		mu 0 4 14 13 11 12
		f 4 64 -48 -63 65
		mu 0 4 20 19 13 14
		f 4 -59 -55 -56 66
		mu 0 4 15 16 17 18
		f 4 -65 67 68 -36
		mu 0 4 19 20 38 37
		f 4 -44 -32 -14 -29
		mu 0 4 29 30 31 32
		f 4 -51 -34 69 -39
		mu 0 4 33 34 37 36
		f 4 -57 -70 -69 70
		mu 0 4 35 36 37 38;
	setAttr ".cd" -type "dataPolyComponent" Index_Data Edge 0 ;
	setAttr ".cvd" -type "dataPolyComponent" Index_Data Vertex 0 ;
	setAttr ".hfd" -type "dataPolyComponent" Index_Data Face 0 ;
createNode transform -n "FaceFitEyeLidInnerCurve" -p "FaceFitEyeLidInner";
	setAttr ".it" no;
createNode transform -n "upperEyeLidInnerCurve" -p "FaceFitEyeLidInnerCurve";
createNode nurbsCurve -n "upperEyeLidInnerCurveShape" -p "upperEyeLidInnerCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 15;
	setAttr ".cc" -type "nurbsCurve" 
		1 4 0 no 3
		5 0 1 2 3 4
		5
		-0.07474263012 0.57759666440000001 0.31616252659999999
		-0.079418174920000001 0.58636128899999995 0.31848225000000002
		-0.1196755394 0.60010850429999996 0.3175173402
		-0.16121160979999999 0.5895329118 0.30038377640000002
		-0.1665322185 0.58204072709999999 0.29353672269999997
		;
createNode transform -n "lowerEyeLidInnerCurve" -p "FaceFitEyeLidInnerCurve";
createNode nurbsCurve -n "lowerEyeLidInnerCurveShape" -p "lowerEyeLidInnerCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 15;
	setAttr ".cc" -type "nurbsCurve" 
		1 4 0 no 3
		5 0 1 2 3 4
		5
		-0.07474263012 0.57759666440000001 0.31616252659999999
		-0.078915223480000005 0.57220518590000002 0.31560635570000001
		-0.1091744229 0.56611168379999999 0.31437027449999999
		-0.15005597470000001 0.57293164730000001 0.3041197658
		-0.1665322185 0.58204072709999999 0.29353672269999997
		;
createNode transform -n "EyeLidProfileInner" -p "FaceFitEyeLidInnerCurve";
createNode nurbsCurve -n "EyeLidProfileInnerShape" -p "EyeLidProfileInner";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		-0.0022331999658692079 3.962500737768013e-035 -6.4712547985702658e-019
		-0.0015791108396116837 -9.6692651761466835e-020 0.0015791108396116828
		-6.7290673311121696e-019 -1.3674405950288523e-019 0.0022331999658692083
		0.0015791108396116824 -9.6692651761466883e-020 0.0015791108396116835
		0.0022331999658692079 -7.344550111034955e-035 1.1994560580484947e-018
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		;
createNode transform -n "FaceFitEyeLidInnerLoc" -p "FaceFitEyeLidInner";
createNode transform -n "FaceFitLipOuter" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr -k on ".radius";
	setAttr ".selection" -type "string" "headTopology.e[130] headTopology.e[131] headTopology.e[136] headTopology.e[137] headTopology.e[142] headTopology.e[143] headTopology.e[178] headTopology.e[874] headTopology.e[875] headTopology.e[880] headTopology.e[881] headTopology.e[886] headTopology.e[887] headTopology.e[922] ";
createNode transform -n "FaceFitLipOuterGeo" -p "FaceFitLipOuter";
	setAttr ".it" no;
createNode transform -n "upperLipCylinderOuter" -p "FaceFitLipOuterGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "upperLipCylinderOuterShape" -p "upperLipCylinderOuter";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "lowerLipCylinderOuter" -p "FaceFitLipOuterGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "lowerLipCylinderOuterShape" -p "lowerLipCylinderOuter";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "FaceFitLipOuterCurve" -p "FaceFitLipOuter";
	setAttr ".it" no;
createNode transform -n "upperLipOuterCurve" -p "FaceFitLipOuterCurve";
createNode nurbsCurve -n "upperLipOuterCurveShape" -p "upperLipOuterCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 14;
	setAttr ".cc" -type "nurbsCurve" 
		1 4 0 no 3
		5 0 1 2 3 4
		5
		0 0.38187393549999998 0.40226742630000001
		-0.036561824380000003 0.38183665280000001 0.39585435390000001
		-0.076959617440000003 0.36388435959999998 0.36888226870000002
		-0.08954785019 0.34423041339999999 0.3567681909
		-0.096940830350000007 0.33097583060000002 0.34966582060000001
		;
createNode transform -n "lowerLipOuterCurve" -p "FaceFitLipOuterCurve";
createNode nurbsCurve -n "lowerLipOuterCurveShape" -p "lowerLipOuterCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 14;
	setAttr ".cc" -type "nurbsCurve" 
		1 3 0 no 3
		4 0 1 2 3
		4
		0 0.2634415329 0.3953900635
		-0.047330308709999998 0.2773981392 0.3814023137
		-0.089789897199999996 0.31092262269999998 0.3529399037
		-0.096940830350000007 0.33097583060000002 0.34966582060000001
		;
createNode transform -n "LipProfileOuter" -p "FaceFitLipOuterCurve";
createNode nurbsCurve -n "LipProfileOuterShape" -p "LipProfileOuter";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		-0.0022331999658692079 3.962500737768013e-035 -6.4712547985702658e-019
		-0.0015791108396116837 -9.6692651761466835e-020 0.0015791108396116828
		-6.7290673311121696e-019 -1.3674405950288523e-019 0.0022331999658692083
		0.0015791108396116824 -9.6692651761466883e-020 0.0015791108396116835
		0.0022331999658692079 -7.344550111034955e-035 1.1994560580484947e-018
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		;
createNode transform -n "FaceFitLipOuterLoc" -p "FaceFitLipOuter";
createNode transform -n "FaceFitLipMain" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr -k on ".radius";
	setAttr ".selection" -type "string" "headTopology.e[146] headTopology.e[149] headTopology.e[154] headTopology.e[157] headTopology.e[159] headTopology.e[162] headTopology.e[177] headTopology.e[890] headTopology.e[893] headTopology.e[898] headTopology.e[901] headTopology.e[903] headTopology.e[906] headTopology.e[921] ";
createNode transform -n "FaceFitLipMainGeo" -p "FaceFitLipMain";
	setAttr ".it" no;
createNode transform -n "upperLipCylinderMain" -p "FaceFitLipMainGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "upperLipCylinderMainShape" -p "upperLipCylinderMain";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "lowerLipCylinderMain" -p "FaceFitLipMainGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "lowerLipCylinderMainShape" -p "lowerLipCylinderMain";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "FaceFitLipMainCurve" -p "FaceFitLipMain";
	setAttr ".it" no;
createNode transform -n "upperLipMainCurve" -p "FaceFitLipMainCurve";
createNode nurbsCurve -n "upperLipMainCurveShape" -p "upperLipMainCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 13;
	setAttr ".cc" -type "nurbsCurve" 
		1 4 0 no 3
		5 0 1 2 3 4
		5
		0 0.35582759980000001 0.39320519570000001
		-0.034793231639999998 0.35201880340000002 0.38350516559999998
		-0.066325761380000001 0.33907404540000002 0.361661762
		-0.075100854039999998 0.33210116620000002 0.35115513209999999
		-0.081588439639999996 0.32809415460000002 0.34487614039999998
		;
createNode transform -n "lowerLipMainCurve" -p "FaceFitLipMainCurve";
createNode nurbsCurve -n "lowerLipMainCurveShape" -p "lowerLipMainCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 13;
	setAttr ".cc" -type "nurbsCurve" 
		1 3 0 no 3
		4 0 1 2 3
		4
		0 0.30255413060000003 0.39344844220000003
		-0.04808838293 0.31456699970000002 0.37755018470000001
		-0.078047782179999997 0.32678452130000002 0.34799331430000002
		-0.081588439639999996 0.32809415460000002 0.34487614039999998
		;
createNode transform -n "LipProfileMain" -p "FaceFitLipMainCurve";
createNode nurbsCurve -n "LipProfileMainShape" -p "LipProfileMain";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		-0.0022331999658692079 3.962500737768013e-035 -6.4712547985702658e-019
		-0.0015791108396116837 -9.6692651761466835e-020 0.0015791108396116828
		-6.7290673311121696e-019 -1.3674405950288523e-019 0.0022331999658692083
		0.0015791108396116824 -9.6692651761466883e-020 0.0015791108396116835
		0.0022331999658692079 -7.344550111034955e-035 1.1994560580484947e-018
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		;
createNode transform -n "FaceFitLipMainLoc" -p "FaceFitLipMain";
createNode transform -n "FaceFitLipInner" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr -k on ".radius";
	setAttr ".selection" -type "string" "headTopology.e[164] headTopology.e[166] headTopology.e[168] headTopology.e[180] headTopology.e[743] headTopology.e[758] headTopology.e[759] headTopology.e[908] headTopology.e[910] headTopology.e[912] headTopology.e[924] headTopology.e[1486] headTopology.e[1501] headTopology.e[1502] ";
createNode transform -n "FaceFitLipInnerGeo" -p "FaceFitLipInner";
	setAttr ".t" -type "double3" 0 -0.35622556891217699 0 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode transform -n "upperLipCylinderInner" -p "FaceFitLipInnerGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "upperLipCylinderInnerShape" -p "upperLipCylinderInner";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "lowerLipCylinderInner" -p "FaceFitLipInnerGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "lowerLipCylinderInnerShape" -p "lowerLipCylinderInner";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "LipInnerAreaMesh" -p "FaceFitLipInnerGeo";
createNode mesh -n "LipInnerAreaMeshShape" -p "LipInnerAreaMesh";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr -s 70 ".uvst[0].uvsp[0:69]" -type "float2" 0.5494439 0.33521003
		 0.55142099 0.33590496 0.5512867 0.33589867 0.54892689 0.33523396 0.54659361 0.33506688
		 0.5464251 0.33480421 0.54020387 0.335419 0.53824586 0.334562 0.52145696 0.33808997
		 0.54231286 0.33718696 0.54865205 0.33624405 0.55109477 0.33879787 0.55291802 0.336299
		 0.55571479 0.33707881 0.55107468 0.33455294 0.55279785 0.33287087 0.52916408 0.31808296
		 0.52838796 0.32593 0.52105999 0.34470084 0.5442394 0.34017208 0.52145594 0.33298999
		 0.52046323 0.33098567 0.56078899 0.33684099 0.55675739 0.32739735 0.55643493 0.34283608
		 0.55107659 0.35428381 0.52089894 0.36100924 0.52991796 0.30687597 0.52729726 0.33188695
		 0.52773309 0.33058804 0.45025 0.335899 0.45011696 0.33590496 0.45511299 0.334804
		 0.45494297 0.33506697 0.50076866 0.34038401 0.45044297 0.33879799 0.45288596 0.33624399
		 0.44582298 0.33707896 0.44861996 0.336299 0.45046297 0.334553 0.44873998 0.33287096
		 0.50076866 0.33506101 0.48008099 0.33808997 0.46133399 0.335419 0.463292 0.334562
		 0.48047796 0.34470099 0.45729798 0.34017196 0.459225 0.33718696 0.473151 0.32593
		 0.47237498 0.31808296 0.50076866 0.31110397 0.50076854 0.32151696 0.44047797 0.338669
		 0.44390896 0.343916 0.45355198 0.35561201 0.48063898 0.36100897 0.50077379 0.35774356
		 0.50076866 0.300035 0.47161996 0.30687597 0.44478098 0.32739696 0.48008198 0.33298999
		 0.48107502 0.33098501 0.50076914 0.33019707 0.5007689 0.33158004 0.45209298 0.33520997
		 0.452611 0.33523399 0.47424001 0.33188701 0.47380501 0.33058801 0.50076902 0.32669699
		 0.50076962 0.32925004;
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr -s 70 ".vt[0:69]"  0 0.37115821 0.40769431 0 0.36157823 0.40466967
		 0 0.3558276 0.3932052 0 0.38187394 0.40226743 0 0.3557798 0.38707194 0 0.30295268 0.38844943
		 0.09694083 0.33097583 0.34966582 0.088761076 0.32836196 0.34994715 0.08954785 0.34423041 0.35676819
		 0.083521515 0.33672619 0.35824794 0.083950371 0.32093146 0.35324609 0.047481146 0.29328009 0.38684383
		 0.076959617 0.36388436 0.36888227 0.075527743 0.3502228 0.37227532 0 0.27903384 0.40231183
		 0.081003167 0.32468319 0.35137373 0.084762126 0.32770503 0.34826997 0.08158844 0.32809415 0.34487614
		 0.075100854 0.33210117 0.35115513 0.079502627 0.33328584 0.35588881 0.078047782 0.32678452 0.34799331
		 0.072058618 0.3431758 0.36901516 0.037451126 0.35918 0.39458942 0.034793232 0.3520188 0.38350517
		 0.066325761 0.33907405 0.36166176 0.048862189 0.30722502 0.3840853 0.048088383 0.314567 0.37755018
		 0 0.30255413 0.39344844 0 0.29453176 0.3998467 0.089789897 0.31092259 0.3529399 0.047330309 0.27739814 0.38140231
		 0.079916582 0.32855919 0.34220532 0.07246428 0.33227566 0.34748927 0.06239165 0.33864319 0.35624769
		 0.036561824 0.38183665 0.39585435 0.039236777 0.36979458 0.3985039 0.033218391 0.35057461 0.37671068
		 0 0.26344153 0.39539006 0.076116286 0.32707947 0.34541324 0.045682777 0.31454891 0.37244228
		 -0.09694083 0.33097583 0.34966582 -0.088761076 0.32836196 0.34994715 -0.08954785 0.34423041 0.35676819
		 -0.083521515 0.33672619 0.35824794 -0.083950371 0.32093146 0.35324609 -0.047481146 0.29328009 0.38684383
		 -0.076959617 0.36388436 0.36888227 -0.075527743 0.3502228 0.37227532 -0.081003167 0.32468319 0.35137373
		 -0.084762126 0.32770503 0.34826997 -0.08158844 0.32809415 0.34487614 -0.075100854 0.33210117 0.35115513
		 -0.079502627 0.33328584 0.35588881 -0.078047782 0.32678452 0.34799331 -0.072058618 0.3431758 0.36901516
		 -0.037451126 0.35918003 0.39458942 -0.034793232 0.3520188 0.38350517 -0.066325761 0.33907405 0.36166176
		 -0.048862189 0.30722502 0.3840853 -0.048088383 0.314567 0.37755018 -0.089789897 0.31092262 0.3529399
		 -0.047330309 0.27739814 0.38140231 -0.079916582 0.32855919 0.34220532 -0.072464287 0.33227566 0.34748927
		 -0.062391654 0.33864319 0.35624769 -0.036561824 0.38183665 0.39585435 -0.039236777 0.36979458 0.3985039
		 -0.033218391 0.35057461 0.37671068 -0.076116286 0.32707947 0.34541324 -0.045682777 0.31454891 0.37244228;
	setAttr -s 126 ".ed[0:125]"  3 0 1 14 37 1 27 28 1 0 1 1 1 2 1 28 14 1
		 2 4 1 5 27 1 29 10 1 10 7 1 7 6 1 6 29 0 8 6 0 7 9 1 9 8 1 30 11 1 11 10 1 29 30 0
		 3 34 0 34 35 1 35 0 1 9 13 1 13 12 1 12 8 0 30 37 0 14 11 1 15 20 1 20 17 1 17 16 1
		 16 15 1 17 18 1 18 19 1 19 16 1 15 25 1 25 26 1 26 20 1 22 21 1 21 24 1 24 23 1 23 22 1
		 18 24 1 21 19 1 25 28 1 27 26 1 20 38 1 38 31 0 31 17 1 31 32 0 32 18 1 32 33 0 33 24 1
		 35 22 1 22 1 1 9 19 1 21 13 1 7 16 1 10 15 1 11 25 1 23 2 1 34 12 0 13 35 1 33 36 0
		 36 23 1 36 4 0 26 39 1 39 38 0 5 39 0 60 44 1 44 41 1 41 40 1 40 60 0 42 40 0 41 43 1
		 43 42 1 61 45 1 45 44 1 60 61 0 3 65 0 65 66 1 66 0 1 43 47 1 47 46 1 46 42 0 61 37 0
		 14 45 1 48 53 1 53 50 1 50 49 1 49 48 1 50 51 1 51 52 1 52 49 1 48 58 1 58 59 1 59 53 1
		 55 54 1 54 57 1 57 56 1 56 55 1 51 57 1 54 52 1 58 28 1 27 59 1 53 68 1 68 62 0 62 50 1
		 62 63 0 63 51 1 63 64 0 64 57 1 66 55 1 55 1 1 43 52 1 54 47 1 41 49 1 44 48 1 45 58 1
		 56 2 1 65 46 0 47 66 1 64 67 0 67 56 1 67 4 0 59 69 1 69 68 0 5 69 0;
	setAttr -s 56 ".fc[0:55]" -type "polyFaces" 
		f 4 -12 -11 -10 -9
		mu 0 4 23 22 13 15
		f 4 -15 -14 10 -13
		mu 0 4 24 11 13 22
		f 4 -18 8 -17 -16
		mu 0 4 27 23 15 16
		f 4 0 -21 -20 -19
		mu 0 4 56 34 18 26
		f 4 -24 -23 -22 14
		mu 0 4 24 25 19 11
		f 4 -26 1 -25 15
		mu 0 4 16 50 57 27
		f 4 -30 -29 -28 -27
		mu 0 4 14 12 1 0
		f 4 28 -33 -32 -31
		mu 0 4 1 12 10 4
		f 4 -36 -35 -34 26
		mu 0 4 0 29 17 14
		f 4 -40 -39 -38 -37
		mu 0 4 8 20 6 9
		f 4 -42 37 -41 31
		mu 0 4 10 9 6 4
		f 4 -44 2 -43 34
		mu 0 4 29 68 51 17
		f 4 27 -47 -46 -45
		mu 0 4 0 1 2 3
		f 4 46 30 -49 -48
		mu 0 4 2 1 4 5
		f 4 48 40 -51 -50
		mu 0 4 5 4 6 7
		f 4 3 -53 -52 20
		mu 0 4 34 41 8 18
		f 4 -55 41 -54 21
		mu 0 4 19 9 10 11
		f 4 53 32 -56 13
		mu 0 4 11 10 12 13
		f 4 55 29 -57 9
		mu 0 4 13 12 14 15
		f 4 16 56 33 -58
		mu 0 4 16 15 14 17
		f 4 4 -59 39 52
		mu 0 4 41 63 20 8
		f 4 19 -61 22 -60
		mu 0 4 26 18 19 25
		f 4 -63 -62 50 38
		mu 0 4 20 21 7 6
		f 4 51 36 54 60
		mu 0 4 18 8 9 19
		f 4 42 5 25 57
		mu 0 4 17 51 50 16
		f 4 58 6 -64 62
		mu 0 4 20 63 62 21
		f 4 44 -66 -65 35
		mu 0 4 0 3 28 29
		f 4 -67 7 43 64
		mu 0 4 28 69 68 29
		f 4 67 68 69 70
		mu 0 4 59 40 37 52
		f 4 71 -70 72 73
		mu 0 4 53 52 37 35
		f 4 74 75 -68 76
		mu 0 4 58 49 40 59
		f 4 77 78 79 -1
		mu 0 4 56 55 45 34
		f 4 -74 80 81 82
		mu 0 4 53 35 46 54
		f 4 -75 83 -2 84
		mu 0 4 49 58 57 50
		f 4 85 86 87 88
		mu 0 4 39 64 31 38
		f 4 89 90 91 -88
		mu 0 4 31 33 36 38
		f 4 -86 92 93 94
		mu 0 4 64 39 48 67
		f 4 95 96 97 98
		mu 0 4 42 47 43 60
		f 4 -91 99 -97 100
		mu 0 4 36 33 43 47
		f 4 -94 101 -3 102
		mu 0 4 67 48 51 68
		f 4 103 104 105 -87
		mu 0 4 64 65 30 31
		f 4 106 107 -90 -106
		mu 0 4 30 32 33 31
		f 4 108 109 -100 -108
		mu 0 4 32 44 43 33
		f 4 -80 110 111 -4
		mu 0 4 34 45 42 41
		f 4 -81 112 -101 113
		mu 0 4 46 35 36 47
		f 4 -73 114 -92 -113
		mu 0 4 35 37 38 36
		f 4 -69 115 -89 -115
		mu 0 4 37 40 39 38
		f 4 116 -93 -116 -76
		mu 0 4 49 48 39 40
		f 4 -112 -99 117 -5
		mu 0 4 41 42 60 63
		f 4 118 -82 119 -79
		mu 0 4 55 54 46 45
		f 4 -98 -110 120 121
		mu 0 4 60 43 44 61
		f 4 -120 -114 -96 -111
		mu 0 4 45 46 47 42
		f 4 -117 -85 -6 -102
		mu 0 4 48 49 50 51
		f 4 -122 122 -7 -118
		mu 0 4 60 61 62 63
		f 4 -95 123 124 -104
		mu 0 4 64 67 66 65
		f 4 -124 -103 -8 125
		mu 0 4 66 67 68 69;
	setAttr ".cd" -type "dataPolyComponent" Index_Data Edge 0 ;
	setAttr ".cvd" -type "dataPolyComponent" Index_Data Vertex 0 ;
	setAttr ".hfd" -type "dataPolyComponent" Index_Data Face 0 ;
createNode transform -n "FaceFitLipInnerCurve" -p "FaceFitLipInner";
	setAttr ".it" no;
createNode transform -n "upperLipInnerCurve" -p "FaceFitLipInnerCurve";
createNode nurbsCurve -n "upperLipInnerCurveShape" -p "upperLipInnerCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 15;
	setAttr ".cc" -type "nurbsCurve" 
		1 4 0 no 3
		5 0 1 2 3 4
		5
		0 0.35577979679999999 0.3870719373
		-0.033218391239999999 0.3505746126 0.3767106831
		-0.062391653659999997 0.33864319320000003 0.3562476933
		-0.072464287279999998 0.33227565879999998 0.34748926759999998
		-0.079916581510000001 0.32855919 0.34220531580000002
		;
createNode transform -n "lowerLipInnerCurve" -p "FaceFitLipInnerCurve";
createNode nurbsCurve -n "lowerLipInnerCurveShape" -p "lowerLipInnerCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 15;
	setAttr ".cc" -type "nurbsCurve" 
		1 3 0 no 3
		4 0 1 2 3
		4
		0 0.302952677 0.38844943050000003
		-0.045682776719999998 0.31454890969999999 0.37244227530000001
		-0.076116286219999996 0.3270794749 0.34541323779999999
		-0.079916581510000001 0.32855919 0.34220531580000002
		;
createNode transform -n "LipProfileInner" -p "FaceFitLipInnerCurve";
createNode nurbsCurve -n "LipProfileInnerShape" -p "LipProfileInner";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		-0.0022331999658692079 3.962500737768013e-035 -6.4712547985702658e-019
		-0.0015791108396116837 -9.6692651761466835e-020 0.0015791108396116828
		-6.7290673311121696e-019 -1.3674405950288523e-019 0.0022331999658692083
		0.0015791108396116824 -9.6692651761466883e-020 0.0015791108396116835
		0.0022331999658692079 -7.344550111034955e-035 1.1994560580484947e-018
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		;
createNode transform -n "FaceFitLipInnerLoc" -p "FaceFitLipInner";
createNode transform -n "FaceFitEyeBrowInner" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[736] ";
createNode transform -n "FaceFitEyeBrowInnerGeo" -p "FaceFitEyeBrowInner";
createNode transform -n "FaceFitEyeBrowInnerCurve" -p "FaceFitEyeBrowInner";
	setAttr ".it" no;
createNode transform -n "FaceFitEyeBrowInnerLoc" -p "FaceFitEyeBrowInner";
createNode transform -n "EyeBrowInner" -p "FaceFitEyeBrowInner";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.072478903699296923 0.84215471140165499 0.71986714729841805 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "EyeBrowInnerShape" -p "EyeBrowInner";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "EyeBrowInnerSphere" -p "FaceFitEyeBrowInner";
createNode nurbsSurface -n "EyeBrowInnerSphereShape" -p "EyeBrowInnerSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitEyeBrowOuter" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[741] ";
createNode transform -n "FaceFitEyeBrowOuterGeo" -p "FaceFitEyeBrowOuter";
createNode transform -n "FaceFitEyeBrowOuterCurve" -p "FaceFitEyeBrowOuter";
	setAttr ".it" no;
createNode transform -n "FaceFitEyeBrowOuterLoc" -p "FaceFitEyeBrowOuter";
createNode transform -n "EyeBrowOuter" -p "FaceFitEyeBrowOuter";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.39184553167908637 0.8220933055728612 0.53365815180653775 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "EyeBrowOuterShape" -p "EyeBrowOuter";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "EyeBrowOuterSphere" -p "FaceFitEyeBrowOuter";
createNode nurbsSurface -n "EyeBrowOuterSphereShape" -p "EyeBrowOuterSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitEyeBrowMid1" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[739] ";
createNode transform -n "FaceFitEyeBrowMid1Geo" -p "FaceFitEyeBrowMid1";
createNode transform -n "FaceFitEyeBrowMid1Curve" -p "FaceFitEyeBrowMid1";
	setAttr ".it" no;
createNode transform -n "FaceFitEyeBrowMid1Loc" -p "FaceFitEyeBrowMid1";
createNode transform -n "EyeBrowMid1" -p "FaceFitEyeBrowMid1";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.25316236241859347 0.89842986744198594 0.67604368796367464 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "EyeBrowMid1Shape" -p "EyeBrowMid1";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "EyeBrowMid1Sphere" -p "FaceFitEyeBrowMid1";
createNode nurbsSurface -n "EyeBrowMid1SphereShape" -p "EyeBrowMid1Sphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitEyeBrowMiddle" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[28] ";
createNode transform -n "FaceFitEyeBrowMiddleGeo" -p "FaceFitEyeBrowMiddle";
createNode transform -n "FaceFitEyeBrowMiddleCurve" -p "FaceFitEyeBrowMiddle";
	setAttr ".it" no;
createNode transform -n "FaceFitEyeBrowMiddleLoc" -p "FaceFitEyeBrowMiddle";
createNode transform -n "EyeBrowMiddle" -p "FaceFitEyeBrowMiddle";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" 0 0.84651553936218327 0.72430284449471205 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "EyeBrowMiddleShape" -p "EyeBrowMiddle";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "EyeBrowMiddleSphere" -p "FaceFitEyeBrowMiddle";
createNode nurbsSurface -n "EyeBrowMiddleSphereShape" -p "EyeBrowMiddleSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitForeHead" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr -k on ".radius";
	setAttr ".selection" -type "string" "headTopology.vtx[13] headTopology.vtx[27] headTopology.vtx[500] ";
createNode transform -n "FaceFitForeHeadGeo" -p "FaceFitForeHead";
	setAttr ".t" -type "double3" 0 -0.35622556891217699 0 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode transform -n "ForeHeadCylinder" -p "FaceFitForeHeadGeo";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
createNode nurbsSurface -n "ForeHeadCylinderShape" -p "ForeHeadCylinder";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".tw" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 4;
createNode transform -n "ForeHeadAreaMesh" -p "FaceFitForeHeadGeo";
createNode mesh -n "ForeHeadAreaMeshShape" -p "ForeHeadAreaMesh";
	setAttr -k off ".v";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr -s 33 ".uvst[0].uvsp[0:32]" -type "float2" 0.38638496 0.55992103
		 0.39519197 0.58009595 0.43248698 0.59360296 0.46581796 0.57284492 0.47338399 0.56174099
		 0.485607 0.56663799 0.47334301 0.58778793 0.43478096 0.60700792 0.49342093 0.565593
		 0.50076896 0.56473303 0.33086699 0.70623493 0.41821799 0.73453093 0.46665001 0.73518693
		 0.50077814 0.73684156 0.30438098 0.77241492 0.50069904 0.83251619 0.50081986 0.91007566
		 0.38621399 0.588516 0.37635395 0.55106795 0.33133897 0.51173693 0.36190194 0.53724098
		 0.39431199 0.81176293 0.46098498 0.82867199 0.36597601 0.87608093 0.447395 0.90362889
		 0.50079495 0.61276698 0.50076878 0.6583423 0.48612899 0.61775595 0.47579998 0.67571694
		 0.434165 0.64780098 0.43060997 0.68061692 0.33723596 0.61170995 0.37153396 0.60821396;
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr -s 33 ".vt[0:32]"  0 0.94751775 0.23805511 0 0.58619416 0.38456333
		 0 0.6463275 0.38922495 0 0.70018804 0.37959611 0 0.77645236 0.36317438 0 0.87572432 0.31759435
		 -0.056497507 0.94484264 0.23462793 -0.13616696 0.92626655 0.21588725 -0.2393436 0.54038882 0.19889866
		 -0.065095 0.59355474 0.34366539 -0.047705859 0.57518095 0.33617526 -0.18132105 0.57163346 0.28221646
		 -0.17767349 0.59468269 0.30510604 -0.12728684 0.60905582 0.34060919 -0.035068437 0.58000726 0.35834074
		 -0.01822729 0.58493191 0.37894166 -0.038948622 0.64398408 0.3868413 -0.058144741 0.6084137 0.36902374
		 -0.13389963 0.63233197 0.36364213 -0.13604407 0.67422515 0.3632915 -0.19467275 0.60929823 0.30910006
		 -0.21056946 0.63320351 0.28677654 -0.055050109 0.77595919 0.35679093 -0.054534063 0.70209742 0.37605017
		 -0.13659126 0.70713747 0.34817135 -0.13786581 0.77304071 0.32515484 -0.22835176 0.75055498 0.21698651
		 -0.1378265 0.86049187 0.28130725 -0.057161804 0.87154025 0.31200546 -0.22308072 0.82957578 0.19154701
		 -0.19561549 0.56253219 0.2737934 -0.21425714 0.54739141 0.25838819 -0.23541012 0.64598686 0.21739116;
	setAttr -s 53 ".ed[0:52]"  2 1 0 4 3 0 5 4 0 0 5 0 3 2 0 6 0 0 8 32 0
		 32 26 0 26 29 0 10 9 0 12 11 0 13 12 0 9 13 0 17 9 1 10 14 0 14 17 1 11 30 0 12 20 1
		 20 30 1 13 18 1 18 20 1 17 18 1 15 14 0 15 16 1 16 17 1 16 19 1 19 18 1 19 21 1 21 20 1
		 1 15 0 2 16 1 22 25 1 25 24 1 24 23 1 23 22 1 25 26 1 32 24 1 4 22 1 23 3 1 22 28 1
		 28 27 1 27 25 1 27 29 0 5 28 1 27 7 0 6 28 1 31 30 0 21 31 1 31 8 0 21 32 1 6 7 0
		 23 16 1 24 19 1;
	setAttr -s 21 ".fc[0:20]" -type "polyFaces" 
		f 4 13 -10 14 15
		mu 0 4 6 3 4 5
		f 4 -17 -11 17 18
		mu 0 4 18 0 1 17
		f 4 -18 -12 19 20
		mu 0 4 17 1 2 7
		f 4 -20 -13 -14 21
		mu 0 4 7 2 3 6
		f 4 -23 23 24 -16
		mu 0 4 5 8 27 6
		f 4 -25 25 26 -22
		mu 0 4 6 27 29 7
		f 4 -27 27 28 -21
		mu 0 4 7 29 32 17
		f 4 -30 -1 30 -24
		mu 0 4 8 9 25 27
		f 4 31 32 33 34
		mu 0 4 12 11 30 28
		f 4 -33 35 -8 36
		mu 0 4 30 11 10 31
		f 4 37 -35 38 -2
		mu 0 4 13 12 28 26
		f 4 -32 39 40 41
		mu 0 4 11 12 22 21
		f 4 -36 -42 42 -9
		mu 0 4 10 11 21 14
		f 4 -40 -38 -3 43
		mu 0 4 22 12 13 15
		f 4 -44 -4 -6 45
		mu 0 4 22 15 16 24
		f 4 46 -19 -29 47
		mu 0 4 20 18 17 32
		f 4 -49 -48 49 -7
		mu 0 4 19 20 32 31
		f 4 -45 -41 -46 50
		mu 0 4 23 21 22 24
		f 4 -5 -39 51 -31
		mu 0 4 25 26 28 27
		f 4 -52 -34 52 -26
		mu 0 4 27 28 30 29
		f 4 -53 -37 -50 -28
		mu 0 4 29 30 31 32;
	setAttr ".cd" -type "dataPolyComponent" Index_Data Edge 0 ;
	setAttr ".cvd" -type "dataPolyComponent" Index_Data Vertex 0 ;
	setAttr ".hfd" -type "dataPolyComponent" Index_Data Face 0 ;
createNode transform -n "FaceFitForeHeadCurve" -p "FaceFitForeHead";
	setAttr ".it" no;
createNode transform -n "ForeHeadCurve" -p "FaceFitForeHeadCurve";
createNode nurbsCurve -n "ForeHeadCurveShape" -p "ForeHeadCurve";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".cc" -type "nurbsCurve" 
		1 20 0 no 3
		21 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21
		21
		-0.18132105469703674 0.57163345813751221 0.28221645951271057
		-0.1956154853105545 0.56253218650817871 0.27379339933395386
		-0.2142571359872818 0.54739141464233398 0.25838819146156311
		-0.23934359848499298 0.54038882255554199 0.19889865815639496
		-0.23541012406349182 0.64598685503005981 0.21739116311073303
		-0.27185207605361938 0.63594108819961548 0.10320389270782471
		-0.2704366147518158 0.73186022043228149 0.092674069106578827
		-0.22835175693035126 0.75055497884750366 0.21698650717735291
		-0.22308072447776794 0.82957577705383301 0.19154700636863708
		-0.13782650232315063 0.86049187183380127 0.28130725026130676
		-0.13616696000099182 0.92626655101776123 0.21588724851608276
		-0.05649750679731369 0.9448426365852356 0.23462793231010437
		0 0.9475177526473999 0.23805510997772217
		0 0.87572431564331055 0.31759434938430786
		0 0.77645236253738403 0.36317437887191772
		0 0.7001880407333374 0.37959611415863037
		0 0.64632749557495117 0.38922494649887085
		0 0.58619415760040283 0.38456332683563232
		-0.018227290362119675 0.58493191003799438 0.37894165515899658
		-0.035068437457084656 0.58000725507736206 0.35834074020385742
		-0.047705858945846558 0.57518094778060913 0.33617526292800903
		;
createNode transform -n "ForeHeadProfile" -p "FaceFitForeHeadCurve";
createNode nurbsCurve -n "ForeHeadProfileShape" -p "ForeHeadProfile";
	setAttr -k off ".v";
	setAttr ".cc" -type "nurbsCurve" 
		3 8 2 no 3
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		11
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		-0.0022331999658692079 3.962500737768013e-035 -6.4712547985702658e-019
		-0.0015791108396116837 -9.6692651761466835e-020 0.0015791108396116828
		-6.7290673311121696e-019 -1.3674405950288523e-019 0.0022331999658692083
		0.0015791108396116824 -9.6692651761466883e-020 0.0015791108396116835
		0.0022331999658692079 -7.344550111034955e-035 1.1994560580484947e-018
		0.0015791108396116848 9.6692651761466811e-020 -0.0015791108396116824
		-2.5478141364549723e-019 1.367440595028852e-019 -0.0022331999658692079
		-0.0015791108396116832 9.6692651761466871e-020 -0.0015791108396116832
		;
createNode transform -n "FaceFitForeHeadLoc" -p "FaceFitForeHead";
createNode transform -n "FaceFitJawPivot" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[504] ";
createNode transform -n "FaceFitJawPivotGeo" -p "FaceFitJawPivot";
createNode transform -n "FaceFitJawPivotCurve" -p "FaceFitJawPivot";
	setAttr ".it" no;
createNode transform -n "FaceFitJawPivotLoc" -p "FaceFitJawPivot";
createNode transform -n "JawPivot" -p "FaceFitJawPivot";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.48387920524815375 0.62715622095151757 0.1963978388698219 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "JawPivotShape" -p "JawPivot";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "JawPivotSphere" -p "FaceFitJawPivot";
createNode nurbsSurface -n "JawPivotSphereShape" -p "JawPivotSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitJawCorner" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[493] ";
createNode transform -n "FaceFitJawCornerGeo" -p "FaceFitJawCorner";
createNode transform -n "FaceFitJawCornerCurve" -p "FaceFitJawCorner";
	setAttr ".it" no;
createNode transform -n "FaceFitJawCornerLoc" -p "FaceFitJawCorner";
createNode transform -n "JawCorner" -p "FaceFitJawCorner";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.40853959657661332 0.30676513919467335 0.24727259374634711 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "JawCornerShape" -p "JawCorner";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "JawCornerSphere" -p "FaceFitJawCorner";
createNode nurbsSurface -n "JawCornerSphereShape" -p "JawCornerSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitJaw" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[115] ";
createNode transform -n "FaceFitJawGeo" -p "FaceFitJaw";
createNode transform -n "FaceFitJawCurve" -p "FaceFitJaw";
	setAttr ".it" no;
createNode transform -n "FaceFitJawLoc" -p "FaceFitJaw";
createNode transform -n "Jaw" -p "FaceFitJaw";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" 0 0.00075749134355318493 0.69753254509570417 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "JawShape" -p "Jaw";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "JawSphere" -p "FaceFitJaw";
createNode nurbsSurface -n "JawSphereShape" -p "JawSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitThroat" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[318] ";
createNode transform -n "FaceFitThroatGeo" -p "FaceFitThroat";
createNode transform -n "FaceFitThroatCurve" -p "FaceFitThroat";
	setAttr ".it" no;
createNode transform -n "FaceFitThroatLoc" -p "FaceFitThroat";
createNode transform -n "Throat" -p "FaceFitThroat";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" 0 -0.32072809545214298 0.32319451869606369 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "ThroatShape" -p "Throat";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "ThroatSphere" -p "FaceFitThroat";
createNode nurbsSurface -n "ThroatSphereShape" -p "ThroatSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitCheek" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[762] ";
createNode transform -n "FaceFitCheekGeo" -p "FaceFitCheek";
createNode transform -n "FaceFitCheekCurve" -p "FaceFitCheek";
	setAttr ".it" no;
createNode transform -n "FaceFitCheekLoc" -p "FaceFitCheek";
createNode transform -n "Cheek" -p "FaceFitCheek";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.36733032139068217 0.40049221623002368 0.40543313333796954 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "CheekShape" -p "Cheek";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "CheekSphere" -p "FaceFitCheek";
createNode nurbsSurface -n "CheekSphereShape" -p "CheekSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitLine" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[449] ";
createNode transform -n "FaceFitLineGeo" -p "FaceFitLine";
createNode transform -n "FaceFitLineCurve" -p "FaceFitLine";
	setAttr ".it" no;
createNode transform -n "FaceFitLineLoc" -p "FaceFitLine";
createNode transform -n "Line" -p "FaceFitLine";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.20520205892032012 0.26562094478516513 0.64134884044039342 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "LineShape" -p "Line";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "LineSphere" -p "FaceFitLine";
createNode nurbsSurface -n "LineSphereShape" -p "LineSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitCheekRaiser" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[438] ";
createNode transform -n "FaceFitCheekRaiserGeo" -p "FaceFitCheekRaiser";
createNode transform -n "FaceFitCheekRaiserCurve" -p "FaceFitCheekRaiser";
	setAttr ".it" no;
createNode transform -n "FaceFitCheekRaiserLoc" -p "FaceFitCheekRaiser";
createNode transform -n "CheekRaiser" -p "FaceFitCheekRaiser";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.22368104799251662 0.59963207153639075 0.62597990555722283 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "CheekRaiserShape" -p "CheekRaiser";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "CheekRaiserSphere" -p "FaceFitCheekRaiser";
createNode nurbsSurface -n "CheekRaiserSphereShape" -p "CheekRaiserSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitNose" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[3] ";
createNode transform -n "FaceFitNoseGeo" -p "FaceFitNose";
createNode transform -n "FaceFitNoseCurve" -p "FaceFitNose";
	setAttr ".it" no;
createNode transform -n "FaceFitNoseLoc" -p "FaceFitNose";
createNode transform -n "Nose" -p "FaceFitNose";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" 0 0.45180659216722524 0.83847266133302156 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "NoseShape" -p "Nose";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "NoseSphere" -p "FaceFitNose";
createNode nurbsSurface -n "NoseSphereShape" -p "NoseSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitNoseUnder" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[6] ";
createNode transform -n "FaceFitNoseUnderGeo" -p "FaceFitNoseUnder";
createNode transform -n "FaceFitNoseUnderCurve" -p "FaceFitNoseUnder";
	setAttr ".it" no;
createNode transform -n "FaceFitNoseUnderLoc" -p "FaceFitNoseUnder";
createNode transform -n "NoseUnder" -p "FaceFitNoseUnder";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" 0 0.41684482195376676 0.74765250715832676 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "NoseUnderShape" -p "NoseUnder";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "NoseUnderSphere" -p "FaceFitNoseUnder";
createNode nurbsSurface -n "NoseUnderSphereShape" -p "NoseUnderSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitNoseCorner" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[482] ";
createNode transform -n "FaceFitNoseCornerGeo" -p "FaceFitNoseCorner";
createNode transform -n "FaceFitNoseCornerCurve" -p "FaceFitNoseCorner";
	setAttr ".it" no;
createNode transform -n "FaceFitNoseCornerLoc" -p "FaceFitNoseCorner";
createNode transform -n "NoseCorner" -p "FaceFitNoseCorner";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.10686999009546953 0.43235467737625594 0.68111832476386069 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "NoseCornerShape" -p "NoseCorner";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "NoseCornerSphere" -p "FaceFitNoseCorner";
createNode nurbsSurface -n "NoseCornerSphereShape" -p "NoseCornerSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitNoseSide" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[415] ";
createNode transform -n "FaceFitNoseSideGeo" -p "FaceFitNoseSide";
createNode transform -n "FaceFitNoseSideCurve" -p "FaceFitNoseSide";
	setAttr ".it" no;
createNode transform -n "FaceFitNoseSideLoc" -p "FaceFitNoseSide";
createNode transform -n "NoseSide" -p "FaceFitNoseSide";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.089720172589893343 0.56284476752019663 0.7086148002911008 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "NoseSideShape" -p "NoseSide";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "NoseSideSphere" -p "FaceFitNoseSide";
createNode nurbsSurface -n "NoseSideSphereShape" -p "NoseSideSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitNoseMiddle" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[2] ";
createNode transform -n "FaceFitNoseMiddleGeo" -p "FaceFitNoseMiddle";
createNode transform -n "FaceFitNoseMiddleCurve" -p "FaceFitNoseMiddle";
	setAttr ".it" no;
createNode transform -n "FaceFitNoseMiddleLoc" -p "FaceFitNoseMiddle";
createNode transform -n "NoseMiddle" -p "FaceFitNoseMiddle";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" 0 0.60401585939588731 0.76730307498819383 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "NoseMiddleShape" -p "NoseMiddle";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "NoseMiddleSphere" -p "FaceFitNoseMiddle";
createNode nurbsSurface -n "NoseMiddleSphereShape" -p "NoseMiddleSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode transform -n "FaceFitNostril" -p "FaceFitSkeleton";
	addAttr -ci true -k true -sn "radius" -ln "radius" -dv 1 -min 0 -at "double";
	addAttr -ci true -sn "selection" -ln "selection" -dt "string";
	setAttr ".selection" -type "string" "headTopology.vtx[419] ";
createNode transform -n "FaceFitNostrilGeo" -p "FaceFitNostril";
createNode transform -n "FaceFitNostrilCurve" -p "FaceFitNostril";
	setAttr ".it" no;
createNode transform -n "FaceFitNostrilLoc" -p "FaceFitNostril";
createNode transform -n "Nostril" -p "FaceFitNostril";
	addAttr -ci true -k true -sn "falloffRadius" -ln "falloffRadius" -dv 1 -min 0 -at "double";
	addAttr -ci true -k true -sn "falloffMode" -ln "falloffMode" -dv 1 -min 0 -max 1 
		-en "volume:surface" -at "enum";
	setAttr ".t" -type "double3" -0.033433682179147897 0.43229711125275633 0.80119693260617042 ;
	setAttr ".s" -type "double3" 1.8608849484338312 1.8608849484338312 1.8608849484338312 ;
createNode locator -n "NostrilShape" -p "Nostril";
	setAttr -k off ".v";
	setAttr ".ove" yes;
	setAttr ".ovc" 17;
	setAttr ".los" -type "double3" 0.028212383389472961 0.028212383389472961 0.028212383389472961 ;
createNode transform -n "NostrilSphere" -p "FaceFitNostril";
createNode nurbsSurface -n "NostrilSphereShape" -p "NostrilSphere";
	setAttr -k off ".v";
	setAttr ".ovdt" 2;
	setAttr ".ove" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr ".dvu" 0;
	setAttr ".dvv" 0;
	setAttr ".cpr" 4;
	setAttr ".cps" 1;
	setAttr ".cc" -type "nurbsSurface" 
		3 3 0 2 no 
		9 0 0 0 1 2 3 4 4 4
		13 -2 -1 0 1 2 3 4 5 6 7 8 9 10
		
		77
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		1.5470824281834676e-018 -0.016121362056933597 -4.0813135784129076e-018
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		1.8565382238118412e-018 -0.016121362056933597 0.0045579126831809605
		-0.0032229309663334275 -0.016121362056933597 0.0032229309663334297
		-0.0045579126831809622 -0.016121362056933597 9.2200848035780688e-019
		-0.0032229309663334301 -0.016121362056933597 -0.0032229309663334275
		-2.9838329516118714e-018 -0.016121362056933597 -0.0045579126831809622
		0.0032229309663334266 -0.016121362056933597 -0.0032229309663334336
		0.0045579126831809614 -0.016121362056933597 -1.0029145422349728e-018
		0.0032229309663334292 -0.016121362056933597 0.0032229309663334275
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		3.2086157449919139e-018 -0.012632886716893466 0.014054017228719625
		-0.0099376908853402134 -0.012632886716893466 0.0099376908853402186
		-0.014054017228719627 -0.012632886716893466 2.5973216126438633e-018
		-0.0099376908853402168 -0.012632886716893466 -0.0099376908853402099
		-6.684553225060006e-018 -0.012632886716893466 -0.014054017228719625
		0.0099376908853402099 -0.012632886716893466 -0.0099376908853402255
		0.014054017228719625 -0.012632886716893466 1.6937409514072865e-018
		0.0099376908853402151 -0.012632886716893466 0.0099376908853402151
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		3.3079573963642302e-018 -1.0131122824897633e-018 0.019771390976070682
		-0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		-0.019771390976070686 1.9753625118702776e-019 3.5362088945426706e-018
		-0.013980484632670091 1.0535940389834369e-018 -0.013980484632670084
		-8.1979555946277991e-018 1.4081847848638193e-018 -0.019771390976070682
		0.013980484632670084 1.053594038983438e-018 -0.013980484632670103
		0.019771390976070682 1.9753625118702769e-019 4.6769763704205889e-018
		0.01398048463267009 -6.5852153660938149e-019 0.013980484632670093
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		1.4941479904114645e-018 0.012632886716893469 0.014054017228719629
		-0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		-0.01405401722871963 0.012632886716893469 2.4299362862468833e-018
		-0.0099376908853402186 0.012632886716893469 -0.0099376908853402151
		-4.9700854704795573e-018 0.012632886716893469 -0.014054017228719629
		0.0099376908853402151 0.012632886716893469 -0.0099376908853402255
		0.014054017228719629 0.012632886716893469 4.9552911341712083e-018
		0.0099376908853402168 0.012632886716893469 0.0099376908853402203
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		-3.3136672823432145e-019 0.016121362056933593 0.004557912683180964
		-0.003222930966333431 0.016121362056933593 0.003222930966333431
		-0.004557912683180964 0.016121362056933593 7.0840097240357566e-019
		-0.0032229309663334297 0.016121362056933593 -0.0032229309663334292
		-7.9592799956570919e-019 0.016121362056933593 -0.0045579126831809622
		0.0032229309663334301 0.016121362056933593 -0.0032229309663334314
		0.0045579126831809631 0.016121362056933593 3.1592878539031224e-018
		0.0032229309663334292 0.016121362056933593 0.0032229309663334327
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		2.8050643401152851e-018 0.016121362056933597 -2.7083318988585146e-019
		
		;
	setAttr ".nufa" 4.5;
	setAttr ".nvfa" 4.5;
createNode lightLinker -s -n "lightLinker1";
	setAttr -s 16 ".lnk";
	setAttr -s 16 ".slnk";
createNode displayLayerManager -n "layerManager";
	setAttr ".cdl" 1;
	setAttr -s 4 ".dli[1:3]"  7 6 1;
	setAttr -s 3 ".dli";
createNode displayLayer -n "defaultLayer";
createNode renderLayerManager -n "renderLayerManager";
createNode renderLayer -n "defaultRenderLayer";
	setAttr ".g" yes;
createNode renderLayerManager -n "renderLayerManager1";
createNode renderLayer -n "defaultRenderLayer1";
	setAttr ".g" yes;
createNode displayLayer -n "ProfileCurves";
	setAttr ".c" 6;
	setAttr ".do" 2;
createNode displayLayer -n "MainCurves";
	setAttr ".c" 13;
	setAttr ".do" 1;
createNode groupId -n "groupId1";
	setAttr ".ihi" 0;
createNode groupParts -n "groupParts1";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "f[0:755]";
createNode lambert -n "asRed";
	setAttr ".c" -type "float3" 1 0 0 ;
createNode shadingEngine -n "asRedSG";
	setAttr ".ihi" 0;
	setAttr -s 4 ".dsm";
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo1";
createNode lambert -n "asRed2";
	setAttr ".c" -type "float3" 1 0 1 ;
createNode shadingEngine -n "asRed2SG";
	setAttr ".ihi" 0;
	setAttr -s 2 ".dsm";
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo2";
createNode lambert -n "asGreen";
	setAttr ".c" -type "float3" 0 1 0 ;
createNode shadingEngine -n "asGreenSG";
	setAttr ".ihi" 0;
	setAttr -s 4 ".dsm";
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo3";
createNode lambert -n "asGreen2";
	setAttr ".c" -type "float3" 1 1 0 ;
createNode shadingEngine -n "asGreen2SG";
	setAttr ".ihi" 0;
	setAttr -s 2 ".dsm";
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo4";
createNode lambert -n "asBlue";
	setAttr ".c" -type "float3" 0 0 1 ;
createNode shadingEngine -n "asBlueSG";
	setAttr ".ihi" 0;
	setAttr -s 2 ".dsm";
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo5";
createNode lambert -n "asBlue2";
	setAttr ".c" -type "float3" 0 1 1 ;
createNode shadingEngine -n "asBlue2SG";
	setAttr ".ihi" 0;
	setAttr -s 19 ".dsm";
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo6";
createNode shadingEngine -n "irisPhongSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo11";
createNode phong -n "irisPhong";
	setAttr ".c" -type "float3" 0.11451896 0.23926146 0.31706721 ;
	setAttr ".sc" -type "float3" 0.20661999 0.20661999 0.20661999 ;
	setAttr ".rfl" 0.019999999552965164;
	setAttr ".cp" 5.2399997711181641;
createNode shadingEngine -n "eyeWhitePhongSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo12";
createNode phong -n "eyeWhitePhong";
	setAttr ".dc" 1;
	setAttr ".c" -type "float3" 0.93387997 0.93387997 0.93387997 ;
	setAttr ".sc" -type "float3" 0.074380003 0.074380003 0.074380003 ;
	setAttr ".rfl" 0.0099999997764825821;
	setAttr ".cp" 5.2399997711181641;
createNode shadingEngine -n "pupilPhongSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo10";
createNode phong -n "pupilPhong";
	setAttr ".dc" 1;
	setAttr ".c" -type "float3" 0 0 0 ;
	setAttr ".sc" -type "float3" 0.090226598 0.090226598 0.090226598 ;
	setAttr ".rfl" 0;
	setAttr ".cp" 5.2399997711181641;
createNode lambert -n "asWhite";
	setAttr ".c" -type "float3" 1 1 1 ;
createNode shadingEngine -n "asWhiteSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo13";
createNode lambert -n "asBlack";
	setAttr ".c" -type "float3" 0 0 0 ;
createNode shadingEngine -n "asBlackSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo14";
createNode shadingEngine -n "gumSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo8";
createNode lambert -n "gumLambert";
	setAttr ".c" -type "float3" 0.73333335 0.40784314 0.40784314 ;
createNode shadingEngine -n "teethSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo9";
createNode lambert -n "teethLambert";
	setAttr ".c" -type "float3" 0.78225374 0.78225374 0.78225374 ;
	setAttr ".ambc" -type "float3" 0.57257956 0.57257956 0.57257956 ;
createNode lambert -n "asBones";
	setAttr ".c" -type "float3" 0.77999997 0.75999999 0.72000003 ;
createNode shadingEngine -n "asBonesSG";
	setAttr ".ihi" 0;
	setAttr ".ro" yes;
createNode materialInfo -n "materialInfo15";
createNode extrude -n "extrude1";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode extrude -n "extrude2";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode reverseSurface -n "reverseSurface1";
createNode extrude -n "extrude3";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode extrude -n "extrude4";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode reverseSurface -n "reverseSurface2";
createNode extrude -n "extrude5";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode extrude -n "extrude6";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode reverseSurface -n "reverseSurface3";
createNode extrude -n "extrude7";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode extrude -n "extrude8";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode extrude -n "extrude9";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode extrude -n "extrude10";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode reverseSurface -n "reverseSurface4";
createNode extrude -n "extrude11";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode extrude -n "extrude12";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode reverseSurface -n "reverseSurface5";
createNode extrude -n "extrude13";
	setAttr ".fpt" yes;
	setAttr ".ucp" 1;
	setAttr ".upn" yes;
createNode reverseSurface -n "reverseSurface6";
createNode script -n "uiConfigurationScriptNode";
	setAttr ".b" -type "string" (
		"// Maya Mel UI Configuration File.\n//\n//  This script is machine generated.  Edit at your own risk.\n//\n//\n\nglobal string $gMainPane;\nif (`paneLayout -exists $gMainPane`) {\n\n\tglobal int $gUseScenePanelConfig;\n\tint    $useSceneConfig = $gUseScenePanelConfig;\n\tint    $menusOkayInPanels = `optionVar -q allowMenusInPanels`;\tint    $nVisPanes = `paneLayout -q -nvp $gMainPane`;\n\tint    $nPanes = 0;\n\tstring $editorName;\n\tstring $panelName;\n\tstring $itemFilterName;\n\tstring $panelConfig;\n\n\t//\n\t//  get current state of the UI\n\t//\n\tsceneUIReplacement -update $gMainPane;\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"Top View\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"Top View\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"top\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"wireframe\" \n"
		+ "                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 1\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -rendererName \"base_OpenGL_Renderer\" \n"
		+ "                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 256 256 \n                -bumpResolution 512 512 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 1\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n"
		+ "                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"Top View\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"top\" \n            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"wireframe\" \n            -activeOnly 0\n            -ignorePanZoom 0\n"
		+ "            -wireframeOnShaded 0\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n            -xray 0\n            -jointXray 1\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -rendererName \"base_OpenGL_Renderer\" \n            -objectFilterShowInHUD 1\n            -isFiltered 0\n            -colorResolution 256 256 \n            -bumpResolution 512 512 \n            -textureCompression 0\n"
		+ "            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 1\n            -occlusionCulling 0\n            -shadingModel 0\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n"
		+ "            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"Side View\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"Side View\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"side\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"wireframe\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n"
		+ "                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 1\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -rendererName \"base_OpenGL_Renderer\" \n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 256 256 \n                -bumpResolution 512 512 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n"
		+ "                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 1\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n"
		+ "                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"Side View\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"side\" \n            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"wireframe\" \n            -activeOnly 0\n            -ignorePanZoom 0\n            -wireframeOnShaded 0\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n"
		+ "            -xray 0\n            -jointXray 1\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -rendererName \"base_OpenGL_Renderer\" \n            -objectFilterShowInHUD 1\n            -isFiltered 0\n            -colorResolution 256 256 \n            -bumpResolution 512 512 \n            -textureCompression 0\n            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 1\n            -occlusionCulling 0\n"
		+ "            -shadingModel 0\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\n"
		+ "modelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"Front View\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"Front View\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"front\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"wireframe\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 1\n                -activeComponentsXray 0\n                -displayTextures 0\n"
		+ "                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -rendererName \"base_OpenGL_Renderer\" \n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 256 256 \n                -bumpResolution 512 512 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 1\n                -occlusionCulling 0\n"
		+ "                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n"
		+ "                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"Front View\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"front\" \n            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"wireframe\" \n            -activeOnly 0\n            -ignorePanZoom 0\n            -wireframeOnShaded 0\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n            -xray 0\n            -jointXray 1\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n"
		+ "            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -rendererName \"base_OpenGL_Renderer\" \n            -objectFilterShowInHUD 1\n            -isFiltered 0\n            -colorResolution 256 256 \n            -bumpResolution 512 512 \n            -textureCompression 0\n            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 1\n            -occlusionCulling 0\n            -shadingModel 0\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n"
		+ "            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"Persp View\")) `;\n"
		+ "\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"Persp View\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"persp\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"smoothShaded\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 1\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 1\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n"
		+ "                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -rendererName \"base_OpenGL_Renderer\" \n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 256 256 \n                -bumpResolution 512 512 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 1\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n"
		+ "                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n"
		+ "\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"Persp View\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"persp\" \n            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"smoothShaded\" \n            -activeOnly 0\n            -ignorePanZoom 0\n            -wireframeOnShaded 1\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n            -xray 0\n            -jointXray 1\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n"
		+ "            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -rendererName \"base_OpenGL_Renderer\" \n            -objectFilterShowInHUD 1\n            -isFiltered 0\n            -colorResolution 256 256 \n            -bumpResolution 512 512 \n            -textureCompression 0\n            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 1\n            -occlusionCulling 0\n            -shadingModel 0\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n"
		+ "            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"outlinerPanel\" (localizedPanelLabel(\"Outliner\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `outlinerPanel -unParent -l (localizedPanelLabel(\"Outliner\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            outlinerEditor -e \n"
		+ "                -docTag \"isolOutln_fromSeln\" \n                -showShapes 0\n                -showAttributes 0\n                -showConnected 0\n                -showAnimCurvesOnly 0\n                -showMuteInfo 0\n                -organizeByLayer 1\n                -showAnimLayerWeight 1\n                -autoExpandLayers 1\n                -autoExpand 0\n                -showDagOnly 1\n                -showAssets 1\n                -showContainedOnly 1\n                -showPublishedAsConnected 0\n                -showContainerContents 1\n                -ignoreDagHierarchy 0\n                -expandConnections 0\n                -showUpstreamCurves 1\n                -showUnitlessCurves 1\n                -showCompounds 1\n                -showLeafs 1\n                -showNumericAttrsOnly 0\n                -highlightActive 1\n                -autoSelectNewObjects 0\n                -doNotSelectNewObjects 0\n                -dropIsParent 1\n                -transmitFilters 0\n                -setFilter \"defaultSetFilter\" \n                -showSetMembers 0\n"
		+ "                -allowMultiSelection 1\n                -alwaysToggleSelect 0\n                -directSelect 0\n                -displayMode \"DAG\" \n                -expandObjects 0\n                -setsIgnoreFilters 1\n                -containersIgnoreFilters 0\n                -editAttrName 0\n                -showAttrValues 0\n                -highlightSecondary 0\n                -showUVAttrsOnly 0\n                -showTextureNodesOnly 0\n                -attrAlphaOrder \"default\" \n                -animLayerFilterOptions \"allAffecting\" \n                -sortOrder \"none\" \n                -longNames 0\n                -niceNames 1\n                -showNamespace 1\n                -showPinIcons 0\n                -mapMotionTrails 0\n                $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\toutlinerPanel -edit -l (localizedPanelLabel(\"Outliner\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        outlinerEditor -e \n            -docTag \"isolOutln_fromSeln\" \n            -showShapes 0\n"
		+ "            -showAttributes 0\n            -showConnected 0\n            -showAnimCurvesOnly 0\n            -showMuteInfo 0\n            -organizeByLayer 1\n            -showAnimLayerWeight 1\n            -autoExpandLayers 1\n            -autoExpand 0\n            -showDagOnly 1\n            -showAssets 1\n            -showContainedOnly 1\n            -showPublishedAsConnected 0\n            -showContainerContents 1\n            -ignoreDagHierarchy 0\n            -expandConnections 0\n            -showUpstreamCurves 1\n            -showUnitlessCurves 1\n            -showCompounds 1\n            -showLeafs 1\n            -showNumericAttrsOnly 0\n            -highlightActive 1\n            -autoSelectNewObjects 0\n            -doNotSelectNewObjects 0\n            -dropIsParent 1\n            -transmitFilters 0\n            -setFilter \"defaultSetFilter\" \n            -showSetMembers 0\n            -allowMultiSelection 1\n            -alwaysToggleSelect 0\n            -directSelect 0\n            -displayMode \"DAG\" \n            -expandObjects 0\n"
		+ "            -setsIgnoreFilters 1\n            -containersIgnoreFilters 0\n            -editAttrName 0\n            -showAttrValues 0\n            -highlightSecondary 0\n            -showUVAttrsOnly 0\n            -showTextureNodesOnly 0\n            -attrAlphaOrder \"default\" \n            -animLayerFilterOptions \"allAffecting\" \n            -sortOrder \"none\" \n            -longNames 0\n            -niceNames 1\n            -showNamespace 1\n            -showPinIcons 0\n            -mapMotionTrails 0\n            $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"graphEditor\" (localizedPanelLabel(\"Graph Editor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"graphEditor\" -l (localizedPanelLabel(\"Graph Editor\")) -mbv $menusOkayInPanels `;\n\n\t\t\t$editorName = ($panelName+\"OutlineEd\");\n            outlinerEditor -e \n                -showShapes 1\n                -showAttributes 1\n                -showConnected 1\n"
		+ "                -showAnimCurvesOnly 1\n                -showMuteInfo 0\n                -organizeByLayer 1\n                -showAnimLayerWeight 1\n                -autoExpandLayers 1\n                -autoExpand 1\n                -showDagOnly 0\n                -showAssets 1\n                -showContainedOnly 0\n                -showPublishedAsConnected 0\n                -showContainerContents 0\n                -ignoreDagHierarchy 0\n                -expandConnections 1\n                -showUpstreamCurves 1\n                -showUnitlessCurves 1\n                -showCompounds 0\n                -showLeafs 1\n                -showNumericAttrsOnly 1\n                -highlightActive 0\n                -autoSelectNewObjects 1\n                -doNotSelectNewObjects 0\n                -dropIsParent 1\n                -transmitFilters 1\n                -setFilter \"0\" \n                -showSetMembers 0\n                -allowMultiSelection 1\n                -alwaysToggleSelect 0\n                -directSelect 0\n                -displayMode \"DAG\" \n"
		+ "                -expandObjects 0\n                -setsIgnoreFilters 1\n                -containersIgnoreFilters 0\n                -editAttrName 0\n                -showAttrValues 0\n                -highlightSecondary 0\n                -showUVAttrsOnly 0\n                -showTextureNodesOnly 0\n                -attrAlphaOrder \"default\" \n                -animLayerFilterOptions \"allAffecting\" \n                -sortOrder \"none\" \n                -longNames 0\n                -niceNames 1\n                -showNamespace 1\n                -showPinIcons 1\n                -mapMotionTrails 1\n                $editorName;\n\n\t\t\t$editorName = ($panelName+\"GraphEd\");\n            animCurveEditor -e \n                -displayKeys 1\n                -displayTangents 0\n                -displayActiveKeys 0\n                -displayActiveKeyTangents 1\n                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"integer\" \n                -snapValue \"none\" \n                -showResults \"off\" \n                -showBufferCurves \"off\" \n"
		+ "                -smoothness \"fine\" \n                -resultSamples 1\n                -resultScreenSamples 0\n                -resultUpdate \"delayed\" \n                -showUpstreamCurves 1\n                -stackedCurves 0\n                -stackedCurvesMin -1\n                -stackedCurvesMax 1\n                -stackedCurvesSpace 0.2\n                -displayNormalized 0\n                -preSelectionHighlight 0\n                -constrainDrag 0\n                -classicMode 1\n                $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Graph Editor\")) -mbv $menusOkayInPanels  $panelName;\n\n\t\t\t$editorName = ($panelName+\"OutlineEd\");\n            outlinerEditor -e \n                -showShapes 1\n                -showAttributes 1\n                -showConnected 1\n                -showAnimCurvesOnly 1\n                -showMuteInfo 0\n                -organizeByLayer 1\n                -showAnimLayerWeight 1\n                -autoExpandLayers 1\n                -autoExpand 1\n"
		+ "                -showDagOnly 0\n                -showAssets 1\n                -showContainedOnly 0\n                -showPublishedAsConnected 0\n                -showContainerContents 0\n                -ignoreDagHierarchy 0\n                -expandConnections 1\n                -showUpstreamCurves 1\n                -showUnitlessCurves 1\n                -showCompounds 0\n                -showLeafs 1\n                -showNumericAttrsOnly 1\n                -highlightActive 0\n                -autoSelectNewObjects 1\n                -doNotSelectNewObjects 0\n                -dropIsParent 1\n                -transmitFilters 1\n                -setFilter \"0\" \n                -showSetMembers 0\n                -allowMultiSelection 1\n                -alwaysToggleSelect 0\n                -directSelect 0\n                -displayMode \"DAG\" \n                -expandObjects 0\n                -setsIgnoreFilters 1\n                -containersIgnoreFilters 0\n                -editAttrName 0\n                -showAttrValues 0\n                -highlightSecondary 0\n"
		+ "                -showUVAttrsOnly 0\n                -showTextureNodesOnly 0\n                -attrAlphaOrder \"default\" \n                -animLayerFilterOptions \"allAffecting\" \n                -sortOrder \"none\" \n                -longNames 0\n                -niceNames 1\n                -showNamespace 1\n                -showPinIcons 1\n                -mapMotionTrails 1\n                $editorName;\n\n\t\t\t$editorName = ($panelName+\"GraphEd\");\n            animCurveEditor -e \n                -displayKeys 1\n                -displayTangents 0\n                -displayActiveKeys 0\n                -displayActiveKeyTangents 1\n                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"integer\" \n                -snapValue \"none\" \n                -showResults \"off\" \n                -showBufferCurves \"off\" \n                -smoothness \"fine\" \n                -resultSamples 1\n                -resultScreenSamples 0\n                -resultUpdate \"delayed\" \n                -showUpstreamCurves 1\n                -stackedCurves 0\n"
		+ "                -stackedCurvesMin -1\n                -stackedCurvesMax 1\n                -stackedCurvesSpace 0.2\n                -displayNormalized 0\n                -preSelectionHighlight 0\n                -constrainDrag 0\n                -classicMode 1\n                $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"dopeSheetPanel\" (localizedPanelLabel(\"Dope Sheet\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"dopeSheetPanel\" -l (localizedPanelLabel(\"Dope Sheet\")) -mbv $menusOkayInPanels `;\n\n\t\t\t$editorName = ($panelName+\"OutlineEd\");\n            outlinerEditor -e \n                -showShapes 1\n                -showAttributes 1\n                -showConnected 1\n                -showAnimCurvesOnly 1\n                -showMuteInfo 0\n                -organizeByLayer 1\n                -showAnimLayerWeight 1\n                -autoExpandLayers 1\n                -autoExpand 0\n"
		+ "                -showDagOnly 0\n                -showAssets 1\n                -showContainedOnly 0\n                -showPublishedAsConnected 0\n                -showContainerContents 0\n                -ignoreDagHierarchy 0\n                -expandConnections 1\n                -showUpstreamCurves 1\n                -showUnitlessCurves 0\n                -showCompounds 1\n                -showLeafs 1\n                -showNumericAttrsOnly 1\n                -highlightActive 0\n                -autoSelectNewObjects 0\n                -doNotSelectNewObjects 1\n                -dropIsParent 1\n                -transmitFilters 0\n                -setFilter \"0\" \n                -showSetMembers 0\n                -allowMultiSelection 1\n                -alwaysToggleSelect 0\n                -directSelect 0\n                -displayMode \"DAG\" \n                -expandObjects 0\n                -setsIgnoreFilters 1\n                -containersIgnoreFilters 0\n                -editAttrName 0\n                -showAttrValues 0\n                -highlightSecondary 0\n"
		+ "                -showUVAttrsOnly 0\n                -showTextureNodesOnly 0\n                -attrAlphaOrder \"default\" \n                -animLayerFilterOptions \"allAffecting\" \n                -sortOrder \"none\" \n                -longNames 0\n                -niceNames 1\n                -showNamespace 1\n                -showPinIcons 0\n                -mapMotionTrails 1\n                $editorName;\n\n\t\t\t$editorName = ($panelName+\"DopeSheetEd\");\n            dopeSheetEditor -e \n                -displayKeys 1\n                -displayTangents 0\n                -displayActiveKeys 0\n                -displayActiveKeyTangents 0\n                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"integer\" \n                -snapValue \"none\" \n                -outliner \"dopeSheetPanel1OutlineEd\" \n                -showSummary 1\n                -showScene 0\n                -hierarchyBelow 0\n                -showTicks 1\n                -selectionWindow 0 0 0 0 \n                $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n"
		+ "\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Dope Sheet\")) -mbv $menusOkayInPanels  $panelName;\n\n\t\t\t$editorName = ($panelName+\"OutlineEd\");\n            outlinerEditor -e \n                -showShapes 1\n                -showAttributes 1\n                -showConnected 1\n                -showAnimCurvesOnly 1\n                -showMuteInfo 0\n                -organizeByLayer 1\n                -showAnimLayerWeight 1\n                -autoExpandLayers 1\n                -autoExpand 0\n                -showDagOnly 0\n                -showAssets 1\n                -showContainedOnly 0\n                -showPublishedAsConnected 0\n                -showContainerContents 0\n                -ignoreDagHierarchy 0\n                -expandConnections 1\n                -showUpstreamCurves 1\n                -showUnitlessCurves 0\n                -showCompounds 1\n                -showLeafs 1\n                -showNumericAttrsOnly 1\n                -highlightActive 0\n                -autoSelectNewObjects 0\n                -doNotSelectNewObjects 1\n"
		+ "                -dropIsParent 1\n                -transmitFilters 0\n                -setFilter \"0\" \n                -showSetMembers 0\n                -allowMultiSelection 1\n                -alwaysToggleSelect 0\n                -directSelect 0\n                -displayMode \"DAG\" \n                -expandObjects 0\n                -setsIgnoreFilters 1\n                -containersIgnoreFilters 0\n                -editAttrName 0\n                -showAttrValues 0\n                -highlightSecondary 0\n                -showUVAttrsOnly 0\n                -showTextureNodesOnly 0\n                -attrAlphaOrder \"default\" \n                -animLayerFilterOptions \"allAffecting\" \n                -sortOrder \"none\" \n                -longNames 0\n                -niceNames 1\n                -showNamespace 1\n                -showPinIcons 0\n                -mapMotionTrails 1\n                $editorName;\n\n\t\t\t$editorName = ($panelName+\"DopeSheetEd\");\n            dopeSheetEditor -e \n                -displayKeys 1\n                -displayTangents 0\n"
		+ "                -displayActiveKeys 0\n                -displayActiveKeyTangents 0\n                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"integer\" \n                -snapValue \"none\" \n                -outliner \"dopeSheetPanel1OutlineEd\" \n                -showSummary 1\n                -showScene 0\n                -hierarchyBelow 0\n                -showTicks 1\n                -selectionWindow 0 0 0 0 \n                $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"clipEditorPanel\" (localizedPanelLabel(\"Trax Editor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"clipEditorPanel\" -l (localizedPanelLabel(\"Trax Editor\")) -mbv $menusOkayInPanels `;\n\n\t\t\t$editorName = clipEditorNameFromPanel($panelName);\n            clipEditor -e \n                -displayKeys 0\n                -displayTangents 0\n                -displayActiveKeys 0\n                -displayActiveKeyTangents 0\n"
		+ "                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"none\" \n                -snapValue \"none\" \n                -manageSequencer 0 \n                $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Trax Editor\")) -mbv $menusOkayInPanels  $panelName;\n\n\t\t\t$editorName = clipEditorNameFromPanel($panelName);\n            clipEditor -e \n                -displayKeys 0\n                -displayTangents 0\n                -displayActiveKeys 0\n                -displayActiveKeyTangents 0\n                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"none\" \n                -snapValue \"none\" \n                -manageSequencer 0 \n                $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"sequenceEditorPanel\" (localizedPanelLabel(\"Camera Sequencer\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"sequenceEditorPanel\" -l (localizedPanelLabel(\"Camera Sequencer\")) -mbv $menusOkayInPanels `;\n"
		+ "\t\t\t$editorName = sequenceEditorNameFromPanel($panelName);\n            clipEditor -e \n                -displayKeys 0\n                -displayTangents 0\n                -displayActiveKeys 0\n                -displayActiveKeyTangents 0\n                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"none\" \n                -snapValue \"none\" \n                -manageSequencer 1 \n                $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Camera Sequencer\")) -mbv $menusOkayInPanels  $panelName;\n\n\t\t\t$editorName = sequenceEditorNameFromPanel($panelName);\n            clipEditor -e \n                -displayKeys 0\n                -displayTangents 0\n                -displayActiveKeys 0\n                -displayActiveKeyTangents 0\n                -displayInfinities 0\n                -autoFit 0\n                -snapTime \"none\" \n                -snapValue \"none\" \n                -manageSequencer 1 \n                $editorName;\n\t\tif (!$useSceneConfig) {\n"
		+ "\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"hyperGraphPanel\" (localizedPanelLabel(\"Hypergraph Hierarchy\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"hyperGraphPanel\" -l (localizedPanelLabel(\"Hypergraph Hierarchy\")) -mbv $menusOkayInPanels `;\n\n\t\t\t$editorName = ($panelName+\"HyperGraphEd\");\n            hyperGraph -e \n                -graphLayoutStyle \"hierarchicalLayout\" \n                -orientation \"horiz\" \n                -mergeConnections 0\n                -zoom 1\n                -animateTransition 0\n                -showRelationships 1\n                -showShapes 0\n                -showDeformers 0\n                -showExpressions 0\n                -showConstraints 0\n                -showUnderworld 0\n                -showInvisible 0\n                -transitionFrames 1\n                -opaqueContainers 0\n                -freeform 0\n                -imagePosition 0 0 \n                -imageScale 1\n"
		+ "                -imageEnabled 0\n                -graphType \"DAG\" \n                -heatMapDisplay 0\n                -updateSelection 1\n                -updateNodeAdded 1\n                -useDrawOverrideColor 0\n                -limitGraphTraversal -1\n                -range 0 0 \n                -iconSize \"smallIcons\" \n                -showCachedConnections 0\n                $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Hypergraph Hierarchy\")) -mbv $menusOkayInPanels  $panelName;\n\n\t\t\t$editorName = ($panelName+\"HyperGraphEd\");\n            hyperGraph -e \n                -graphLayoutStyle \"hierarchicalLayout\" \n                -orientation \"horiz\" \n                -mergeConnections 0\n                -zoom 1\n                -animateTransition 0\n                -showRelationships 1\n                -showShapes 0\n                -showDeformers 0\n                -showExpressions 0\n                -showConstraints 0\n                -showUnderworld 0\n                -showInvisible 0\n"
		+ "                -transitionFrames 1\n                -opaqueContainers 0\n                -freeform 0\n                -imagePosition 0 0 \n                -imageScale 1\n                -imageEnabled 0\n                -graphType \"DAG\" \n                -heatMapDisplay 0\n                -updateSelection 1\n                -updateNodeAdded 1\n                -useDrawOverrideColor 0\n                -limitGraphTraversal -1\n                -range 0 0 \n                -iconSize \"smallIcons\" \n                -showCachedConnections 0\n                $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"hyperShadePanel\" (localizedPanelLabel(\"Hypershade\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"hyperShadePanel\" -l (localizedPanelLabel(\"Hypershade\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Hypershade\")) -mbv $menusOkayInPanels  $panelName;\n"
		+ "\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"visorPanel\" (localizedPanelLabel(\"Visor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"visorPanel\" -l (localizedPanelLabel(\"Visor\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Visor\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"createNodePanel\" (localizedPanelLabel(\"Create Node\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"createNodePanel\" -l (localizedPanelLabel(\"Create Node\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Create Node\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n"
		+ "\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"polyTexturePlacementPanel\" (localizedPanelLabel(\"UV Texture Editor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"polyTexturePlacementPanel\" -l (localizedPanelLabel(\"UV Texture Editor\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"UV Texture Editor\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"renderWindowPanel\" (localizedPanelLabel(\"Render View\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"renderWindowPanel\" -l (localizedPanelLabel(\"Render View\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Render View\")) -mbv $menusOkayInPanels  $panelName;\n"
		+ "\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"blendShapePanel\" (localizedPanelLabel(\"Blend Shape\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\tblendShapePanel -unParent -l (localizedPanelLabel(\"Blend Shape\")) -mbv $menusOkayInPanels ;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tblendShapePanel -edit -l (localizedPanelLabel(\"Blend Shape\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"dynRelEdPanel\" (localizedPanelLabel(\"Dynamic Relationships\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"dynRelEdPanel\" -l (localizedPanelLabel(\"Dynamic Relationships\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Dynamic Relationships\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n"
		+ "\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"relationshipPanel\" (localizedPanelLabel(\"Relationship Editor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"relationshipPanel\" -l (localizedPanelLabel(\"Relationship Editor\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Relationship Editor\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"referenceEditorPanel\" (localizedPanelLabel(\"Reference Editor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"referenceEditorPanel\" -l (localizedPanelLabel(\"Reference Editor\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Reference Editor\")) -mbv $menusOkayInPanels  $panelName;\n"
		+ "\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"componentEditorPanel\" (localizedPanelLabel(\"Component Editor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"componentEditorPanel\" -l (localizedPanelLabel(\"Component Editor\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Component Editor\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"dynPaintScriptedPanelType\" (localizedPanelLabel(\"Paint Effects\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"dynPaintScriptedPanelType\" -l (localizedPanelLabel(\"Paint Effects\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Paint Effects\")) -mbv $menusOkayInPanels  $panelName;\n"
		+ "\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"scriptEditorPanel\" (localizedPanelLabel(\"Script Editor\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"scriptEditorPanel\" -l (localizedPanelLabel(\"Script Editor\")) -mbv $menusOkayInPanels `;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Script Editor\")) -mbv $menusOkayInPanels  $panelName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextScriptedPanel \"Stereo\" (localizedPanelLabel(\"Stereo\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `scriptedPanel -unParent  -type \"Stereo\" -l (localizedPanelLabel(\"Stereo\")) -mbv $menusOkayInPanels `;\nstring $editorName = ($panelName+\"Editor\");\n            stereoCameraView -e \n                -camera \"persp\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n"
		+ "                -displayAppearance \"wireframe\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n"
		+ "                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 4 4 \n                -bumpResolution 4 4 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 0\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n"
		+ "                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                -displayMode \"centerEye\" \n                -viewColor 0 0 0 1 \n                $editorName;\nstereoCameraView -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tscriptedPanel -edit -l (localizedPanelLabel(\"Stereo\")) -mbv $menusOkayInPanels  $panelName;\nstring $editorName = ($panelName+\"Editor\");\n            stereoCameraView -e \n                -camera \"persp\" \n                -useInteractiveMode 0\n"
		+ "                -displayLights \"default\" \n                -displayAppearance \"wireframe\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n"
		+ "                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 4 4 \n                -bumpResolution 4 4 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 0\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n"
		+ "                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                -displayMode \"centerEye\" \n                -viewColor 0 0 0 1 \n                $editorName;\nstereoCameraView -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels `;\n"
		+ "\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"persp\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"smoothShaded\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n"
		+ "                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 4 4 \n                -bumpResolution 4 4 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 0\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n"
		+ "                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"persp\" \n"
		+ "            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"smoothShaded\" \n            -activeOnly 0\n            -ignorePanZoom 0\n            -wireframeOnShaded 0\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n            -xray 0\n            -jointXray 0\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -objectFilterShowInHUD 1\n            -isFiltered 0\n"
		+ "            -colorResolution 4 4 \n            -bumpResolution 4 4 \n            -textureCompression 0\n            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 0\n            -occlusionCulling 0\n            -shadingModel 0\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n"
		+ "            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"persp\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"smoothShaded\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n"
		+ "                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 4 4 \n                -bumpResolution 4 4 \n                -textureCompression 0\n"
		+ "                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 0\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n"
		+ "                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"persp\" \n            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"smoothShaded\" \n            -activeOnly 0\n            -ignorePanZoom 0\n            -wireframeOnShaded 0\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n"
		+ "            -backfaceCulling 0\n            -xray 0\n            -jointXray 0\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -objectFilterShowInHUD 1\n            -isFiltered 0\n            -colorResolution 4 4 \n            -bumpResolution 4 4 \n            -textureCompression 0\n            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 0\n            -occlusionCulling 0\n            -shadingModel 0\n"
		+ "            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n"
		+ "\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            modelEditor -e \n                -camera \"persp\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"smoothShaded\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n"
		+ "                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 4 4 \n                -bumpResolution 4 4 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 0\n                -occlusionCulling 0\n                -shadingModel 0\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n"
		+ "                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n"
		+ "                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"persp\" \n            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"smoothShaded\" \n            -activeOnly 0\n            -ignorePanZoom 0\n            -wireframeOnShaded 0\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n            -xray 0\n            -jointXray 0\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n"
		+ "            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -objectFilterShowInHUD 1\n            -isFiltered 0\n            -colorResolution 4 4 \n            -bumpResolution 4 4 \n            -textureCompression 0\n            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 0\n            -occlusionCulling 0\n            -shadingModel 0\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n"
		+ "            -lights 1\n            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n            -hairSystems 1\n            -follicles 1\n            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\t$panelName = `sceneUIReplacement -getNextPanel \"modelPanel\" (localizedPanelLabel(\"\")) `;\n\tif (\"\" == $panelName) {\n\t\tif ($useSceneConfig) {\n\t\t\t$panelName = `modelPanel -unParent -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels `;\n\t\t\t$editorName = $panelName;\n            modelEditor -e \n"
		+ "                -camera \"persp\" \n                -useInteractiveMode 0\n                -displayLights \"default\" \n                -displayAppearance \"smoothShaded\" \n                -activeOnly 0\n                -ignorePanZoom 0\n                -wireframeOnShaded 0\n                -headsUpDisplay 1\n                -selectionHiliteDisplay 1\n                -useDefaultMaterial 0\n                -bufferMode \"double\" \n                -twoSidedLighting 1\n                -backfaceCulling 0\n                -xray 0\n                -jointXray 0\n                -activeComponentsXray 0\n                -displayTextures 0\n                -smoothWireframe 0\n                -lineWidth 1\n                -textureAnisotropic 1\n                -textureHilight 1\n                -textureSampling 6\n                -textureDisplay \"modulate\" \n                -textureMaxSize 32768\n                -fogging 0\n                -fogSource \"fragment\" \n                -fogMode \"linear\" \n                -fogStart 0\n                -fogEnd 100\n"
		+ "                -fogDensity 0.1\n                -fogColor 0.5 0.5 0.5 1 \n                -maxConstantTransparency 1\n                -rendererName \"base_OpenGL_Renderer\" \n                -objectFilterShowInHUD 1\n                -isFiltered 0\n                -colorResolution 256 256 \n                -bumpResolution 512 512 \n                -textureCompression 0\n                -transparencyAlgorithm \"frontAndBackCull\" \n                -transpInShadows 0\n                -cullingOverride \"none\" \n                -lowQualityLighting 0\n                -maximumNumHardwareLights 1\n                -occlusionCulling 0\n                -shadingModel 1\n                -useBaseRenderer 0\n                -useReducedRenderer 0\n                -smallObjectCulling 0\n                -smallObjectThreshold -1 \n                -interactiveDisableShadows 0\n                -interactiveBackFaceCull 0\n                -sortTransparent 1\n                -nurbsCurves 1\n                -nurbsSurfaces 1\n                -polymeshes 1\n                -subdivSurfaces 1\n"
		+ "                -planes 1\n                -lights 1\n                -cameras 1\n                -controlVertices 1\n                -hulls 1\n                -grid 1\n                -joints 1\n                -ikHandles 1\n                -deformers 1\n                -dynamics 1\n                -fluids 1\n                -hairSystems 1\n                -follicles 1\n                -nCloths 1\n                -nParticles 1\n                -nRigids 1\n                -dynamicConstraints 1\n                -locators 1\n                -manipulators 1\n                -dimensions 1\n                -handles 1\n                -pivots 1\n                -textures 1\n                -strokes 1\n                -motionTrails 1\n                -shadows 0\n                $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\t}\n\t} else {\n\t\t$label = `panel -q -label $panelName`;\n\t\tmodelPanel -edit -l (localizedPanelLabel(\"\")) -mbv $menusOkayInPanels  $panelName;\n\t\t$editorName = $panelName;\n        modelEditor -e \n            -camera \"persp\" \n"
		+ "            -useInteractiveMode 0\n            -displayLights \"default\" \n            -displayAppearance \"smoothShaded\" \n            -activeOnly 0\n            -ignorePanZoom 0\n            -wireframeOnShaded 0\n            -headsUpDisplay 1\n            -selectionHiliteDisplay 1\n            -useDefaultMaterial 0\n            -bufferMode \"double\" \n            -twoSidedLighting 1\n            -backfaceCulling 0\n            -xray 0\n            -jointXray 0\n            -activeComponentsXray 0\n            -displayTextures 0\n            -smoothWireframe 0\n            -lineWidth 1\n            -textureAnisotropic 1\n            -textureHilight 1\n            -textureSampling 6\n            -textureDisplay \"modulate\" \n            -textureMaxSize 32768\n            -fogging 0\n            -fogSource \"fragment\" \n            -fogMode \"linear\" \n            -fogStart 0\n            -fogEnd 100\n            -fogDensity 0.1\n            -fogColor 0.5 0.5 0.5 1 \n            -maxConstantTransparency 1\n            -rendererName \"base_OpenGL_Renderer\" \n"
		+ "            -objectFilterShowInHUD 1\n            -isFiltered 0\n            -colorResolution 256 256 \n            -bumpResolution 512 512 \n            -textureCompression 0\n            -transparencyAlgorithm \"frontAndBackCull\" \n            -transpInShadows 0\n            -cullingOverride \"none\" \n            -lowQualityLighting 0\n            -maximumNumHardwareLights 1\n            -occlusionCulling 0\n            -shadingModel 1\n            -useBaseRenderer 0\n            -useReducedRenderer 0\n            -smallObjectCulling 0\n            -smallObjectThreshold -1 \n            -interactiveDisableShadows 0\n            -interactiveBackFaceCull 0\n            -sortTransparent 1\n            -nurbsCurves 1\n            -nurbsSurfaces 1\n            -polymeshes 1\n            -subdivSurfaces 1\n            -planes 1\n            -lights 1\n            -cameras 1\n            -controlVertices 1\n            -hulls 1\n            -grid 1\n            -joints 1\n            -ikHandles 1\n            -deformers 1\n            -dynamics 1\n            -fluids 1\n"
		+ "            -hairSystems 1\n            -follicles 1\n            -nCloths 1\n            -nParticles 1\n            -nRigids 1\n            -dynamicConstraints 1\n            -locators 1\n            -manipulators 1\n            -dimensions 1\n            -handles 1\n            -pivots 1\n            -textures 1\n            -strokes 1\n            -motionTrails 1\n            -shadows 0\n            $editorName;\nmodelEditor -e -viewSelected 0 $editorName;\n\t\tif (!$useSceneConfig) {\n\t\t\tpanel -e -l $label $panelName;\n\t\t}\n\t}\n\n\n\tif ($useSceneConfig) {\n        string $configName = `getPanel -cwl (localizedPanelLabel(\"Current Layout\"))`;\n        if (\"\" != $configName) {\n\t\t\tpanelConfiguration -edit -label (localizedPanelLabel(\"Current Layout\")) \n\t\t\t\t-defaultImage \"vacantCell.xP:/\"\n\t\t\t\t-image \"\"\n\t\t\t\t-sc false\n\t\t\t\t-configString \"global string $gMainPane; paneLayout -e -cn \\\"quad\\\" -ps 1 50 50 -ps 2 50 50 -ps 3 50 50 -ps 4 50 50 $gMainPane;\"\n\t\t\t\t-removeAllPanels\n\t\t\t\t-ap false\n\t\t\t\t\t(localizedPanelLabel(\"Top View\")) \n\t\t\t\t\t\"modelPanel\"\n"
		+ "\t\t\t\t\t\"$panelName = `modelPanel -unParent -l (localizedPanelLabel(\\\"Top View\\\")) -mbv $menusOkayInPanels `;\\n$editorName = $panelName;\\nmodelEditor -e \\n    -cam `findStartUpCamera top` \\n    -useInteractiveMode 0\\n    -displayLights \\\"default\\\" \\n    -displayAppearance \\\"wireframe\\\" \\n    -activeOnly 0\\n    -ignorePanZoom 0\\n    -wireframeOnShaded 0\\n    -headsUpDisplay 1\\n    -selectionHiliteDisplay 1\\n    -useDefaultMaterial 0\\n    -bufferMode \\\"double\\\" \\n    -twoSidedLighting 1\\n    -backfaceCulling 0\\n    -xray 0\\n    -jointXray 1\\n    -activeComponentsXray 0\\n    -displayTextures 0\\n    -smoothWireframe 0\\n    -lineWidth 1\\n    -textureAnisotropic 1\\n    -textureHilight 1\\n    -textureSampling 6\\n    -textureDisplay \\\"modulate\\\" \\n    -textureMaxSize 32768\\n    -fogging 0\\n    -fogSource \\\"fragment\\\" \\n    -fogMode \\\"linear\\\" \\n    -fogStart 0\\n    -fogEnd 100\\n    -fogDensity 0.1\\n    -fogColor 0.5 0.5 0.5 1 \\n    -maxConstantTransparency 1\\n    -rendererName \\\"base_OpenGL_Renderer\\\" \\n    -objectFilterShowInHUD 1\\n    -isFiltered 0\\n    -colorResolution 256 256 \\n    -bumpResolution 512 512 \\n    -textureCompression 0\\n    -transparencyAlgorithm \\\"frontAndBackCull\\\" \\n    -transpInShadows 0\\n    -cullingOverride \\\"none\\\" \\n    -lowQualityLighting 0\\n    -maximumNumHardwareLights 1\\n    -occlusionCulling 0\\n    -shadingModel 0\\n    -useBaseRenderer 0\\n    -useReducedRenderer 0\\n    -smallObjectCulling 0\\n    -smallObjectThreshold -1 \\n    -interactiveDisableShadows 0\\n    -interactiveBackFaceCull 0\\n    -sortTransparent 1\\n    -nurbsCurves 1\\n    -nurbsSurfaces 1\\n    -polymeshes 1\\n    -subdivSurfaces 1\\n    -planes 1\\n    -lights 1\\n    -cameras 1\\n    -controlVertices 1\\n    -hulls 1\\n    -grid 1\\n    -joints 1\\n    -ikHandles 1\\n    -deformers 1\\n    -dynamics 1\\n    -fluids 1\\n    -hairSystems 1\\n    -follicles 1\\n    -nCloths 1\\n    -nParticles 1\\n    -nRigids 1\\n    -dynamicConstraints 1\\n    -locators 1\\n    -manipulators 1\\n    -dimensions 1\\n    -handles 1\\n    -pivots 1\\n    -textures 1\\n    -strokes 1\\n    -motionTrails 1\\n    -shadows 0\\n    $editorName;\\nmodelEditor -e -viewSelected 0 $editorName\"\n"
		+ "\t\t\t\t\t\"modelPanel -edit -l (localizedPanelLabel(\\\"Top View\\\")) -mbv $menusOkayInPanels  $panelName;\\n$editorName = $panelName;\\nmodelEditor -e \\n    -cam `findStartUpCamera top` \\n    -useInteractiveMode 0\\n    -displayLights \\\"default\\\" \\n    -displayAppearance \\\"wireframe\\\" \\n    -activeOnly 0\\n    -ignorePanZoom 0\\n    -wireframeOnShaded 0\\n    -headsUpDisplay 1\\n    -selectionHiliteDisplay 1\\n    -useDefaultMaterial 0\\n    -bufferMode \\\"double\\\" \\n    -twoSidedLighting 1\\n    -backfaceCulling 0\\n    -xray 0\\n    -jointXray 1\\n    -activeComponentsXray 0\\n    -displayTextures 0\\n    -smoothWireframe 0\\n    -lineWidth 1\\n    -textureAnisotropic 1\\n    -textureHilight 1\\n    -textureSampling 6\\n    -textureDisplay \\\"modulate\\\" \\n    -textureMaxSize 32768\\n    -fogging 0\\n    -fogSource \\\"fragment\\\" \\n    -fogMode \\\"linear\\\" \\n    -fogStart 0\\n    -fogEnd 100\\n    -fogDensity 0.1\\n    -fogColor 0.5 0.5 0.5 1 \\n    -maxConstantTransparency 1\\n    -rendererName \\\"base_OpenGL_Renderer\\\" \\n    -objectFilterShowInHUD 1\\n    -isFiltered 0\\n    -colorResolution 256 256 \\n    -bumpResolution 512 512 \\n    -textureCompression 0\\n    -transparencyAlgorithm \\\"frontAndBackCull\\\" \\n    -transpInShadows 0\\n    -cullingOverride \\\"none\\\" \\n    -lowQualityLighting 0\\n    -maximumNumHardwareLights 1\\n    -occlusionCulling 0\\n    -shadingModel 0\\n    -useBaseRenderer 0\\n    -useReducedRenderer 0\\n    -smallObjectCulling 0\\n    -smallObjectThreshold -1 \\n    -interactiveDisableShadows 0\\n    -interactiveBackFaceCull 0\\n    -sortTransparent 1\\n    -nurbsCurves 1\\n    -nurbsSurfaces 1\\n    -polymeshes 1\\n    -subdivSurfaces 1\\n    -planes 1\\n    -lights 1\\n    -cameras 1\\n    -controlVertices 1\\n    -hulls 1\\n    -grid 1\\n    -joints 1\\n    -ikHandles 1\\n    -deformers 1\\n    -dynamics 1\\n    -fluids 1\\n    -hairSystems 1\\n    -follicles 1\\n    -nCloths 1\\n    -nParticles 1\\n    -nRigids 1\\n    -dynamicConstraints 1\\n    -locators 1\\n    -manipulators 1\\n    -dimensions 1\\n    -handles 1\\n    -pivots 1\\n    -textures 1\\n    -strokes 1\\n    -motionTrails 1\\n    -shadows 0\\n    $editorName;\\nmodelEditor -e -viewSelected 0 $editorName\"\n"
		+ "\t\t\t\t-ap false\n\t\t\t\t\t(localizedPanelLabel(\"Persp View\")) \n\t\t\t\t\t\"modelPanel\"\n"
		+ "\t\t\t\t\t\"$panelName = `modelPanel -unParent -l (localizedPanelLabel(\\\"Persp View\\\")) -mbv $menusOkayInPanels `;\\n$editorName = $panelName;\\nmodelEditor -e \\n    -cam `findStartUpCamera persp` \\n    -useInteractiveMode 0\\n    -displayLights \\\"default\\\" \\n    -displayAppearance \\\"smoothShaded\\\" \\n    -activeOnly 0\\n    -ignorePanZoom 0\\n    -wireframeOnShaded 1\\n    -headsUpDisplay 1\\n    -selectionHiliteDisplay 1\\n    -useDefaultMaterial 0\\n    -bufferMode \\\"double\\\" \\n    -twoSidedLighting 1\\n    -backfaceCulling 0\\n    -xray 0\\n    -jointXray 1\\n    -activeComponentsXray 0\\n    -displayTextures 0\\n    -smoothWireframe 0\\n    -lineWidth 1\\n    -textureAnisotropic 1\\n    -textureHilight 1\\n    -textureSampling 6\\n    -textureDisplay \\\"modulate\\\" \\n    -textureMaxSize 32768\\n    -fogging 0\\n    -fogSource \\\"fragment\\\" \\n    -fogMode \\\"linear\\\" \\n    -fogStart 0\\n    -fogEnd 100\\n    -fogDensity 0.1\\n    -fogColor 0.5 0.5 0.5 1 \\n    -maxConstantTransparency 1\\n    -rendererName \\\"base_OpenGL_Renderer\\\" \\n    -objectFilterShowInHUD 1\\n    -isFiltered 0\\n    -colorResolution 256 256 \\n    -bumpResolution 512 512 \\n    -textureCompression 0\\n    -transparencyAlgorithm \\\"frontAndBackCull\\\" \\n    -transpInShadows 0\\n    -cullingOverride \\\"none\\\" \\n    -lowQualityLighting 0\\n    -maximumNumHardwareLights 1\\n    -occlusionCulling 0\\n    -shadingModel 0\\n    -useBaseRenderer 0\\n    -useReducedRenderer 0\\n    -smallObjectCulling 0\\n    -smallObjectThreshold -1 \\n    -interactiveDisableShadows 0\\n    -interactiveBackFaceCull 0\\n    -sortTransparent 1\\n    -nurbsCurves 1\\n    -nurbsSurfaces 1\\n    -polymeshes 1\\n    -subdivSurfaces 1\\n    -planes 1\\n    -lights 1\\n    -cameras 1\\n    -controlVertices 1\\n    -hulls 1\\n    -grid 1\\n    -joints 1\\n    -ikHandles 1\\n    -deformers 1\\n    -dynamics 1\\n    -fluids 1\\n    -hairSystems 1\\n    -follicles 1\\n    -nCloths 1\\n    -nParticles 1\\n    -nRigids 1\\n    -dynamicConstraints 1\\n    -locators 1\\n    -manipulators 1\\n    -dimensions 1\\n    -handles 1\\n    -pivots 1\\n    -textures 1\\n    -strokes 1\\n    -motionTrails 1\\n    -shadows 0\\n    $editorName;\\nmodelEditor -e -viewSelected 0 $editorName\"\n"
		+ "\t\t\t\t\t\"modelPanel -edit -l (localizedPanelLabel(\\\"Persp View\\\")) -mbv $menusOkayInPanels  $panelName;\\n$editorName = $panelName;\\nmodelEditor -e \\n    -cam `findStartUpCamera persp` \\n    -useInteractiveMode 0\\n    -displayLights \\\"default\\\" \\n    -displayAppearance \\\"smoothShaded\\\" \\n    -activeOnly 0\\n    -ignorePanZoom 0\\n    -wireframeOnShaded 1\\n    -headsUpDisplay 1\\n    -selectionHiliteDisplay 1\\n    -useDefaultMaterial 0\\n    -bufferMode \\\"double\\\" \\n    -twoSidedLighting 1\\n    -backfaceCulling 0\\n    -xray 0\\n    -jointXray 1\\n    -activeComponentsXray 0\\n    -displayTextures 0\\n    -smoothWireframe 0\\n    -lineWidth 1\\n    -textureAnisotropic 1\\n    -textureHilight 1\\n    -textureSampling 6\\n    -textureDisplay \\\"modulate\\\" \\n    -textureMaxSize 32768\\n    -fogging 0\\n    -fogSource \\\"fragment\\\" \\n    -fogMode \\\"linear\\\" \\n    -fogStart 0\\n    -fogEnd 100\\n    -fogDensity 0.1\\n    -fogColor 0.5 0.5 0.5 1 \\n    -maxConstantTransparency 1\\n    -rendererName \\\"base_OpenGL_Renderer\\\" \\n    -objectFilterShowInHUD 1\\n    -isFiltered 0\\n    -colorResolution 256 256 \\n    -bumpResolution 512 512 \\n    -textureCompression 0\\n    -transparencyAlgorithm \\\"frontAndBackCull\\\" \\n    -transpInShadows 0\\n    -cullingOverride \\\"none\\\" \\n    -lowQualityLighting 0\\n    -maximumNumHardwareLights 1\\n    -occlusionCulling 0\\n    -shadingModel 0\\n    -useBaseRenderer 0\\n    -useReducedRenderer 0\\n    -smallObjectCulling 0\\n    -smallObjectThreshold -1 \\n    -interactiveDisableShadows 0\\n    -interactiveBackFaceCull 0\\n    -sortTransparent 1\\n    -nurbsCurves 1\\n    -nurbsSurfaces 1\\n    -polymeshes 1\\n    -subdivSurfaces 1\\n    -planes 1\\n    -lights 1\\n    -cameras 1\\n    -controlVertices 1\\n    -hulls 1\\n    -grid 1\\n    -joints 1\\n    -ikHandles 1\\n    -deformers 1\\n    -dynamics 1\\n    -fluids 1\\n    -hairSystems 1\\n    -follicles 1\\n    -nCloths 1\\n    -nParticles 1\\n    -nRigids 1\\n    -dynamicConstraints 1\\n    -locators 1\\n    -manipulators 1\\n    -dimensions 1\\n    -handles 1\\n    -pivots 1\\n    -textures 1\\n    -strokes 1\\n    -motionTrails 1\\n    -shadows 0\\n    $editorName;\\nmodelEditor -e -viewSelected 0 $editorName\"\n"
		+ "\t\t\t\t-ap false\n\t\t\t\t\t(localizedPanelLabel(\"Side View\")) \n\t\t\t\t\t\"modelPanel\"\n"
		+ "\t\t\t\t\t\"$panelName = `modelPanel -unParent -l (localizedPanelLabel(\\\"Side View\\\")) -mbv $menusOkayInPanels `;\\n$editorName = $panelName;\\nmodelEditor -e \\n    -cam `findStartUpCamera side` \\n    -useInteractiveMode 0\\n    -displayLights \\\"default\\\" \\n    -displayAppearance \\\"wireframe\\\" \\n    -activeOnly 0\\n    -ignorePanZoom 0\\n    -wireframeOnShaded 0\\n    -headsUpDisplay 1\\n    -selectionHiliteDisplay 1\\n    -useDefaultMaterial 0\\n    -bufferMode \\\"double\\\" \\n    -twoSidedLighting 1\\n    -backfaceCulling 0\\n    -xray 0\\n    -jointXray 1\\n    -activeComponentsXray 0\\n    -displayTextures 0\\n    -smoothWireframe 0\\n    -lineWidth 1\\n    -textureAnisotropic 1\\n    -textureHilight 1\\n    -textureSampling 6\\n    -textureDisplay \\\"modulate\\\" \\n    -textureMaxSize 32768\\n    -fogging 0\\n    -fogSource \\\"fragment\\\" \\n    -fogMode \\\"linear\\\" \\n    -fogStart 0\\n    -fogEnd 100\\n    -fogDensity 0.1\\n    -fogColor 0.5 0.5 0.5 1 \\n    -maxConstantTransparency 1\\n    -rendererName \\\"base_OpenGL_Renderer\\\" \\n    -objectFilterShowInHUD 1\\n    -isFiltered 0\\n    -colorResolution 256 256 \\n    -bumpResolution 512 512 \\n    -textureCompression 0\\n    -transparencyAlgorithm \\\"frontAndBackCull\\\" \\n    -transpInShadows 0\\n    -cullingOverride \\\"none\\\" \\n    -lowQualityLighting 0\\n    -maximumNumHardwareLights 1\\n    -occlusionCulling 0\\n    -shadingModel 0\\n    -useBaseRenderer 0\\n    -useReducedRenderer 0\\n    -smallObjectCulling 0\\n    -smallObjectThreshold -1 \\n    -interactiveDisableShadows 0\\n    -interactiveBackFaceCull 0\\n    -sortTransparent 1\\n    -nurbsCurves 1\\n    -nurbsSurfaces 1\\n    -polymeshes 1\\n    -subdivSurfaces 1\\n    -planes 1\\n    -lights 1\\n    -cameras 1\\n    -controlVertices 1\\n    -hulls 1\\n    -grid 1\\n    -joints 1\\n    -ikHandles 1\\n    -deformers 1\\n    -dynamics 1\\n    -fluids 1\\n    -hairSystems 1\\n    -follicles 1\\n    -nCloths 1\\n    -nParticles 1\\n    -nRigids 1\\n    -dynamicConstraints 1\\n    -locators 1\\n    -manipulators 1\\n    -dimensions 1\\n    -handles 1\\n    -pivots 1\\n    -textures 1\\n    -strokes 1\\n    -motionTrails 1\\n    -shadows 0\\n    $editorName;\\nmodelEditor -e -viewSelected 0 $editorName\"\n"
		+ "\t\t\t\t\t\"modelPanel -edit -l (localizedPanelLabel(\\\"Side View\\\")) -mbv $menusOkayInPanels  $panelName;\\n$editorName = $panelName;\\nmodelEditor -e \\n    -cam `findStartUpCamera side` \\n    -useInteractiveMode 0\\n    -displayLights \\\"default\\\" \\n    -displayAppearance \\\"wireframe\\\" \\n    -activeOnly 0\\n    -ignorePanZoom 0\\n    -wireframeOnShaded 0\\n    -headsUpDisplay 1\\n    -selectionHiliteDisplay 1\\n    -useDefaultMaterial 0\\n    -bufferMode \\\"double\\\" \\n    -twoSidedLighting 1\\n    -backfaceCulling 0\\n    -xray 0\\n    -jointXray 1\\n    -activeComponentsXray 0\\n    -displayTextures 0\\n    -smoothWireframe 0\\n    -lineWidth 1\\n    -textureAnisotropic 1\\n    -textureHilight 1\\n    -textureSampling 6\\n    -textureDisplay \\\"modulate\\\" \\n    -textureMaxSize 32768\\n    -fogging 0\\n    -fogSource \\\"fragment\\\" \\n    -fogMode \\\"linear\\\" \\n    -fogStart 0\\n    -fogEnd 100\\n    -fogDensity 0.1\\n    -fogColor 0.5 0.5 0.5 1 \\n    -maxConstantTransparency 1\\n    -rendererName \\\"base_OpenGL_Renderer\\\" \\n    -objectFilterShowInHUD 1\\n    -isFiltered 0\\n    -colorResolution 256 256 \\n    -bumpResolution 512 512 \\n    -textureCompression 0\\n    -transparencyAlgorithm \\\"frontAndBackCull\\\" \\n    -transpInShadows 0\\n    -cullingOverride \\\"none\\\" \\n    -lowQualityLighting 0\\n    -maximumNumHardwareLights 1\\n    -occlusionCulling 0\\n    -shadingModel 0\\n    -useBaseRenderer 0\\n    -useReducedRenderer 0\\n    -smallObjectCulling 0\\n    -smallObjectThreshold -1 \\n    -interactiveDisableShadows 0\\n    -interactiveBackFaceCull 0\\n    -sortTransparent 1\\n    -nurbsCurves 1\\n    -nurbsSurfaces 1\\n    -polymeshes 1\\n    -subdivSurfaces 1\\n    -planes 1\\n    -lights 1\\n    -cameras 1\\n    -controlVertices 1\\n    -hulls 1\\n    -grid 1\\n    -joints 1\\n    -ikHandles 1\\n    -deformers 1\\n    -dynamics 1\\n    -fluids 1\\n    -hairSystems 1\\n    -follicles 1\\n    -nCloths 1\\n    -nParticles 1\\n    -nRigids 1\\n    -dynamicConstraints 1\\n    -locators 1\\n    -manipulators 1\\n    -dimensions 1\\n    -handles 1\\n    -pivots 1\\n    -textures 1\\n    -strokes 1\\n    -motionTrails 1\\n    -shadows 0\\n    $editorName;\\nmodelEditor -e -viewSelected 0 $editorName\"\n"
		+ "\t\t\t\t-ap false\n\t\t\t\t\t(localizedPanelLabel(\"Front View\")) \n\t\t\t\t\t\"modelPanel\"\n"
		+ "\t\t\t\t\t\"$panelName = `modelPanel -unParent -l (localizedPanelLabel(\\\"Front View\\\")) -mbv $menusOkayInPanels `;\\n$editorName = $panelName;\\nmodelEditor -e \\n    -cam `findStartUpCamera front` \\n    -useInteractiveMode 0\\n    -displayLights \\\"default\\\" \\n    -displayAppearance \\\"wireframe\\\" \\n    -activeOnly 0\\n    -ignorePanZoom 0\\n    -wireframeOnShaded 0\\n    -headsUpDisplay 1\\n    -selectionHiliteDisplay 1\\n    -useDefaultMaterial 0\\n    -bufferMode \\\"double\\\" \\n    -twoSidedLighting 1\\n    -backfaceCulling 0\\n    -xray 0\\n    -jointXray 1\\n    -activeComponentsXray 0\\n    -displayTextures 0\\n    -smoothWireframe 0\\n    -lineWidth 1\\n    -textureAnisotropic 1\\n    -textureHilight 1\\n    -textureSampling 6\\n    -textureDisplay \\\"modulate\\\" \\n    -textureMaxSize 32768\\n    -fogging 0\\n    -fogSource \\\"fragment\\\" \\n    -fogMode \\\"linear\\\" \\n    -fogStart 0\\n    -fogEnd 100\\n    -fogDensity 0.1\\n    -fogColor 0.5 0.5 0.5 1 \\n    -maxConstantTransparency 1\\n    -rendererName \\\"base_OpenGL_Renderer\\\" \\n    -objectFilterShowInHUD 1\\n    -isFiltered 0\\n    -colorResolution 256 256 \\n    -bumpResolution 512 512 \\n    -textureCompression 0\\n    -transparencyAlgorithm \\\"frontAndBackCull\\\" \\n    -transpInShadows 0\\n    -cullingOverride \\\"none\\\" \\n    -lowQualityLighting 0\\n    -maximumNumHardwareLights 1\\n    -occlusionCulling 0\\n    -shadingModel 0\\n    -useBaseRenderer 0\\n    -useReducedRenderer 0\\n    -smallObjectCulling 0\\n    -smallObjectThreshold -1 \\n    -interactiveDisableShadows 0\\n    -interactiveBackFaceCull 0\\n    -sortTransparent 1\\n    -nurbsCurves 1\\n    -nurbsSurfaces 1\\n    -polymeshes 1\\n    -subdivSurfaces 1\\n    -planes 1\\n    -lights 1\\n    -cameras 1\\n    -controlVertices 1\\n    -hulls 1\\n    -grid 1\\n    -joints 1\\n    -ikHandles 1\\n    -deformers 1\\n    -dynamics 1\\n    -fluids 1\\n    -hairSystems 1\\n    -follicles 1\\n    -nCloths 1\\n    -nParticles 1\\n    -nRigids 1\\n    -dynamicConstraints 1\\n    -locators 1\\n    -manipulators 1\\n    -dimensions 1\\n    -handles 1\\n    -pivots 1\\n    -textures 1\\n    -strokes 1\\n    -motionTrails 1\\n    -shadows 0\\n    $editorName;\\nmodelEditor -e -viewSelected 0 $editorName\"\n"
		+ "\t\t\t\t\t\"modelPanel -edit -l (localizedPanelLabel(\\\"Front View\\\")) -mbv $menusOkayInPanels  $panelName;\\n$editorName = $panelName;\\nmodelEditor -e \\n    -cam `findStartUpCamera front` \\n    -useInteractiveMode 0\\n    -displayLights \\\"default\\\" \\n    -displayAppearance \\\"wireframe\\\" \\n    -activeOnly 0\\n    -ignorePanZoom 0\\n    -wireframeOnShaded 0\\n    -headsUpDisplay 1\\n    -selectionHiliteDisplay 1\\n    -useDefaultMaterial 0\\n    -bufferMode \\\"double\\\" \\n    -twoSidedLighting 1\\n    -backfaceCulling 0\\n    -xray 0\\n    -jointXray 1\\n    -activeComponentsXray 0\\n    -displayTextures 0\\n    -smoothWireframe 0\\n    -lineWidth 1\\n    -textureAnisotropic 1\\n    -textureHilight 1\\n    -textureSampling 6\\n    -textureDisplay \\\"modulate\\\" \\n    -textureMaxSize 32768\\n    -fogging 0\\n    -fogSource \\\"fragment\\\" \\n    -fogMode \\\"linear\\\" \\n    -fogStart 0\\n    -fogEnd 100\\n    -fogDensity 0.1\\n    -fogColor 0.5 0.5 0.5 1 \\n    -maxConstantTransparency 1\\n    -rendererName \\\"base_OpenGL_Renderer\\\" \\n    -objectFilterShowInHUD 1\\n    -isFiltered 0\\n    -colorResolution 256 256 \\n    -bumpResolution 512 512 \\n    -textureCompression 0\\n    -transparencyAlgorithm \\\"frontAndBackCull\\\" \\n    -transpInShadows 0\\n    -cullingOverride \\\"none\\\" \\n    -lowQualityLighting 0\\n    -maximumNumHardwareLights 1\\n    -occlusionCulling 0\\n    -shadingModel 0\\n    -useBaseRenderer 0\\n    -useReducedRenderer 0\\n    -smallObjectCulling 0\\n    -smallObjectThreshold -1 \\n    -interactiveDisableShadows 0\\n    -interactiveBackFaceCull 0\\n    -sortTransparent 1\\n    -nurbsCurves 1\\n    -nurbsSurfaces 1\\n    -polymeshes 1\\n    -subdivSurfaces 1\\n    -planes 1\\n    -lights 1\\n    -cameras 1\\n    -controlVertices 1\\n    -hulls 1\\n    -grid 1\\n    -joints 1\\n    -ikHandles 1\\n    -deformers 1\\n    -dynamics 1\\n    -fluids 1\\n    -hairSystems 1\\n    -follicles 1\\n    -nCloths 1\\n    -nParticles 1\\n    -nRigids 1\\n    -dynamicConstraints 1\\n    -locators 1\\n    -manipulators 1\\n    -dimensions 1\\n    -handles 1\\n    -pivots 1\\n    -textures 1\\n    -strokes 1\\n    -motionTrails 1\\n    -shadows 0\\n    $editorName;\\nmodelEditor -e -viewSelected 0 $editorName\"\n"
		+ "\t\t\t\t$configName;\n\n            setNamedPanelLayout (localizedPanelLabel(\"Current Layout\"));\n        }\n\n        panelHistory -e -clear mainPanelHistory;\n        setFocus `paneLayout -q -p1 $gMainPane`;\n        sceneUIReplacement -deleteRemaining;\n        sceneUIReplacement -clear;\n\t}\n\n\ngrid -spacing 5 -size 12 -divisions 5 -displayAxes yes -displayGridLines yes -displayDivisionLines yes -displayPerspectiveLabels no -displayOrthographicLabels no -displayAxesBold yes -perspectiveLabelPosition axis -orthographicLabelPosition edge;\nviewManip -drawCompass 0 -compassAngle 0 -frontParameters \"\" -homeParameters \"\" -selectionLockParameters \"\";\n}\n");
	setAttr ".st" 3;
createNode script -n "sceneConfigurationScriptNode";
	setAttr ".b" -type "string" "playbackOptions -min 1 -max 24 -ast 1 -aet 48 ";
	setAttr ".st" 6;
select -ne :time1;
	setAttr -av -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".o" 0;
	setAttr -av ".unw";
	setAttr -k on ".etw";
	setAttr -k on ".tps";
	setAttr -k on ".tms";
lockNode -l 1 ;
select -ne :renderPartition;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 16 ".st";
	setAttr -cb on ".an";
	setAttr -cb on ".pt";
lockNode -l 1 ;
select -ne :initialShadingGroup;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".mwc";
	setAttr -cb on ".an";
	setAttr -cb on ".il";
	setAttr -cb on ".vo";
	setAttr -cb on ".eo";
	setAttr -cb on ".fo";
	setAttr -cb on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :initialParticleSE;
	setAttr -av -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".mwc";
	setAttr -cb on ".an";
	setAttr -cb on ".il";
	setAttr -cb on ".vo";
	setAttr -cb on ".eo";
	setAttr -cb on ".fo";
	setAttr -cb on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :defaultShaderList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 16 ".s";
lockNode -l 1 ;
select -ne :postProcessList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 2 ".p";
lockNode -l 1 ;
select -ne :defaultRenderingList1;
	setAttr -s 2 ".r";
lockNode -l 1 ;
select -ne :renderGlobalsList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
lockNode -l 1 ;
select -ne :defaultRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".macc";
	setAttr -k on ".macd";
	setAttr -k on ".macq";
	setAttr -k on ".mcfr";
	setAttr -cb on ".ifg";
	setAttr -k on ".clip";
	setAttr -k on ".edm";
	setAttr -k on ".edl";
	setAttr -cb on ".ren";
	setAttr -av -k on ".esr";
	setAttr -k on ".ors";
	setAttr -cb on ".sdf";
	setAttr -av -k on ".outf";
	setAttr -cb on ".imfkey";
	setAttr -k on ".gama";
	setAttr -k on ".an";
	setAttr -cb on ".ar";
	setAttr -k on ".fs" 1;
	setAttr -k on ".ef" 10;
	setAttr -av -k on ".bfs";
	setAttr -cb on ".me";
	setAttr -cb on ".se";
	setAttr -k on ".be";
	setAttr -cb on ".ep";
	setAttr -k on ".fec";
	setAttr -av -k on ".ofc";
	setAttr -cb on ".ofe";
	setAttr -cb on ".efe";
	setAttr -cb on ".oft";
	setAttr -cb on ".umfn";
	setAttr -cb on ".ufe";
	setAttr -cb on ".pff";
	setAttr -cb on ".peie";
	setAttr -cb on ".ifp";
	setAttr -k on ".comp";
	setAttr -k on ".cth";
	setAttr -k on ".soll";
	setAttr -k on ".sosl";
	setAttr -k on ".rd";
	setAttr -k on ".lp";
	setAttr -av -k on ".sp";
	setAttr -k on ".shs";
	setAttr -av -k on ".lpr";
	setAttr -cb on ".gv";
	setAttr -cb on ".sv";
	setAttr -k on ".mm";
	setAttr -k on ".npu";
	setAttr -k on ".itf";
	setAttr -k on ".shp";
	setAttr -cb on ".isp";
	setAttr -k on ".uf";
	setAttr -k on ".oi";
	setAttr -k on ".rut";
	setAttr -cb on ".mb";
	setAttr -av -k on ".mbf";
	setAttr -k on ".afp";
	setAttr -k on ".pfb";
	setAttr -k on ".pram";
	setAttr -k on ".poam";
	setAttr -k on ".prlm";
	setAttr -k on ".polm";
	setAttr -cb on ".prm";
	setAttr -cb on ".pom";
	setAttr -cb on ".pfrm";
	setAttr -cb on ".pfom";
	setAttr -av -k on ".bll";
	setAttr -k on ".bls";
	setAttr -av -k on ".smv";
	setAttr -k on ".ubc";
	setAttr -k on ".mbc";
	setAttr -cb on ".mbt";
	setAttr -k on ".udbx";
	setAttr -k on ".smc";
	setAttr -k on ".kmv";
	setAttr -cb on ".isl";
	setAttr -cb on ".ism";
	setAttr -cb on ".imb";
	setAttr -k on ".rlen";
	setAttr -av -k on ".frts";
	setAttr -k on ".tlwd";
	setAttr -k on ".tlht";
	setAttr -k on ".jfc";
	setAttr -cb on ".rsb";
	setAttr -k on ".ope";
	setAttr -k on ".oppf";
	setAttr -cb on ".hbl";
select -ne :defaultResolution;
	setAttr -av -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -av -k on ".w";
	setAttr -av -k on ".h";
	setAttr -av -k on ".pa" 1;
	setAttr -av -k on ".al";
	setAttr -av -k on ".dar";
	setAttr -av -k on ".ldar";
	setAttr -k on ".dpi";
	setAttr -av -k on ".off";
	setAttr -av -k on ".fld";
	setAttr -av -k on ".zsl";
	setAttr -k on ".isu";
	setAttr -k on ".pdu";
select -ne :defaultLightSet;
	setAttr -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -k on ".mwc";
	setAttr -k on ".an";
	setAttr -k on ".il";
	setAttr -k on ".vo";
	setAttr -k on ".eo";
	setAttr -k on ".fo";
	setAttr -k on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :defaultObjectSet;
	setAttr -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -k on ".mwc";
	setAttr -k on ".an";
	setAttr -k on ".il";
	setAttr -k on ".vo";
	setAttr -k on ".eo";
	setAttr -k on ".fo";
	setAttr -k on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :hardwareRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k off ".ctrs" 256;
	setAttr -av -k off ".btrs" 512;
	setAttr -k off ".fbfm";
	setAttr -k off -cb on ".ehql";
	setAttr -k off -cb on ".eams";
	setAttr -k off -cb on ".eeaa";
	setAttr -k off -cb on ".engm";
	setAttr -k off -cb on ".mes";
	setAttr -k off -cb on ".emb";
	setAttr -av -k off -cb on ".mbbf";
	setAttr -k off -cb on ".mbs";
	setAttr -k off -cb on ".trm";
	setAttr -k off -cb on ".tshc";
	setAttr -k off ".enpt";
	setAttr -k off -cb on ".clmt";
	setAttr -k off -cb on ".tcov";
	setAttr -k off -cb on ".lith";
	setAttr -k off -cb on ".sobc";
	setAttr -k off -cb on ".cuth";
	setAttr -k off -cb on ".hgcd";
	setAttr -k off -cb on ".hgci";
	setAttr -k off -cb on ".mgcs";
	setAttr -k off -cb on ".twa";
	setAttr -k off -cb on ".twz";
	setAttr -k on ".hwcc";
	setAttr -k on ".hwdp";
	setAttr -k on ".hwql";
	setAttr -k on ".hwfr";
	setAttr -k on ".soll";
	setAttr -k on ".sosl";
	setAttr -k on ".bswa";
	setAttr -k on ".shml";
	setAttr -k on ".hwel";
lockNode -l 1 ;
select -ne :hardwareRenderingGlobals;
	setAttr ".otfna" -type "stringArray" 22 "NURBS Curves" "NURBS Surfaces" "Polygons" "Subdiv Surface" "Particles" "Particle Instance" "Fluids" "Strokes" "Image Planes" "UI" "Lights" "Cameras" "Locators" "Joints" "IK Handles" "Deformers" "Motion Trails" "Components" "Hair Systems" "Follicles" "Misc. UI" "Ornaments"  ;
	setAttr ".otfva" -type "Int32Array" 22 0 1 1 1 1 1
		 1 1 1 0 0 0 0 0 0 0 0 0
		 0 0 0 0 ;
	setAttr ".fprt" yes;
select -ne :defaultHardwareRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -av -k on ".rp";
	setAttr -k on ".cai";
	setAttr -k on ".coi";
	setAttr -cb on ".bc";
	setAttr -av -k on ".bcb";
	setAttr -av -k on ".bcg";
	setAttr -av -k on ".bcr";
	setAttr -k on ".ei";
	setAttr -av -k on ".ex";
	setAttr -av -k on ".es";
	setAttr -av -k on ".ef";
	setAttr -av -k on ".bf";
	setAttr -k on ".fii";
	setAttr -av -k on ".sf";
	setAttr -k on ".gr";
	setAttr -k on ".li";
	setAttr -k on ".ls";
	setAttr -av -k on ".mb";
	setAttr -k on ".ti";
	setAttr -k on ".txt";
	setAttr -k on ".mpr";
	setAttr -k on ".wzd";
	setAttr -k on ".fn" -type "string" "im";
	setAttr -k on ".if";
	setAttr -k on ".res" -type "string" "ntsc_4d 646 485 1.333";
	setAttr -k on ".as";
	setAttr -k on ".ds";
	setAttr -k on ".lm";
	setAttr -av -k on ".fir";
	setAttr -k on ".aap";
	setAttr -av -k on ".gh";
	setAttr -cb on ".sd";
lockNode -l 1 ;
connectAttr "groupId1.id" "headTopologyShape.iog.og[0].gid";
connectAttr ":initialShadingGroup.mwc" "headTopologyShape.iog.og[0].gco";
connectAttr "groupParts1.og" "headTopologyShape.i";
connectAttr "MainCurves.di" "eyeLidMainCurve.do";
connectAttr "eyeLidMainLocShape0.wp" "eyeLidMainCurveShape.cp[0]";
connectAttr "eyeLidMainLocShape1.wp" "eyeLidMainCurveShape.cp[1]";
connectAttr "eyeLidMainLocShape2.wp" "eyeLidMainCurveShape.cp[2]";
connectAttr "eyeLidMainLocShape3.wp" "eyeLidMainCurveShape.cp[3]";
connectAttr "eyeLidMainLocShape4.wp" "eyeLidMainCurveShape.cp[4]";
connectAttr "eyeLidMainLocShape5.wp" "eyeLidMainCurveShape.cp[5]";
connectAttr "eyeLidMainLocShape6.wp" "eyeLidMainCurveShape.cp[6]";
connectAttr "eyeLidMainLocShape7.wp" "eyeLidMainCurveShape.cp[7]";
connectAttr "eyeLidMainLocShape0.wp" "eyeLidMainCurveShape.cp[8]";
connectAttr "MainCurves.di" "EyeBrowMainCurve2.do";
connectAttr "eyeBrowMainLocShape0.wp" "EyeBrowMainCurve2Shape.cp[0]";
connectAttr "eyeBrowMainLocShape1.wp" "EyeBrowMainCurve2Shape.cp[1]";
connectAttr "eyeBrowMainLocShape2.wp" "EyeBrowMainCurve2Shape.cp[2]";
connectAttr "eyeBrowMainLocShape3.wp" "EyeBrowMainCurve2Shape.cp[3]";
connectAttr "MainCurves.di" "upperLipMainCurve2.do";
connectAttr "upperLipMainLocShape0.wp" "upperLipMainCurve2Shape.cp[0]";
connectAttr "upperLipMainLocShape1.wp" "upperLipMainCurve2Shape.cp[1]";
connectAttr "lipMainLocShape0.wp" "upperLipMainCurve2Shape.cp[2]";
connectAttr "MainCurves.di" "lowerLipMainCurve2.do";
connectAttr "lowerLipMainLocShape0.wp" "lowerLipMainCurve2Shape.cp[0]";
connectAttr "lowerLipMainLocShape1.wp" "lowerLipMainCurve2Shape.cp[1]";
connectAttr "lipMainLocShape0.wp" "lowerLipMainCurve2Shape.cp[2]";
connectAttr "MainCurves.di" "JawCurveMid.do";
connectAttr "lipMainLocShape0.wp" "JawCurveMidShape.cp[0]";
connectAttr "JawMidLocShape1.wp" "JawCurveMidShape.cp[1]";
connectAttr "JawMidLocShape2.wp" "JawCurveMidShape.cp[2]";
connectAttr "MainCurves.di" "EarCurve.do";
connectAttr "EarLocShape0.wp" "EarCurveShape.cp[0]";
connectAttr "EarLocShape1.wp" "EarCurveShape.cp[1]";
connectAttr "EarLocShape2.wp" "EarCurveShape.cp[2]";
connectAttr "EarLocShape3.wp" "EarCurveShape.cp[3]";
connectAttr "EarLocShape4.wp" "EarCurveShape.cp[4]";
connectAttr "EarLocShape5.wp" "EarCurveShape.cp[5]";
connectAttr "ProfileCurves.di" "HeadProfileSideCurve.do";
connectAttr "HeadProfileSideLoc0Shape.wp" "HeadProfileSideCurveShape.cp[0]";
connectAttr "HeadProfileSideLoc1Shape.wp" "HeadProfileSideCurveShape.cp[1]";
connectAttr "HeadProfileSideLoc2Shape.wp" "HeadProfileSideCurveShape.cp[2]";
connectAttr "HeadProfileSideLoc3Shape.wp" "HeadProfileSideCurveShape.cp[3]";
connectAttr "HeadProfileSideLoc4Shape.wp" "HeadProfileSideCurveShape.cp[4]";
connectAttr "HeadProfileSideLoc5Shape.wp" "HeadProfileSideCurveShape.cp[5]";
connectAttr "HeadProfileSideLoc6Shape.wp" "HeadProfileSideCurveShape.cp[6]";
connectAttr "HeadProfileSideLoc0Shape.wp" "HeadProfileSideCurveShape.cp[10]";
connectAttr "HeadProfileSideLoc1Shape.wp" "HeadProfileSideCurveShape.cp[11]";
connectAttr "HeadProfileSideLoc2Shape.wp" "HeadProfileSideCurveShape.cp[12]";
connectAttr "HeadProfileSideLoc3Shape.wp" "HeadProfileSideCurveShape.cp[13]";
connectAttr "HeadProfileSideLoc4Shape.wp" "HeadProfileSideCurveShape.cp[14]";
connectAttr "HeadProfileSideLoc5Shape.wp" "HeadProfileSideCurveShape.cp[15]";
connectAttr "HeadProfileSideLoc6Shape.wp" "HeadProfileSideCurveShape.cp[16]";
connectAttr "ProfileCurves.di" "HeadProfileFrontCurve.do";
connectAttr "HeadProfileFrontLocShape0.wp" "HeadProfileFrontCurveShape.cp[0]";
connectAttr "HeadProfileFrontLocShape1.wp" "HeadProfileFrontCurveShape.cp[1]";
connectAttr "HeadProfileFrontLocShape2.wp" "HeadProfileFrontCurveShape.cp[2]";
connectAttr "HeadProfileFrontLocShape3.wp" "HeadProfileFrontCurveShape.cp[3]";
connectAttr "HeadProfileFrontLocShape4.wp" "HeadProfileFrontCurveShape.cp[4]";
connectAttr "HeadProfileFrontLocShape5.wp" "HeadProfileFrontCurveShape.cp[5]";
connectAttr "ProfileCurves.di" "noseProfileCurve.do";
connectAttr "noseProfileLocShape0.wp" "noseProfileCurveShape.cp[0]";
connectAttr "noseProfileLocShape1.wp" "noseProfileCurveShape.cp[1]";
connectAttr "noseProfileLocShape2.wp" "noseProfileCurveShape.cp[2]";
connectAttr "noseProfileLocShape3.wp" "noseProfileCurveShape.cp[3]";
connectAttr "noseProfileLocShape4.wp" "noseProfileCurveShape.cp[4]";
connectAttr "ProfileCurves.di" "chinProfileCurve.do";
connectAttr "chinProfileLocShape0.wp" "chinProfileCurveShape.cp[0]";
connectAttr "chinProfileLocShape1.wp" "chinProfileCurveShape.cp[1]";
connectAttr "chinProfileLocShape2.wp" "chinProfileCurveShape.cp[2]";
connectAttr "FaceFitSkeleton.sy" "FaceFitSkeleton.sx";
connectAttr "FaceFitSkeleton.sy" "FaceFitSkeleton.sz";
connectAttr "extrude1.os" "upperEyeLidCylinderOuterShape.cr";
connectAttr "reverseSurface1.os" "lowerEyeLidCylinderOuterShape.cr";
connectAttr "FaceFitEyeLidOuter.radius" "EyeLidProfileOuter.sx";
connectAttr "FaceFitEyeLidOuter.radius" "EyeLidProfileOuter.sy";
connectAttr "FaceFitEyeLidOuter.radius" "EyeLidProfileOuter.sz";
connectAttr "extrude3.os" "upperEyeLidCylinderMainShape.cr";
connectAttr "reverseSurface2.os" "lowerEyeLidCylinderMainShape.cr";
connectAttr "FaceFitEyeLidMain.radius" "EyeLidProfileMain.sx";
connectAttr "FaceFitEyeLidMain.radius" "EyeLidProfileMain.sy";
connectAttr "FaceFitEyeLidMain.radius" "EyeLidProfileMain.sz";
connectAttr "extrude5.os" "upperEyeLidCylinderInnerShape.cr";
connectAttr "reverseSurface3.os" "lowerEyeLidCylinderInnerShape.cr";
connectAttr "FaceFitEyeLidInner.radius" "EyeLidProfileInner.sx";
connectAttr "FaceFitEyeLidInner.radius" "EyeLidProfileInner.sy";
connectAttr "FaceFitEyeLidInner.radius" "EyeLidProfileInner.sz";
connectAttr "extrude7.os" "upperLipCylinderOuterShape.cr";
connectAttr "extrude8.os" "lowerLipCylinderOuterShape.cr";
connectAttr "FaceFitLipOuter.radius" "LipProfileOuter.sx";
connectAttr "FaceFitLipOuter.radius" "LipProfileOuter.sy";
connectAttr "FaceFitLipOuter.radius" "LipProfileOuter.sz";
connectAttr "extrude9.os" "upperLipCylinderMainShape.cr";
connectAttr "reverseSurface4.os" "lowerLipCylinderMainShape.cr";
connectAttr "FaceFitLipMain.radius" "LipProfileMain.sx";
connectAttr "FaceFitLipMain.radius" "LipProfileMain.sy";
connectAttr "FaceFitLipMain.radius" "LipProfileMain.sz";
connectAttr "extrude11.os" "upperLipCylinderInnerShape.cr";
connectAttr "reverseSurface5.os" "lowerLipCylinderInnerShape.cr";
connectAttr "FaceFitLipInner.radius" "LipProfileInner.sx";
connectAttr "FaceFitLipInner.radius" "LipProfileInner.sy";
connectAttr "FaceFitLipInner.radius" "LipProfileInner.sz";
connectAttr "EyeBrowInner.t" "EyeBrowInnerSphere.t";
connectAttr "EyeBrowInner.r" "EyeBrowInnerSphere.r";
connectAttr "EyeBrowInner.s" "EyeBrowInnerSphere.s";
connectAttr "EyeBrowOuter.t" "EyeBrowOuterSphere.t";
connectAttr "EyeBrowOuter.r" "EyeBrowOuterSphere.r";
connectAttr "EyeBrowOuter.s" "EyeBrowOuterSphere.s";
connectAttr "EyeBrowMid1.t" "EyeBrowMid1Sphere.t";
connectAttr "EyeBrowMid1.r" "EyeBrowMid1Sphere.r";
connectAttr "EyeBrowMid1.s" "EyeBrowMid1Sphere.s";
connectAttr "EyeBrowMiddle.t" "EyeBrowMiddleSphere.t";
connectAttr "EyeBrowMiddle.r" "EyeBrowMiddleSphere.r";
connectAttr "EyeBrowMiddle.s" "EyeBrowMiddleSphere.s";
connectAttr "reverseSurface6.os" "ForeHeadCylinderShape.cr";
connectAttr "FaceFitForeHead.radius" "ForeHeadProfile.sx";
connectAttr "FaceFitForeHead.radius" "ForeHeadProfile.sy";
connectAttr "FaceFitForeHead.radius" "ForeHeadProfile.sz";
connectAttr "JawPivot.t" "JawPivotSphere.t";
connectAttr "JawPivot.r" "JawPivotSphere.r";
connectAttr "JawPivot.s" "JawPivotSphere.s";
connectAttr "JawCorner.t" "JawCornerSphere.t";
connectAttr "JawCorner.r" "JawCornerSphere.r";
connectAttr "JawCorner.s" "JawCornerSphere.s";
connectAttr "Jaw.t" "JawSphere.t";
connectAttr "Jaw.r" "JawSphere.r";
connectAttr "Jaw.s" "JawSphere.s";
connectAttr "Throat.t" "ThroatSphere.t";
connectAttr "Throat.r" "ThroatSphere.r";
connectAttr "Throat.s" "ThroatSphere.s";
connectAttr "Cheek.t" "CheekSphere.t";
connectAttr "Cheek.r" "CheekSphere.r";
connectAttr "Cheek.s" "CheekSphere.s";
connectAttr "Line.t" "LineSphere.t";
connectAttr "Line.r" "LineSphere.r";
connectAttr "Line.s" "LineSphere.s";
connectAttr "CheekRaiser.t" "CheekRaiserSphere.t";
connectAttr "CheekRaiser.r" "CheekRaiserSphere.r";
connectAttr "CheekRaiser.s" "CheekRaiserSphere.s";
connectAttr "Nose.t" "NoseSphere.t";
connectAttr "Nose.r" "NoseSphere.r";
connectAttr "Nose.s" "NoseSphere.s";
connectAttr "NoseUnder.t" "NoseUnderSphere.t";
connectAttr "NoseUnder.r" "NoseUnderSphere.r";
connectAttr "NoseUnder.s" "NoseUnderSphere.s";
connectAttr "NoseCorner.t" "NoseCornerSphere.t";
connectAttr "NoseCorner.r" "NoseCornerSphere.r";
connectAttr "NoseCorner.s" "NoseCornerSphere.s";
connectAttr "NoseSide.t" "NoseSideSphere.t";
connectAttr "NoseSide.r" "NoseSideSphere.r";
connectAttr "NoseSide.s" "NoseSideSphere.s";
connectAttr "NoseMiddle.t" "NoseMiddleSphere.t";
connectAttr "NoseMiddle.r" "NoseMiddleSphere.r";
connectAttr "NoseMiddle.s" "NoseMiddleSphere.s";
connectAttr "Nostril.t" "NostrilSphere.t";
connectAttr "Nostril.r" "NostrilSphere.r";
connectAttr "Nostril.s" "NostrilSphere.s";
relationship "link" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asRedSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asRed2SG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asGreenSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asGreen2SG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asBlueSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asBlue2SG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asWhiteSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asBlackSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "asBonesSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "eyeWhitePhongSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "pupilPhongSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "irisPhongSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "gumSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "teethSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asRedSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asRed2SG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asGreenSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asGreen2SG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asBlueSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asBlue2SG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asWhiteSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asBlackSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "asBonesSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "eyeWhitePhongSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "pupilPhongSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "irisPhongSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "gumSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "teethSG.message" ":defaultLightSet.message";
connectAttr "layerManager.dli[0]" "defaultLayer.id";
connectAttr "renderLayerManager.rlmi[0]" "defaultRenderLayer.rlid";
connectAttr "renderLayerManager1.rlmi[0]" "defaultRenderLayer1.rlid";
connectAttr "layerManager.dli[2]" "ProfileCurves.id";
connectAttr "layerManager.dli[1]" "MainCurves.id";
connectAttr "headTopologyShapeOrig.w" "groupParts1.ig";
connectAttr "groupId1.id" "groupParts1.gi";
connectAttr "asRed.oc" "asRedSG.ss";
connectAttr "upperEyeLidCylinderMainShape.iog" "asRedSG.dsm" -na;
connectAttr "EyeLidInnerAreaMeshShape.iog" "asRedSG.dsm" -na;
connectAttr "upperLipCylinderMainShape.iog" "asRedSG.dsm" -na;
connectAttr "LipInnerAreaMeshShape.iog" "asRedSG.dsm" -na;
connectAttr "asRedSG.msg" "materialInfo1.sg";
connectAttr "asRed.msg" "materialInfo1.m";
connectAttr "asRed2.oc" "asRed2SG.ss";
connectAttr "lowerEyeLidCylinderMainShape.iog" "asRed2SG.dsm" -na;
connectAttr "lowerLipCylinderMainShape.iog" "asRed2SG.dsm" -na;
connectAttr "asRed2SG.msg" "materialInfo2.sg";
connectAttr "asRed2.msg" "materialInfo2.m";
connectAttr "asGreen.oc" "asGreenSG.ss";
connectAttr "upperEyeLidCylinderOuterShape.iog" "asGreenSG.dsm" -na;
connectAttr "upperLipCylinderOuterShape.iog" "asGreenSG.dsm" -na;
connectAttr "ForeHeadCylinderShape.iog" "asGreenSG.dsm" -na;
connectAttr "ForeHeadAreaMeshShape.iog" "asGreenSG.dsm" -na;
connectAttr "asGreenSG.msg" "materialInfo3.sg";
connectAttr "asGreen.msg" "materialInfo3.m";
connectAttr "asGreen2.oc" "asGreen2SG.ss";
connectAttr "lowerEyeLidCylinderOuterShape.iog" "asGreen2SG.dsm" -na;
connectAttr "lowerLipCylinderOuterShape.iog" "asGreen2SG.dsm" -na;
connectAttr "asGreen2SG.msg" "materialInfo4.sg";
connectAttr "asGreen2.msg" "materialInfo4.m";
connectAttr "asBlue.oc" "asBlueSG.ss";
connectAttr "upperEyeLidCylinderInnerShape.iog" "asBlueSG.dsm" -na;
connectAttr "upperLipCylinderInnerShape.iog" "asBlueSG.dsm" -na;
connectAttr "asBlueSG.msg" "materialInfo5.sg";
connectAttr "asBlue.msg" "materialInfo5.m";
connectAttr "asBlue2.oc" "asBlue2SG.ss";
connectAttr "lowerEyeLidCylinderInnerShape.iog" "asBlue2SG.dsm" -na;
connectAttr "lowerLipCylinderInnerShape.iog" "asBlue2SG.dsm" -na;
connectAttr "EyeBrowInnerSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "EyeBrowOuterSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "EyeBrowMid1SphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "EyeBrowMiddleSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "JawPivotSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "JawCornerSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "JawSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "ThroatSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "CheekSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "LineSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "CheekRaiserSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "NoseSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "NoseUnderSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "NoseCornerSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "NoseSideSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "NoseMiddleSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "NostrilSphereShape.iog" "asBlue2SG.dsm" -na;
connectAttr "asBlue2SG.msg" "materialInfo6.sg";
connectAttr "asBlue2.msg" "materialInfo6.m";
connectAttr "irisPhong.oc" "irisPhongSG.ss";
connectAttr "irisPhongSG.msg" "materialInfo11.sg";
connectAttr "irisPhong.msg" "materialInfo11.m";
connectAttr "eyeWhitePhong.oc" "eyeWhitePhongSG.ss";
connectAttr "eyeWhitePhongSG.msg" "materialInfo12.sg";
connectAttr "eyeWhitePhong.msg" "materialInfo12.m";
connectAttr "pupilPhong.oc" "pupilPhongSG.ss";
connectAttr "pupilPhongSG.msg" "materialInfo10.sg";
connectAttr "pupilPhong.msg" "materialInfo10.m";
connectAttr "asWhite.oc" "asWhiteSG.ss";
connectAttr "asWhiteSG.msg" "materialInfo13.sg";
connectAttr "asWhite.msg" "materialInfo13.m";
connectAttr "asBlack.oc" "asBlackSG.ss";
connectAttr "asBlackSG.msg" "materialInfo14.sg";
connectAttr "asBlack.msg" "materialInfo14.m";
connectAttr "gumLambert.oc" "gumSG.ss";
connectAttr "gumSG.msg" "materialInfo8.sg";
connectAttr "gumLambert.msg" "materialInfo8.m";
connectAttr "teethLambert.oc" "teethSG.ss";
connectAttr "teethSG.msg" "materialInfo9.sg";
connectAttr "teethLambert.msg" "materialInfo9.m";
connectAttr "asBones.oc" "asBonesSG.ss";
connectAttr "asBonesSG.msg" "materialInfo15.sg";
connectAttr "asBones.msg" "materialInfo15.m";
connectAttr "EyeLidProfileOuterShape.ws" "extrude1.pr";
connectAttr "upperEyeLidOuterCurveShape.ws" "extrude1.pt";
connectAttr "EyeLidProfileOuterShape.ws" "extrude2.pr";
connectAttr "lowerEyeLidOuterCurveShape.ws" "extrude2.pt";
connectAttr "extrude2.os" "reverseSurface1.is";
connectAttr "EyeLidProfileMainShape.ws" "extrude3.pr";
connectAttr "upperEyeLidMainCurveShape.ws" "extrude3.pt";
connectAttr "EyeLidProfileMainShape.ws" "extrude4.pr";
connectAttr "lowerEyeLidMainCurveShape.ws" "extrude4.pt";
connectAttr "extrude4.os" "reverseSurface2.is";
connectAttr "EyeLidProfileInnerShape.ws" "extrude5.pr";
connectAttr "upperEyeLidInnerCurveShape.ws" "extrude5.pt";
connectAttr "EyeLidProfileInnerShape.ws" "extrude6.pr";
connectAttr "lowerEyeLidInnerCurveShape.ws" "extrude6.pt";
connectAttr "extrude6.os" "reverseSurface3.is";
connectAttr "LipProfileOuterShape.ws" "extrude7.pr";
connectAttr "upperLipOuterCurveShape.ws" "extrude7.pt";
connectAttr "LipProfileOuterShape.ws" "extrude8.pr";
connectAttr "lowerLipOuterCurveShape.ws" "extrude8.pt";
connectAttr "LipProfileMainShape.ws" "extrude9.pr";
connectAttr "upperLipMainCurveShape.ws" "extrude9.pt";
connectAttr "LipProfileMainShape.ws" "extrude10.pr";
connectAttr "lowerLipMainCurveShape.ws" "extrude10.pt";
connectAttr "extrude10.os" "reverseSurface4.is";
connectAttr "LipProfileInnerShape.ws" "extrude11.pr";
connectAttr "upperLipInnerCurveShape.ws" "extrude11.pt";
connectAttr "LipProfileInnerShape.ws" "extrude12.pr";
connectAttr "lowerLipInnerCurveShape.ws" "extrude12.pt";
connectAttr "extrude12.os" "reverseSurface5.is";
connectAttr "ForeHeadProfileShape.ws" "extrude13.pr";
connectAttr "ForeHeadCurveShape.ws" "extrude13.pt";
connectAttr "extrude13.os" "reverseSurface6.is";
connectAttr "asRedSG.pa" ":renderPartition.st" -na;
connectAttr "asRed2SG.pa" ":renderPartition.st" -na;
connectAttr "asGreenSG.pa" ":renderPartition.st" -na;
connectAttr "asGreen2SG.pa" ":renderPartition.st" -na;
connectAttr "asBlueSG.pa" ":renderPartition.st" -na;
connectAttr "asBlue2SG.pa" ":renderPartition.st" -na;
connectAttr "eyeWhitePhongSG.pa" ":renderPartition.st" -na;
connectAttr "pupilPhongSG.pa" ":renderPartition.st" -na;
connectAttr "irisPhongSG.pa" ":renderPartition.st" -na;
connectAttr "asWhiteSG.pa" ":renderPartition.st" -na;
connectAttr "asBlackSG.pa" ":renderPartition.st" -na;
connectAttr "gumSG.pa" ":renderPartition.st" -na;
connectAttr "teethSG.pa" ":renderPartition.st" -na;
connectAttr "asBonesSG.pa" ":renderPartition.st" -na;
connectAttr "headTopologyShape.iog.og[0]" ":initialShadingGroup.dsm" -na;
connectAttr "groupId1.msg" ":initialShadingGroup.gn" -na;
connectAttr "asRed.msg" ":defaultShaderList1.s" -na;
connectAttr "asRed2.msg" ":defaultShaderList1.s" -na;
connectAttr "asGreen.msg" ":defaultShaderList1.s" -na;
connectAttr "asGreen2.msg" ":defaultShaderList1.s" -na;
connectAttr "asBlue.msg" ":defaultShaderList1.s" -na;
connectAttr "asBlue2.msg" ":defaultShaderList1.s" -na;
connectAttr "eyeWhitePhong.msg" ":defaultShaderList1.s" -na;
connectAttr "pupilPhong.msg" ":defaultShaderList1.s" -na;
connectAttr "irisPhong.msg" ":defaultShaderList1.s" -na;
connectAttr "asWhite.msg" ":defaultShaderList1.s" -na;
connectAttr "asBlack.msg" ":defaultShaderList1.s" -na;
connectAttr "gumLambert.msg" ":defaultShaderList1.s" -na;
connectAttr "teethLambert.msg" ":defaultShaderList1.s" -na;
connectAttr "asBones.msg" ":defaultShaderList1.s" -na;
connectAttr "defaultRenderLayer.msg" ":defaultRenderingList1.r" -na;
connectAttr "defaultRenderLayer1.msg" ":defaultRenderingList1.r" -na;
// End of defaultHead.ma
