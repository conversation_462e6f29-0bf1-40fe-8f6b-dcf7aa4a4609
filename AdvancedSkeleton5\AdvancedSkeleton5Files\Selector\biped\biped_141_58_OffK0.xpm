/* XPM */
static char *biped_141_58_OffK0[] = {
/* columns rows colors chars-per-pixel */
"38 18 52 1",
"  c gray75",
". c #F5F7F6",
"X c #F8FAFA",
"o c #F9FAFA",
"O c #FBFCFD",
"+ c #FBFDFD",
"@ c #FCFCFD",
"# c #FCFDFD",
"$ c #FDFCFC",
"% c #FDFCFD",
"& c #FDFDFC",
"* c #FDFDFD",
"= c #FCFDFE",
"- c #FDFCFE",
"; c #FDFCFF",
": c #FDFDFE",
"> c #FDFDFF",
", c #FCFEFD",
"< c #FDFEFC",
"1 c #FDFEFD",
"2 c #FDFFFC",
"3 c #FDFFFD",
"4 c #FCFEFE",
"5 c #FCFFFE",
"6 c #FDFEFE",
"7 c #FDFEFF",
"8 c #FDFFFE",
"9 c #FDFFFF",
"0 c #FEFCFD",
"q c #FEFDFC",
"w c #FEFDFD",
"e c #FFFCFD",
"r c #FFFDFC",
"t c #FFFDFD",
"y c #FEFCFE",
"u c #FEFCFF",
"i c #FEFDFE",
"p c #FEFDFF",
"a c #FFFDFE",
"s c #FFFDFF",
"d c #FEFEFD",
"f c #FEFFFD",
"g c #FFFEFD",
"h c #FFFFFD",
"j c #FEFEFE",
"k c #FEFEFF",
"l c #FEFFFE",
"z c #FEFFFF",
"x c #FFFEFE",
"c c #FFFEFF",
"v c #FFFFFE",
"b c gray100",
/* pixels */
"                                      ",
" 6666666666e6ee6O6666666666q66666666e ",
" 666666666666ee666666666666q666666666 ",
" 666666666666666666666666666666666666 ",
" 666666666e6666666666666e66666666e666 ",
" 6e66666666e666666666666666666666ee66 ",
" q66e66666e6666666666666ee666666ee666 ",
" 66666666666666666666666e6666qe666666 ",
" 66666666666ee6e66666666666666666666e ",
" 666666e6666e66666666666666666666ee66 ",
" 66666666666eee6666666666q6666666666e ",
" 66666666666e666666666e6666q66q66Oee6 ",
" 6qq666qq66666666q66e6qe666666e6e6666 ",
" e66qqoe66e666q66qq666666q666e6e6e666 ",
" ee6qq6qe6eeq6qe666666666q66q6e666666 ",
" o666666ee666q6666q666q66666666666e6o ",
" .666q66666666q6666666q6666q6q6q6e66. ",
"                                      "
};
