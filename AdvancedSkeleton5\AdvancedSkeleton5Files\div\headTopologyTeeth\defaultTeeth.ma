//Maya ASCII 2012 scene
//Name: headTopologyTeeth.ma
//Last modified: Wed, Jun 14, 2017 06:16:08 PM
//Codeset: 1252
requires maya "2012";
requires "stereoCamera" "10.0";
currentUnit -l centimeter -a degree -t pal;
fileInfo "application" "maya";
fileInfo "product" "Maya 2012";
fileInfo "version" "2012 x64";
fileInfo "cutIdentifier" "201201172029-821146";
fileInfo "osv" "Microsoft Business Edition, 64-bit  (Build 9200)\n";

createNode transform -s -n "persp";
	setAttr ".v" no;
	setAttr ".t" -type "double3" -0.98910519683879017 0.091682738341819503 1.5822416480981365 ;
	setAttr ".r" -type "double3" 0.26168039910868374 -394.99999999994895 -1.5166954426931747e-017 ;
	setAttr ".rp" -type "double3" 1.6653345369377348e-016 1.7763568394002505e-015 -8.8817841970012523e-016 ;
	setAttr ".rpt" -type "double3" -1.192114569309255e-015 -6.2316096147110221e-016 
		-3.7291656471792423e-016 ;
createNode camera -s -n "perspShape" -p "persp";
	setAttr -k off ".v" no;
	setAttr ".fl" 34.999999999999979;
	setAttr ".ncp" 1;
	setAttr ".coi" 2.3849536392300941;
	setAttr ".imn" -type "string" "persp";
	setAttr ".den" -type "string" "persp_depth";
	setAttr ".man" -type "string" "persp_mask";
	setAttr ".hc" -type "string" "viewSet -p %camera";
createNode transform -s -n "top";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 0 100.10020876233999 2.2225277174214852e-014 ;
	setAttr ".r" -type "double3" -89.999999999999986 0 0 ;
createNode camera -s -n "topShape" -p "top";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 100.1;
	setAttr ".ow" 3.8879929629086649;
	setAttr ".imn" -type "string" "top";
	setAttr ".den" -type "string" "top_depth";
	setAttr ".man" -type "string" "top_mask";
	setAttr ".hc" -type "string" "viewSet -t %camera";
	setAttr ".o" yes;
createNode transform -s -n "front";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 0 0 100.10348650540688 ;
createNode camera -s -n "frontShape" -p "front";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 100.14798769323998;
	setAttr ".ow" 3.8988532784475156;
	setAttr ".imn" -type "string" "front";
	setAttr ".den" -type "string" "front_depth";
	setAttr ".man" -type "string" "front_mask";
	setAttr ".tp" -type "double3" 0.036420378044872415 -0.0016400192686883419 -0.046927362680435181 ;
	setAttr ".hc" -type "string" "viewSet -f %camera";
	setAttr ".o" yes;
createNode transform -s -n "side";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 100.11744290758662 0.099455376106725502 -0.13736851876687228 ;
	setAttr ".r" -type "double3" 0 89.999999999999972 0 ;
	setAttr ".rp" -type "double3" -2.2204460492503131e-016 0 -1.4210854715202004e-014 ;
	setAttr ".rpt" -type "double3" -1.3988810110276976e-014 0 1.4432899320127038e-014 ;
createNode camera -s -n "sideShape" -p "side";
	setAttr -k off ".v" no;
	setAttr ".rnd" no;
	setAttr ".ncp" 1;
	setAttr ".coi" 100.1;
	setAttr ".ow" 2.0464569244339907;
	setAttr ".imn" -type "string" "side";
	setAttr ".den" -type "string" "side_depth";
	setAttr ".man" -type "string" "side_mask";
	setAttr ".tp" -type "double3" 0 -0.58053102479830465 1.3545723911960308 ;
	setAttr ".hc" -type "string" "viewSet -s %camera";
	setAttr ".o" yes;
createNode transform -n "headTopologyTeeth";
createNode transform -n "upperTeeth" -p "headTopologyTeeth";
createNode mesh -n "upperTeethShape" -p "upperTeeth";
	setAttr -k off ".v";
	setAttr -s 14 ".iog[0].og";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".dr" 1;
createNode mesh -n "upperTeethShapeOrig1" -p "upperTeeth";
	setAttr -k off ".v";
	setAttr ".io" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr -s 1475 ".uvst[0].uvsp";
	setAttr ".uvst[0].uvsp[0:249]" -type "float2" 0.26021901 0.61748898 0.25817001
		 0.60626698 0.243129 0.49557501 0.26126999 0.65016901 0.287808 0.51427299 0.209401
		 0.60688502 0.23414899 0.61419702 0.27754301 0.627397 0.31124201 0.93360901 0.22313499
		 0.62860399 0.21386001 0.505624 0.27740201 0.56302601 0.22677501 0.66391402 0.168136
		 0.65543002 0.219199 0.65092897 0.219576 0.343373 0.27827001 0.49935701 0.213899 0.411906
		 0.310413 0.55538303 0.220458 0.57408702 0.190947 0.64203602 0.246979 0.630867 0.20771299
		 0.62916797 0.24025699 0.358486 0.193718 0.48509401 0.235901 0.55418098 0.251791 0.405267
		 0.30108401 0.61433703 0.30282301 0.62639803 0.191277 0.61770701 0.304167 0.359283
		 0.19037101 0.50389802 0.261538 0.55701298 0.26336101 0.33070999 0.194968 0.39403
		 0.233493 0.60495001 0.20825499 0.61693799 0.207332 0.64090401 0.210629 0.399019 0.194959
		 0.43334699 0.18679 0.54874402 0.237445 0.41045401 0.225196 0.60031301 0.217233 0.62844998
		 0.31081101 0.51027602 0.233362 0.50730097 0.28310901 0.96880001 0.221797 0.36258301
		 0.26225099 0.638327 0.19038001 0.93576002 0.191275 0.56372303 0.16636001 0.64395303
		 0.190945 0.405233 0.214986 0.560166 0.266231 0.41667101 0.16941801 0.67198402 0.291706
		 0.53175199 0.22327 0.639925 0.212043 0.59557402 0.245039 0.56537002 0.27105999 0.53171003
		 0.235705 0.96686602 0.27633399 0.61611998 0.23447201 0.62028903 0.30098301 0.57382601
		 0.300805 0.49178401 0.30313301 0.637932 0.22239099 0.325941 0.207903 0.451015 0.259624
		 0.505629 0.219602 0.43533701 0.219254 0.486835 0.261314 0.62845403 0.205759 0.43313399
		 0.178892 0.401609 0.228586 0.41554701 0.233807 0.42638701 0.24793801 0.64052999 0.231388
		 0.54378903 0.183419 0.39174101 0.25612301 0.455073 0.208222 0.42099801 0.220265 0.60857397
		 0.195765 0.364528 0.28716201 0.54885697 0.235488 0.64003998 0.25195301 0.64862198
		 0.27026501 0.43573001 0.24551199 0.43815601 0.16789301 0.60798103 0.19069199 0.63013202
		 0.26096201 0.40300801 0.220349 0.42485899 0.23499601 0.62991297 0.28503799 0.34283799
		 0.25266799 0.62984699 0.181577 0.43475601 0.152206 0.963458 0.235549 0.64711303 0.26326001
		 0.35532001 0.250918 0.61921102 0.216906 0.64008802 0.217953 0.61704099 0.243818 0.422189
		 0.30705601 0.34198901 0.30598101 0.323585 0.228329 0.45500401 0.151987 0.93514502
		 0.26969701 0.546363 0.27770001 0.65022802 0.204558 0.408095 0.180924 0.455753 0.29870901
		 0.60445702 0.26356599 0.93333101 0.237894 0.34468299 0.227106 0.61081201 0.233781
		 0.43884599 0.168274 0.432852 0.194701 0.57650203 0.25391099 0.639368 0.189189 0.96764302
		 0.22389799 0.618065 0.28300399 0.933402 0.220781 0.41733801 0.192278 0.60682499 0.26436999
		 0.97244197 0.196355 0.323145 0.20322201 0.97362602 0.28105301 0.357968 0.221044 0.40707201
		 0.167814 0.59715402 0.244973 0.62019998 0.19854 0.41973501 0.24455699 0.64955199
		 0.31255701 0.96282703 0.25860801 0.43679899 0.26763001 0.342421 0.255665 0.41909301
		 0.21012001 0.546606 0.25585401 0.66905302 0.26944301 0.45513701 0.165617 0.63074303
		 0.240914 0.457167 0.31468901 0.53209603 0.22631 0.64984798 0.20906299 0.65156102
		 0.26757699 0.51762098 0.166611 0.61774302 0.30253801 0.64947301 0.234501 0.65908903
		 0.185348 0.41865799 0.198373 0.45254701 0.192945 0.341378 0.167234 0.45724401 0.247648
		 0.60942698 0.193767 0.65310901 0.274625 0.67034101 0.27283299 0.60504901 0.194389
		 0.594639 0.241319 0.330062 0.23603299 0.94050002 0.173216 0.414316 0.27924401 0.325697
		 0.2016 0.93403602 0.24097501 0.61168897 0.27812701 0.63773203 0.252408 0.35231301
		 0.25162101 0.54182601 0.25023299 0.55076897 0.247318 0.51063901 0.253539 0.34266701
		 0.21160799 0.71417499 0.29857701 0.725923 0.16964 0.73681402 0.18369199 0.73727798
		 0.27905101 0.697505 0.241998 0.73866802 0.237694 0.70364797 0.198282 0.725164 0.20117299
		 0.73816001 0.228339 0.69460303 0.267616 0.74346501 0.225805 0.736426 0.238334 0.68923002
		 0.24616601 0.70250303 0.18197501 0.72401398 0.28413999 0.71362501 0.26945099 0.73174298
		 0.251782 0.74178499 0.28568399 0.72924501 0.21413501 0.70325601 0.29920301 0.73779202
		 0.28479001 0.74190402 0.171002 0.69899201 0.25628901 0.68418902 0.244995 0.73037201
		 0.22068401 0.70408899 0.18201999 0.71148902 0.250882 0.71783602 0.19936199 0.69949698
		 0.17012 0.72358501 0.19760799 0.712098 0.26139101 0.69984198 0.29717901 0.71156102
		 0.170487 0.71113801 0.218872 0.737391 0.183329 0.69923502 0.213294 0.72634202 0.28917101
		 0.68070501 0.29268301 0.69508803 0.274699 0.68251902 0.26642799 0.71601099 0.242946
		 0.717336 0.25415 0.732463 0.220581 0.727 0.21864501 0.71544099 0.233266 0.73440301
		 0.23214699 0.72764099 0.230498 0.71626699 0.229348 0.70684397 0.22751901 0.96607703
		 0.22927 0.939982 0.218439 0.44753101 0.253075 0.334034 0.31163201 0.948044 0.23717999
		 0.95275599 0.26539201 0.952501 0.28424299 0.95089602 0.239814 0.97153199 0.250036
		 0.97656202 0.215569 0.935574 0.22559001 0.936589 0.239576 0.93683302 0.24874701 0.93204403
		 0.22312599 0.96932298 0.21418799 0.970972 0.252285 0.95316601 0.242697 0.95310402
		 0.184543 0.95173699 0.20212901 0.95277297 0.15088101 0.94964802 0.227642 0.95294601
		 0.213254 0.95243198 0.22222701 0.952582 0.232392 0.93853801 0.311598 0.92213702 0.231783
		 0.96847302 0.220752 0.926054 0.232085 0.93036002 0.25738901 0.92260402;
	setAttr ".uvst[0].uvsp[250:499]" 0.281717 0.98595202 0.262225 0.98159403 0.232573
		 0.95250201 0.276517 0.92019302 0.31786901 0.97802502 0.223462 0.98516703 0.234283
		 0.97784001 0.29365301 0.85609001 0.27676001 0.88779902 0.197964 0.85814202 0.235704
		 0.88761503 0.167124 0.88359803 0.166519 0.85868502 0.25680301 0.855923 0.195922 0.88497603
		 0.273312 0.855214 0.26133299 0.891541 0.20753001 0.89205903 0.29719901 0.88011003
		 0.233882 0.86314702 0.207605 0.85667801 0.22856 0.88722199 0.22797801 0.86276299
		 0.29565701 0.86787802 0.235586 0.87417299 0.26068699 0.87317002 0.27628401 0.870956
		 0.239654 0.89151001 0.24821 0.89578903 0.217167 0.85822302 0.224857 0.85958099 0.23694301
		 0.85983998 0.244865 0.856103 0.22497199 0.890163 0.217389 0.89056498 0.248951 0.874237
		 0.24047101 0.87423801 0.193692 0.87175202 0.207702 0.87296098 0.16625001 0.87146902
		 0.22757401 0.87465203 0.216429 0.87349099 0.22330301 0.87426901 0.230489 0.86147398
		 0.292142 0.84668702 0.23248699 0.88943303 0.21974801 0.85011703 0.229919 0.85433102
		 0.25240701 0.845281 0.276692 0.90284503 0.26004201 0.899809 0.231464 0.87405097 0.266451
		 0.84432501 0.30250001 0.89256603 0.225069 0.90349102 0.235091 0.89771903 0.225802
		 0.252857 0.21476001 0.241892 0.21919499 0.237571 0.226294 0.23837 0.21742301 0.230507
		 0.22485 0.22745299 0.21071 0.23049299 0.21048801 0.223676 0.216529 0.22457699 0.22198801
		 0.221974 0.25106099 0.242764 0.237838 0.233513 0.242489 0.227787 0.250301 0.22743399
		 0.232491 0.22443201 0.239638 0.22208001 0.246728 0.21736901 0.23212899 0.218178 0.23759501
		 0.217398 0.24178199 0.213043 0.226602 0.222095 0.228572 0.221347 0.227604 0.21686301
		 0.22514001 0.217655 0.276871 0.227228 0.26233 0.22119001 0.26669201 0.214239 0.273781
		 0.211375 0.255409 0.21305799 0.26292801 0.209223 0.26631799 0.20028199 0.25451899
		 0.206205 0.26003501 0.20471101 0.261866 0.19774 0.24686299 0.21027499 0.249127 0.20903499
		 0.24384201 0.20606899 0.246534 0.20427801 0.29973301 0.206368 0.28372601 0.202454
		 0.28616899 0.193821 0.29347199 0.18914001 0.273579 0.19421799 0.28002799 0.188618
		 0.28453401 0.18027 0.27070001 0.18803 0.27573901 0.184576 0.27813101 0.177937 0.26547399
		 0.19314601 0.267315 0.191651 0.264384 0.18810999 0.26243499 0.19013099 0.31704 0.17904601
		 0.29999599 0.178425 0.30077201 0.16954 0.30766499 0.162874 0.28944001 0.171904 0.294047
		 0.165364 0.29644299 0.15670501 0.284848 0.16599 0.28846201 0.161844 0.28944701 0.15571401
		 0.28138101 0.17318299 0.282608 0.17106 0.27818799 0.16761801 0.27691901 0.170084
		 0.332762 0.14806101 0.30156001 0.145817 0.30526999 0.138218 0.30854699 0.12984601
		 0.29702401 0.14183401 0.30111599 0.136335 0.30259499 0.129003 0.29219899 0.150774
		 0.29374799 0.14803199 0.288376 0.14907999 0.29047701 0.14551599 0.340049 0.113145
		 0.312116 0.114241 0.312545 0.105716 0.31384301 0.097525999 0.30656201 0.110738 0.30833799
		 0.104214 0.30833599 0.097110003 0.30643499 0.121147 0.307179 0.117649 0.302313 0.115548
		 0.30156201 0.119504 0.34027901 0.082305998 0.31535399 0.083718002 0.31551701 0.076638997
		 0.31604099 0.07096 0.30950099 0.081501 0.311315 0.075728998 0.311279 0.069127001
		 0.30590799 0.08901 0.310527 0.090369001 0.31090701 0.087160997 0.30638099 0.085473001
		 0.212819 0.184946 0.219906 0.195262 0.215077 0.196541 0.210833 0.194075 0.21978299
		 0.202133 0.21513 0.20170601 0.210226 0.201866 0.227487 0.18101799 0.235293 0.18992899
		 0.231187 0.193239 0.226798 0.193453 0.23550799 0.19644199 0.231801 0.19791199 0.228267
		 0.199283 0.22449 0.19756401 0.222727 0.198128 0.22314601 0.202949 0.22541 0.20217399
		 0.23776101 0.171106 0.24610899 0.17517 0.244524 0.180603 0.240936 0.184986 0.250622
		 0.181109 0.247389 0.184237 0.24474999 0.18885601 0.239471 0.19078401 0.237938 0.192095
		 0.240982 0.193959 0.238738 0.195931 0.244954 0.154984 0.254677 0.15683199 0.25302199
		 0.162752 0.249635 0.167283 0.259772 0.163131 0.257689 0.166842 0.25600901 0.17132699
		 0.25250801 0.17487399 0.251661 0.17680199 0.25626299 0.17761201 0.254843 0.17984401
		 0.25173399 0.137421 0.26205301 0.138356 0.26091999 0.144976 0.25786299 0.149343 0.26857701
		 0.144466 0.26620501 0.148572 0.264653 0.15264501 0.26057801 0.155734 0.25973001 0.15747499
		 0.264099 0.15803599 0.262916 0.160438 0.257557 0.112361 0.269604 0.11109 0.26942199
		 0.119639 0.26588601 0.12637 0.275915 0.123064 0.27450499 0.129026 0.27800101 0.116955
		 0.26925901 0.133205 0.268139 0.13659699 0.27297601 0.14004999 0.27476999 0.135644
		 0.26000601 0.084303997 0.27389801 0.081701003 0.273954 0.090374 0.27077699 0.097593002
		 0.28434601 0.088004999 0.28264099 0.093684003 0.28211999 0.099722996 0.27777201 0.104488
		 0.27725101 0.107756 0.28369001 0.106293 0.28258899 0.11028 0.26689401 0.057378002
		 0.28203401 0.050595999 0.28143901 0.061269999 0.27757001 0.068443 0.291749 0.059911001
		 0.28793201 0.065884002 0.28752801 0.072499 0.283391 0.076888002 0.282529 0.080219999
		 0.28878099 0.078966998 0.28794801 0.083227001 0.31294301 0.062696002 0.319713 0.060027
		 0.30997401 0.063983001 0.210582 0.21312401 0.215773 0.21311 0.220304 0.212045 0.22382
		 0.210545 0.226409 0.209814 0.23058 0.20933899 0.234712 0.20844001 0.237995 0.20532501;
	setAttr ".uvst[0].uvsp[500:749]" 0.241183 0.201166 0.24397799 0.198973 0.25018501
		 0.196935 0.26777399 0.17645299 0.26945901 0.171626 0.27009401 0.165415 0.27128699
		 0.16274799 0.27560899 0.15885399 0.278097 0.155285 0.27953699 0.15066899 0.281342
		 0.144977 0.283344 0.14098901 0.28685901 0.13548 0.28957 0.130224 0.29111299 0.124067
		 0.29219201 0.115324 0.29340601 0.111308 0.295701 0.105857 0.29694599 0.099941999
		 0.29746899 0.093718 0.297501 0.086745001 0.297975 0.082650997 0.299189 0.077482 0.300482
		 0.071788996 0.30209801 0.065713003 0.26466799 0.179919 0.26059201 0.183054 0.25871599
		 0.185175 0.25590801 0.190079 0.25373599 0.19474 0.203058 0.22757199 0.20305499 0.21921299
		 0.203087 0.245966 0.203088 0.26101601 0.203061 0.212606 0.203068 0.203729 0.20310199
		 0.19502801 0.203114 0.186156 0.211127 0.26011199 0.20732801 0.245179 0.20598 0.228733
		 0.206071 0.22070301 0.20581099 0.212777 0.205789 0.203161 0.20621499 0.194609 0.206623
		 0.18780801 0.33512801 0.056492001 0.20311899 0.171233 0.21887299 0.168578 0.22434101
		 0.15665901 0.232647 0.14596701 0.232954 0.128408 0.237 0.099366002 0.245251 0.076439001
		 0.248129 0.050765 0.267993 0.027278 0.20315801 0.019726001 0.35837001 0.069765002
		 0.36992601 0.086960003 0.372756 0.119978 0.36661601 0.15913901 0.35102201 0.196483
		 0.328352 0.22924 0.30150101 0.255923 0.27078301 0.276012 0.241181 0.28752401 0.218933
		 0.29187101 0.203069 0.29277101 0.20313901 0.070660003 0.203153 0.13712101 0.32178101
		 0.108806 0.32370299 0.078647003 0.31549099 0.140937 0.158537 0.81758201 0.28616101
		 0.82577902 0.229224 0.81206697 0.264788 0.808002 0.232978 0.78888202 0.239353 0.807109
		 0.27962801 0.76659203 0.28145 0.78052503 0.30763301 0.81170201 0.23963 0.79946399
		 0.30660701 0.79039103 0.182845 0.79074299 0.16054501 0.78296602 0.158537 0.80182302
		 0.229633 0.79742402 0.26098201 0.78117102 0.158995 0.79200202 0.30579701 0.780945
		 0.263684 0.79430002 0.28327999 0.805453 0.18327899 0.80410999 0.28442201 0.81632203
		 0.30505401 0.76538301 0.20471001 0.80656302 0.219363 0.79446602 0.21925101 0.80723798
		 0.239043 0.79156798 0.20430399 0.79193801 0.24832 0.81695902 0.24676999 0.78573102
		 0.229827 0.80583698 0.26266301 0.82746202 0.24965701 0.79715902 0.25003499 0.80876398
		 0.241797 0.77683002 0.245535 0.82299602 0.30709901 0.801449 0.283048 0.79276699 0.25664201
		 0.76973802 0.15899301 0.81084102 0.26434299 0.81879902 0.22881299 0.789886 0.234345
		 0.79906499 0.234504 0.80597001 0.221065 0.82036 0.20852201 0.82247901 0.183943 0.81696802
		 0.234319 0.81183702 0.18425301 0.77749002 0.209133 0.77668601 0.22220001 0.78181797
		 0.239297 0.81209803 0.244911 0.75111002 0.234753 0.74455702 0.184183 0.75097901 0.168356
		 0.74931902 0.204041 0.75278503 0.186765 0.51884401 0.18531001 0.53220302 0.229096
		 0.53201997 0.22917201 0.52096802 0.20831899 0.53260201 0.209525 0.52046901 0.24873
		 0.51985598 0.25045699 0.53016698 0.231837 0.47760001 0.26522201 0.477209 0.252056
		 0.477869 0.216738 0.46774501 0.23025 0.38153201 0.199368 0.384121 0.25037301 0.36836001
		 0.232703 0.30921301 0.201241 0.306555 0.25252801 0.31901401 0.30491301 0.061864 0.30340499
		 0.049941 0.302665 0.057758 0.306793 0.040130001 0.74051201 0.617486 0.743092 0.606673
		 0.756868 0.49546 0.73923099 0.64993501 0.71262401 0.51438498 0.79069799 0.60675901
		 0.76675999 0.61376899 0.722776 0.62742102 0.68884403 0.93317401 0.77744901 0.62836301
		 0.78667802 0.50560802 0.72281802 0.562859 0.77316803 0.66387802 0.83184302 0.65539902
		 0.78107899 0.65090501 0.78109598 0.34446299 0.72184402 0.499567 0.78640199 0.411883
		 0.68944103 0.55545503 0.77964401 0.57415402 0.80923402 0.64184201 0.75420898 0.63036001
		 0.79257101 0.62903303 0.76033098 0.35884899 0.80639303 0.48492301 0.76504803 0.55526501
		 0.74790603 0.405413 0.69880301 0.61450702 0.69713402 0.62657702 0.808824 0.617576
		 0.69584203 0.35931301 0.80976403 0.50377202 0.73931903 0.55685401 0.73715597 0.330639
		 0.804717 0.39414001 0.76643002 0.60486102 0.79196602 0.616781 0.79297298 0.64080602
		 0.78908002 0.39922601 0.80570698 0.432015 0.81338102 0.548729 0.76093203 0.4104 0.774737
		 0.60024703 0.783198 0.628326 0.68902302 0.51023602 0.76775098 0.50749201 0.716151
		 0.96820098 0.77850002 0.36351401 0.73844999 0.63809502 0.81105399 0.935417 0.80889302
		 0.563748 0.83362001 0.643911 0.80992198 0.40523699 0.78512102 0.56040001 0.73359102
		 0.41721201 0.83057302 0.67195803 0.70881498 0.53173298 0.77738899 0.63972998 0.78789002
		 0.59551501 0.75498801 0.56543702 0.73047298 0.53162301 0.764507 0.96649098 0.72399098
		 0.616225 0.76665199 0.61970103 0.69890702 0.57393903 0.699045 0.49169001 0.69689202
		 0.63810003 0.77806097 0.32664001 0.79073602 0.44918501 0.74136603 0.50562102 0.77984202
		 0.43311501 0.780797 0.48668501 0.73936898 0.62830502 0.79412401 0.431595 0.82083303
		 0.40162501 0.77066702 0.41495201 0.76567799 0.42450899 0.75332499 0.63996798 0.76980001
		 0.544644 0.81625301 0.39178401 0.743922 0.45552301 0.79212201 0.420645 0.77992702
		 0.60841101 0.80424702 0.36449501 0.71330798 0.54869801 0.76591498 0.63941598 0.74889201
		 0.64815998 0.729671 0.43629301 0.753295 0.437087 0.83206302 0.60790002 0.809434 0.62997502
		 0.73877603 0.40351501 0.77987498 0.42414901 0.766222 0.62928301 0.71485299 0.342336
		 0.74833399 0.62948501 0.81891298 0.43533 0.84788603 0.96350002;
	setAttr ".uvst[0].uvsp[750:999]" 0.765697 0.64657003 0.736453 0.35442701 0.75003803
		 0.61895001 0.783517 0.64002103 0.78241801 0.61686498 0.75469202 0.42101899 0.69294602
		 0.34202 0.69401002 0.32361701 0.77193898 0.45257801 0.84814203 0.93516803 0.73171902
		 0.54611099 0.72252798 0.650204 0.79598403 0.40760899 0.82012302 0.45599601 0.70112503
		 0.60461402 0.73771697 0.93394399 0.76273298 0.34605601 0.77334201 0.61056101 0.76538402
		 0.43629599 0.83262902 0.43264401 0.80546701 0.576563 0.74719203 0.638924 0.810803
		 0.96824503 0.77670503 0.6178 0.71770197 0.93365502 0.77924299 0.417308 0.80781001
		 0.60672998 0.735614 0.97086197 0.80362803 0.32311401 0.79393101 0.97253603 0.71894002
		 0.35775101 0.77872002 0.407341 0.832129 0.59706903 0.75612402 0.61974102 0.80203301
		 0.41983101 0.75650001 0.64899802 0.68740201 0.96240801 0.74111801 0.43706799 0.73250902
		 0.341144 0.743882 0.419245 0.78943801 0.546498 0.74428302 0.66895902 0.73061001 0.455695
		 0.83435702 0.63068402 0.75931102 0.45712799 0.68514502 0.53211302 0.77424502 0.64969897
		 0.79121202 0.65152597 0.73378098 0.51769102 0.83335203 0.61767203 0.69753802 0.64962202
		 0.76546103 0.65900201 0.81609797 0.419213 0.80017501 0.45164701 0.80705303 0.34134501
		 0.83282602 0.45718601 0.75325102 0.60907203 0.80664402 0.65275103 0.72555202 0.67034203
		 0.72746599 0.605281 0.80554402 0.59457302 0.76043397 0.331736 0.76474798 0.94109601
		 0.82731402 0.41391501 0.72020501 0.32480201 0.79510897 0.934623 0.75987703 0.61129099
		 0.72219002 0.63770998 0.74768901 0.351237 0.75115401 0.54211998 0.75159502 0.55120802
		 0.75433898 0.510934 0.74722201 0.34215701 0.78882802 0.71398699 0.70134002 0.72598702
		 0.83031201 0.73640001 0.81705701 0.73697799 0.720819 0.69770402 0.758735 0.73888803
		 0.76236898 0.704108 0.80226499 0.72501397 0.79938698 0.73801398 0.77145302 0.69444901
		 0.732063 0.74377 0.77507699 0.73663503 0.76144803 0.68911302 0.75383198 0.70281798
		 0.81838602 0.72369897 0.71570897 0.71392399 0.730349 0.73219901 0.74840802 0.74198002
		 0.71394098 0.72978401 0.785923 0.70305902 0.700755 0.73785502 0.71432197 0.74335301
		 0.82879102 0.69858903 0.74348402 0.68412602 0.75547397 0.73086399 0.77928901 0.70400798
		 0.81818199 0.71117598 0.74912697 0.71829897 0.80081803 0.69918901 0.82977003 0.72317302
		 0.80276299 0.71184301 0.73852402 0.70005798 0.70269102 0.71162701 0.82935601 0.71073103
		 0.78160203 0.73749202 0.81724101 0.69900602 0.78734499 0.72635603 0.71060002 0.68074602
		 0.70713502 0.69514102 0.72507602 0.68251902 0.73347503 0.716389 0.757128 0.71789801
		 0.74601001 0.73288399 0.779899 0.72708398 0.78147602 0.71539497 0.76774597 0.73460901
		 0.76859498 0.728131 0.76971 0.71664298 0.77069402 0.707093 0.77133501 0.96560597
		 0.77053201 0.94069499 0.78083998 0.444089 0.748537 0.33455101 0.68839002 0.94761902
		 0.76398897 0.953215 0.73562598 0.95242101 0.71608001 0.95071298 0.760423 0.97143197
		 0.74996603 0.97645903 0.78307003 0.936059 0.77399498 0.93710899 0.76112401 0.93725598
		 0.75135201 0.931894 0.77521199 0.969253 0.78331101 0.97104698 0.74914998 0.953444
		 0.75868899 0.95367098 0.81530702 0.95177299 0.79660797 0.95288199 0.84923202 0.94968402
		 0.77151 0.95294499 0.78521001 0.95291102 0.77670699 0.95285201 0.76775199 0.93866402
		 0.68853903 0.921691 0.767672 0.968373 0.77936202 0.92598802 0.76803601 0.93027502
		 0.74277502 0.92241102 0.71815902 0.98569101 0.73768902 0.98141903 0.76765102 0.95285898
		 0.72365302 0.91991401 0.68201703 0.977593 0.77653402 0.98513502 0.76569802 0.97777802
		 0.70627803 0.85632902 0.72305399 0.88751 0.80198503 0.85815102 0.764171 0.88681901
		 0.83290303 0.88353902 0.83344001 0.85861802 0.74362099 0.85684502 0.80411702 0.88497901
		 0.72711903 0.85625798 0.73880899 0.89006501 0.79003799 0.89056098 0.70278102 0.88036501
		 0.767259 0.86407202 0.79233903 0.85671002 0.76974899 0.88628101 0.77210099 0.86341602
		 0.70429802 0.86812502 0.76556498 0.87467098 0.74019098 0.87325698 0.72406697 0.87156099
		 0.76038003 0.89034402 0.75167799 0.89289099 0.78266001 0.85826802 0.77506799 0.85992998
		 0.76416898 0.860829 0.75638902 0.85711998 0.772708 0.889382 0.78020102 0.89027297
		 0.752451 0.874174 0.76092499 0.87465799 0.80617601 0.87166601 0.79162002 0.87264198
		 0.833745 0.87140203 0.77163702 0.87435198 0.78246599 0.87326002 0.77564299 0.87389302
		 0.77005601 0.861884 0.70777202 0.84692001 0.76639801 0.889036 0.78017902 0.850196
		 0.77017301 0.85471702 0.74751401 0.84543502 0.72333902 0.90306503 0.73995101 0.89974397
		 0.76871598 0.87444198 0.73346603 0.84450603 0.69750202 0.89283401 0.77499503 0.90359801
		 0.76493502 0.897457 0.180388 0.252828 0.191416 0.24187601 0.18696401 0.237561 0.17987999
		 0.23834001 0.188682 0.230546 0.181196 0.227577 0.19541 0.230497 0.195618 0.223683
		 0.189564 0.224618 0.1841 0.222056 0.15511701 0.24276701 0.168332 0.23350801 0.163707
		 0.227759 0.155893 0.22743499 0.17372601 0.224217 0.166583 0.22197901 0.159481 0.217319
		 0.174091 0.218004 0.168616 0.21727701 0.164415 0.212962 0.179546 0.222086 0.177627
		 0.221231 0.178599 0.216754 0.181026 0.217619 0.129337 0.227173 0.14386199 0.221187
		 0.13952599 0.21419799 0.13237999 0.21132199 0.15080801 0.213008 0.143315 0.209225
		 0.139898 0.200285 0.151705 0.206165 0.146198 0.204714 0.14436001 0.197742 0.159348
		 0.21021099 0.157087 0.20897301 0.16236 0.205993 0.159676 0.204208 0.106466 0.20630901
		 0.122464 0.202406 0.120048 0.193781 0.112749 0.18909501;
	setAttr ".uvst[0].uvsp[1000:1249]" 0.132654 0.19420099 0.126214 0.18859901 0.121711
		 0.18025699 0.135548 0.188014 0.130514 0.18456399 0.12812801 0.17794 0.140761 0.19314
		 0.138925 0.19164 0.141863 0.188085 0.143804 0.19011 0.089189999 0.178977 0.106244
		 0.17837501 0.105484 0.16949099 0.098587997 0.16282099 0.116816 0.17188001 0.112223
		 0.165327 0.109827 0.156665 0.121419 0.165977 0.117808 0.161817 0.116804 0.15569501
		 0.12487 0.173196 0.123642 0.17106999 0.12806 0.167647 0.129328 0.17011601 0.073480003
		 0.147989 0.104685 0.145788 0.100978 0.13818599 0.097695 0.12980901 0.109238 0.141811
		 0.105145 0.13631 0.103656 0.128968 0.114046 0.150755 0.112527 0.147995 0.117863 0.14907999
		 0.115798 0.145491 0.066202 0.113066 0.094090998 0.114191 0.093680002 0.105675 0.092370003
		 0.097429998 0.099636003 0.110683 0.097880997 0.104161 0.097870998 0.097023003 0.099779002
		 0.121111 0.099021003 0.117611 0.103855 0.115483 0.104632 0.119441 0.065977998 0.082216002
		 0.090875998 0.083612002 0.090718001 0.076536 0.090196997 0.070862003 0.096727997
		 0.081401996 0.094921 0.075630002 0.094958 0.069030002 0.100291 0.088910997 0.095679
		 0.090259999 0.095311001 0.087052003 0.099834003 0.085375004 0.193389 0.184917 0.186188
		 0.19516601 0.19112401 0.196459 0.19539499 0.194043 0.186435 0.202013 0.191011 0.201649
		 0.195915 0.20184401 0.178691 0.180926 0.17084201 0.189824 0.17494699 0.193132 0.17931999
		 0.19334599 0.170643 0.19633999 0.174353 0.197806 0.177894 0.19916999 0.181638 0.197455
		 0.183402 0.198017 0.18306901 0.20282599 0.180785 0.202058 0.168466 0.170973 0.16011
		 0.17496599 0.161661 0.18052 0.165205 0.18489601 0.15563101 0.18103001 0.15879101
		 0.184117 0.161384 0.18875501 0.16666099 0.190685 0.168199 0.191992 0.165167 0.19386099
		 0.16742 0.19582801 0.161327 0.154882 0.151613 0.15673099 0.153247 0.162582 0.15662199
		 0.167108 0.14651 0.162926 0.14858501 0.16664 0.15025499 0.171166 0.153753 0.174749
		 0.154597 0.176678 0.150002 0.177515 0.15142301 0.179763 0.154603 0.137381 0.144326
		 0.138345 0.14541601 0.144876 0.148453 0.14926399 0.13771901 0.144284 0.1401 0.148416
		 0.141647 0.15255199 0.145779 0.15565801 0.146624 0.15738501 0.142175 0.15802 0.14332201
		 0.160412 0.148747 0.11237 0.136685 0.1111 0.136843 0.119631 0.14040101 0.126388 0.13033199
		 0.123046 0.131759 0.129044 0.128197 0.116929 0.137023 0.13328201 0.138154 0.136667
		 0.133209 0.140084 0.131457 0.135699 0.146245 0.084347002 0.13235401 0.081739999 0.13232499
		 0.090426996 0.13549601 0.097676001 0.122007 0.087963 0.123668 0.093663 0.124156 0.099734001
		 0.12851 0.104577 0.128956 0.107852 0.122573 0.106328 0.123609 0.110356 0.139375 0.057360001
		 0.124238 0.050554 0.124836 0.061239999 0.12871701 0.068412997 0.114519 0.059865002
		 0.118349 0.065856002 0.118789 0.072488002 0.123032 0.076848999 0.123886 0.080187999
		 0.117531 0.079005003 0.118277 0.083309002 0.093300998 0.062601 0.086544998 0.059937
		 0.096267998 0.063887998 0.195544 0.213118 0.19035999 0.21310399 0.185867 0.212015
		 0.182381 0.210465 0.17979801 0.209709 0.17562599 0.209216 0.171487 0.208326 0.168201
		 0.20522501 0.16500901 0.201076 0.162211 0.198892 0.156005 0.19687501 0.138503 0.176402
		 0.13681 0.17159399 0.13613901 0.165446 0.13495301 0.16278 0.13068201 0.158838 0.128196
		 0.155246 0.126724 0.150635 0.124882 0.14499301 0.122898 0.14101 0.119415 0.13548701
		 0.11672 0.130227 0.115163 0.124049 0.113931 0.115208 0.112721 0.11118 0.110482 0.105755
		 0.109276 0.099852003 0.108757 0.093639001 0.108689 0.086708002 0.108244 0.082604997
		 0.107077 0.077422 0.105774 0.071718 0.104152 0.065635003 0.14160199 0.179867 0.14566401
		 0.183 0.147533 0.185128 0.150313 0.190043 0.152464 0.194695 0.195051 0.26010501 0.198845
		 0.245179 0.20013601 0.228734 0.200039 0.220704 0.20031101 0.21277399 0.20035 0.203154
		 0.199995 0.19460499 0.199605 0.187802 0.071162 0.056402002 0.18736599 0.16853 0.181932
		 0.156598 0.17365 0.145905 0.173356 0.128371 0.169287 0.099353001 0.161018 0.076439999
		 0.158154 0.050746001 0.13831601 0.027233001 0.047910001 0.069664001 0.036345001 0.086846001
		 0.033493001 0.119865 0.039609 0.15903001 0.055178002 0.196382 0.077831 0.229156 0.104662
		 0.25585601 0.135361 0.27596501 0.164958 0.28749701 0.18720201 0.29186001 0.084461004
		 0.108741 0.082532004 0.078555003 0.090759002 0.14088599 0.84134603 0.81796402 0.71385503
		 0.82569402 0.77078301 0.81199199 0.73522401 0.808007 0.767057 0.78877503 0.76073003
		 0.80682999 0.72035903 0.76651102 0.71856099 0.78046399 0.69237602 0.811634 0.76044399
		 0.79911703 0.69339401 0.79032302 0.81721902 0.79096502 0.83955902 0.78333902 0.84144902
		 0.80220801 0.77045399 0.79729402 0.73900902 0.78114903 0.84105301 0.79238302 0.69419903
		 0.78087902 0.73631001 0.79431802 0.71672797 0.805399 0.81670201 0.80432802 0.71557999
		 0.81625098 0.69493002 0.76531702 0.79527003 0.80663902 0.78070199 0.79443997 0.78074402
		 0.80722499 0.76100802 0.791381 0.79576099 0.79201603 0.75166601 0.81683803 0.75331497
		 0.785577 0.77019602 0.805758 0.73734802 0.82736403 0.75030398 0.797176 0.75001597
		 0.80878299 0.75820303 0.77672303 0.75446302 0.82288802 0.69290602 0.80137902 0.71696198
		 0.79271501;
	setAttr ".uvst[0].uvsp[1250:1474]" 0.74334902 0.76964003 0.84093601 0.81122202
		 0.73565203 0.81873697 0.77126801 0.789783 0.76576197 0.79877901 0.765607 0.805659
		 0.77883202 0.820333 0.79134703 0.82253402 0.815952 0.81717902 0.76564503 0.81172502
		 0.81589103 0.77770603 0.79101002 0.77673203 0.77790803 0.78178197 0.76072001 0.811858
		 0.75508302 0.75100303 0.76555401 0.74462402 0.81582803 0.750624 0.83164901 0.74889803
		 0.79598302 0.75251901 0.81338203 0.51875401 0.81485301 0.53214502 0.77242798 0.53245401
		 0.77239299 0.52124703 0.79209697 0.53256798 0.79119903 0.52046603 0.75348699 0.52021098
		 0.75204802 0.53036797 0.76835102 0.47793201 0.73496503 0.477741 0.748133 0.47832301
		 0.78338802 0.467985 0.76977301 0.38152 0.80065602 0.38409001 0.749641 0.368357 0.76727301
		 0.309201 0.79873401 0.30652601 0.74745703 0.31901601 0.101337 0.061778001 0.102868
		 0.049865998 0.103594 0.057677999 0.099509001 0.040056001 0.231876 0.40095901 0.246757
		 0.396227 0.246757 0.396227 0.25782901 0.39343101 0.231876 0.40095901 0.241255 0.60058302
		 0.25363401 0.59530401 0.25363401 0.59530401 0.270915 0.59304702 0.270915 0.59304702
		 0.29756701 0.59379202 0.241255 0.60058302 0.196666 0.67257702 0.214699 0.66965801
		 0.302008 0.66820103 0.24254 0.664424 0.214699 0.66965801 0.196666 0.67257702 0.24254
		 0.664424 0.264319 0.48276001 0.288239 0.47301 0.264319 0.48276001 0.26674101 0.58062702
		 0.291738 0.58948803 0.26674101 0.58062702 0.190347 0.91905302 0.208799 0.92285901
		 0.190347 0.91905302 0.155166 0.91612202 0.191183 0.98846602 0.15397 0.98585302 0.191183
		 0.98846602 0.210859 0.99012703 0.248015 0.97997302 0.244314 0.92578202 0.244314 0.92578202
		 0.208799 0.92285901 0.248015 0.97997302 0.210859 0.99012703 0.192919 0.84535402 0.207532
		 0.84750903 0.192919 0.84535402 0.16651 0.84246802 0.195344 0.90463102 0.167336 0.90249503
		 0.195344 0.90463102 0.21226101 0.90753102 0.24680001 0.898808 0.240309 0.84932202
		 0.240309 0.84932202 0.207532 0.84750903 0.24680001 0.898808 0.21226101 0.90753102
		 0.206321 0.82836503 0.22382 0.82372802 0.182484 0.82501602 0.206321 0.82836503 0.22382
		 0.82372802 0.186313 0.76706702 0.210049 0.76999903 0.210049 0.76999903 0.224316 0.77731401
		 0.224316 0.77731401 0.186313 0.76706702 0.16052 0.77195001 0.182484 0.82501602 0.31021401
		 0.81951803 0.203032 0.68264103 0.219714 0.688941 0.18512 0.68247902 0.170734 0.68351102
		 0.203032 0.68264103 0.18512 0.68247902 0.219714 0.688941 0.223838 0.74932402 0.300392
		 0.74946201 0.28485101 0.75419903 0.28485101 0.75419903 0.26465401 0.756796 0.26465401
		 0.756796 0.223838 0.74932402 0.183493 0.48297501 0.170239 0.48325801 0.202592 0.47763601
		 0.183493 0.48297501 0.202592 0.47763601 0.27021101 0.37661001 0.29966801 0.37887001
		 0.27021101 0.37661001 0.271786 0.30654201 0.30352601 0.30338001 0.271786 0.30654201
		 0.75293702 0.396649 0.76785201 0.40129101 0.74185002 0.39392 0.75293702 0.396649
		 0.76785201 0.40129101 0.75862801 0.60047102 0.74619502 0.59522402 0.728881 0.59305501
		 0.74619502 0.59522402 0.70221603 0.59393901 0.728881 0.59305501 0.75862801 0.60047102
		 0.80330801 0.672571 0.78525501 0.66964602 0.69815302 0.66834801 0.75751102 0.66429502
		 0.80330801 0.672571 0.78525501 0.66964602 0.75751102 0.66429502 0.71163303 0.47286201
		 0.73561603 0.48262501 0.73561603 0.48262501 0.73324102 0.580742 0.70818502 0.58963603
		 0.73324102 0.580742 0.80977702 0.91902101 0.79131502 0.922809 0.80977702 0.91902101
		 0.84498203 0.91613001 0.84609002 0.98590702 0.80884302 0.98848301 0.80884302 0.98848301
		 0.78914702 0.99011803 0.751926 0.97986102 0.75583601 0.92564702 0.75583601 0.92564702
		 0.79131502 0.922809 0.751926 0.97986102 0.78914702 0.99011803 0.80699402 0.84535801
		 0.79238802 0.847552 0.80699402 0.84535801 0.83340001 0.84239602 0.83274502 0.90244102
		 0.80473202 0.90465403 0.80473202 0.90465403 0.78781903 0.90760303 0.75316501 0.898655
		 0.75962102 0.84945101 0.75962102 0.84945101 0.79238802 0.847552 0.75316501 0.898655
		 0.78781903 0.90760303 0.79350001 0.82843202 0.776039 0.82368302 0.81735402 0.82523602
		 0.79350001 0.82843202 0.776039 0.82368302 0.79013902 0.77004403 0.81389803 0.76727003
		 0.77581602 0.77726603 0.79013902 0.77004403 0.77581602 0.77726603 0.81389803 0.76727003
		 0.83965802 0.77232403 0.81735402 0.82523602 0.68980002 0.81945401 0.78004402 0.68875003
		 0.79669303 0.68238002 0.81459802 0.68213803 0.82898903 0.68310702 0.79669303 0.68238002
		 0.81459802 0.68213803 0.78004402 0.68875003 0.77616602 0.74914402 0.69960201 0.74953002
		 0.71515602 0.75421703 0.71515602 0.75421703 0.73536003 0.75675201 0.73536003 0.75675201
		 0.77616602 0.74914402 0.82997602 0.483217 0.81672502 0.483015 0.79759502 0.47779101
		 0.81672502 0.483015 0.79759502 0.47779101 0.72980702 0.37662101 0.70035303 0.37889701
		 0.72980702 0.37662101 0.69645202 0.30341101 0.72819102 0.306555 0.72819102 0.306555;
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr -s 1291 ".vt";
	setAttr ".vt[0:165]"  0.39061093 0.047709107 0.0057262182 0.36903894 0.056335092 -0.016124874
		 0.36384746 0.063512444 -0.19438747 0.38306645 0.047814965 0.088163242 0.43917662 0.10354578 -0.13060224
		 0.30516136 0.052188516 -0.025743693 0.33512276 0.032515168 -0.0013709664 0.40496969 0.087621331 0.037823737
		 0.099335596 0.17452967 0.38381413 0.29674846 0.0062786341 0.021402419 0.31479928 0.099145532 -0.17666784
		 0.39103687 0.086358666 -0.015020847 0.32496685 0.032872796 0.099321887 0.28366917 0.15471041 0.070691913
		 0.2869994 0.020541787 0.079149589 0.32993603 0.1344372 -0.38594905 0.41847214 0.10488188 -0.16881111
		 0.37306914 0.087201715 -0.33778295 0.41545904 0.15980494 -0.040100515 0.33574724 0.093875527 -0.022986561
		 0.25438422 0.084733605 0.054703414 0.37545303 0.0048691034 0.038651615 0.26293406 0.034106851 0.019595712
		 0.38764036 0.097375512 -0.35567525 0.36336243 0.16493475 -0.17169172 0.31568134 0.032054543 -0.051379502
		 0.40272295 0.14829028 -0.32740018 0.37512919 0.16019595 0.018995017 0.3833763 0.15868151 0.040496498
		 0.27544802 0.088351846 -0.0062762499 0.44780713 0.2321564 -0.38715765 0.32016098 0.16084826 -0.15681511
		 0.38210368 0.038732171 -0.018804759 0.43406096 0.10645831 -0.45381919 0.37679386 0.14656603 -0.33643505
		 0.33788526 0.028115869 -0.018589258 0.28122625 0.042770028 -0.014259249 0.25926587 0.037715554 0.054682434
		 0.38474077 0.11364329 -0.34674677 0.32285005 0.0860039 -0.28069565 0.30296409 0.15389407 -0.080459177
		 0.41117603 0.11195529 -0.33295831 0.33823296 0.033967614 -0.024887681 0.2814177 0.0062385798 0.018746376
		 0.43386966 0.16655314 -0.11662918 0.32698905 0.040210366 -0.18667492 0.015108479 0.090230584 0.42296892
		 0.36775666 0.13937533 -0.33513188 0.40238702 0.040170312 0.063774019 0.10862581 0.062791467 0.32574788
		 0.3154189 0.15302241 -0.053735167 0.26988548 0.15424693 0.052955836 0.34792292 0.13854754 -0.32846221
		 0.30373991 0.089070916 -0.046824336 0.425161 0.17839015 -0.30275792 0.32280988 0.15684187 0.082451329
		 0.43608558 0.10376132 -0.082781434 0.29360995 0.012265801 0.052203625 0.33706212 0.05813849 -0.025448352
		 0.33850017 0.050891519 -0.026408792 0.41967639 0.047143579 -0.090373456 0.021484863 -0.043262839 0.40727863
		 0.39112675 0.09109652 0.0093616247 0.33384323 0.033525109 0.011783719 0.38054976 0.15604556 -0.025928169
		 0.41335446 0.16720927 -0.15167087 0.38236168 0.15825617 0.059947193 0.33541137 0.14848101 -0.43888348
		 0.35938215 0.04951632 -0.23500884 0.40172762 0.055503488 -0.1738275 0.38794327 0.10823214 -0.27051622
		 0.36454499 0.10668528 -0.19229165 0.40291679 0.039090753 0.03574729 0.35073969 0.076009393 -0.28370214
		 0.34711158 0.16558039 -0.31347898 0.40828836 0.087515473 -0.32565448 0.43706465 0.074746728 -0.29871097
		 0.37522125 0.0090662241 0.064462155 0.30973077 0.017905831 -0.083909929 0.37071329 0.17164195 -0.32404396
		 0.44922164 0.14211619 -0.21662903 0.35588175 0.075063348 -0.31390455 0.31190664 0.027358651 -0.024875879
		 0.36330819 0.21892703 -0.34859863 0.43083706 0.08726275 -0.03867659 0.33189446 0.030427575 0.053246498
		 0.37419006 0.023852944 0.085523546 0.44062668 0.17751849 -0.26262167 0.44207406 0.10978854 -0.26681268
		 0.30413485 0.15734541 -0.00042036176 0.25554085 0.08445704 0.023074657 0.39539093 0.17466986 -0.31805301
		 0.38839507 0.097402215 -0.29702687 0.33357131 0.02400744 0.030709445 0.4629012 0.17298281 -0.43470144
		 0.39046204 0.0099636316 0.035531998 0.30349863 0.11967051 -0.26212031 0.034625534 0.15991843 0.29435769
		 0.33073786 0.031043649 0.070699155 0.4490748 0.11606753 -0.39185286 0.3808552 0.02147162 0.0061804056
		 0.27787 0.011449456 0.053152084 0.29387724 0.016957879 -0.013355166 0.4351019 0.11122382 -0.31387639
		 0.44786388 0.23656619 -0.4249312 0.41590863 0.23594916 -0.44870341 0.42642182 0.059869409 -0.21807653
		 0.10063644 0.16468489 0.3039791 0.41578072 0.031296372 -0.039232582 0.3841947 0.090291619 0.085630015
		 0.35920948 0.10376132 -0.3443417 0.32905626 0.11750376 -0.20903927 0.35776648 0.15998137 0.0062586665
		 0.11891171 0.040673852 0.40632069 0.36667836 0.085598588 -0.39418688 0.3185786 0.02400744 -0.016086817
		 0.42981568 0.081426263 -0.26721454 0.31570497 0.15450823 -0.25954437 0.34003043 0.1534096 -0.034745634
		 0.39031073 0.012520432 0.063073605 0.014538473 0.059048295 0.32090861 0.30477113 0.01584971 -0.005505681
		 0.11416496 0.094453454 0.40389907 0.3889367 0.094403863 -0.3190867 0.30159459 0.091566682 -0.016245782
		 0.012345998 0.040385842 0.42651427 0.33021039 0.2241112 -0.42394987 0.011075921 0.023681283 0.35674295
		 0.46074682 0.16554415 -0.38762328 0.38826254 0.09448874 -0.34175676 0.33143312 0.15879786 0.00017097592
		 0.36845216 0.019052148 0.0091963112 0.3326773 0.08747828 -0.31529763 0.36170465 0.021548867 0.081642464
		 0.031924568 0.17286646 0.39379737 0.44687295 0.14767516 -0.26451105 0.44992164 0.1236788 -0.43139043
		 0.43176916 0.1481787 -0.3105841 0.28864095 0.085851312 -0.080364227 0.32537609 0.053950906 0.098671466
		 0.43407282 0.17286646 -0.22106639 0.2706871 0.15490115 0.028382212 0.4409931 0.093984246 -0.2086989
		 0.43514639 0.16358912 -0.077464521 0.30017567 0.019668221 0.076375216 0.27617896 0.046273828 0.080280021
		 0.42413461 0.028218865 -0.14076033 0.28466752 0.15612566 0.0070865452 0.36791319 0.15887225 0.075426787
		 0.32691103 0.026376367 0.092465311 0.31608832 0.12761748 -0.30397338 0.3378 0.065643907 -0.2241078
		 0.32871807 0.21978819 -0.38532442 0.33581108 0.15385401 -0.21218252 0.3644338 0.033273339 -0.015278012
		 0.27409285 0.087403893 0.077806324 0.32478237 0.090256333 0.09398149 0.36733603 0.093875527 -0.0086516142
		 0.3349123 0.093682885 -0.016290396 0.36762699 0.11411059 -0.43754956 0.10717602 -0.040712714 0.39620036
		 0.32655627 0.15998137 -0.29504496 0.43473762 0.16276896 -0.47445545 0.10902315 0.026930451 0.34850502
		 0.35618889 0.028977036 -0.0088596642 0.40462211 0.087957025 0.063473374;
	setAttr ".vt[166:331]" 0.41342765 0.11459124 -0.38909456 0.3613238 0.072069764 -0.070149362
		 0.35177344 0.051606774 -0.047243029 0.36776638 0.064998269 -0.16616416 0.40806133 0.12995684 -0.41571644
		 0.23983645 -0.0040458441 0.10235859 0.32426211 0.13587248 0.19643661 0.24864635 0.12916529 0.15595721
		 0.24187505 0.093648553 0.1585616 0.34647554 0.09697783 0.13368492 0.28570798 -0.0052713156 0.20427912
		 0.32542336 -0.0028480291 0.1201327 0.21517737 0.047672868 0.13033344 0.22917655 0.046702027 0.16782324
		 0.3055349 0.0052295923 0.096365377 0.28979269 0.052111268 0.22085413 0.24909571 -0.0055602789 0.17268349
		 0.31118998 0.015557885 0.094653144 0.34027398 0.0010486841 0.12486301 0.22754219 0.099065423 0.12745745
		 0.34899437 0.10488188 0.17897959 0.32228956 0.055910707 0.22464308 0.28915891 0.0071102381 0.21800447
		 0.31926459 0.10462916 0.21152848 0.26771164 0.0074402094 0.085223377 0.29864061 0.13429511 0.19338539
		 0.28957981 0.098023057 0.20948526 0.27256966 0.12631381 0.097053751 0.31686151 0.050021768 0.099508658
		 0.30691618 -0.015118003 0.19828621 0.27338338 -0.0013259649 0.092373177 0.24137115 0.097845674 0.10097526
		 0.33890694 -0.013021827 0.17358449 0.27029684 0.050424218 0.084362373 0.23499972 0.12670577 0.12824762
		 0.23658428 0.048318505 0.09399803 0.34992856 0.044650674 0.1304113 0.34474644 0.13552153 0.17355858
		 0.24596205 0.12540591 0.10430041 0.2388712 0.0014778376 0.17122915 0.27130911 0.09333384 0.092950389
		 0.22708106 -0.0069955587 0.13899238 0.31123972 0.12898886 0.10874684 0.34036398 0.13226855 0.13384353
		 0.31482989 0.091264367 0.106086 0.35127908 0.044507623 0.18085742 0.3234556 -0.020353675 0.16270755
		 0.3151705 0.0015865564 0.21834478 0.24014843 -0.013883948 0.14544417 0.25008667 -0.014246345 0.11214337
		 0.27024469 0.0069977045 0.17840254 0.27480251 0.0094944239 0.16428968 0.28568912 0.0030761957 0.1376961
		 0.29607916 0.010833383 0.11416306 0.019230902 -0.041181922 0.38584366 0.10636719 -0.03973043 0.37983617
		 0.38760746 0.084418893 -0.23463246 0.4039197 0.12140048 -0.43977043 0.068087474 0.17546809 0.40419704
		 0.068735465 -0.043489814 0.40646759 0.067791767 0.042770028 0.42864063 0.067269102 0.097653985 0.42866194
		 0.012792997 -0.028511405 0.41221017 0.012539932 0.0020653009 0.42058492 0.11133623 -0.0026992559 0.36297491
		 0.11356418 -0.02810514 0.37328932 0.11513471 -0.027600646 0.39799562 0.11870128 0.0014511347 0.40308067
		 0.011409402 -0.027963996 0.37820414 0.011336081 -0.0026773214 0.36824462 0.068293221 0.0033279657 0.42231387
		 0.068593621 -0.026703238 0.41349906 0.061860055 0.058215737 0.30766174 0.060398392 0.024068475 0.34114966
		 0.06810873 0.16038668 0.28639677 0.063243628 -0.041433692 0.38241163 0.061985433 -0.002951026 0.35916236
		 0.062711477 -0.027563453 0.37227935 0.11292559 -0.043914199 0.38538221 0.11026248 0.1708523 0.35438257
		 0.012589606 -0.047521949 0.39811143 0.12537572 -0.00093114376 0.3830834 0.12348597 -0.028934836 0.38330105
		 0.12337722 0.030092835 0.37911463 0.0049856994 0.075147271 0.37793455 0.0018660889 0.033603311 0.40466061
		 0.067474842 -0.051645637 0.39402908 0.11859481 0.077556252 0.37027615 0.02102603 0.16933978 0.35161778
		 0.0018873763 -0.00093114376 0.40656668 0.0019417743 -0.030740142 0.4013091 0.20099613 0.144611 0.3492786
		 0.13615128 0.083699822 0.39709452 0.19150254 0.055291772 0.28504148 0.14586954 -0.033920646 0.38156724
		 0.12682784 0.13248122 0.29607508 0.18110295 0.13659441 0.28623813 0.22105476 0.042770028 0.34187809
		 0.11993827 0.052363992 0.30901438 0.21307242 0.087706208 0.34507582 0.13822782 0.04128325 0.39939585
		 0.12395903 0.022950768 0.33867082 0.15091681 0.14348757 0.374964 0.21167232 -0.027460456 0.33697984
		 0.19947529 0.026845574 0.29836681 0.13766259 -0.032469153 0.36457381 0.2062372 -0.027172446 0.32385093
		 0.18193555 0.14548266 0.37356839 0.18500543 -0.033338904 0.35885742 0.18831651 0.043777108 0.3817803
		 0.18494166 0.09059298 0.38388512 0.13973211 -0.021145225 0.38971302 0.13979596 0.0048691034 0.39430353
		 0.20630828 0.0036064386 0.30792883 0.2099576 -0.016746879 0.31619498 0.21824259 -0.015234351 0.3365353
		 0.22245485 0.0097471476 0.34038097 0.128694 -0.02122438 0.3617405 0.12553889 -0.00040280819 0.35099575
		 0.18861234 0.0078655481 0.37431344 0.18686207 -0.018545508 0.36544672 0.15173988 0.046664834 0.27841693
		 0.15944543 0.018876672 0.30529177 0.15094516 0.13292468 0.28089082 0.17330271 -0.03230226 0.34108341
		 0.16557589 -0.0021680593 0.32163244 0.16989458 -0.021067023 0.33333036 0.21273439 -0.029808402 0.32624456
		 0.20186651 0.1415478 0.32329527 0.13524298 -0.037824988 0.37817594 0.22124624 0.0077911615 0.31915841
		 0.21960022 -0.016205192 0.3214573 0.21747164 0.038481355 0.31433848 0.11965923 0.069686532 0.36163411
		 0.12456211 0.03464663 0.38428485 0.18020904 -0.04073751 0.34814104 0.2084842 0.072321534 0.31597969
		 0.13123414 0.14046443 0.34464061 0.12678298 0.0019565821 0.38708508 0.12688459 -0.023352981 0.38584831
		 0.076857373 0.2468468 0.40418744 0.049192395 0.18575633 0.42209387 0.071814917 0.16838992 0.42082855
		 0.099756569 0.18226016 0.40840214 0.070726953 0.12883627 0.42722628 0.11002599 0.12352812 0.41415656
		 0.036210183 0.1197468 0.43183348 0.036874775 0.091062188 0.42220506 0.070350878 0.10527384 0.4193196
		 0.10216904 0.098134637 0.41001052 0.17384431 0.24819338 0.36337695 0.15370762 0.17984164 0.3904579
		 0.18166591 0.16272891 0.38024065 0.2013225 0.18127787 0.35390469 0.14994475 0.1214844 0.39989009
		 0.18400493 0.1285454 0.38524985 0.21068376 0.1242615 0.35187539 0.15557377 0.096141458 0.39002502
		 0.18232577 0.10808337 0.37872672 0.20382465 0.095889688 0.35747617 0.1253686 0.099532723 0.40587369
		 0.13590053 0.098854661 0.40208018 0.13432768 0.075616479 0.39281365 0.12159623 0.075902581 0.39712518
		 0.26511249 0.25900996 0.28657413 0.24748048 0.19203627 0.31474751;
	setAttr ".vt[332:497]" 0.2789602 0.18040621 0.29831001 0.29665619 0.19445574 0.27067325
		 0.24885446 0.1330353 0.32102221 0.28415173 0.14814341 0.30279908 0.30472136 0.12468684 0.26844785
		 0.25932965 0.10942996 0.31034604 0.28119779 0.12804186 0.29639179 0.29490596 0.10618174 0.26913849
		 0.22447935 0.088019967 0.33602676 0.23439655 0.089978814 0.3273609 0.21944416 0.055970788 0.33109069
		 0.23038046 0.057423234 0.31974754 0.33748788 0.27023375 0.1847813 0.32486525 0.19935954 0.21709612
		 0.34461883 0.18034518 0.1861956 0.36230275 0.19639552 0.15119648 0.32225412 0.13031542 0.22210994
		 0.3459883 0.13782847 0.18868145 0.36036345 0.13031542 0.14520796 0.31869459 0.10715258 0.20658055
		 0.33644012 0.11753905 0.18262447 0.34447461 0.1051966 0.14899917 0.30602688 0.095754266 0.241263
		 0.30976379 0.09759295 0.22929522 0.30080709 0.075426698 0.22005489 0.2967366 0.074452996 0.23305371
		 0.40525848 0.26850855 0.07008639 0.3919664 0.19372332 0.10344668 0.40948972 0.17507517 0.071983367
		 0.42648315 0.19459116 0.032291502 0.38240916 0.12793124 0.10177682 0.40354854 0.13393366 0.069611043
		 0.4105351 0.12916529 0.02748087 0.37285393 0.10371935 0.086325586 0.38738972 0.11368334 0.063364863
		 0.39003879 0.10603869 0.033162087 0.35516006 0.095386147 0.12126331 0.3605763 0.094514489 0.11006896
		 0.34360173 0.070382714 0.10225706 0.33889279 0.071101785 0.11419608 0.45604515 0.28852999 -0.065069169
		 0.42740551 0.13734972 -0.032444566 0.44124383 0.14536631 -0.072273463 0.44628638 0.15254366 -0.11565232
		 0.41331181 0.12170184 -0.044565767 0.42723763 0.13515627 -0.075733423 0.42911327 0.135306 -0.11324933
		 0.39847988 0.10148966 0.0048937798 0.40326947 0.1045891 -0.010796189 0.38293892 0.089789033 0.0012561381
		 0.38937655 0.093732476 -0.018549472 0.47322762 0.29951251 -0.21757296 0.45049652 0.16129076 -0.19638819
		 0.451875 0.16319621 -0.23849934 0.4528873 0.16936266 -0.27961248 0.43431652 0.14511454 -0.20920014
		 0.43793029 0.15471041 -0.24266672 0.43587047 0.15456927 -0.27859563 0.43733668 0.13761103 -0.15938333
		 0.43796116 0.13973391 -0.17709342 0.42193711 0.12021601 -0.18373981 0.42210972 0.11859667 -0.16490385
		 0.47171137 0.31282675 -0.34965149 0.46256769 0.18065798 -0.35087189 0.46703327 0.18893397 -0.38862178
		 0.46737415 0.19856894 -0.42022917 0.44368464 0.16232455 -0.36037967 0.45125049 0.17842638 -0.39193079
		 0.44923109 0.18492091 -0.43016061 0.42838717 0.13720858 -0.32283086 0.44457412 0.15503657 -0.31726566
		 0.44739094 0.15724719 -0.33367738 0.43058199 0.13897669 -0.3405315 0.050483793 0.10505641 0.25474426
		 0.090932287 0.065137506 0.29207772 0.064728923 0.062286973 0.29686716 0.040848199 0.070773721 0.2881588
		 0.09160395 0.053447366 0.31919879 0.06589023 0.05670321 0.31747451 0.037877619 0.05251658 0.31989157
		 0.12921663 0.11354315 0.23482528 0.17064428 0.073373437 0.26115116 0.14631191 0.062286973 0.26951918
		 0.12632176 0.06665194 0.28218672 0.17141071 0.054202676 0.27758175 0.14928719 0.056007981 0.28446436
		 0.13058135 0.052943826 0.29674169 0.11412719 0.055211663 0.29944047 0.1053123 0.054936051 0.3026332
		 0.10865416 0.044280648 0.32530546 0.11984368 0.044675469 0.31960812 0.17957515 0.13536894 0.18696669
		 0.21111178 0.10451663 0.18813483 0.20608824 0.093757272 0.21150708 0.19360986 0.08643496 0.23567894
		 0.23069033 0.084203362 0.20397428 0.21795405 0.087453485 0.22127536 0.21009937 0.075315118 0.24339405
		 0.19029394 0.066762567 0.26120344 0.18472625 0.063344598 0.26807389 0.19860259 0.051038384 0.27057651
		 0.19062746 0.046526551 0.28026393 0.21027689 0.1578995 0.10540031 0.23536623 0.11530936 0.088622123
		 0.22441563 0.10365546 0.11573584 0.22121558 0.10909426 0.14607121 0.23608993 0.08234942 0.096892938
		 0.2271615 0.084313989 0.12011138 0.22714019 0.080052972 0.14885733 0.23273855 0.083219171 0.17559958
		 0.23210472 0.082688928 0.18571764 0.2469601 0.068467736 0.18171851 0.24671163 0.068893075 0.19349447
		 0.23544666 0.18298113 0.016118914 0.26178226 0.14211619 -0.003249377 0.25177318 0.12606204 0.023888469
		 0.24672133 0.12627757 0.050486565 0.26948553 0.1063534 0.0013461113 0.25827971 0.10365546 0.024446577
		 0.25424954 0.094090104 0.045974076 0.24912883 0.093732476 0.06954965 0.24651782 0.09166491 0.077886626
		 0.26037279 0.078958154 0.072399676 0.2572366 0.076684594 0.082983449 0.25217286 0.21048129 -0.11141124
		 0.2794736 0.16384184 -0.1441097 0.27494675 0.14662707 -0.1067616 0.27150792 0.15081465 -0.066930413
		 0.28736365 0.12302554 -0.10618708 0.28695688 0.11520541 -0.074037462 0.29432666 0.12685931 -0.13970578
		 0.28150755 0.12504351 -0.043690622 0.27896506 0.12274706 -0.026732534 0.29594439 0.10234511 -0.022681296
		 0.30009526 0.1047684 -0.043274522 0.25951895 0.24587309 -0.24671367 0.29187396 0.19881976 -0.29343447
		 0.28656903 0.17670214 -0.25511479 0.28445452 0.18080866 -0.21061248 0.30894312 0.14862216 -0.29911333
		 0.30039322 0.14215147 -0.26416141 0.30312014 0.13248122 -0.22998253 0.30114299 0.13901389 -0.19712612
		 0.29926747 0.13540804 -0.18174806 0.32108098 0.11996233 -0.20030957 0.31824273 0.11725008 -0.18230399
		 0.27290803 0.27504122 -0.38881317 0.30979693 0.24746096 -0.44544646 0.30361211 0.21357119 -0.40588513
		 0.30098683 0.2128998 -0.36500573 0.32510406 0.18856394 -0.44476721 0.31593925 0.18369639 -0.40483013
		 0.32611161 0.17485774 -0.37113371 0.32165569 0.17813838 -0.34102085 0.31928104 0.17431891 -0.3243185
		 0.33655143 0.15829432 -0.34398913 0.3349123 0.15344965 -0.32525712 0.44397828 0.200544 -0.46149418
		 0.44381464 0.24718249 -0.45451918 0.43593448 0.1862179 -0.45895591 0.037522838 0.065538049 0.37482423
		 0.068025939 0.076219201 0.36976302 0.095690906 0.070093751 0.3668206 0.11460723 0.055250764 0.36482686
		 0.12701704 0.056623101 0.35936102 0.14482658 0.074452996 0.34751177;
	setAttr ".vt[498:663]" 0.16728818 0.082460046 0.33619228 0.18783182 0.070345521 0.32181457
		 0.20541196 0.04280436 0.30861232 0.21700794 0.045984864 0.29624498 0.23819029 0.085069299 0.27564257
		 0.29019487 0.097266793 0.15446229 0.29676503 0.087201715 0.12856214 0.29978758 0.066842675 0.099049911
		 0.30329764 0.068505883 0.086982861 0.31919363 0.093757272 0.063769192 0.32761586 0.10447657 0.044486403
		 0.33354056 0.10048068 0.019891143 0.34183028 0.089498162 -0.01072526 0.34767932 0.092674851 -0.031020612
		 0.35691023 0.11389601 -0.06179595 0.36376202 0.12753642 -0.090761632 0.36746353 0.12844431 -0.12353542
		 0.37210855 0.11201632 -0.17351764 0.37424213 0.1130749 -0.19279081 0.3770898 0.13619959 -0.21910802
		 0.37782767 0.14796793 -0.25116938 0.37997043 0.14808047 -0.2857362 0.3826952 0.13533175 -0.32395878
		 0.38411421 0.13782847 -0.34394881 0.3875742 0.15908396 -0.36838791 0.38916367 0.17250216 -0.39976144
		 0.39139619 0.17755663 -0.4375996 0.28215331 0.089358926 0.17860612 0.27676788 0.064127564 0.20181242
		 0.27326989 0.064127564 0.21367124 0.26229089 0.088889718 0.23783353 0.25037771 0.099170327 0.26134071
		 0 0.10051596 0.42428392 0 0.058719277 0.41448748 0 0.19899142 0.42046672 0 0.26604617 0.41571504
		 0 0.038623452 0.38306677 0 0.037786126 0.33215496 0 0.066338181 0.29394633 0 0.10178149 0.26127213
		 0.021747403 0.26503718 0.41384441 0.015848758 0.19643557 0.42072669 0.013795822 0.10740435 0.42666572
		 0.014588146 0.069477677 0.41686222 0.013043709 0.046197534 0.38094988 0.013578227 0.042014718 0.32852912
		 0.015839295 0.068137765 0.29163057 0.018098 0.094801545 0.2666572 0.44866338 0.32559741 -0.44825414
		 0 0.15988123 0.20913264 0.083874747 0.16398776 0.19418028 0.11385986 0.20303881 0.1453307
		 0.15801457 0.22313654 0.089830711 0.16110343 0.2618233 0.0096335411 0.17635144 0.29416621 -0.13741592
		 0.2014904 0.30441821 -0.26125222 0.19833769 0.32284987 -0.3922219 0.23963766 0.31804812 -0.50949132
		 0 0.34285128 -0.50421298 0.47036558 0.41480982 -0.39565989 0.47779459 0.45717394 -0.32402036
		 0.47863692 0.46587241 -0.20203406 0.45750654 0.46627772 -0.060173124 0.40132993 0.45858347 0.068832934
		 0.32998097 0.44966853 0.18198337 0.25234789 0.44455206 0.27694339 0.16140135 0.4384495 0.34819761
		 0.077011101 0.43097079 0.38854218 0.024710912 0.42567408 0.40404543 0 0.42437899 0.40801907
		 0 0.34021533 -0.26629952 0 0.26389086 0.067617178 0.46444598 0.20838702 -0.22812614
		 0.46727461 0.22974741 -0.37183872 0.45246887 0.19837725 -0.073157936 0.22831324 0.15627444 0.29288682
		 0.22501892 0.091626763 0.30743688 0.24192014 -0.03298223 0.28894386 0.27569646 0.036203027 0.30784836
		 0.27028751 -0.028763175 0.23732969 0.26460865 -0.034206748 0.28454486 0.27528509 0.093623757 0.21146706
		 0.30086377 0.093623757 0.24132898 0.24789664 0.15543139 0.30341852 0.2746416 -0.031689048 0.26732188
		 0.28799745 0.15980494 0.27289918 0.20586348 0.09141314 0.21033397 0.23217803 0.15778887 0.21906623
		 0.21699153 0.15612566 0.25560495 0.25658143 -0.034071326 0.25214264 0.30158997 0.036203027 0.23175976
		 0.21845081 0.15709364 0.23445371 0.29253376 0.16124117 0.24925944 0.29909003 0.03685534 0.27263382
		 0.27758634 0.090192437 0.30738261 0.19701564 0.088637948 0.25396147 0.24817331 0.09084475 0.31828824
		 0.26086476 0.15969145 0.22187603 0.21466666 0.02922976 0.26982406 0.24671648 -0.0060905218 0.24165794
		 0.23100962 -0.009308219 0.27673763 0.27792218 -0.024613738 0.24660817 0.23207158 0.033486962 0.22923616
		 0.24937972 -0.0098212957 0.31354612 0.29349411 -0.003744483 0.23525104 0.24798664 -0.03626287 0.27272865
		 0.22580868 0.031436563 0.32016382 0.29128501 -0.0048021078 0.27216581 0.27212745 -0.0072101355 0.30256963
		 0.28352273 -0.0054477453 0.21254805 0.23379111 -0.012154937 0.31513086 0.27295765 0.15775263 0.29611507
		 0.29808247 0.093253732 0.27585074 0.28570095 0.034497857 0.20503381 0.22039968 0.15565836 0.27655074
		 0.2472132 0.03414309 0.32192093 0.2645756 -0.02703321 0.23371574 0.26457325 -0.041359305 0.25955245
		 0.25763637 -0.041724563 0.27498746 0.2235218 -0.014110923 0.31052831 0.21086822 0.027034402 0.31201118
		 0.20833518 0.089727044 0.28983316 0.24981475 -0.03490293 0.28750587 0.24312386 0.094453454 0.20072708
		 0.26361772 0.032810807 0.2031132 0.2682699 -0.006239295 0.21472621 0.25480759 -0.031042457 0.29263374
		 0.26205683 0.0072513819 0.19925573 0.26513147 -0.0030997992 0.19491348 0.26392058 0.092385888 0.18903859
		 0.27225044 0.13197672 0.179074 0.25707328 0.047345757 0.19953477 0.30407566 0.1574198 -0.13195735
		 0.30037898 0.15540659 -0.10985759 0.31638151 0.04901278 -0.11482686 0.31901616 0.054320931 -0.13919723
		 0.2911455 0.094766259 -0.11670929 0.296758 0.098273873 -0.14152187 0.37156695 0.076764703 -0.13634014
		 0.36911687 0.077808022 -0.10762742 0.39193314 0.08773005 -0.18027234 0.39297378 0.16294444 -0.18626335
		 0.39662087 0.13183558 -0.18107179 0.39137983 0.05825007 -0.19857359 0.42541876 0.15005744 -0.34152964
		 0.41186187 0.22440016 -0.35152456 0.42291871 0.10934603 -0.35627353 0.37465847 0.15594447 -0.47512674
		 0.36555254 0.23046649 -0.44797531 0.39051887 0.11646807 -0.46680427 0.40255752 0.18214953 -0.46540344
		 0.37957785 0.23980868 -0.48980868 0.38285601 0.19422877 -0.47702801 0.37512422 0.28701365 -0.50740111
		 -0.39061093 0.047709107 0.0057262182 -0.36903894 0.056335092 -0.016124874 -0.36384746 0.063512444 -0.19438747
		 -0.38306645 0.047849298 0.088163242 -0.43917662 0.10354578 -0.13060224 -0.30516136 0.052188516 -0.025743693
		 -0.33512276 0.032515168 -0.0013709664 -0.40496969 0.087621331 0.037823737 -0.099335596 0.17452967 0.38381413
		 -0.29674846 0.0062786341 0.021402419 -0.31479928 0.099145532 -0.17666784 -0.39103687 0.086358666 -0.015020847;
	setAttr ".vt[664:829]" -0.32496685 0.032872796 0.099321887 -0.28366917 0.15471041 0.070691913
		 -0.2869994 0.020541787 0.079149589 -0.32993603 0.1344372 -0.38594905 -0.41847214 0.10488188 -0.16881111
		 -0.37306914 0.087201715 -0.33778295 -0.41545904 0.15980494 -0.040100515 -0.33574724 0.093875527 -0.022986561
		 -0.25438422 0.084733605 0.054703414 -0.37545303 0.0048691034 0.038651615 -0.26293406 0.034106851 0.019595712
		 -0.38764036 0.097375512 -0.35567525 -0.36336243 0.16493475 -0.17169172 -0.31568134 0.032054543 -0.051379502
		 -0.40272295 0.14829028 -0.32740018 -0.37512919 0.16019595 0.018995017 -0.3833763 0.15868151 0.040496498
		 -0.27544802 0.088351846 -0.0062762499 -0.44780713 0.2321564 -0.38715765 -0.32016098 0.16084826 -0.15681511
		 -0.38210368 0.038732171 -0.018804759 -0.43406096 0.10645831 -0.45381919 -0.37679386 0.14656603 -0.33643505
		 -0.33788526 0.028115869 -0.018589258 -0.28122625 0.042770028 -0.014259249 -0.25926587 0.037715554 0.054682434
		 -0.38474077 0.11364329 -0.34674677 -0.32285005 0.0860039 -0.28069565 -0.30296409 0.15389407 -0.080459177
		 -0.41117603 0.11195529 -0.33295831 -0.33823296 0.033967614 -0.024887681 -0.2814177 0.0062385798 0.018746376
		 -0.43386966 0.16655314 -0.11662918 -0.32698905 0.040210366 -0.18667492 -0.015108479 0.090230584 0.42296892
		 -0.36775666 0.13937533 -0.33513188 -0.40238702 0.040170312 0.063774019 -0.10862581 0.062791467 0.32574788
		 -0.3154189 0.15302241 -0.053735167 -0.26988548 0.15424693 0.052955836 -0.34792292 0.13854754 -0.32846221
		 -0.30373991 0.089070916 -0.046824336 -0.425161 0.17839015 -0.30275792 -0.32280988 0.15684187 0.082451329
		 -0.43608558 0.10376132 -0.082781434 -0.29360995 0.012265801 0.052203625 -0.33706212 0.05813849 -0.025448352
		 -0.33850017 0.050891519 -0.026408792 -0.41967639 0.047143579 -0.090373456 -0.021484863 -0.043262839 0.40727863
		 -0.39112675 0.09109652 0.0093616247 -0.33384323 0.033525109 0.011783719 -0.38054976 0.15604556 -0.025928169
		 -0.41335446 0.16720927 -0.15167087 -0.38236168 0.15825617 0.059947193 -0.33541137 0.14848101 -0.43888348
		 -0.35938215 0.04951632 -0.23500884 -0.40172762 0.055503488 -0.17382956 -0.38794327 0.10823214 -0.27051622
		 -0.36454499 0.10668528 -0.19229165 -0.40291679 0.039090753 0.03574729 -0.35073969 0.076009393 -0.28370214
		 -0.34711158 0.16558039 -0.31347898 -0.40828836 0.087515473 -0.32565448 -0.43706465 0.074746728 -0.29871097
		 -0.37522125 0.0090662241 0.064462155 -0.30973077 0.017905831 -0.083909929 -0.37071329 0.17164195 -0.32404396
		 -0.44922164 0.14211619 -0.21662903 -0.35588175 0.075063348 -0.31390455 -0.31190664 0.027358651 -0.024875879
		 -0.36330819 0.21892703 -0.34859863 -0.43083706 0.08726275 -0.03867659 -0.33189446 0.030427575 0.053246498
		 -0.37419006 0.023852944 0.085523546 -0.44062668 0.17751849 -0.26262167 -0.44207406 0.10978854 -0.26681268
		 -0.30413485 0.15734541 -0.00042036176 -0.25554085 0.08445704 0.023074657 -0.39539093 0.17466986 -0.31805301
		 -0.38839507 0.097402215 -0.29702687 -0.33357131 0.02400744 0.030709445 -0.4629012 0.17298281 -0.43470144
		 -0.39046204 0.0099636316 0.035531998 -0.30349863 0.11967051 -0.26212031 -0.034625534 0.15991843 0.29435769
		 -0.33073786 0.031043649 0.070699155 -0.4490748 0.11606753 -0.39185286 -0.3808552 0.02147162 0.0061804056
		 -0.27787 0.011449456 0.053152084 -0.29387724 0.016957879 -0.013355166 -0.4351019 0.11122382 -0.31387639
		 -0.44786388 0.23656619 -0.4249312 -0.41590863 0.23594916 -0.44870341 -0.42642182 0.059869409 -0.21807653
		 -0.10063644 0.16468489 0.3039791 -0.41578072 0.031296372 -0.039232582 -0.3841947 0.090291619 0.085630015
		 -0.35920686 0.10376132 -0.3443417 -0.32905626 0.11750376 -0.20903927 -0.35776648 0.15998137 0.0062586665
		 -0.11891171 0.040673852 0.40632069 -0.36667836 0.085598588 -0.39418688 -0.3185786 0.02400744 -0.016086817
		 -0.42981568 0.081426263 -0.26721454 -0.31570497 0.15450823 -0.25954437 -0.34003043 0.1534096 -0.034745634
		 -0.39031073 0.012520432 0.063073605 -0.014538473 0.059087396 0.32090861 -0.30477113 0.01584971 -0.005505681
		 -0.11416496 0.094453454 0.40389907 -0.3889367 0.094403863 -0.3190867 -0.30159459 0.091566682 -0.016245782
		 -0.012345998 0.040385842 0.42651427 -0.33021039 0.2241112 -0.42394987 -0.011075921 0.023681283 0.35674295
		 -0.46074682 0.16554415 -0.38762328 -0.38826254 0.09448874 -0.34175676 -0.33143312 0.15879786 0.00017097592
		 -0.36845216 0.019052148 0.0091963112 -0.3326773 0.08747828 -0.31529763 -0.36170465 0.021548867 0.081642464
		 -0.031924568 0.17286646 0.39379737 -0.44687295 0.14767516 -0.26451105 -0.44992164 0.1236788 -0.43139043
		 -0.43176916 0.1481787 -0.3105841 -0.28864095 0.085851312 -0.080364227 -0.32537609 0.053950906 0.098671466
		 -0.43407282 0.17286646 -0.22106639 -0.2706871 0.15490115 0.028382212 -0.4409931 0.093984246 -0.2086989
		 -0.43514639 0.16358912 -0.077464521 -0.30017567 0.019668221 0.076375216 -0.27617896 0.046273828 0.080280021
		 -0.42413461 0.028218865 -0.14076033 -0.28466752 0.15612566 0.0070865452 -0.36791319 0.15887225 0.075426787
		 -0.32691103 0.026376367 0.092465311 -0.31608832 0.12761748 -0.30397338 -0.3378 0.065643907 -0.2241078
		 -0.32871807 0.21978819 -0.38532442 -0.33581108 0.15385401 -0.21218252 -0.3644338 0.033273339 -0.015278012
		 -0.27409285 0.087403893 0.077806324 -0.32478237 0.090256333 0.09398149 -0.36733603 0.093875527 -0.0086516142
		 -0.3349123 0.093682885 -0.016290396 -0.36762699 0.11411059 -0.43754956 -0.10717602 -0.040712714 0.39620036
		 -0.32655627 0.15998137 -0.29504496 -0.43473762 0.16276896 -0.47445545 -0.10902315 0.026930451 0.34850502
		 -0.35618889 0.028977036 -0.0088596642 -0.40462211 0.087957025 0.063473374 -0.41342765 0.11459124 -0.38909456
		 -0.3613238 0.072069764 -0.070149362 -0.35177344 0.051606774 -0.047243029 -0.36776638 0.064998269 -0.16616416
		 -0.40806133 0.12995684 -0.41571644 -0.23983645 -0.0040458441 0.10235859 -0.32426211 0.13587248 0.19643661
		 -0.24864635 0.12916529 0.15595721 -0.24187505 0.093648553 0.1585616 -0.34647554 0.09697783 0.13368492
		 -0.28570798 -0.0052713156 0.20427912 -0.32542336 -0.0028480291 0.1201327;
	setAttr ".vt[830:995]" -0.21517737 0.047672868 0.13033344 -0.22917655 0.046702027 0.16782324
		 -0.3055349 0.0052295923 0.096365377 -0.28979269 0.052111268 0.22085413 -0.24909571 -0.0055602789 0.17268349
		 -0.31118998 0.015557885 0.094653144 -0.34027398 0.0010486841 0.12486301 -0.22754219 0.099065423 0.12745745
		 -0.34899437 0.10488188 0.17897959 -0.32228956 0.055910707 0.22464308 -0.28915891 0.0071102381 0.21800447
		 -0.31926459 0.10462916 0.21152848 -0.26771164 0.0074402094 0.085223377 -0.29864061 0.13429511 0.19338539
		 -0.28957981 0.098023057 0.20948526 -0.27256966 0.12631381 0.097053751 -0.31686151 0.050021768 0.099508658
		 -0.30691618 -0.015118003 0.19828621 -0.27338338 -0.0013259649 0.092373177 -0.24137115 0.097845674 0.10097526
		 -0.33890694 -0.013021827 0.17358449 -0.27029684 0.050424218 0.084362373 -0.23499972 0.12670577 0.12824762
		 -0.23658428 0.048318505 0.09399803 -0.34992856 0.044650674 0.1304113 -0.34474644 0.13552153 0.17355858
		 -0.24596205 0.12540591 0.10430041 -0.2388712 0.0014778376 0.17122915 -0.27130911 0.09333384 0.092950389
		 -0.22708106 -0.0069955587 0.13899238 -0.31123972 0.12898886 0.10874684 -0.34036398 0.13226855 0.13384353
		 -0.31482989 0.091264367 0.106086 -0.35127908 0.044507623 0.18085742 -0.3234556 -0.020353675 0.16270755
		 -0.3151705 0.0015865564 0.21834478 -0.24014843 -0.013883948 0.14544417 -0.25008667 -0.014246345 0.11214337
		 -0.27024469 0.0069977045 0.17840254 -0.27480251 0.0094944239 0.16428968 -0.28568912 0.0030761957 0.1376961
		 -0.29607916 0.010833383 0.11416306 -0.019230902 -0.041181922 0.38584366 -0.10636719 -0.03973043 0.37983617
		 -0.38760746 0.084418893 -0.23463246 -0.4039197 0.12140048 -0.43977043 -0.068087474 0.17546809 0.40419704
		 -0.068735465 -0.043489814 0.40646759 -0.067791767 0.042770028 0.42864063 -0.067269102 0.097653985 0.42866194
		 -0.012792997 -0.028511405 0.41221017 -0.012539932 0.0020653009 0.42058492 -0.11133623 -0.0026992559 0.36297491
		 -0.11356418 -0.02810514 0.37328932 -0.11513471 -0.027600646 0.39799562 -0.11870128 0.0014511347 0.40308067
		 -0.011409402 -0.027963996 0.37820414 -0.011336081 -0.0026773214 0.36824462 -0.068293221 0.0033279657 0.42231387
		 -0.068593621 -0.026703238 0.41349906 -0.061860055 0.058215737 0.30766174 -0.060398392 0.024068475 0.34114966
		 -0.06810873 0.16038668 0.28639677 -0.063243628 -0.041433692 0.38241163 -0.061985433 -0.002951026 0.35916236
		 -0.062711477 -0.027563453 0.37227935 -0.11292559 -0.043914199 0.38538221 -0.11026248 0.1708523 0.35438257
		 -0.012589606 -0.047521949 0.39811143 -0.12537572 -0.00093114376 0.3830834 -0.12348597 -0.028934836 0.38330105
		 -0.12337722 0.030092835 0.37911463 -0.0049856994 0.075147271 0.37793455 -0.0018660889 0.033603311 0.40466061
		 -0.067474842 -0.051645637 0.39402908 -0.11859481 0.077556252 0.37027615 -0.02102603 0.16933978 0.35161778
		 -0.0018873763 -0.00093114376 0.40656668 -0.0019417743 -0.030740142 0.4013091 -0.20099613 0.144611 0.3492786
		 -0.13615128 0.083699822 0.39709452 -0.19150254 0.055291772 0.28504148 -0.14586954 -0.033920646 0.38156724
		 -0.12682784 0.13248122 0.29607508 -0.18110295 0.13659441 0.28623813 -0.22105476 0.042770028 0.34187809
		 -0.11993827 0.052363992 0.30901438 -0.21307242 0.087706208 0.34507582 -0.13822782 0.04128325 0.39939585
		 -0.12395903 0.022950768 0.33867082 -0.15091681 0.14348757 0.374964 -0.21167232 -0.027460456 0.33697984
		 -0.19947529 0.026845574 0.29836681 -0.13766259 -0.032469153 0.36457381 -0.2062372 -0.027172446 0.32385093
		 -0.18193555 0.14548266 0.37356839 -0.18500543 -0.033338904 0.35885742 -0.18831651 0.043777108 0.3817803
		 -0.18494166 0.09059298 0.38388512 -0.13973211 -0.021145225 0.38971302 -0.13979596 0.0048691034 0.39430353
		 -0.20630828 0.0036064386 0.30792883 -0.2099576 -0.016746879 0.31619498 -0.21824259 -0.015234351 0.3365353
		 -0.22245485 0.0097471476 0.34038097 -0.128694 -0.02122438 0.3617405 -0.12553889 -0.00040280819 0.35099575
		 -0.18861234 0.0078655481 0.37431344 -0.18686207 -0.018545508 0.36544672 -0.15173988 0.046664834 0.27841693
		 -0.15944543 0.018876672 0.30529177 -0.15094516 0.13292468 0.28089082 -0.17330271 -0.03230226 0.34108341
		 -0.16557589 -0.0021680593 0.32163244 -0.16989458 -0.021067023 0.33333036 -0.21273439 -0.029808402 0.32624456
		 -0.20186651 0.1415478 0.32329527 -0.13524298 -0.037824988 0.37817594 -0.22124624 0.0077911615 0.31915841
		 -0.21960022 -0.016205192 0.3214573 -0.21747164 0.038481355 0.31433848 -0.11965923 0.069686532 0.36163411
		 -0.12456211 0.03464663 0.38428485 -0.18020904 -0.04073751 0.34814104 -0.2084842 0.072321534 0.31597969
		 -0.13123414 0.14046443 0.34464061 -0.12678298 0.0019565821 0.38708508 -0.12688459 -0.023352981 0.38584831
		 -0.076857373 0.2468468 0.40418744 -0.049192395 0.18575633 0.42209387 -0.071814917 0.16838992 0.42082855
		 -0.099756569 0.18226016 0.40840214 -0.070726953 0.12883627 0.42722628 -0.11002599 0.12352812 0.41415656
		 -0.036210183 0.1197468 0.43183348 -0.036874775 0.091062188 0.42220506 -0.070350878 0.10527384 0.4193196
		 -0.10216904 0.098134637 0.41001052 -0.17384431 0.24819338 0.36337695 -0.15370762 0.17984164 0.3904579
		 -0.18166591 0.16272891 0.38024065 -0.2013225 0.18127787 0.35390469 -0.14994475 0.1214844 0.39989009
		 -0.18400493 0.1285454 0.38524985 -0.21068376 0.1242615 0.35187539 -0.15557377 0.096141458 0.39002502
		 -0.18232577 0.10808337 0.37872672 -0.20382465 0.095889688 0.35747617 -0.1253686 0.099532723 0.40587369
		 -0.13590053 0.098854661 0.40208018 -0.13432768 0.075616479 0.39281365 -0.12159623 0.075902581 0.39712518
		 -0.26511249 0.25900996 0.28657413 -0.24748048 0.19203627 0.31474751 -0.2789602 0.18040621 0.29831001
		 -0.29665619 0.19445574 0.27067325 -0.24885446 0.1330353 0.32102221 -0.28415173 0.14814341 0.30279908
		 -0.30472136 0.12468684 0.26844785 -0.25932965 0.10942996 0.31034604 -0.28119779 0.12804186 0.29639179
		 -0.29490596 0.10618174 0.26913849 -0.22447935 0.088019967 0.33602676 -0.23439655 0.089978814 0.3273609
		 -0.21944416 0.055970788 0.33109069 -0.23038046 0.057423234 0.31974754;
	setAttr ".vt[996:1161]" -0.33748788 0.27023375 0.1847813 -0.32486525 0.19935954 0.21709612
		 -0.34461883 0.18034518 0.1861956 -0.36230275 0.19639552 0.15119648 -0.32225412 0.13031542 0.22210994
		 -0.3459883 0.13782847 0.18868145 -0.36036345 0.13031542 0.14520796 -0.31869459 0.10715258 0.20658055
		 -0.33644012 0.11753905 0.18262447 -0.34447461 0.1051966 0.14899917 -0.30602688 0.095754266 0.241263
		 -0.30976379 0.09759295 0.22929522 -0.30080709 0.075426698 0.22005489 -0.2967366 0.074452996 0.23305371
		 -0.40525848 0.26850855 0.07008639 -0.3919664 0.19372332 0.10344668 -0.40948972 0.17507517 0.071983367
		 -0.42648315 0.19459116 0.032291502 -0.38240916 0.12793124 0.10177682 -0.40354854 0.13393366 0.069611043
		 -0.4105351 0.12916529 0.02748087 -0.37285393 0.10371935 0.086325586 -0.38738972 0.11368334 0.063364863
		 -0.39003879 0.10603869 0.033162087 -0.35516006 0.095386147 0.12126331 -0.3605763 0.094514489 0.11006896
		 -0.34360173 0.070382714 0.10225706 -0.33889279 0.071101785 0.11419608 -0.45604515 0.28852999 -0.065069169
		 -0.42740551 0.13734972 -0.032444566 -0.44124383 0.14536631 -0.072273463 -0.44628638 0.15254366 -0.11565232
		 -0.41331181 0.12170184 -0.044565767 -0.42723763 0.13515627 -0.075733423 -0.42911327 0.135306 -0.11324933
		 -0.39847988 0.10148966 0.0048937798 -0.40326947 0.1045891 -0.010796189 -0.38293892 0.089789033 0.0012561381
		 -0.38937655 0.093732476 -0.018549472 -0.47322971 0.29951251 -0.21757296 -0.45049652 0.16129076 -0.19638819
		 -0.451875 0.16319621 -0.23849934 -0.4528873 0.16933978 -0.27961248 -0.43431652 0.14511454 -0.20920014
		 -0.43793029 0.15471041 -0.24266672 -0.43587047 0.15456927 -0.27859563 -0.43733668 0.13761103 -0.15938333
		 -0.43796116 0.13973391 -0.17709342 -0.42193711 0.12021601 -0.18373981 -0.42210972 0.11859667 -0.16490385
		 -0.47171137 0.31282675 -0.34965149 -0.46256769 0.18065798 -0.35087189 -0.46703327 0.18893397 -0.38862178
		 -0.46737415 0.19856894 -0.42022917 -0.44368464 0.16232455 -0.36037967 -0.45125049 0.17842638 -0.39193079
		 -0.44923109 0.18492091 -0.43016061 -0.42838717 0.13720858 -0.32283086 -0.44457412 0.15503657 -0.31726566
		 -0.44739094 0.15724719 -0.33367738 -0.43058199 0.13897669 -0.3405315 -0.050483793 0.10505641 0.25474426
		 -0.090932287 0.065137506 0.29207772 -0.064728923 0.062286973 0.29686716 -0.040848199 0.070773721 0.2881588
		 -0.09160395 0.053447366 0.31919879 -0.06589023 0.05670321 0.31747451 -0.037877619 0.05251658 0.31989157
		 -0.12921663 0.11354315 0.23482528 -0.17064428 0.073373437 0.26115116 -0.14631191 0.062286973 0.26951918
		 -0.12632176 0.06665194 0.28218672 -0.17141071 0.054202676 0.27758175 -0.14928719 0.056007981 0.28446436
		 -0.13058135 0.052943826 0.29674169 -0.11412719 0.055211663 0.29944047 -0.1053123 0.054936051 0.3026332
		 -0.10865416 0.044280648 0.32530546 -0.11984368 0.044675469 0.31960812 -0.17957515 0.13536894 0.18696669
		 -0.21111178 0.10451663 0.18813483 -0.20608824 0.093757272 0.21150708 -0.19360986 0.08643496 0.23567894
		 -0.23069033 0.084203362 0.20397428 -0.21795405 0.087453485 0.22127536 -0.21009937 0.075315118 0.24339405
		 -0.19029394 0.066762567 0.26120344 -0.18472625 0.063344598 0.26807389 -0.19860259 0.051038384 0.27057651
		 -0.19062746 0.046526551 0.28026393 -0.21027689 0.1578995 0.10540031 -0.23536623 0.11530936 0.088622123
		 -0.22441563 0.10365546 0.11573584 -0.22121558 0.10909426 0.14607121 -0.23608993 0.08234942 0.096892938
		 -0.2271615 0.084313989 0.12011138 -0.22714019 0.080052972 0.14885733 -0.23273855 0.083219171 0.17559958
		 -0.23210472 0.082688928 0.18571764 -0.2469601 0.068467736 0.18171851 -0.24671163 0.068893075 0.19349447
		 -0.23544666 0.18298113 0.016118914 -0.26178226 0.14211619 -0.003249377 -0.25177318 0.12606204 0.023888469
		 -0.24672133 0.12627757 0.050486565 -0.26948553 0.1063534 0.0013461113 -0.25827971 0.10365546 0.024446577
		 -0.25424954 0.094090104 0.045974076 -0.24912883 0.093732476 0.06954965 -0.24651782 0.09166491 0.077886626
		 -0.26037279 0.078958154 0.072399676 -0.2572366 0.076684594 0.082983449 -0.25217286 0.21048129 -0.11141124
		 -0.2794736 0.16384184 -0.1441097 -0.27494675 0.14662707 -0.1067616 -0.27150792 0.15081465 -0.066930413
		 -0.28736365 0.12302554 -0.10618708 -0.28695688 0.11520541 -0.074037462 -0.29432666 0.12685931 -0.13970578
		 -0.28150755 0.12504351 -0.043690622 -0.27896506 0.12274706 -0.026732534 -0.29594439 0.10234511 -0.022681296
		 -0.30009526 0.1047684 -0.043274522 -0.25951895 0.24587309 -0.24671367 -0.29187396 0.19881976 -0.29343447
		 -0.28656903 0.17670214 -0.25511479 -0.28445452 0.18080866 -0.21061248 -0.30894312 0.14862216 -0.29911333
		 -0.30039322 0.14215147 -0.26416141 -0.30312014 0.13248122 -0.22998253 -0.30114299 0.13901389 -0.19712612
		 -0.29926747 0.13540804 -0.18174806 -0.32108098 0.11996233 -0.20030957 -0.31824273 0.11725008 -0.18230399
		 -0.27290803 0.27504122 -0.38881317 -0.30979693 0.24746096 -0.44544646 -0.30361211 0.21357119 -0.40588513
		 -0.30098683 0.2128998 -0.36500573 -0.32510406 0.18856394 -0.44476721 -0.31593925 0.18369639 -0.40483013
		 -0.32611161 0.17485774 -0.37113371 -0.32165569 0.17813838 -0.34102085 -0.31928104 0.17431891 -0.3243185
		 -0.33655143 0.15829432 -0.34398913 -0.3349123 0.15344965 -0.32525712 -0.44397828 0.200544 -0.46149418
		 -0.44381464 0.24718249 -0.45451918 -0.43593448 0.1862179 -0.45895591 -0.037522838 0.065538049 0.37482423
		 -0.068025939 0.076219201 0.36976302 -0.095690906 0.070093751 0.3668206 -0.11460723 0.055250764 0.36482686
		 -0.12701704 0.056623101 0.35936102 -0.14482658 0.074452996 0.34751177 -0.16728818 0.082460046 0.33619228
		 -0.18783182 0.070345521 0.32181457 -0.20541196 0.04280436 0.30861232 -0.21700794 0.045984864 0.29624498
		 -0.23819029 0.085069299 0.27564257 -0.29019487 0.097266793 0.15446229 -0.29676503 0.087201715 0.12856214
		 -0.29978758 0.066842675 0.099049911 -0.30329764 0.068505883 0.086982861 -0.31919363 0.093757272 0.063769192
		 -0.32761586 0.10447657 0.044486403 -0.33354056 0.10048068 0.019891143;
	setAttr ".vt[1162:1290]" -0.34183028 0.089498162 -0.01072526 -0.34767932 0.092674851 -0.031020612
		 -0.35691023 0.11389601 -0.06179595 -0.36376202 0.12753642 -0.090761632 -0.36746353 0.12844431 -0.12353542
		 -0.37210855 0.11201632 -0.17351764 -0.37424213 0.1130749 -0.19279081 -0.3770898 0.13619959 -0.21910802
		 -0.37782767 0.14796793 -0.25116938 -0.37997043 0.14808047 -0.2857362 -0.3826952 0.13533175 -0.32395878
		 -0.38411421 0.13782847 -0.34394881 -0.3875742 0.15908396 -0.36838791 -0.38916367 0.17250216 -0.39976144
		 -0.39139619 0.17755663 -0.4375996 -0.28215331 0.089358926 0.17860612 -0.27676788 0.064127564 0.20181242
		 -0.27326989 0.064127564 0.21367124 -0.26229089 0.088889718 0.23783353 -0.25037771 0.099170327 0.26134071
		 -0.021747403 0.26503718 0.41384441 -0.015848758 0.19643557 0.42072669 -0.013795822 0.10740435 0.42666572
		 -0.014588146 0.069477677 0.41686222 -0.013043709 0.046197534 0.38094988 -0.013578227 0.042014718 0.32852912
		 -0.015839295 0.068137765 0.29163057 -0.018098 0.094801545 0.2666572 -0.44866338 0.32559741 -0.44825414
		 -0.083874747 0.16398776 0.19418028 -0.11385986 0.20303881 0.1453307 -0.15801457 0.22313654 0.089830711
		 -0.16110343 0.2618233 0.0096335411 -0.17635144 0.29416621 -0.13741592 -0.2014904 0.30441821 -0.26125222
		 -0.19833769 0.32284987 -0.3922219 -0.23963766 0.31804812 -0.50949132 -0.47036558 0.41480982 -0.39565989
		 -0.47779459 0.45717394 -0.32402036 -0.47863692 0.46587241 -0.20203406 -0.45750654 0.46627772 -0.060173124
		 -0.40132993 0.45858347 0.068832934 -0.32998097 0.44966853 0.18198337 -0.25234789 0.44455206 0.27694339
		 -0.16140135 0.4384495 0.34819761 -0.077011101 0.43097079 0.38854218 -0.024710912 0.42567408 0.40404543
		 -0.46444598 0.20838702 -0.22812614 -0.46727461 0.22974741 -0.37183872 -0.45246887 0.19837725 -0.073157936
		 -0.22831324 0.15627444 0.29288682 -0.22501892 0.091626763 0.30743688 -0.24192014 -0.03298223 0.28894386
		 -0.27569646 0.036203027 0.30784836 -0.27028751 -0.028763175 0.23732969 -0.26460865 -0.034206748 0.28454486
		 -0.27528509 0.093623757 0.21146706 -0.30086377 0.093623757 0.24132898 -0.24789664 0.15543139 0.30341852
		 -0.2746416 -0.031689048 0.26732188 -0.28799745 0.15980494 0.27289918 -0.20586348 0.09141314 0.21033397
		 -0.23217803 0.15778887 0.21906623 -0.21699153 0.15612566 0.25560495 -0.25658143 -0.034071326 0.25214264
		 -0.30158997 0.036203027 0.23175976 -0.21845081 0.15709364 0.23445371 -0.29253376 0.16124117 0.24925944
		 -0.29909003 0.03685534 0.27263382 -0.27758634 0.090192437 0.30738261 -0.19701564 0.088637948 0.25396147
		 -0.24817331 0.09084475 0.31828824 -0.26086476 0.15969145 0.22187603 -0.21466666 0.02922976 0.26982406
		 -0.24671648 -0.0060905218 0.24165794 -0.23100962 -0.009308219 0.27673763 -0.27792218 -0.024613738 0.24660817
		 -0.23207158 0.033486962 0.22923616 -0.24937972 -0.0098212957 0.31354612 -0.29349411 -0.003744483 0.23525104
		 -0.24798664 -0.03626287 0.27272865 -0.22580868 0.031436563 0.32016149 -0.29128501 -0.0048021078 0.27216581
		 -0.27212745 -0.0072101355 0.30256963 -0.28352273 -0.0054477453 0.21254805 -0.23379111 -0.012154937 0.31513086
		 -0.27295765 0.15775263 0.29611507 -0.29808247 0.093253732 0.27585074 -0.28570095 0.034497857 0.20503381
		 -0.22039968 0.15565836 0.27655074 -0.2472132 0.03414309 0.32192093 -0.2645756 -0.02703321 0.23371574
		 -0.26457325 -0.041384101 0.25955245 -0.25763637 -0.041724563 0.27498746 -0.2235218 -0.014110923 0.31052831
		 -0.21086822 0.027034402 0.31201118 -0.20833518 0.089727044 0.28983316 -0.24981475 -0.03490293 0.28750587
		 -0.24312386 0.094453454 0.20072708 -0.26361772 0.032810807 0.2031132 -0.2682699 -0.006239295 0.21472621
		 -0.25480759 -0.031042457 0.29263374 -0.26205683 0.0072513819 0.19925573 -0.26513147 -0.0030997992 0.19491348
		 -0.26392058 0.092385888 0.18903859 -0.27225044 0.13197672 0.179074 -0.25707328 0.047345757 0.19953477
		 -0.30407566 0.1574198 -0.13195735 -0.30037898 0.15540659 -0.10985759 -0.31638151 0.04901278 -0.11482686
		 -0.31901616 0.054320931 -0.13919723 -0.2911455 0.094766259 -0.11670929 -0.296758 0.098273873 -0.14152187
		 -0.37156695 0.076764703 -0.13634014 -0.36911687 0.077808022 -0.10762742 -0.39193314 0.08773005 -0.18027234
		 -0.39297378 0.16294444 -0.18626335 -0.39662087 0.13183558 -0.18107179 -0.39137983 0.05825007 -0.19857359
		 -0.42541876 0.15005744 -0.34152964 -0.41186187 0.22440016 -0.35152456 -0.42291871 0.10934603 -0.35627353
		 -0.37465847 0.15594447 -0.47512674 -0.36555254 0.23046649 -0.44797531 -0.39051887 0.11646807 -0.46680427
		 -0.40255752 0.18214953 -0.46540344 -0.37957785 0.23980868 -0.48980868 -0.38285601 0.19422877 -0.47702801
		 -0.37512422 0.28701365 -0.50740111;
	setAttr -s 2456 ".ed";
	setAttr ".ed[0:165]"  309 306 1 306 307 1 307 308 1 308 309 1 308 310 1 310 311 1
		 311 309 1 307 312 1 312 310 1 314 310 1 312 313 1 313 314 1 314 315 1 315 311 1 319 316 1
		 316 317 1 317 318 1 318 319 1 317 320 1 320 321 1 321 318 1 322 319 1 321 322 1 320 323 1
		 323 324 1 324 321 1 325 322 1 324 325 1 317 309 1 311 320 1 316 306 1 311 326 1 326 327 1
		 327 320 1 327 328 1 328 323 1 329 328 1 326 329 1 315 329 1 333 330 1 330 331 1 331 332 1
		 332 333 1 335 332 1 331 334 1 334 335 1 335 336 1 336 333 1 338 335 1 334 337 1 337 338 1
		 338 339 1 339 336 1 322 334 1 331 319 1 330 316 1 322 340 1 340 341 1 341 334 1 340 342 1
		 342 343 1 343 341 1 343 337 1 325 342 1 347 344 1 344 345 1 345 346 1 346 347 1 349 346 1
		 345 348 1 348 349 1 349 350 1 350 347 1 352 349 1 348 351 1 351 352 1 353 350 1 352 353 1
		 348 336 1 336 354 1 354 355 1 355 348 1 355 356 1 356 351 1 357 354 1 339 357 1 357 356 1
		 345 333 1 344 330 1 361 358 1 358 359 1 359 360 1 360 361 1 363 360 1 359 362 1 362 363 1
		 364 361 1 363 364 1 366 363 1 362 365 1 365 366 1 367 364 1 366 367 1 359 347 1 350 362 1
		 358 344 1 350 368 1 368 369 1 369 362 1 369 370 1 370 365 1 371 368 1 353 371 1 371 370 1
		 377 374 1 374 373 1 373 376 1 376 377 1 378 375 1 375 374 1 377 378 1 380 373 1 373 364 1
		 364 379 1 379 380 1 381 379 1 367 381 1 380 382 1 382 376 1 381 382 1 388 385 1 385 384 1
		 384 387 1 387 388 1 389 386 1 386 385 1 388 389 1 384 375 1 375 390 1 390 391 1 391 384 1
		 392 387 1 391 392 1 393 390 1 378 393 1 393 392 1 399 396 1 396 395 1 395 398 1 398 399 1
		 399 400 1 400 397 1 397 396 1 389 401 1 401 402 1 402 386 1 402 403 1 403 395 1 395 386 1
		 404 398 1 403 404 1 401 404 1 408 405 1 405 406 1 406 407 1 407 408 1;
	setAttr ".ed[166:331]" 410 407 1 406 409 1 409 410 1 411 408 1 410 411 1 415 412 1
		 412 413 1 413 414 1 414 415 1 413 416 1 416 417 1 417 414 1 418 415 1 417 418 1 415 406 1
		 405 412 1 415 419 1 419 420 1 420 406 1 421 409 1 420 421 1 419 422 1 422 421 1 418 422 1
		 426 423 1 423 424 1 424 425 1 425 426 1 424 427 1 427 428 1 428 425 1 428 429 1 429 426 1
		 412 423 1 426 413 1 426 430 1 430 431 1 431 413 1 433 431 1 430 432 1 432 433 1 433 416 1
		 429 432 1 437 434 1 434 435 1 435 436 1 436 437 1 435 438 1 438 439 1 439 436 1 439 440 1
		 440 437 1 437 424 1 423 434 1 437 441 1 441 442 1 442 424 1 444 442 1 441 443 1 443 444 1
		 440 443 1 444 427 1 448 445 1 445 446 1 446 447 1 447 448 1 450 447 1 446 449 1 449 450 1
		 450 451 1 451 448 1 448 435 1 434 445 1 448 452 1 452 453 1 453 435 1 451 454 1 454 452 1
		 455 453 1 454 455 1 455 438 1 459 456 1 456 457 1 457 458 1 458 459 1 458 460 1 460 461 1
		 461 459 1 457 462 1 462 460 1 459 446 1 445 456 1 459 463 1 463 464 1 464 446 1 465 449 1
		 464 465 1 466 463 1 461 466 1 466 465 1 470 467 1 467 468 1 468 469 1 469 470 1 468 471 1
		 471 472 1 472 469 1 473 470 1 472 473 1 470 457 1 456 467 1 475 457 1 470 474 1 474 475 1
		 474 476 1 476 477 1 477 475 1 477 462 1 473 476 1 481 478 1 478 479 1 479 480 1 480 481 1
		 479 482 1 482 483 1 483 480 1 483 484 1 484 481 1 481 468 1 467 478 1 481 485 1 485 486 1
		 486 468 1 488 486 1 485 487 1 487 488 1 484 487 1 488 471 1 492 493 1 493 314 1 313 492 1
		 494 493 1 493 410 1 409 494 1 495 494 1 421 495 1 496 495 1 422 496 1 497 496 1 418 497 1
		 498 497 1 417 498 1 499 498 1 416 499 1 500 499 1 433 500 1 501 500 1 432 501 1 527 526 1
		 526 356 1 357 527 1 528 527 1 339 528 1 529 528 1 338 529 1 502 501 1;
	setAttr ".ed[332:497]" 429 502 1 526 525 1 525 351 1 525 503 1 503 352 1 503 504 1
		 504 353 1 505 504 1 504 438 1 455 505 1 506 505 1 454 506 1 507 506 1 451 507 1 508 507 1
		 450 508 1 509 508 1 449 509 1 510 509 1 465 510 1 511 510 1 466 511 1 512 511 1 461 512 1
		 513 512 1 460 513 1 514 513 1 462 514 1 515 514 1 477 515 1 516 515 1 476 516 1 517 516 1
		 473 517 1 518 517 1 472 518 1 519 518 1 471 519 1 520 519 1 488 520 1 521 520 1 487 521 1
		 522 521 1 484 522 1 523 522 1 483 523 1 524 523 1 482 524 1 491 489 1 489 397 1 400 491 1
		 492 411 1 494 315 1 495 329 1 496 328 1 497 323 1 498 324 1 499 325 1 500 342 1 501 343 1
		 502 337 1 503 439 1 505 371 1 506 370 1 507 365 1 508 366 1 509 367 1 510 381 1 511 382 1
		 512 376 1 513 377 1 514 378 1 515 393 1 516 392 1 517 387 1 518 388 1 519 389 1 520 401 1
		 521 404 1 522 398 1 523 399 1 524 400 1 525 440 1 526 443 1 527 444 1 528 427 1 529 428 1
		 502 529 1 540 541 1 541 313 1 312 540 1 539 540 1 307 539 1 538 539 1 306 538 1 541 542 1
		 542 492 1 543 544 1 544 408 1 411 543 1 544 545 1 545 405 1 542 543 1 538 533 1 533 532 1
		 532 539 1 532 530 1 530 540 1 530 531 1 531 541 1 531 534 1 534 542 1 543 535 1 535 536 1
		 536 544 1 536 537 1 537 545 1 537 547 1 547 405 1 547 548 1 548 412 1 548 549 1 549 423 1
		 549 550 1 550 434 1 550 551 1 551 445 1 551 552 1 552 456 1 552 553 1 553 467 1 553 554 1
		 554 478 1 554 555 1 555 479 1 558 394 1 394 546 1 546 557 0 557 558 0 383 394 1 558 559 0
		 559 383 1 560 372 1 372 383 1 559 560 0 358 372 1 560 561 0 561 358 1 561 562 0 562 344 1
		 562 563 0 563 330 1 563 564 0 564 316 1 564 565 0 565 306 1 565 566 0 566 538 1 566 567 0
		 567 533 1 554 568 1 568 556 1 556 555 0 552 568 1 547 569 1 569 549 1;
	setAttr ".ed[498:663]" 551 569 1 569 568 1 373 572 1 572 361 1 572 372 1 384 570 1
		 570 572 1 572 375 1 570 383 1 395 571 1 571 570 1 570 386 1 571 394 1 490 571 1 571 397 1
		 489 490 1 490 546 1 534 535 1 649 490 1 489 650 1 650 649 1 648 650 1 491 648 1 479 649 1
		 650 482 1 524 648 1 555 651 0 651 649 1 651 546 0 961 960 1 960 959 1 959 958 1 958 961 1
		 961 963 1 963 962 1 962 960 1 962 964 1 964 959 1 966 965 1 965 964 1 962 966 1 963 967 1
		 967 966 1 971 970 1 970 969 1 969 968 1 968 971 1 970 973 1 973 972 1 972 969 1 974 973 1
		 971 974 1 973 976 1 976 975 1 975 972 1 977 976 1 974 977 1 972 963 1 961 969 1 958 968 1
		 972 979 1 979 978 1 978 963 1 975 980 1 980 979 1 981 978 1 980 981 1 981 967 1 985 984 1
		 984 983 1 983 982 1 982 985 1 987 986 1 986 983 1 984 987 1 985 988 1 988 987 1 990 989 1
		 989 986 1 987 990 1 988 991 1 991 990 1 971 983 1 986 974 1 968 982 1 986 993 1 993 992 1
		 992 974 1 993 995 1 995 994 1 994 992 1 989 995 1 994 977 1 999 998 1 998 997 1 997 996 1
		 996 999 1 1001 1000 1 1000 997 1 998 1001 1 999 1002 1 1002 1001 1 1004 1003 1 1003 1000 1
		 1001 1004 1 1005 1004 1 1002 1005 1 1000 1007 1 1007 1006 1 1006 988 1 988 1000 1
		 1003 1008 1 1008 1007 1 1009 991 1 1006 1009 1 1008 1009 1 985 997 1 982 996 1 1013 1012 1
		 1012 1011 1 1011 1010 1 1010 1013 1 1015 1014 1 1014 1011 1 1012 1015 1 1016 1015 1
		 1013 1016 1 1018 1017 1 1017 1014 1 1015 1018 1 1019 1018 1 1016 1019 1 1014 1002 1
		 999 1011 1 996 1010 1 1014 1021 1 1021 1020 1 1020 1002 1 1017 1022 1 1022 1021 1
		 1023 1005 1 1020 1023 1 1022 1023 1 1029 1028 1 1028 1025 1 1025 1026 1 1026 1029 1
		 1030 1029 1 1026 1027 1 1027 1030 1 1032 1031 1 1031 1016 1 1016 1025 1 1025 1032 1
		 1033 1019 1 1031 1033 1 1028 1034 1 1034 1032 1 1034 1033 1 1040 1039 1 1039 1036 1
		 1036 1037 1 1037 1040 1 1041 1040 1 1037 1038 1 1038 1041 1;
	setAttr ".ed[664:829]" 1036 1043 1 1043 1042 1 1042 1027 1 1027 1036 1 1044 1043 1
		 1039 1044 1 1045 1030 1 1042 1045 1 1044 1045 1 1051 1050 1 1050 1047 1 1047 1048 1
		 1048 1051 1 1048 1049 1 1049 1052 1 1052 1051 1 1038 1054 1 1054 1053 1 1053 1041 1
		 1038 1047 1 1047 1055 1 1055 1054 1 1056 1055 1 1050 1056 1 1056 1053 1 1060 1059 1
		 1059 1058 1 1058 1057 1 1057 1060 1 1062 1061 1 1061 1058 1 1059 1062 1 1063 1062 1
		 1060 1063 1 1067 1066 1 1066 1065 1 1065 1064 1 1064 1067 1 1066 1069 1 1069 1068 1
		 1068 1065 1 1070 1069 1 1067 1070 1 1064 1057 1 1058 1067 1 1058 1072 1 1072 1071 1
		 1071 1067 1 1073 1072 1 1061 1073 1 1073 1074 1 1074 1071 1 1074 1070 1 1078 1077 1
		 1077 1076 1 1076 1075 1 1075 1078 1 1077 1080 1 1080 1079 1 1079 1076 1 1078 1081 1
		 1081 1080 1 1065 1078 1 1075 1064 1 1065 1083 1 1083 1082 1 1082 1078 1 1085 1084 1
		 1084 1082 1 1083 1085 1 1068 1085 1 1084 1081 1 1089 1088 1 1088 1087 1 1087 1086 1
		 1086 1089 1 1088 1091 1 1091 1090 1 1090 1087 1 1089 1092 1 1092 1091 1 1086 1075 1
		 1076 1089 1 1076 1094 1 1094 1093 1 1093 1089 1 1096 1095 1 1095 1093 1 1094 1096 1
		 1095 1092 1 1079 1096 1 1100 1099 1 1099 1098 1 1098 1097 1 1097 1100 1 1102 1101 1
		 1101 1098 1 1099 1102 1 1100 1103 1 1103 1102 1 1097 1086 1 1087 1100 1 1087 1105 1
		 1105 1104 1 1104 1100 1 1104 1106 1 1106 1103 1 1107 1106 1 1105 1107 1 1090 1107 1
		 1111 1110 1 1110 1109 1 1109 1108 1 1108 1111 1 1111 1113 1 1113 1112 1 1112 1110 1
		 1112 1114 1 1114 1109 1 1108 1097 1 1098 1111 1 1098 1116 1 1116 1115 1 1115 1111 1
		 1117 1116 1 1101 1117 1 1118 1113 1 1115 1118 1 1117 1118 1 1122 1121 1 1121 1120 1
		 1120 1119 1 1119 1122 1 1121 1124 1 1124 1123 1 1123 1120 1 1125 1124 1 1122 1125 1
		 1119 1108 1 1109 1122 1 1127 1126 1 1126 1122 1 1109 1127 1 1127 1129 1 1129 1128 1
		 1128 1126 1 1114 1129 1 1128 1125 1 1133 1132 1 1132 1131 1 1131 1130 1 1130 1133 1
		 1132 1135 1 1135 1134 1 1134 1131 1 1133 1136 1 1136 1135 1 1130 1119 1 1120 1133 1
		 1120 1138 1 1138 1137 1 1137 1133 1 1140 1139 1 1139 1137 1 1138 1140 1 1139 1136 1;
	setAttr ".ed[830:995]" 1123 1140 1 1144 965 1 966 1145 1 1145 1144 1 1146 1061 1
		 1062 1145 1 1145 1146 1 1147 1073 1 1146 1147 1 1148 1074 1 1147 1148 1 1149 1070 1
		 1148 1149 1 1150 1069 1 1149 1150 1 1151 1068 1 1150 1151 1 1152 1085 1 1151 1152 1
		 1153 1084 1 1152 1153 1 1179 1009 1 1008 1178 1 1178 1179 1 1180 991 1 1179 1180 1
		 1181 990 1 1180 1181 1 1154 1081 1 1153 1154 1 1003 1177 1 1177 1178 1 1004 1155 1
		 1155 1177 1 1005 1156 1 1156 1155 1 1157 1107 1 1090 1156 1 1156 1157 1 1158 1106 1
		 1157 1158 1 1159 1103 1 1158 1159 1 1160 1102 1 1159 1160 1 1161 1101 1 1160 1161 1
		 1162 1117 1 1161 1162 1 1163 1118 1 1162 1163 1 1164 1113 1 1163 1164 1 1165 1112 1
		 1164 1165 1 1166 1114 1 1165 1166 1 1167 1129 1 1166 1167 1 1168 1128 1 1167 1168 1
		 1169 1125 1 1168 1169 1 1170 1124 1 1169 1170 1 1171 1123 1 1170 1171 1 1172 1140 1
		 1171 1172 1 1173 1139 1 1172 1173 1 1174 1136 1 1173 1174 1 1175 1135 1 1174 1175 1
		 1176 1134 1 1175 1176 1 1143 1052 1 1049 1141 1 1141 1143 1 1063 1144 1 967 1146 1
		 981 1147 1 980 1148 1 975 1149 1 976 1150 1 977 1151 1 994 1152 1 995 1153 1 989 1154 1
		 1091 1155 1 1023 1157 1 1022 1158 1 1017 1159 1 1018 1160 1 1019 1161 1 1033 1162 1
		 1034 1163 1 1028 1164 1 1029 1165 1 1030 1166 1 1045 1167 1 1044 1168 1 1039 1169 1
		 1040 1170 1 1041 1171 1 1053 1172 1 1056 1173 1 1050 1174 1 1051 1175 1 1052 1176 1
		 1092 1177 1 1095 1178 1 1096 1179 1 1079 1180 1 1080 1181 1 1181 1154 1 1184 964 1
		 965 1185 1 1185 1184 1 1183 959 1 1184 1183 1 1182 958 1 1183 1182 1 1144 1186 1
		 1186 1185 1 1187 1063 1 1060 1188 1 1188 1187 1 1057 1189 1 1189 1188 1 1187 1186 1
		 1183 532 1 533 1182 1 1184 530 1 1185 531 1 1186 534 1 1188 536 1 535 1187 1 1189 537 1
		 1057 547 1 1064 1191 1 1191 547 1 1075 1192 1 1192 1191 1 1086 1193 1 1193 1192 1
		 1097 1194 1 1194 1193 1 1108 1195 1 1195 1194 1 1119 1196 1 1196 1195 1 1130 1197 1
		 1197 1196 1 1131 1198 1 1198 1197 1 1200 1199 0 1199 1190 0 1190 1046 1 1046 1200 1
		 1035 1201 1 1201 1200 0 1046 1035 1 1202 1201 0 1035 1024 1;
	setAttr ".ed[996:1161]" 1024 1202 1 1010 1203 1 1203 1202 0 1024 1010 1 996 1204 1
		 1204 1203 0 982 1205 1 1205 1204 0 968 1206 1 1206 1205 0 958 1207 1 1207 1206 0
		 1182 1208 1 1208 1207 0 567 1208 0 1198 556 0 568 1197 1 568 1195 1 1192 569 1 569 1194 1
		 1013 1211 1 1211 1025 1 1024 1211 1 1027 1211 1 1211 1209 1 1209 1036 1 1035 1209 1
		 1038 1209 1 1209 1210 1 1210 1047 1 1046 1210 1 1142 1141 1 1049 1210 1 1210 1142 1
		 1190 1142 1 1288 1289 1 1289 1141 1 1142 1288 1 1287 1143 1 1289 1287 1 1134 1289 1
		 1288 1131 1 1287 1176 1 1288 1290 1 1290 1198 0 1190 1290 0 5 36 1 36 29 1 29 124 1
		 124 5 1 102 82 1 82 115 1 115 121 1 121 102 1 0 1 1 1 157 1 157 62 1 62 0 1 154 100 1
		 100 131 1 131 164 1 164 154 1 102 36 1 5 82 1 0 100 1 154 1 1 14 101 1 101 57 1 57 144 1
		 144 14 1 119 86 1 86 133 1 133 77 1 77 119 1 3 48 1 48 165 1 165 109 1 109 3 1 3 86 1
		 119 48 1 145 37 1 37 101 1 14 145 1 20 37 1 145 155 1 155 20 1 37 22 1 22 43 1 43 101 1
		 48 72 1 72 7 1 7 165 1 20 90 1 90 22 1 43 9 1 9 57 1 119 95 1 95 72 1 77 21 1 21 95 1
		 100 95 1 21 131 1 43 102 1 121 9 1 90 29 1 36 22 1 0 72 1 62 7 1 38 110 1 110 52 1
		 52 34 1 34 38 1 26 41 1 41 38 1 34 26 1 111 96 1 96 39 1 39 151 1 151 111 1 135 80 1
		 80 142 1 142 88 1 88 135 1 88 103 1 103 137 1 137 135 1 96 150 1 150 132 1 132 39 1
		 150 52 1 110 132 1 26 137 1 103 41 1 168 167 1 167 108 1 108 32 1 32 168 1 63 93 1
		 93 9 1 121 63 1 93 85 1 85 57 1 85 98 1 98 144 1 85 77 1 133 98 1 93 21 1 63 131 1
		 115 6 1 6 63 1 6 164 1 92 76 1 76 116 1 116 70 1 70 92 1 75 76 1 92 123 1 123 75 1
		 92 81 1 81 17 1 17 123 1 70 73 1 73 81 1 25 78 1 78 167 1 168 25 1;
	setAttr ".ed[1162:1327]" 136 99 1 99 166 1 166 170 1 170 136 1 23 114 1 114 170 1
		 166 23 1 56 4 1 4 44 1 44 143 0 143 56 1 138 53 1 53 50 1 50 40 0 40 138 1 11 84 1
		 84 18 1 18 64 0 64 11 1 4 16 1 16 65 1 65 44 0 53 19 1 19 118 1 118 50 0 96 117 1
		 117 161 0 161 150 1 111 153 1 153 117 0 135 87 1 87 140 0 140 80 1 137 54 1 54 87 0
		 52 74 1 74 79 0 79 34 1 91 26 1 79 91 0 91 54 0 161 74 0 90 141 1 141 147 0 147 29 1
		 165 66 1 66 148 0 148 109 1 20 51 1 51 141 0 27 62 1 157 112 1 112 27 0 28 7 1 27 28 0
		 147 89 0 89 124 1 28 66 0 155 13 1 13 51 0 67 15 1 15 152 1 152 126 0 126 67 1 30 128 1
		 128 94 1 94 104 1 104 30 0 15 47 1 47 83 1 83 152 0 94 162 1 162 105 1 105 104 0
		 81 132 1 110 17 1 76 103 1 88 116 1 68 151 1 39 73 1 73 68 1 142 106 1 106 116 1
		 75 41 1 38 129 1 129 17 1 75 129 1 94 136 1 136 33 1 33 162 1 114 15 1 67 159 1 159 114 1
		 23 47 1 128 99 1 4 146 1 146 69 1 69 16 1 108 84 1 11 32 1 53 25 1 25 59 1 59 19 1
		 56 60 1 60 146 1 138 78 1 130 158 1 158 124 1 89 130 0 5 58 1 58 42 1 42 82 1 158 58 1
		 58 1 1 154 42 1 158 157 1 130 112 0 42 35 1 35 115 1 164 35 1 109 156 1 156 139 1
		 139 3 1 149 12 1 12 14 1 144 149 1 155 156 1 156 55 1 55 13 0 12 139 1 139 145 1
		 148 55 0 86 12 1 149 133 1 24 71 1 71 10 1 10 31 1 31 24 0 45 10 1 71 2 1 2 45 1
		 16 71 1 24 65 0 69 2 1 70 222 1 222 68 1 106 222 1 56 84 1 108 60 1 143 18 0 19 11 1
		 64 118 0 59 32 1 159 223 1 223 170 1 223 33 1 227 226 1 226 113 1 113 122 1 122 227 1
		 125 226 1 227 46 1 46 125 1 122 8 1 8 224 0 224 227 1;
	setAttr ".ed[1328:1493]" 224 134 0 134 46 1 232 233 1 233 236 1 236 237 1 237 232 1
		 229 228 1 228 237 1 236 229 1 237 225 1 225 160 1 160 232 1 233 113 1 226 236 1 61 225 1
		 228 61 1 125 229 1 127 120 1 120 238 1 238 239 1 239 127 1 238 49 1 49 163 1 163 239 1
		 107 49 1 238 240 1 240 107 0 120 97 1 97 240 0 234 235 1 235 242 1 242 243 1 243 234 1
		 242 230 1 230 231 1 231 243 1 235 127 1 239 242 1 163 230 1 241 243 1 231 221 1 221 241 1
		 241 220 1 220 234 1 113 249 1 249 253 1 253 122 1 245 8 0 253 245 1 249 163 1 49 253 1
		 107 245 0 225 252 1 252 244 1 244 160 1 254 250 1 250 46 1 134 254 0 120 250 1 254 97 0
		 250 251 1 251 125 1 127 251 1 61 246 1 246 252 1 256 255 1 255 235 1 234 256 1 247 248 1
		 248 231 1 230 247 1 256 228 1 229 255 1 247 233 1 232 248 1 247 249 1 244 248 1 244 221 1
		 251 255 1 256 246 1 220 246 1 241 252 1 276 275 1 275 263 1 263 265 1 265 276 1 266 275 1
		 276 258 1 258 266 1 265 257 1 257 273 0 273 276 1 273 268 0 268 258 1 281 282 1 282 285 1
		 285 286 1 286 281 1 278 277 1 277 286 1 285 278 1 286 274 1 274 269 1 269 281 1 282 263 1
		 275 285 1 260 274 1 277 260 1 266 278 1 267 264 1 264 287 1 287 288 1 288 267 1 287 259 1
		 259 270 1 270 288 1 262 259 1 287 289 1 289 262 0 264 261 1 261 289 0 283 284 1 284 291 1
		 291 292 1 292 283 1 291 279 1 279 280 1 280 292 1 284 267 1 288 291 1 270 279 1 290 292 1
		 280 272 1 272 290 1 290 271 1 271 283 1 263 298 1 298 302 1 302 265 1 294 257 0 302 294 1
		 298 270 1 259 302 1 262 294 0 274 301 1 301 293 1 293 269 1 303 299 1 299 258 1 268 303 0
		 264 299 1 303 261 0 299 300 1 300 266 1 267 300 1 260 295 1 295 301 1 305 304 1 304 284 1
		 283 305 1 296 297 1 297 280 1 279 296 1 305 277 1 278 304 1 296 282 1;
	setAttr ".ed[1494:1659]" 281 297 1 296 298 1 293 297 1 293 272 1 300 304 1 305 295 1
		 271 295 1 290 301 1 594 574 1 574 604 1 604 613 1 613 594 1 604 608 1 608 601 1 601 613 1
		 607 611 1 611 588 1 588 602 1 602 607 1 602 599 1 599 577 1 577 607 1 611 579 1 579 580 1
		 580 588 1 580 610 1 610 591 1 591 588 1 591 605 1 605 602 1 605 582 1 582 599 1 592 594 1
		 613 576 1 576 592 1 584 600 1 600 596 1 596 593 1 593 584 1 598 597 1 597 587 1 587 603 1
		 603 598 1 600 597 1 598 596 1 587 615 1 615 616 1 616 603 1 575 617 1 617 598 1 603 575 1
		 604 618 1 618 617 1 617 608 1 618 596 1 618 619 1 619 593 1 574 619 1 620 608 1 575 620 1
		 622 621 1 621 579 1 611 622 1 584 621 1 622 600 1 597 623 1 623 614 1 614 587 1 622 623 1
		 607 623 1 577 614 1 579 595 1 595 590 0 590 580 1 621 585 1 585 595 0 619 612 1 612 586 0
		 586 593 1 581 594 1 592 609 1 609 581 0 590 583 0 583 610 1 586 589 0 589 584 1 574 573 1
		 573 612 0 581 573 0 589 585 0 591 576 1 576 606 1 606 605 1 606 578 1 578 582 1 610 592 1
		 615 582 1 578 616 1 583 609 0 577 615 1 616 620 1 601 606 1 601 624 1 624 578 1 624 620 1
		 180 183 1 183 184 1 184 177 1 177 180 1 171 190 1 190 196 1 196 215 1 215 171 1 184 198 1
		 198 212 1 212 177 1 218 212 1 212 195 1 195 217 1 217 218 1 215 214 1 214 207 1 207 171 1
		 198 213 1 213 195 1 215 218 1 217 214 1 213 188 1 188 176 1 176 195 1 182 205 1 205 207 1
		 214 182 1 217 216 1 216 182 1 176 216 1 196 219 1 219 218 1 219 177 1 193 206 1 206 197 1
		 197 204 1 204 193 0 172 189 1 189 186 1 186 203 1 203 172 0 185 174 1 174 173 1 173 200 0
		 200 185 1 186 175 1 175 209 1 209 203 0 175 210 1 210 208 1 208 209 0 197 185 1 200 204 0
		 191 192 1 192 189 1 172 191 0 178 185 1 197 201 1 201 178 1 181 187 1;
	setAttr ".ed[1660:1825]" 187 189 1 192 181 1 187 211 1 211 186 1 206 199 1 199 201 1
		 175 202 1 202 194 1 194 210 1 211 202 1 178 179 1 179 174 1 202 184 1 183 194 1 198 211 1
		 187 213 1 201 171 1 207 178 1 181 188 1 205 179 1 199 190 1 199 194 1 183 190 1 210 206 1
		 193 208 0 180 196 1 188 625 1 625 626 1 626 176 1 625 205 1 182 626 1 174 627 1 627 628 1
		 628 173 0 627 192 1 191 628 0 179 629 1 629 627 1 629 181 1 629 625 1 634 635 1 635 633 1
		 633 632 1 632 634 1 636 637 1 637 632 1 633 636 1 634 631 1 631 630 0 630 635 1 637 167 1
		 78 632 1 40 631 0 634 138 1 633 45 1 45 169 1 169 636 1 635 10 1 630 31 0 636 146 1
		 60 637 1 169 69 1 80 640 1 640 638 1 638 142 1 140 639 0 639 640 1 638 641 1 641 106 1
		 111 640 1 639 153 0 151 638 1 68 641 1 47 642 1 642 643 1 643 83 0 642 128 1 30 643 0
		 23 644 1 644 642 1 644 99 1 646 645 1 645 67 1 126 646 0 162 645 1 646 105 0 33 647 1
		 647 645 1 647 159 1 657 776 1 776 681 1 681 688 1 688 657 1 754 773 1 773 767 1 767 734 1
		 734 754 1 652 714 1 714 809 1 809 653 1 653 652 1 806 816 1 816 783 1 783 752 1 752 806 1
		 734 657 1 688 754 1 653 806 1 752 652 1 666 796 1 796 709 1 709 753 1 753 666 1 771 729 1
		 729 785 1 785 738 1 738 771 1 655 761 1 761 817 1 817 700 1 700 655 1 700 771 1 738 655 1
		 797 666 1 753 689 1 689 797 1 672 807 1 807 797 1 689 672 1 753 695 1 695 674 1 674 689 1
		 817 659 1 659 724 1 724 700 1 674 742 1 742 672 1 709 661 1 661 695 1 724 747 1 747 771 1
		 747 673 1 673 729 1 783 673 1 747 752 1 661 773 1 754 695 1 674 688 1 681 742 1 724 652 1
		 659 714 1 690 686 1 686 704 1 704 762 1 762 690 1 678 686 1 690 693 1 693 678 1 763 803 1
		 803 691 1 691 748 1 748 763 1 787 740 1 740 794 1 794 732 1 732 787 1;
	setAttr ".ed[1826:1991]" 787 789 1 789 755 1 755 740 1 691 784 1 784 802 1 802 748 1
		 784 762 1 704 802 1 693 755 1 789 678 1 820 684 1 684 760 1 760 819 1 819 820 1 715 773 1
		 661 745 1 745 715 1 709 737 1 737 745 1 796 750 1 750 737 1 750 785 1 729 737 1 673 745 1
		 783 715 1 715 658 1 658 767 1 816 658 1 744 722 1 722 768 1 768 728 1 728 744 1 727 775 1
		 775 744 1 728 727 1 775 669 1 669 733 1 733 744 1 733 725 1 725 722 1 677 820 1 819 730 1
		 730 677 1 788 822 1 822 818 1 818 751 1 751 788 1 675 818 1 822 766 1 766 675 1 708 795 1
		 795 696 0 696 656 1 656 708 1 790 692 1 692 702 0 702 705 1 705 790 1 663 716 1 716 670 0
		 670 736 1 736 663 1 696 717 0 717 668 1 668 656 1 702 770 0 770 671 1 671 705 1 802 813 1
		 813 769 0 769 748 1 769 805 0 805 763 1 732 792 1 792 739 0 739 787 1 739 706 0 706 789 1
		 686 731 1 731 726 0 726 704 1 743 731 0 678 743 1 706 743 0 726 813 0 681 799 1 799 793 0
		 793 742 1 761 800 1 800 718 0 718 817 1 793 703 0 703 672 1 679 764 0 764 809 1 714 679 1
		 680 679 0 659 680 1 776 741 1 741 799 0 718 680 0 703 665 0 665 807 1 719 778 1 778 804 0
		 804 667 1 667 719 1 682 756 0 756 746 1 746 780 1 780 682 1 804 735 0 735 699 1 699 667 1
		 756 757 0 757 814 1 814 746 1 669 762 1 784 733 1 768 740 1 755 728 1 720 725 1 725 691 1
		 803 720 1 768 758 1 758 794 1 693 727 1 669 781 1 781 690 1 781 727 1 814 685 1 685 788 1
		 788 746 1 766 811 1 811 719 1 667 766 1 699 675 1 751 780 1 668 721 1 721 798 1 798 656 1
		 684 663 1 736 760 1 671 711 1 711 677 1 677 705 1 798 712 1 712 708 1 730 790 1 782 741 0
		 776 810 1 810 782 1 734 694 1 694 710 1 710 657 1 710 810 1 694 806 1 653 710 1 809 810 1
		 764 782 0 767 687 1 687 694 1 687 816 1 655 791 1 791 808 1 808 761 1;
	setAttr ".ed[1992:2157]" 801 796 1 666 664 1 664 801 1 665 707 0 707 808 1 808 807 1
		 797 791 1 791 664 1 707 800 0 785 801 1 664 738 1 676 683 0 683 662 1 662 723 1 723 676 1
		 697 654 1 654 723 1 662 697 1 717 676 0 723 668 1 654 721 1 720 874 1 874 722 1 874 758 1
		 712 760 1 736 708 1 670 795 0 770 716 0 663 671 1 684 711 1 822 875 1 875 811 1 685 875 1
		 879 774 1 774 765 1 765 878 1 878 879 1 777 698 1 698 879 1 878 777 1 879 876 1 876 660 0
		 660 774 1 698 786 1 786 876 0 884 889 1 889 888 1 888 885 1 885 884 1 881 888 1 889 880 1
		 880 881 1 884 812 1 812 877 1 877 889 1 888 878 1 765 885 1 713 880 1 877 713 1 881 777 1
		 779 891 1 891 890 1 890 772 1 772 779 1 891 815 1 815 701 1 701 890 1 759 892 0 892 890 1
		 701 759 1 892 749 0 749 772 1 886 895 1 895 894 1 894 887 1 887 886 1 895 883 1 883 882 1
		 882 894 1 894 891 1 779 887 1 882 815 1 893 873 1 873 883 1 895 893 1 886 872 1 872 893 1
		 774 905 1 905 901 1 901 765 1 897 905 1 660 897 0 905 701 1 815 901 1 897 759 0 812 896 1
		 896 904 1 904 877 1 906 786 0 698 902 1 902 906 1 749 906 0 902 772 1 777 903 1 903 902 1
		 903 779 1 904 898 1 898 713 1 908 886 1 887 907 1 907 908 1 899 882 1 883 900 1 900 899 1
		 907 881 1 880 908 1 900 884 1 885 899 1 901 899 1 900 896 1 873 896 1 907 903 1 898 908 1
		 898 872 1 904 893 1 928 917 1 917 915 1 915 927 1 927 928 1 918 910 1 910 928 1 927 918 1
		 928 925 1 925 909 0 909 917 1 910 920 1 920 925 0 933 938 1 938 937 1 937 934 1 934 933 1
		 930 937 1 938 929 1 929 930 1 933 921 1 921 926 1 926 938 1 937 927 1 915 934 1 912 929 1
		 926 912 1 930 918 1 919 940 1 940 939 1 939 916 1 916 919 1 940 922 1 922 911 1 911 939 1
		 914 941 0 941 939 1 911 914 1 941 913 0 913 916 1 935 944 1 944 943 1;
	setAttr ".ed[2158:2323]" 943 936 1 936 935 1 944 932 1 932 931 1 931 943 1 943 940 1
		 919 936 1 931 922 1 942 924 1 924 932 1 944 942 1 935 923 1 923 942 1 917 954 1 954 950 1
		 950 915 1 946 954 1 909 946 0 954 911 1 922 950 1 946 914 0 921 945 1 945 953 1 953 926 1
		 955 920 0 910 951 1 951 955 1 913 955 0 951 916 1 918 952 1 952 951 1 952 919 1 953 947 1
		 947 912 1 957 935 1 936 956 1 956 957 1 948 931 1 932 949 1 949 948 1 956 930 1 929 957 1
		 949 933 1 934 948 1 950 948 1 949 945 1 924 945 1 956 952 1 947 957 1 947 923 1 953 942 1
		 1233 1252 1 1252 1243 1 1243 1213 1 1213 1233 1 1252 1240 1 1240 1247 1 1247 1243 1
		 1246 1241 1 1241 1227 1 1227 1250 1 1250 1246 1 1246 1216 1 1216 1238 1 1238 1241 1
		 1227 1219 1 1219 1218 1 1218 1250 1 1227 1230 1 1230 1249 1 1249 1219 1 1241 1244 1
		 1244 1230 1 1238 1221 1 1221 1244 1 1231 1215 1 1215 1252 1 1233 1231 1 1223 1232 1
		 1232 1235 1 1235 1239 1 1239 1223 1 1237 1242 1 1242 1226 1 1226 1236 1 1236 1237 1
		 1235 1237 1 1236 1239 1 1242 1255 1 1255 1254 1 1254 1226 1 1214 1242 1 1237 1256 1
		 1256 1214 1 1247 1256 1 1256 1257 1 1257 1243 1 1235 1257 1 1232 1258 1 1258 1257 1
		 1258 1213 1 1259 1214 1 1247 1259 1 1261 1250 1 1218 1260 1 1260 1261 1 1239 1261 1
		 1260 1223 1 1226 1253 1 1253 1262 1 1262 1236 1 1262 1261 1 1262 1246 1 1253 1216 1
		 1219 1229 1 1229 1234 0 1234 1218 1 1234 1224 0 1224 1260 1 1232 1225 1 1225 1251 0
		 1251 1258 1 1220 1248 0 1248 1231 1 1233 1220 1 1249 1222 1 1222 1229 0 1223 1228 1
		 1228 1225 0 1251 1212 0 1212 1213 1 1212 1220 0 1224 1228 0 1244 1245 1 1245 1215 1
		 1215 1230 1 1221 1217 1 1217 1245 1 1231 1249 1 1255 1217 1 1221 1254 1 1248 1222 0
		 1254 1216 1 1259 1255 1 1245 1240 1 1217 1263 1 1263 1240 1 1259 1263 1 832 829 1
		 829 836 1 836 835 1 835 832 1 823 867 1 867 848 1 848 842 1 842 823 1 829 864 1 864 850 1
		 850 836 1 870 869 1 869 847 1 847 864 1 864 870 1 823 859 1 859 866 1 866 867 1;
	setAttr ".ed[2324:2455]" 847 865 1 865 850 1 866 869 1 870 867 1 847 828 1 828 840 1
		 840 865 1 834 866 1 859 857 1 857 834 1 834 868 1 868 869 1 868 828 1 870 871 1 871 848 1
		 829 871 1 845 856 0 856 849 1 849 858 1 858 845 1 824 855 0 855 838 1 838 841 1 841 824 1
		 837 852 1 852 825 0 825 826 1 826 837 1 855 861 0 861 827 1 827 838 1 861 860 0 860 862 1
		 862 827 1 856 852 0 837 849 1 843 824 0 841 844 1 844 843 1 830 853 1 853 849 1 837 830 1
		 833 844 1 841 839 1 839 833 1 838 863 1 863 839 1 853 851 1 851 858 1 862 846 1 846 854 1
		 854 827 1 854 863 1 826 831 1 831 830 1 846 835 1 836 854 1 865 839 1 863 850 1 830 859 1
		 823 853 1 840 833 1 831 857 1 842 851 1 842 835 1 846 851 1 860 845 0 858 862 1 848 832 1
		 828 1265 1 1265 1264 1 1264 840 1 1265 834 1 857 1264 1 825 1267 0 1267 1266 1 1266 826 1
		 1267 843 0 844 1266 1 1266 1268 1 1268 831 1 833 1268 1 1264 1268 1 1273 1271 1 1271 1272 1
		 1272 1274 1 1274 1273 1 1275 1272 1 1271 1276 1 1276 1275 1 1274 1269 1 1269 1270 0
		 1270 1273 1 1271 730 1 819 1276 1 790 1273 1 1270 692 0 1275 821 1 821 697 1 697 1272 1
		 662 1274 1 683 1269 0 1276 712 1 798 1275 1 721 821 1 794 1277 1 1277 1279 1 1279 732 1
		 1279 1278 1 1278 792 0 758 1280 1 1280 1277 1 805 1278 0 1279 763 1 1277 803 1 1280 720 1
		 735 1282 0 1282 1281 1 1281 699 1 1282 682 0 780 1281 1 1281 1283 1 1283 675 1 751 1283 1
		 1285 778 0 719 1284 1 1284 1285 1 757 1285 0 1284 814 1 1284 1286 1 1286 685 1 811 1286 1;
	setAttr -s 1182 ".fc";
	setAttr ".fc[0:499]" -type "polyFaces" 
		f 4 0 1 2 3
		mu 0 4 309 306 307 308
		f 4 -4 4 5 6
		mu 0 4 309 308 310 311
		f 4 -3 7 8 -5
		mu 0 4 308 307 312 310
		f 4 9 -9 10 11
		mu 0 4 314 310 312 313
		f 4 -10 12 13 -6
		mu 0 4 310 314 315 311
		f 4 14 15 16 17
		mu 0 4 319 316 317 318
		f 4 -17 18 19 20
		mu 0 4 318 317 320 321
		f 4 21 -18 -21 22
		mu 0 4 322 319 318 321
		f 4 -20 23 24 25
		mu 0 4 321 320 323 324
		f 4 26 -23 -26 27
		mu 0 4 325 322 321 324
		f 4 -19 28 -7 29
		mu 0 4 320 317 309 311
		f 4 30 -1 -29 -16
		mu 0 4 316 306 309 317
		f 4 -30 31 32 33
		mu 0 4 320 311 326 327
		f 4 -34 34 35 -24
		mu 0 4 320 327 328 323
		f 4 36 -35 -33 37
		mu 0 4 329 328 327 326
		f 4 38 -38 -32 -14
		mu 0 4 315 329 326 311
		f 4 39 40 41 42
		mu 0 4 333 330 331 332
		f 4 43 -42 44 45
		mu 0 4 335 332 331 334
		f 4 -44 46 47 -43
		mu 0 4 332 335 336 333
		f 4 48 -46 49 50
		mu 0 4 338 335 334 337
		f 4 -49 51 52 -47
		mu 0 4 335 338 339 336
		f 4 53 -45 54 -22
		mu 0 4 322 334 331 319
		f 4 -41 55 -15 -55
		mu 0 4 331 330 316 319
		f 4 -54 56 57 58
		mu 0 4 334 322 340 341
		f 4 -58 59 60 61
		mu 0 4 341 340 342 343
		f 4 -62 62 -50 -59
		mu 0 4 341 343 337 334
		f 4 63 -60 -57 -27
		mu 0 4 325 342 340 322
		f 4 64 65 66 67
		mu 0 4 347 344 345 346
		f 4 68 -67 69 70
		mu 0 4 349 346 345 348
		f 4 -69 71 72 -68
		mu 0 4 346 349 350 347
		f 4 73 -71 74 75
		mu 0 4 352 349 348 351
		f 4 76 -72 -74 77
		mu 0 4 353 350 349 352
		f 4 78 79 80 81
		mu 0 4 348 336 354 355
		f 4 -82 82 83 -75
		mu 0 4 348 355 356 351
		f 4 84 -80 -53 85
		mu 0 4 357 354 336 339
		f 4 -85 86 -83 -81
		mu 0 4 354 357 356 355
		f 4 87 -48 -79 -70
		mu 0 4 345 333 336 348
		f 4 -66 88 -40 -88
		mu 0 4 345 344 330 333
		f 4 89 90 91 92
		mu 0 4 361 358 359 360
		f 4 93 -92 94 95
		mu 0 4 363 360 359 362
		f 4 96 -93 -94 97
		mu 0 4 364 361 360 363
		f 4 98 -96 99 100
		mu 0 4 366 363 362 365
		f 4 101 -98 -99 102
		mu 0 4 367 364 363 366
		f 4 -95 103 -73 104
		mu 0 4 362 359 347 350
		f 4 -91 105 -65 -104
		mu 0 4 359 358 344 347
		f 4 -105 106 107 108
		mu 0 4 362 350 368 369
		f 4 -109 109 110 -100
		mu 0 4 362 369 370 365
		f 4 111 -107 -77 112
		mu 0 4 371 368 350 353
		f 4 -112 113 -110 -108
		mu 0 4 368 371 370 369
		f 4 114 115 116 117
		mu 0 4 377 374 373 376
		f 4 118 119 -115 120
		mu 0 4 378 375 374 377
		f 4 121 122 123 124
		mu 0 4 380 373 364 379
		f 4 125 -124 -102 126
		mu 0 4 381 379 364 367
		f 4 -122 127 128 -117
		mu 0 4 373 380 382 376
		f 4 -126 129 -128 -125
		mu 0 4 379 381 382 380
		f 4 130 131 132 133
		mu 0 4 388 385 384 387
		f 4 134 135 -131 136
		mu 0 4 389 386 385 388
		f 4 137 138 139 140
		mu 0 4 384 375 390 391
		f 4 141 -133 -141 142
		mu 0 4 392 387 384 391
		f 4 143 -139 -119 144
		mu 0 4 393 390 375 378
		f 4 -144 145 -143 -140
		mu 0 4 390 393 392 391
		f 4 146 147 148 149
		mu 0 4 399 396 395 398
		f 4 -147 150 151 152
		mu 0 4 396 399 400 397
		f 4 -135 153 154 155
		mu 0 4 386 389 401 402
		f 4 -156 156 157 158
		mu 0 4 386 402 403 395
		f 4 159 -149 -158 160
		mu 0 4 404 398 395 403
		f 4 161 -161 -157 -155
		mu 0 4 401 404 403 402
		f 4 162 163 164 165
		mu 0 4 408 405 406 407
		f 4 166 -165 167 168
		mu 0 4 410 407 406 409
		f 4 169 -166 -167 170
		mu 0 4 411 408 407 410
		f 4 171 172 173 174
		mu 0 4 415 412 413 414
		f 4 -174 175 176 177
		mu 0 4 414 413 416 417
		f 4 178 -175 -178 179
		mu 0 4 418 415 414 417
		f 4 -172 180 -164 181
		mu 0 4 412 415 406 405
		f 4 -181 182 183 184
		mu 0 4 406 415 419 420
		f 4 185 -168 -185 186
		mu 0 4 421 409 406 420
		f 4 -184 187 188 -187
		mu 0 4 420 419 422 421
		f 4 189 -188 -183 -179
		mu 0 4 418 422 419 415
		f 4 190 191 192 193
		mu 0 4 426 423 424 425
		f 4 -193 194 195 196
		mu 0 4 425 424 427 428
		f 4 -197 197 198 -194
		mu 0 4 425 428 429 426
		f 4 199 -191 200 -173
		mu 0 4 412 423 426 413
		f 4 -201 201 202 203
		mu 0 4 413 426 430 431
		f 4 204 -203 205 206
		mu 0 4 433 431 430 432
		f 4 -205 207 -176 -204
		mu 0 4 431 433 416 413
		f 4 208 -206 -202 -199
		mu 0 4 429 432 430 426
		f 4 209 210 211 212
		mu 0 4 437 434 435 436
		f 4 -212 213 214 215
		mu 0 4 436 435 438 439
		f 4 -216 216 217 -213
		mu 0 4 436 439 440 437
		f 4 -210 218 -192 219
		mu 0 4 434 437 424 423
		f 4 -219 220 221 222
		mu 0 4 424 437 441 442
		f 4 223 -222 224 225
		mu 0 4 444 442 441 443
		f 4 226 -225 -221 -218
		mu 0 4 440 443 441 437
		f 4 -224 227 -195 -223
		mu 0 4 442 444 427 424
		f 4 228 229 230 231
		mu 0 4 448 445 446 447
		f 4 232 -231 233 234
		mu 0 4 450 447 446 449
		f 4 -233 235 236 -232
		mu 0 4 447 450 451 448
		f 4 -229 237 -211 238
		mu 0 4 445 448 435 434
		f 4 -238 239 240 241
		mu 0 4 435 448 452 453
		f 4 -237 242 243 -240
		mu 0 4 448 451 454 452
		f 4 244 -241 -244 245
		mu 0 4 455 453 452 454
		f 4 -245 246 -214 -242
		mu 0 4 453 455 438 435
		f 4 247 248 249 250
		mu 0 4 459 456 457 458
		f 4 -251 251 252 253
		mu 0 4 459 458 460 461
		f 4 -250 254 255 -252
		mu 0 4 458 457 462 460
		f 4 -248 256 -230 257
		mu 0 4 456 459 446 445
		f 4 -257 258 259 260
		mu 0 4 446 459 463 464
		f 4 261 -234 -261 262
		mu 0 4 465 449 446 464
		f 4 263 -259 -254 264
		mu 0 4 466 463 459 461
		f 4 -264 265 -263 -260
		mu 0 4 463 466 465 464
		f 4 266 267 268 269
		mu 0 4 470 467 468 469
		f 4 -269 270 271 272
		mu 0 4 469 468 471 472
		f 4 273 -270 -273 274
		mu 0 4 473 470 469 472
		f 4 -267 275 -249 276
		mu 0 4 467 470 457 456
		f 4 277 -276 278 279
		mu 0 4 475 457 470 474
		f 4 -280 280 281 282
		mu 0 4 475 474 476 477
		f 4 -283 283 -255 -278
		mu 0 4 475 477 462 457
		f 4 284 -281 -279 -274
		mu 0 4 473 476 474 470
		f 4 285 286 287 288
		mu 0 4 481 478 479 480
		f 4 -288 289 290 291
		mu 0 4 480 479 482 483
		f 4 -292 292 293 -289
		mu 0 4 480 483 484 481
		f 4 294 -268 295 -286
		mu 0 4 481 468 467 478
		f 4 -295 296 297 298
		mu 0 4 468 481 485 486
		f 4 299 -298 300 301
		mu 0 4 488 486 485 487
		f 4 302 -301 -297 -294
		mu 0 4 484 487 485 481
		f 4 -300 303 -271 -299
		mu 0 4 486 488 471 468
		f 4 304 305 -12 306
		mu 0 4 492 493 314 313
		f 4 307 308 -169 309
		mu 0 4 494 493 410 409
		f 4 310 -310 -186 311
		mu 0 4 495 494 409 421
		f 4 312 -312 -189 313
		mu 0 4 496 495 421 422
		f 4 314 -314 -190 315
		mu 0 4 497 496 422 418
		f 4 316 -316 -180 317
		mu 0 4 498 497 418 417
		f 4 318 -318 -177 319
		mu 0 4 499 498 417 416
		f 4 320 -320 -208 321
		mu 0 4 500 499 416 433
		f 4 322 -322 -207 323
		mu 0 4 501 500 433 432
		f 4 324 325 -87 326
		mu 0 4 527 526 356 357
		f 4 327 -327 -86 328
		mu 0 4 528 527 357 339
		f 4 329 -329 -52 330
		mu 0 4 529 528 339 338
		f 4 331 -324 -209 332
		mu 0 4 502 501 432 429
		f 4 333 334 -84 -326
		mu 0 4 526 525 351 356
		f 4 335 336 -76 -335
		mu 0 4 525 503 352 351
		f 4 337 338 -78 -337
		mu 0 4 503 504 353 352
		f 4 339 340 -247 341
		mu 0 4 505 504 438 455
		f 4 342 -342 -246 343
		mu 0 4 506 505 455 454
		f 4 344 -344 -243 345
		mu 0 4 507 506 454 451
		f 4 346 -346 -236 347
		mu 0 4 508 507 451 450
		f 4 348 -348 -235 349
		mu 0 4 509 508 450 449
		f 4 350 -350 -262 351
		mu 0 4 510 509 449 465
		f 4 352 -352 -266 353
		mu 0 4 511 510 465 466
		f 4 354 -354 -265 355
		mu 0 4 512 511 466 461
		f 4 356 -356 -253 357
		mu 0 4 513 512 461 460
		f 4 358 -358 -256 359
		mu 0 4 514 513 460 462
		f 4 360 -360 -284 361
		mu 0 4 515 514 462 477
		f 4 362 -362 -282 363
		mu 0 4 516 515 477 476
		f 4 364 -364 -285 365
		mu 0 4 517 516 476 473
		f 4 366 -366 -275 367
		mu 0 4 518 517 473 472
		f 4 368 -368 -272 369
		mu 0 4 519 518 472 471
		f 4 370 -370 -304 371
		mu 0 4 520 519 471 488
		f 4 372 -372 -302 373
		mu 0 4 521 520 488 487
		f 4 374 -374 -303 375
		mu 0 4 522 521 487 484
		f 4 376 -376 -293 377
		mu 0 4 523 522 484 483
		f 4 378 -378 -291 379
		mu 0 4 524 523 483 482
		f 4 380 381 -152 382
		mu 0 4 491 489 397 400
		f 4 -305 383 -171 -309
		mu 0 4 493 492 411 410
		f 4 -308 384 -13 -306
		mu 0 4 493 494 315 314
		f 4 -311 385 -39 -385
		mu 0 4 494 495 329 315
		f 4 -313 386 -37 -386
		mu 0 4 495 496 328 329
		f 4 -315 387 -36 -387
		mu 0 4 496 497 323 328
		f 4 -317 388 -25 -388
		mu 0 4 497 498 324 323
		f 4 -319 389 -28 -389
		mu 0 4 498 499 325 324
		f 4 -321 390 -64 -390
		mu 0 4 499 500 342 325
		f 4 -323 391 -61 -391
		mu 0 4 500 501 343 342
		f 4 -332 392 -63 -392
		mu 0 4 501 502 337 343
		f 4 -338 393 -215 -341
		mu 0 4 504 503 439 438
		f 4 -340 394 -113 -339
		mu 0 4 504 505 371 353
		f 4 -343 395 -114 -395
		mu 0 4 505 506 370 371
		f 4 -345 396 -111 -396
		mu 0 4 506 507 365 370
		f 4 -347 397 -101 -397
		mu 0 4 507 508 366 365
		f 4 -349 398 -103 -398
		mu 0 4 508 509 367 366
		f 4 -351 399 -127 -399
		mu 0 4 509 510 381 367
		f 4 -353 400 -130 -400
		mu 0 4 510 511 382 381
		f 4 -355 401 -129 -401
		mu 0 4 511 512 376 382
		f 4 -357 402 -118 -402
		mu 0 4 512 513 377 376
		f 4 -359 403 -121 -403
		mu 0 4 513 514 378 377
		f 4 -361 404 -145 -404
		mu 0 4 514 515 393 378
		f 4 -363 405 -146 -405
		mu 0 4 515 516 392 393
		f 4 -365 406 -142 -406
		mu 0 4 516 517 387 392
		f 4 -367 407 -134 -407
		mu 0 4 517 518 388 387
		f 4 -369 408 -137 -408
		mu 0 4 518 519 389 388
		f 4 -371 409 -154 -409
		mu 0 4 519 520 401 389
		f 4 -373 410 -162 -410
		mu 0 4 520 521 404 401
		f 4 -375 411 -160 -411
		mu 0 4 521 522 398 404
		f 4 -377 412 -150 -412
		mu 0 4 522 523 399 398
		f 4 -379 413 -151 -413
		mu 0 4 523 524 400 399
		f 4 -336 414 -217 -394
		mu 0 4 503 525 440 439
		f 4 -334 415 -227 -415
		mu 0 4 525 526 443 440
		f 4 -325 416 -226 -416
		mu 0 4 526 527 444 443
		f 4 -328 417 -228 -417
		mu 0 4 527 528 427 444
		f 4 418 -196 -418 -330
		mu 0 4 529 428 427 528
		f 4 -393 419 -331 -51
		mu 0 4 337 502 529 338
		f 4 -419 -420 -333 -198
		mu 0 4 428 529 502 429
		f 4 420 421 -11 422
		mu 0 4 540 541 313 312
		f 4 423 -423 -8 424
		mu 0 4 539 540 312 307
		f 4 425 -425 -2 426
		mu 0 4 538 539 307 306
		f 4 427 428 -307 -422
		mu 0 4 541 542 492 313
		f 4 429 430 -170 431
		mu 0 4 543 544 408 411
		f 4 432 433 -163 -431
		mu 0 4 544 545 405 408
		f 4 434 -432 -384 -429
		mu 0 4 542 543 411 492
		f 4 -426 435 436 437
		mu 0 4 539 538 533 532
		f 4 -424 -438 438 439
		mu 0 4 540 539 532 530
		f 4 -421 -440 440 441
		mu 0 4 541 540 530 531
		f 4 -428 -442 442 443
		mu 0 4 542 541 531 534
		f 4 -430 444 445 446
		mu 0 4 544 543 535 536
		f 4 -433 -447 447 448
		mu 0 4 545 544 536 537
		f 4 -449 449 450 -434
		mu 0 4 545 537 547 405
		f 4 -451 451 452 -182
		mu 0 4 405 547 548 412
		f 4 -200 -453 453 454
		mu 0 4 423 412 548 549
		f 4 -220 -455 455 456
		mu 0 4 434 423 549 550
		f 4 -239 -457 457 458
		mu 0 4 445 434 550 551
		f 4 -258 -459 459 460
		mu 0 4 456 445 551 552
		f 4 -277 -461 461 462
		mu 0 4 467 456 552 553
		f 4 -296 -463 463 464
		mu 0 4 478 467 553 554
		f 4 -287 -465 465 466
		mu 0 4 479 478 554 555
		f 4 467 468 469 470
		mu 0 4 558 394 546 557
		f 4 471 -468 472 473
		mu 0 4 383 394 558 559
		f 4 474 475 -474 476
		mu 0 4 560 372 383 559
		f 4 477 -475 478 479
		mu 0 4 358 372 560 561
		f 4 -106 -480 480 481
		mu 0 4 344 358 561 562
		f 4 -89 -482 482 483
		mu 0 4 330 344 562 563
		f 4 -56 -484 484 485
		mu 0 4 316 330 563 564
		f 4 -31 -486 486 487
		mu 0 4 306 316 564 565
		f 4 -427 -488 488 489
		mu 0 4 538 306 565 566
		f 4 -436 -490 490 491
		mu 0 4 533 538 566 567
		f 4 -466 492 493 494
		mu 0 4 555 554 568 556
		f 4 495 -493 -464 -462
		mu 0 4 552 568 554 553
		f 4 -452 496 497 -454
		mu 0 4 548 547 569 549
		f 4 498 499 -496 -460
		mu 0 4 551 569 568 552
		f 4 -498 -499 -458 -456
		mu 0 4 549 569 551 550
		f 4 500 501 -97 -123
		mu 0 4 373 572 361 364
		f 4 502 -478 -90 -502
		mu 0 4 572 372 358 361
		f 4 -138 503 504 505
		mu 0 4 375 384 570 572
		f 4 506 -476 -503 -505
		mu 0 4 570 383 372 572
		f 4 -159 507 508 509
		mu 0 4 386 395 571 570
		f 4 510 -472 -507 -509
		mu 0 4 571 394 383 570
		f 4 511 512 -382 513
		mu 0 4 490 571 397 489
		f 4 -469 -511 -512 514
		mu 0 4 546 394 571 490
		f 4 -506 -501 -116 -120
		mu 0 4 375 572 373 374
		f 4 -510 -504 -132 -136
		mu 0 4 386 570 384 385
		f 4 -513 -508 -148 -153
		mu 0 4 397 571 395 396
		f 4 -435 -444 515 -445
		mu 0 4 543 542 534 535
		f 4 516 -514 517 518
		mu 0 4 649 490 489 650
		f 4 519 -518 -381 520
		mu 0 4 648 650 489 491
		f 4 521 -519 522 -290
		mu 0 4 479 649 650 482
		f 4 523 -521 -383 -414
		mu 0 4 524 648 491 400
		f 4 -524 -380 -523 -520
		mu 0 4 648 524 482 650
		f 4 -522 -467 524 525
		mu 0 4 649 479 555 651
		f 4 -515 -517 -526 526
		mu 0 4 546 490 649 651
		f 4 527 528 529 530
		mu 0 4 961 960 959 958
		f 4 531 532 533 -528
		mu 0 4 961 963 962 960
		f 4 -534 534 535 -529
		mu 0 4 960 962 964 959
		f 4 536 537 -535 538
		mu 0 4 966 965 964 962
		f 4 -533 539 540 -539
		mu 0 4 962 963 967 966
		f 4 541 542 543 544
		mu 0 4 971 970 969 968
		f 4 545 546 547 -543
		mu 0 4 970 973 972 969
		f 4 548 -546 -542 549
		mu 0 4 974 973 970 971
		f 4 550 551 552 -547
		mu 0 4 973 976 975 972
		f 4 553 -551 -549 554
		mu 0 4 977 976 973 974
		f 4 555 -532 556 -548
		mu 0 4 972 963 961 969
		f 4 -544 -557 -531 557
		mu 0 4 968 969 961 958
		f 4 558 559 560 -556
		mu 0 4 972 979 978 963
		f 4 -553 561 562 -559
		mu 0 4 972 975 980 979
		f 4 563 -560 -563 564
		mu 0 4 981 978 979 980
		f 4 -540 -561 -564 565
		mu 0 4 967 963 978 981
		f 4 566 567 568 569
		mu 0 4 985 984 983 982
		f 4 570 571 -568 572
		mu 0 4 987 986 983 984
		f 4 -567 573 574 -573
		mu 0 4 984 985 988 987
		f 4 575 576 -571 577
		mu 0 4 990 989 986 987
		f 4 -575 578 579 -578
		mu 0 4 987 988 991 990
		f 4 -550 580 -572 581
		mu 0 4 974 971 983 986
		f 4 -581 -545 582 -569
		mu 0 4 983 971 968 982
		f 4 583 584 585 -582
		mu 0 4 986 993 992 974
		f 4 586 587 588 -585
		mu 0 4 993 995 994 992
		f 4 -584 -577 589 -587
		mu 0 4 993 986 989 995
		f 4 -555 -586 -589 590
		mu 0 4 977 974 992 994
		f 4 591 592 593 594
		mu 0 4 999 998 997 996
		f 4 595 596 -593 597
		mu 0 4 1001 1000 997 998
		f 4 -592 598 599 -598
		mu 0 4 998 999 1002 1001
		f 4 600 601 -596 602
		mu 0 4 1004 1003 1000 1001
		f 4 603 -603 -600 604
		mu 0 4 1005 1004 1001 1002
		f 4 605 606 607 608
		mu 0 4 1000 1007 1006 988
		f 4 -602 609 610 -606
		mu 0 4 1000 1003 1008 1007
		f 4 611 -579 -608 612
		mu 0 4 1009 991 988 1006
		f 4 -607 -611 613 -613
		mu 0 4 1006 1007 1008 1009
		f 4 -597 -609 -574 614
		mu 0 4 997 1000 988 985
		f 4 -615 -570 615 -594
		mu 0 4 997 985 982 996
		f 4 616 617 618 619
		mu 0 4 1013 1012 1011 1010
		f 4 620 621 -618 622
		mu 0 4 1015 1014 1011 1012
		f 4 623 -623 -617 624
		mu 0 4 1016 1015 1012 1013
		f 4 625 626 -621 627
		mu 0 4 1018 1017 1014 1015
		f 4 628 -628 -624 629
		mu 0 4 1019 1018 1015 1016
		f 4 630 -599 631 -622
		mu 0 4 1014 1002 999 1011
		f 4 -632 -595 632 -619
		mu 0 4 1011 999 996 1010
		f 4 633 634 635 -631
		mu 0 4 1014 1021 1020 1002
		f 4 -627 636 637 -634
		mu 0 4 1014 1017 1022 1021
		f 4 638 -605 -636 639
		mu 0 4 1023 1005 1002 1020
		f 4 -635 -638 640 -640
		mu 0 4 1020 1021 1022 1023
		f 4 641 642 643 644
		mu 0 4 1029 1028 1025 1026
		f 4 645 -645 646 647
		mu 0 4 1030 1029 1026 1027
		f 4 648 649 650 651
		mu 0 4 1032 1031 1016 1025
		f 4 652 -630 -650 653
		mu 0 4 1033 1019 1016 1031
		f 4 -643 654 655 -652
		mu 0 4 1025 1028 1034 1032
		f 4 -649 -656 656 -654
		mu 0 4 1031 1032 1034 1033
		f 4 657 658 659 660
		mu 0 4 1040 1039 1036 1037
		f 4 661 -661 662 663
		mu 0 4 1041 1040 1037 1038
		f 4 664 665 666 667
		mu 0 4 1036 1043 1042 1027
		f 4 668 -665 -659 669
		mu 0 4 1044 1043 1036 1039
		f 4 670 -648 -667 671
		mu 0 4 1045 1030 1027 1042
		f 4 -666 -669 672 -672
		mu 0 4 1042 1043 1044 1045
		f 4 673 674 675 676
		mu 0 4 1051 1050 1047 1048
		f 4 677 678 679 -677
		mu 0 4 1048 1049 1052 1051
		f 4 680 681 682 -664
		mu 0 4 1038 1054 1053 1041
		f 4 683 684 685 -681
		mu 0 4 1038 1047 1055 1054
		f 4 686 -685 -675 687
		mu 0 4 1056 1055 1047 1050
		f 4 -682 -686 -687 688
		mu 0 4 1053 1054 1055 1056
		f 4 689 690 691 692
		mu 0 4 1060 1059 1058 1057
		f 4 693 694 -691 695
		mu 0 4 1062 1061 1058 1059
		f 4 696 -696 -690 697
		mu 0 4 1063 1062 1059 1060
		f 4 698 699 700 701
		mu 0 4 1067 1066 1065 1064
		f 4 702 703 704 -700
		mu 0 4 1066 1069 1068 1065
		f 4 705 -703 -699 706
		mu 0 4 1070 1069 1066 1067
		f 4 707 -692 708 -702
		mu 0 4 1064 1057 1058 1067
		f 4 709 710 711 -709
		mu 0 4 1058 1072 1071 1067
		f 4 712 -710 -695 713
		mu 0 4 1073 1072 1058 1061
		f 4 -713 714 715 -711
		mu 0 4 1072 1073 1074 1071
		f 4 -707 -712 -716 716
		mu 0 4 1070 1067 1071 1074
		f 4 717 718 719 720
		mu 0 4 1078 1077 1076 1075
		f 4 721 722 723 -719
		mu 0 4 1077 1080 1079 1076
		f 4 -718 724 725 -722
		mu 0 4 1077 1078 1081 1080
		f 4 -701 726 -721 727
		mu 0 4 1064 1065 1078 1075
		f 4 728 729 730 -727
		mu 0 4 1065 1083 1082 1078
		f 4 731 732 -730 733
		mu 0 4 1085 1084 1082 1083
		f 4 -729 -705 734 -734
		mu 0 4 1083 1065 1068 1085
		f 4 -725 -731 -733 735
		mu 0 4 1081 1078 1082 1084
		f 4 736 737 738 739
		mu 0 4 1089 1088 1087 1086
		f 4 740 741 742 -738
		mu 0 4 1088 1091 1090 1087
		f 4 -737 743 744 -741
		mu 0 4 1088 1089 1092 1091
		f 4 745 -720 746 -740
		mu 0 4 1086 1075 1076 1089
		f 4 747 748 749 -747
		mu 0 4 1076 1094 1093 1089
		f 4 750 751 -749 752
		mu 0 4 1096 1095 1093 1094
		f 4 -744 -750 -752 753
		mu 0 4 1092 1089 1093 1095
		f 4 -748 -724 754 -753
		mu 0 4 1094 1076 1079 1096
		f 4 755 756 757 758
		mu 0 4 1100 1099 1098 1097
		f 4 759 760 -757 761
		mu 0 4 1102 1101 1098 1099
		f 4 -756 762 763 -762
		mu 0 4 1099 1100 1103 1102
		f 4 764 -739 765 -759
		mu 0 4 1097 1086 1087 1100
		f 4 766 767 768 -766
		mu 0 4 1087 1105 1104 1100
		f 4 -769 769 770 -763
		mu 0 4 1100 1104 1106 1103
		f 4 771 -770 -768 772
		mu 0 4 1107 1106 1104 1105
		f 4 -767 -743 773 -773
		mu 0 4 1105 1087 1090 1107
		f 4 774 775 776 777
		mu 0 4 1111 1110 1109 1108
		f 4 778 779 780 -775
		mu 0 4 1111 1113 1112 1110
		f 4 -781 781 782 -776
		mu 0 4 1110 1112 1114 1109
		f 4 783 -758 784 -778
		mu 0 4 1108 1097 1098 1111
		f 4 785 786 787 -785
		mu 0 4 1098 1116 1115 1111
		f 4 788 -786 -761 789
		mu 0 4 1117 1116 1098 1101
		f 4 790 -779 -788 791
		mu 0 4 1118 1113 1111 1115
		f 4 -787 -789 792 -792
		mu 0 4 1115 1116 1117 1118
		f 4 793 794 795 796
		mu 0 4 1122 1121 1120 1119
		f 4 797 798 799 -795
		mu 0 4 1121 1124 1123 1120
		f 4 800 -798 -794 801
		mu 0 4 1125 1124 1121 1122
		f 4 802 -777 803 -797
		mu 0 4 1119 1108 1109 1122
		f 4 804 805 -804 806
		mu 0 4 1127 1126 1122 1109
		f 4 807 808 809 -805
		mu 0 4 1127 1129 1128 1126
		f 4 -807 -783 810 -808
		mu 0 4 1127 1109 1114 1129
		f 4 -802 -806 -810 811
		mu 0 4 1125 1122 1126 1128
		f 4 812 813 814 815
		mu 0 4 1133 1132 1131 1130
		f 4 816 817 818 -814
		mu 0 4 1132 1135 1134 1131
		f 4 -813 819 820 -817
		mu 0 4 1132 1133 1136 1135
		f 4 -816 821 -796 822
		mu 0 4 1133 1130 1119 1120
		f 4 823 824 825 -823
		mu 0 4 1120 1138 1137 1133
		f 4 826 827 -825 828
		mu 0 4 1140 1139 1137 1138
		f 4 -820 -826 -828 829
		mu 0 4 1136 1133 1137 1139
		f 4 -824 -800 830 -829
		mu 0 4 1138 1120 1123 1140
		f 4 831 -537 832 833
		mu 0 4 1144 965 966 1145
		f 4 834 -694 835 836
		mu 0 4 1146 1061 1062 1145
		f 4 837 -714 -835 838
		mu 0 4 1147 1073 1061 1146
		f 4 839 -715 -838 840
		mu 0 4 1148 1074 1073 1147
		f 4 841 -717 -840 842
		mu 0 4 1149 1070 1074 1148
		f 4 843 -706 -842 844
		mu 0 4 1150 1069 1070 1149
		f 4 845 -704 -844 846
		mu 0 4 1151 1068 1069 1150
		f 4 847 -735 -846 848
		mu 0 4 1152 1085 1068 1151
		f 4 849 -732 -848 850
		mu 0 4 1153 1084 1085 1152
		f 4 851 -614 852 853
		mu 0 4 1179 1009 1008 1178
		f 4 854 -612 -852 855
		mu 0 4 1180 991 1009 1179
		f 4 856 -580 -855 857
		mu 0 4 1181 990 991 1180
		f 4 858 -736 -850 859
		mu 0 4 1154 1081 1084 1153
		f 4 -853 -610 860 861
		mu 0 4 1178 1008 1003 1177
		f 4 -861 -601 862 863
		mu 0 4 1177 1003 1004 1155
		f 4 -863 -604 864 865
		mu 0 4 1155 1004 1005 1156
		f 4 866 -774 867 868
		mu 0 4 1157 1107 1090 1156
		f 4 869 -772 -867 870
		mu 0 4 1158 1106 1107 1157
		f 4 871 -771 -870 872
		mu 0 4 1159 1103 1106 1158
		f 4 873 -764 -872 874
		mu 0 4 1160 1102 1103 1159
		f 4 875 -760 -874 876
		mu 0 4 1161 1101 1102 1160
		f 4 877 -790 -876 878
		mu 0 4 1162 1117 1101 1161
		f 4 879 -793 -878 880
		mu 0 4 1163 1118 1117 1162
		f 4 881 -791 -880 882
		mu 0 4 1164 1113 1118 1163
		f 4 883 -780 -882 884
		mu 0 4 1165 1112 1113 1164
		f 4 885 -782 -884 886
		mu 0 4 1166 1114 1112 1165
		f 4 887 -811 -886 888
		mu 0 4 1167 1129 1114 1166
		f 4 889 -809 -888 890
		mu 0 4 1168 1128 1129 1167
		f 4 891 -812 -890 892
		mu 0 4 1169 1125 1128 1168
		f 4 893 -801 -892 894
		mu 0 4 1170 1124 1125 1169
		f 4 895 -799 -894 896
		mu 0 4 1171 1123 1124 1170
		f 4 897 -831 -896 898
		mu 0 4 1172 1140 1123 1171
		f 4 899 -827 -898 900
		mu 0 4 1173 1139 1140 1172
		f 4 901 -830 -900 902
		mu 0 4 1174 1136 1139 1173
		f 4 903 -821 -902 904
		mu 0 4 1175 1135 1136 1174
		f 4 905 -818 -904 906
		mu 0 4 1176 1134 1135 1175
		f 4 907 -679 908 909
		mu 0 4 1143 1052 1049 1141
		f 4 -836 -697 910 -834
		mu 0 4 1145 1062 1063 1144
		f 4 -833 -541 911 -837
		mu 0 4 1145 966 967 1146
		f 4 -912 -566 912 -839
		mu 0 4 1146 967 981 1147
		f 4 -913 -565 913 -841
		mu 0 4 1147 981 980 1148
		f 4 -914 -562 914 -843
		mu 0 4 1148 980 975 1149
		f 4 -915 -552 915 -845
		mu 0 4 1149 975 976 1150
		f 4 -916 -554 916 -847
		mu 0 4 1150 976 977 1151
		f 4 -917 -591 917 -849
		mu 0 4 1151 977 994 1152
		f 4 -918 -588 918 -851
		mu 0 4 1152 994 995 1153
		f 4 -919 -590 919 -860
		mu 0 4 1153 995 989 1154
		f 4 -868 -742 920 -866
		mu 0 4 1156 1090 1091 1155
		f 4 -865 -639 921 -869
		mu 0 4 1156 1005 1023 1157
		f 4 -922 -641 922 -871
		mu 0 4 1157 1023 1022 1158
		f 4 -923 -637 923 -873
		mu 0 4 1158 1022 1017 1159
		f 4 -924 -626 924 -875
		mu 0 4 1159 1017 1018 1160
		f 4 -925 -629 925 -877
		mu 0 4 1160 1018 1019 1161
		f 4 -926 -653 926 -879
		mu 0 4 1161 1019 1033 1162
		f 4 -927 -657 927 -881
		mu 0 4 1162 1033 1034 1163
		f 4 -928 -655 928 -883
		mu 0 4 1163 1034 1028 1164
		f 4 -929 -642 929 -885
		mu 0 4 1164 1028 1029 1165
		f 4 -930 -646 930 -887
		mu 0 4 1165 1029 1030 1166
		f 4 -931 -671 931 -889
		mu 0 4 1166 1030 1045 1167
		f 4 -932 -673 932 -891
		mu 0 4 1167 1045 1044 1168
		f 4 -933 -670 933 -893
		mu 0 4 1168 1044 1039 1169
		f 4 -934 -658 934 -895
		mu 0 4 1169 1039 1040 1170
		f 4 -935 -662 935 -897
		mu 0 4 1170 1040 1041 1171
		f 4 -936 -683 936 -899
		mu 0 4 1171 1041 1053 1172
		f 4 -937 -689 937 -901
		mu 0 4 1172 1053 1056 1173
		f 4 -938 -688 938 -903
		mu 0 4 1173 1056 1050 1174
		f 4 -939 -674 939 -905
		mu 0 4 1174 1050 1051 1175
		f 4 -940 -680 940 -907
		mu 0 4 1175 1051 1052 1176
		f 4 -921 -745 941 -864
		mu 0 4 1155 1091 1092 1177
		f 4 -942 -754 942 -862
		mu 0 4 1177 1092 1095 1178
		f 4 -943 -751 943 -854
		mu 0 4 1178 1095 1096 1179
		f 4 -944 -755 944 -856
		mu 0 4 1179 1096 1079 1180
		f 4 -858 -945 -723 945
		mu 0 4 1181 1180 1079 1080
		f 4 -576 -857 946 -920
		mu 0 4 989 990 1181 1154
		f 4 -726 -859 -947 -946
		mu 0 4 1080 1081 1154 1181
		f 4 947 -538 948 949
		mu 0 4 1184 964 965 1185
		f 4 950 -536 -948 951
		mu 0 4 1183 959 964 1184
		f 4 952 -530 -951 953
		mu 0 4 1182 958 959 1183
		f 4 -949 -832 954 955
		mu 0 4 1185 965 1144 1186
		f 4 956 -698 957 958
		mu 0 4 1187 1063 1060 1188
		f 4 -958 -693 959 960
		mu 0 4 1188 1060 1057 1189
		f 4 -955 -911 -957 961
		mu 0 4 1186 1144 1063 1187
		f 4 962 -437 963 -954
		mu 0 4 1183 532 533 1182
		f 4 964 -439 -963 -952
		mu 0 4 1184 530 532 1183
		f 4 965 -441 -965 -950
		mu 0 4 1185 531 530 1184
		f 4 966 -443 -966 -956
		mu 0 4 1186 534 531 1185
		f 4 967 -446 968 -959
		mu 0 4 1188 536 535 1187
		f 4 969 -448 -968 -961
		mu 0 4 1189 537 536 1188
		f 4 -960 970 -450 -970
		mu 0 4 1189 1057 547 537
		f 4 -708 971 972 -971
		mu 0 4 1057 1064 1191 547
		f 4 973 974 -972 -728
		mu 0 4 1075 1192 1191 1064
		f 4 975 976 -974 -746
		mu 0 4 1086 1193 1192 1075
		f 4 977 978 -976 -765
		mu 0 4 1097 1194 1193 1086
		f 4 979 980 -978 -784
		mu 0 4 1108 1195 1194 1097
		f 4 981 982 -980 -803
		mu 0 4 1119 1196 1195 1108
		f 4 983 984 -982 -822
		mu 0 4 1130 1197 1196 1119
		f 4 985 986 -984 -815
		mu 0 4 1131 1198 1197 1130
		f 4 987 988 989 990
		mu 0 4 1200 1199 1190 1046
		f 4 991 992 -991 993
		mu 0 4 1035 1201 1200 1046
		f 4 994 -992 995 996
		mu 0 4 1202 1201 1035 1024
		f 4 997 998 -997 999
		mu 0 4 1010 1203 1202 1024
		f 4 1000 1001 -998 -633
		mu 0 4 996 1204 1203 1010
		f 4 1002 1003 -1001 -616
		mu 0 4 982 1205 1204 996
		f 4 1004 1005 -1003 -583
		mu 0 4 968 1206 1205 982
		f 4 1006 1007 -1005 -558
		mu 0 4 958 1207 1206 968
		f 4 1008 1009 -1007 -953
		mu 0 4 1182 1208 1207 958
		f 4 -492 1010 -1009 -964
		mu 0 4 533 567 1208 1182
		f 4 1011 -494 1012 -987
		mu 0 4 1198 556 568 1197
		f 4 -983 -985 -1013 1013
		mu 0 4 1195 1196 1197 568
		f 4 -975 1014 -497 -973
		mu 0 4 1191 1192 569 547
		f 4 -981 -1014 -500 1015
		mu 0 4 1194 1195 568 569
		f 4 -977 -979 -1016 -1015
		mu 0 4 1192 1193 1194 569
		f 4 -651 -625 1016 1017
		mu 0 4 1025 1016 1013 1211
		f 4 -1017 -620 -1000 1018
		mu 0 4 1211 1013 1010 1024
		f 4 1019 1020 1021 -668
		mu 0 4 1027 1211 1209 1036
		f 4 -1021 -1019 -996 1022
		mu 0 4 1209 1211 1024 1035
		f 4 1023 1024 1025 -684
		mu 0 4 1038 1209 1210 1047;
	setAttr ".fc[500:999]"
		f 4 -1025 -1023 -994 1026
		mu 0 4 1210 1209 1035 1046
		f 4 1027 -909 1028 1029
		mu 0 4 1142 1141 1049 1210
		f 4 1030 -1030 -1027 -990
		mu 0 4 1190 1142 1210 1046
		f 4 -647 -644 -1018 -1020
		mu 0 4 1027 1026 1025 1211
		f 4 -663 -660 -1022 -1024
		mu 0 4 1038 1037 1036 1209
		f 4 -678 -676 -1026 -1029
		mu 0 4 1049 1048 1047 1210
		f 4 -969 -516 -967 -962
		mu 0 4 1187 535 534 1186
		f 4 1031 1032 -1028 1033
		mu 0 4 1288 1289 1141 1142
		f 4 1034 -910 -1033 1035
		mu 0 4 1287 1143 1141 1289
		f 4 -819 1036 -1032 1037
		mu 0 4 1131 1134 1289 1288
		f 4 -941 -908 -1035 1038
		mu 0 4 1176 1052 1143 1287
		f 4 -1036 -1037 -906 -1039
		mu 0 4 1287 1289 1134 1176
		f 4 1039 1040 -986 -1038
		mu 0 4 1288 1290 1198 1131
		f 4 1041 -1040 -1034 -1031
		mu 0 4 1190 1290 1288 1142
		f 4 1042 1043 1044 1045
		mu 0 4 5 36 29 124
		f 4 1046 1047 1048 1049
		mu 0 4 102 82 115 121
		f 4 1050 1051 1052 1053
		mu 0 4 0 1 157 62
		f 4 1054 1055 1056 1057
		mu 0 4 154 100 131 164
		f 4 1058 -1043 1059 -1047
		mu 0 4 102 36 5 82
		f 4 -1051 1060 -1055 1061
		mu 0 4 1 0 100 154
		f 4 1062 1063 1064 1065
		mu 0 4 14 101 57 144
		f 4 1066 1067 1068 1069
		mu 0 4 119 86 133 77
		f 4 1070 1071 1072 1073
		mu 0 4 3 48 165 109
		f 4 1074 -1067 1075 -1071
		mu 0 4 3 86 119 48
		f 4 1076 1077 -1063 1078
		mu 0 4 145 37 101 14
		f 4 1079 -1077 1080 1081
		mu 0 4 20 37 145 155
		f 4 -1078 1082 1083 1084
		mu 0 4 101 37 22 43
		f 4 -1072 1085 1086 1087
		mu 0 4 165 48 72 7
		f 4 -1083 -1080 1088 1089
		mu 0 4 22 37 20 90
		f 4 -1064 -1085 1090 1091
		mu 0 4 57 101 43 9
		f 4 -1086 -1076 1092 1093
		mu 0 4 72 48 119 95
		f 4 -1070 1094 1095 -1093
		mu 0 4 119 77 21 95
		f 4 1096 -1096 1097 -1056
		mu 0 4 100 95 21 131
		f 4 -1091 1098 -1050 1099
		mu 0 4 9 43 102 121
		f 4 -1090 1100 -1044 1101
		mu 0 4 22 90 29 36
		f 4 1102 -1094 -1097 -1061
		mu 0 4 0 72 95 100
		f 4 1103 -1087 -1103 -1054
		mu 0 4 62 7 72 0
		f 4 -1084 -1102 -1059 -1099
		mu 0 4 43 22 36 102
		f 4 1104 1105 1106 1107
		mu 0 4 38 110 52 34
		f 4 1108 1109 -1108 1110
		mu 0 4 26 41 1291 1292
		f 4 1111 1112 1113 1114
		mu 0 4 111 96 39 151
		f 4 1115 1116 1117 1118
		mu 0 4 135 80 142 88
		f 4 -1119 1119 1120 1121
		mu 0 4 135 88 103 137
		f 4 -1113 1122 1123 1124
		mu 0 4 39 96 150 132
		f 4 -1124 1125 -1106 1126
		mu 0 4 132 150 52 110
		f 4 1127 -1121 1128 -1109
		mu 0 4 26 137 103 41
		f 4 1129 1130 1131 1132
		mu 0 4 168 167 108 32
		f 4 1133 1134 -1100 1135
		mu 0 4 63 93 9 121
		f 4 1136 1137 -1092 -1135
		mu 0 4 93 85 57 9
		f 4 -1138 1138 1139 -1065
		mu 0 4 57 85 98 144
		f 4 -1139 1140 -1069 1141
		mu 0 4 98 85 77 133
		f 4 -1141 -1137 1142 -1095
		mu 0 4 77 85 93 21
		f 4 -1143 -1134 1143 -1098
		mu 0 4 21 93 63 131
		f 4 -1136 -1049 1144 1145
		mu 0 4 63 121 115 6
		f 4 -1057 -1144 -1146 1146
		mu 0 4 164 131 63 6
		f 4 1147 1148 1149 1150
		mu 0 4 92 76 116 70
		f 4 1151 -1148 1152 1153
		mu 0 4 75 76 92 123
		f 4 1154 1155 1156 -1153
		mu 0 4 92 81 17 123
		f 4 1157 1158 -1155 -1151
		mu 0 4 70 73 81 92
		f 4 1159 1160 -1130 1161
		mu 0 4 25 78 167 168
		f 4 1162 1163 1164 1165
		mu 0 4 136 99 166 170
		f 4 1166 1167 -1165 1168
		mu 0 4 23 114 170 166
		f 4 1169 1170 1171 1172
		mu 0 4 56 4 44 143
		f 4 1173 1174 1175 1176
		mu 0 4 138 53 50 40
		f 4 1177 1178 1179 1180
		mu 0 4 11 84 18 64
		f 4 1181 1182 1183 -1171
		mu 0 4 4 16 65 44
		f 4 1184 1185 1186 -1175
		mu 0 4 53 19 118 50
		f 4 -1123 1187 1188 1189
		mu 0 4 150 96 117 161
		f 4 -1188 -1112 1190 1191
		mu 0 4 117 96 111 153
		f 4 -1116 1192 1193 1194
		mu 0 4 80 135 87 140
		f 4 -1122 1195 1196 -1193
		mu 0 4 135 137 54 87
		f 4 -1107 1197 1198 1199
		mu 0 4 34 52 74 79
		f 4 1200 -1111 -1200 1201
		mu 0 4 91 26 1293 1294
		f 4 -1196 -1128 -1201 1202
		mu 0 4 54 137 26 91
		f 4 -1126 -1190 1203 -1198
		mu 0 4 52 150 161 74
		f 4 -1101 1204 1205 1206
		mu 0 4 29 90 141 147
		f 4 -1073 1207 1208 1209
		mu 0 4 109 165 66 148
		f 4 -1205 -1089 1210 1211
		mu 0 4 141 90 20 51
		f 4 1212 -1053 1213 1214
		mu 0 4 27 62 157 112
		f 4 1215 -1104 -1213 1216
		mu 0 4 28 7 62 27
		f 4 -1045 -1207 1217 1218
		mu 0 4 124 29 147 89
		f 4 -1088 -1216 1219 -1208
		mu 0 4 165 7 28 66
		f 4 -1211 -1082 1220 1221
		mu 0 4 51 20 155 13
		f 4 1222 1223 1224 1225
		mu 0 4 67 15 152 126
		f 4 1226 1227 1228 1229
		mu 0 4 30 128 94 104
		f 4 -1224 1230 1231 1232
		mu 0 4 152 15 47 83
		f 4 1233 1234 1235 -1229
		mu 0 4 94 162 105 104
		f 4 1236 -1127 1237 -1156
		mu 0 4 81 132 110 17
		f 4 1238 -1120 1239 -1149
		mu 0 4 76 103 88 116
		f 4 1240 -1114 1241 1242
		mu 0 4 68 151 39 73
		f 4 -1118 1243 1244 -1240
		mu 0 4 88 142 106 116
		f 4 -1129 -1239 -1152 1245
		mu 0 4 41 103 76 75
		f 4 -1105 1246 1247 -1238
		mu 0 4 110 38 129 17
		f 4 -1242 -1125 -1237 -1159
		mu 0 4 73 39 132 81
		f 4 -1110 -1246 1248 -1247
		mu 0 4 1295 41 75 129
		f 4 -1234 1249 1250 1251
		mu 0 4 162 94 136 33
		f 4 1252 -1223 1253 1254
		mu 0 4 114 15 67 159
		f 4 -1231 -1253 -1167 1255
		mu 0 4 47 15 114 23
		f 4 -1250 -1228 1256 -1163
		mu 0 4 136 94 128 99
		f 4 -1182 1257 1258 1259
		mu 0 4 16 4 146 69
		f 4 -1132 1260 -1178 1261
		mu 0 4 32 108 84 11
		f 4 1262 1263 1264 -1185
		mu 0 4 53 25 59 19
		f 4 -1258 -1170 1265 1266
		mu 0 4 146 4 56 60
		f 4 -1174 1267 -1160 -1263
		mu 0 4 53 138 78 25
		f 4 1268 1269 -1219 1270
		mu 0 4 130 158 124 89
		f 4 1271 1272 1273 -1060
		mu 0 4 5 58 42 82
		f 4 -1046 -1270 1274 -1272
		mu 0 4 5 124 158 58
		f 4 -1273 1275 -1062 1276
		mu 0 4 1296 1297 1 154
		f 4 -1276 -1275 1277 -1052
		mu 0 4 1 1298 1299 157
		f 4 -1278 -1269 1278 -1214
		mu 0 4 157 1300 1301 112
		f 4 -1048 -1274 1279 1280
		mu 0 4 115 82 42 35
		f 4 -1277 -1058 1281 -1280
		mu 0 4 1302 154 164 35
		f 4 1282 1283 1284 -1074
		mu 0 4 109 156 139 3
		f 4 1285 1286 -1066 1287
		mu 0 4 149 12 14 144
		f 4 1288 1289 1290 -1221
		mu 0 4 155 1303 55 13
		f 4 1291 1292 -1079 -1287
		mu 0 4 12 1304 145 14
		f 4 -1290 -1283 -1210 1293
		mu 0 4 1305 156 109 148
		f 4 -1068 1294 -1286 1295
		mu 0 4 133 86 1306 149
		f 4 -1293 -1284 -1289 -1081
		mu 0 4 145 1307 1308 155
		f 4 -1285 -1292 -1295 -1075
		mu 0 4 3 139 1309 86
		f 4 1296 1297 1298 1299
		mu 0 4 24 71 10 31
		f 4 1300 -1298 1301 1302
		mu 0 4 45 10 71 2
		f 4 1303 -1297 1304 -1183
		mu 0 4 16 1310 1311 65
		f 4 -1302 -1304 -1260 1305
		mu 0 4 2 1312 16 69
		f 4 -1243 -1158 1306 1307
		mu 0 4 68 73 70 222
		f 4 -1245 1308 -1307 -1150
		mu 0 4 116 106 222 70
		f 4 -1266 1309 -1261 1310
		mu 0 4 60 56 84 108
		f 4 -1310 -1173 1311 -1179
		mu 0 4 84 56 143 18
		f 4 1312 -1181 1313 -1186
		mu 0 4 1313 11 64 1314
		f 4 1314 -1262 -1313 -1265
		mu 0 4 59 32 11 1315
		f 4 -1255 1315 1316 -1168
		mu 0 4 114 159 223 170
		f 4 -1251 -1166 -1317 1317
		mu 0 4 33 136 170 223
		f 4 1318 1319 1320 1321
		mu 0 4 227 226 113 122
		f 4 1322 -1319 1323 1324
		mu 0 4 125 226 227 46
		f 4 1325 1326 1327 -1322
		mu 0 4 122 8 224 227
		f 4 1328 1329 -1324 -1328
		mu 0 4 224 134 46 227
		f 4 1330 1331 1332 1333
		mu 0 4 232 233 236 237
		f 4 1334 1335 -1333 1336
		mu 0 4 229 228 237 236
		f 4 1337 1338 1339 -1334
		mu 0 4 237 225 160 232
		f 4 -1332 1340 -1320 1341
		mu 0 4 236 233 113 226
		f 4 1342 -1338 -1336 1343
		mu 0 4 61 225 237 228
		f 4 -1342 -1323 1344 -1337
		mu 0 4 236 226 125 229
		f 4 1345 1346 1347 1348
		mu 0 4 127 120 238 239
		f 4 -1348 1349 1350 1351
		mu 0 4 239 238 49 163
		f 4 1352 -1350 1353 1354
		mu 0 4 107 49 238 240
		f 4 -1354 -1347 1355 1356
		mu 0 4 240 238 120 97
		f 4 1357 1358 1359 1360
		mu 0 4 234 235 242 243
		f 4 -1360 1361 1362 1363
		mu 0 4 243 242 230 231
		f 4 1364 -1349 1365 -1359
		mu 0 4 235 127 239 242
		f 4 -1366 -1352 1366 -1362
		mu 0 4 242 239 163 230
		f 4 1367 -1364 1368 1369
		mu 0 4 241 243 231 221
		f 4 1370 1371 -1361 -1368
		mu 0 4 241 220 234 243
		f 4 1372 1373 1374 -1321
		mu 0 4 113 249 253 122
		f 4 1375 -1326 -1375 1376
		mu 0 4 245 8 122 253
		f 4 -1374 1377 -1351 1378
		mu 0 4 1316 1317 163 49
		f 4 -1379 -1353 1379 -1377
		mu 0 4 1318 49 107 1319
		f 4 -1339 1380 1381 1382
		mu 0 4 160 225 252 244
		f 4 1383 1384 -1330 1385
		mu 0 4 254 250 46 134
		f 4 1386 -1384 1387 -1356
		mu 0 4 120 1320 1321 97
		f 4 -1325 -1385 1388 1389
		mu 0 4 125 46 250 251
		f 4 -1387 -1346 1390 -1389
		mu 0 4 1322 120 127 1323
		f 4 -1343 1391 1392 -1381
		mu 0 4 225 61 246 252
		f 4 1393 1394 -1358 1395
		mu 0 4 256 255 235 234
		f 4 1396 1397 -1363 1398
		mu 0 4 247 248 231 230
		f 4 -1394 1399 -1335 1400
		mu 0 4 1324 256 228 229
		f 4 -1397 1401 -1331 1402
		mu 0 4 248 1325 233 232
		f 4 -1402 1403 -1373 -1341
		mu 0 4 233 1326 249 113
		f 4 -1399 -1367 -1378 -1404
		mu 0 4 247 230 163 1327
		f 4 -1383 1404 -1403 -1340
		mu 0 4 160 244 248 232
		f 4 1405 -1369 -1398 -1405
		mu 0 4 244 221 231 248
		f 4 1406 -1401 -1345 -1390
		mu 0 4 251 1328 229 125
		f 4 -1365 -1395 -1407 -1391
		mu 0 4 127 235 255 1329
		f 4 1407 -1392 -1344 -1400
		mu 0 4 256 246 61 228
		f 4 -1372 1408 -1408 -1396
		mu 0 4 234 220 246 256
		f 4 -1393 -1409 -1371 1409
		mu 0 4 252 246 220 241
		f 4 -1410 -1370 -1406 -1382
		mu 0 4 252 241 221 244
		f 4 1410 1411 1412 1413
		mu 0 4 276 275 263 265
		f 4 1414 -1411 1415 1416
		mu 0 4 266 275 276 258
		f 4 1417 1418 1419 -1414
		mu 0 4 265 257 273 276
		f 4 1420 1421 -1416 -1420
		mu 0 4 273 268 258 276
		f 4 1422 1423 1424 1425
		mu 0 4 281 282 285 286
		f 4 1426 1427 -1425 1428
		mu 0 4 278 277 286 285
		f 4 1429 1430 1431 -1426
		mu 0 4 286 274 269 281
		f 4 -1424 1432 -1412 1433
		mu 0 4 285 282 263 275
		f 4 1434 -1430 -1428 1435
		mu 0 4 260 274 286 277
		f 4 -1434 -1415 1436 -1429
		mu 0 4 285 275 266 278
		f 4 1437 1438 1439 1440
		mu 0 4 267 264 287 288
		f 4 -1440 1441 1442 1443
		mu 0 4 288 287 259 270
		f 4 1444 -1442 1445 1446
		mu 0 4 262 259 287 289
		f 4 -1446 -1439 1447 1448
		mu 0 4 289 287 264 261
		f 4 1449 1450 1451 1452
		mu 0 4 283 284 291 292
		f 4 -1452 1453 1454 1455
		mu 0 4 292 291 279 280
		f 4 1456 -1441 1457 -1451
		mu 0 4 284 267 288 291
		f 4 -1458 -1444 1458 -1454
		mu 0 4 291 288 270 279
		f 4 1459 -1456 1460 1461
		mu 0 4 290 292 280 272
		f 4 1462 1463 -1453 -1460
		mu 0 4 290 271 283 292
		f 4 1464 1465 1466 -1413
		mu 0 4 263 298 302 265
		f 4 1467 -1418 -1467 1468
		mu 0 4 294 257 265 302
		f 4 -1466 1469 -1443 1470
		mu 0 4 1330 1331 270 259
		f 4 -1471 -1445 1471 -1469
		mu 0 4 1332 259 262 1333
		f 4 -1431 1472 1473 1474
		mu 0 4 269 274 301 293
		f 4 1475 1476 -1422 1477
		mu 0 4 303 299 258 268
		f 4 1478 -1476 1479 -1448
		mu 0 4 264 1334 1335 261
		f 4 -1417 -1477 1480 1481
		mu 0 4 266 258 299 300
		f 4 -1479 -1438 1482 -1481
		mu 0 4 1336 264 267 1337
		f 4 -1435 1483 1484 -1473
		mu 0 4 274 260 295 301
		f 4 1485 1486 -1450 1487
		mu 0 4 305 304 284 283
		f 4 1488 1489 -1455 1490
		mu 0 4 296 297 280 279
		f 4 -1486 1491 -1427 1492
		mu 0 4 1338 305 277 278
		f 4 -1489 1493 -1423 1494
		mu 0 4 297 1339 282 281
		f 4 -1494 1495 -1465 -1433
		mu 0 4 282 1340 298 263
		f 4 -1491 -1459 -1470 -1496
		mu 0 4 296 279 270 1341
		f 4 -1475 1496 -1495 -1432
		mu 0 4 269 293 297 281
		f 4 1497 -1461 -1490 -1497
		mu 0 4 293 272 280 297
		f 4 1498 -1493 -1437 -1482
		mu 0 4 300 1342 278 266
		f 4 -1457 -1487 -1499 -1483
		mu 0 4 267 284 304 1343
		f 4 1499 -1484 -1436 -1492
		mu 0 4 305 295 260 277
		f 4 -1464 1500 -1500 -1488
		mu 0 4 283 271 295 305
		f 4 -1485 -1501 -1463 1501
		mu 0 4 301 295 271 290
		f 4 -1502 -1462 -1498 -1474
		mu 0 4 301 290 272 293
		f 4 1502 1503 1504 1505
		mu 0 4 594 574 604 613
		f 4 -1505 1506 1507 1508
		mu 0 4 613 604 608 601
		f 4 1509 1510 1511 1512
		mu 0 4 607 611 588 602
		f 4 -1513 1513 1514 1515
		mu 0 4 607 602 599 577
		f 4 1516 1517 1518 -1511
		mu 0 4 611 579 580 588
		f 4 -1519 1519 1520 1521
		mu 0 4 588 580 610 591
		f 4 -1522 1522 1523 -1512
		mu 0 4 588 591 605 602
		f 4 -1514 -1524 1524 1525
		mu 0 4 599 602 605 582
		f 4 1526 -1506 1527 1528
		mu 0 4 592 594 613 576
		f 4 1529 1530 1531 1532
		mu 0 4 584 600 596 593
		f 4 1533 1534 1535 1536
		mu 0 4 598 597 587 603
		f 4 -1531 1537 -1534 1538
		mu 0 4 596 600 597 598
		f 4 -1536 1539 1540 1541
		mu 0 4 603 587 615 616
		f 4 1542 1543 -1537 1544
		mu 0 4 575 617 598 603
		f 4 1545 1546 1547 -1507
		mu 0 4 1344 618 617 1345
		f 4 -1547 1548 -1539 -1544
		mu 0 4 617 618 596 598
		f 4 1549 1550 -1532 -1549
		mu 0 4 618 619 593 596
		f 4 1551 -1550 -1546 -1504
		mu 0 4 1346 619 618 1347
		f 4 1552 -1548 -1543 1553
		mu 0 4 620 1348 617 575
		f 4 1554 1555 -1517 1556
		mu 0 4 622 621 1349 1350
		f 4 1557 -1555 1558 -1530
		mu 0 4 584 621 622 600
		f 4 -1535 1559 1560 1561
		mu 0 4 587 597 623 614
		f 4 -1559 1562 -1560 -1538
		mu 0 4 600 622 623 597
		f 4 -1557 -1510 1563 -1563
		mu 0 4 622 1351 1352 623
		f 4 -1516 1564 -1561 -1564
		mu 0 4 1353 577 614 623
		f 4 -1518 1565 1566 1567
		mu 0 4 580 579 595 590
		f 4 -1556 1568 1569 -1566
		mu 0 4 1354 621 585 1355
		f 4 -1551 1570 1571 1572
		mu 0 4 593 619 612 586
		f 4 1573 -1527 1574 1575
		mu 0 4 581 594 592 609
		f 4 -1520 -1568 1576 1577
		mu 0 4 610 580 590 583
		f 4 -1533 -1573 1578 1579
		mu 0 4 584 593 586 589
		f 4 -1571 -1552 1580 1581
		mu 0 4 612 619 1356 573
		f 4 -1581 -1503 -1574 1582
		mu 0 4 1357 574 594 581
		f 4 -1558 -1580 1583 -1569
		mu 0 4 621 584 589 585
		f 4 1584 1585 1586 -1523
		mu 0 4 591 576 606 605
		f 4 -1525 -1587 1587 1588
		mu 0 4 582 605 606 578
		f 4 -1521 1589 -1529 -1585
		mu 0 4 591 610 592 576
		f 4 -1541 1590 -1589 1591
		mu 0 4 616 615 582 578
		f 4 -1590 -1578 1592 -1575
		mu 0 4 592 610 583 609
		f 4 1593 -1540 -1562 -1565
		mu 0 4 577 615 587 614
		f 4 -1545 -1542 1594 -1554
		mu 0 4 575 603 616 620
		f 4 -1591 -1594 -1515 -1526
		mu 0 4 582 615 577 599
		f 4 -1528 -1509 1595 -1586
		mu 0 4 576 613 601 606
		f 4 -1588 -1596 1596 1597
		mu 0 4 578 606 601 624
		f 4 -1595 -1592 -1598 1598
		mu 0 4 620 616 578 624
		f 4 -1597 -1508 -1553 -1599
		mu 0 4 624 601 608 620
		f 4 1599 1600 1601 1602
		mu 0 4 180 183 184 177
		f 4 1603 1604 1605 1606
		mu 0 4 171 190 196 215
		f 4 -1602 1607 1608 1609
		mu 0 4 177 184 198 212
		f 4 1610 1611 1612 1613
		mu 0 4 218 212 195 217
		f 4 -1607 1614 1615 1616
		mu 0 4 171 215 214 207
		f 4 -1612 -1609 1617 1618
		mu 0 4 195 212 198 213
		f 4 -1615 1619 -1614 1620
		mu 0 4 214 215 218 217
		f 4 -1619 1621 1622 1623
		mu 0 4 195 213 188 176
		f 4 1624 1625 -1616 1626
		mu 0 4 182 205 207 214
		f 4 -1627 -1621 1627 1628
		mu 0 4 182 214 217 216
		f 4 -1613 -1624 1629 -1628
		mu 0 4 217 195 176 216
		f 4 -1620 -1606 1630 1631
		mu 0 4 218 215 196 219
		f 4 -1610 -1611 -1632 1632
		mu 0 4 177 212 218 219
		f 4 1633 1634 1635 1636
		mu 0 4 193 206 197 204
		f 4 1637 1638 1639 1640
		mu 0 4 172 189 186 203
		f 4 1641 1642 1643 1644
		mu 0 4 185 174 173 200
		f 4 1645 1646 1647 -1640
		mu 0 4 186 175 209 203
		f 4 1648 1649 1650 -1647
		mu 0 4 175 210 208 209
		f 4 -1636 1651 -1645 1652
		mu 0 4 204 197 185 200
		f 4 1653 1654 -1638 1655
		mu 0 4 191 192 189 172
		f 4 1656 -1652 1657 1658
		mu 0 4 178 185 197 201
		f 4 1659 1660 -1655 1661
		mu 0 4 181 187 189 192
		f 4 1662 1663 -1639 -1661
		mu 0 4 187 211 186 189
		f 4 -1658 -1635 1664 1665
		mu 0 4 201 197 206 199
		f 4 -1649 1666 1667 1668
		mu 0 4 210 175 202 194
		f 4 -1664 1669 -1667 -1646
		mu 0 4 186 211 202 175
		f 4 -1642 -1657 1670 1671
		mu 0 4 174 185 178 179
		f 4 1672 -1601 1673 -1668
		mu 0 4 202 184 183 194
		f 4 -1618 1674 -1663 1675
		mu 0 4 213 198 211 187
		f 4 -1675 -1608 -1673 -1670
		mu 0 4 211 198 184 202
		f 4 1676 -1617 1677 -1659
		mu 0 4 201 171 207 178
		f 4 -1676 -1660 1678 -1622
		mu 0 4 213 187 181 188
		f 4 -1678 -1626 1679 -1671
		mu 0 4 178 207 205 179
		f 4 -1604 -1677 -1666 1680
		mu 0 4 190 171 201 199
		f 4 -1681 1681 -1674 1682
		mu 0 4 190 199 1358 1359
		f 4 1683 -1634 1684 -1650
		mu 0 4 1360 206 193 1361
		f 4 -1682 -1665 -1684 -1669
		mu 0 4 1362 199 206 1363
		f 4 -1605 -1683 -1600 1685
		mu 0 4 196 190 1364 180
		f 4 -1633 -1631 -1686 -1603
		mu 0 4 177 219 196 180
		f 4 -1623 1686 1687 1688
		mu 0 4 176 188 625 626
		f 4 -1688 1689 -1625 1690
		mu 0 4 626 1365 205 182
		f 4 -1643 1691 1692 1693
		mu 0 4 173 174 627 628
		f 4 -1693 1694 -1654 1695
		mu 0 4 1366 1367 192 191
		f 4 -1672 1696 1697 -1692
		mu 0 4 174 179 629 627
		f 4 -1698 1698 -1662 -1695
		mu 0 4 1368 1369 181 192
		f 4 -1679 -1699 1699 -1687
		mu 0 4 188 181 1370 625
		f 4 -1700 -1697 -1680 -1690
		mu 0 4 1371 629 179 205
		f 4 -1629 -1630 -1689 -1691
		mu 0 4 182 216 176 626
		f 4 -1147 -1145 -1281 -1282
		mu 0 4 164 6 115 35
		f 4 -1142 -1296 -1288 -1140
		mu 0 4 98 133 149 144
		f 4 1700 1701 1702 1703
		mu 0 4 634 635 633 632
		f 4 1704 1705 -1703 1706
		mu 0 4 636 637 632 633
		f 4 -1701 1707 1708 1709
		mu 0 4 635 634 631 630
		f 4 -1706 1710 -1161 1711
		mu 0 4 632 637 167 78
		f 4 -1177 1712 -1708 1713
		mu 0 4 138 40 631 634
		f 4 -1704 -1712 -1268 -1714
		mu 0 4 634 632 78 138
		f 4 -1707 1714 1715 1716
		mu 0 4 636 633 45 169
		f 4 -1715 -1702 1717 -1301
		mu 0 4 45 633 635 10
		f 4 -1710 1718 -1299 -1718
		mu 0 4 635 630 31 10
		f 4 1719 -1267 1720 -1705
		mu 0 4 636 146 60 637
		f 4 -1720 -1717 1721 -1259
		mu 0 4 146 636 169 69
		f 4 -1721 -1311 -1131 -1711
		mu 0 4 637 60 108 167
		f 4 -1722 -1716 -1303 -1306
		mu 0 4 69 169 45 2
		f 4 -1162 -1133 -1315 -1264
		mu 0 4 25 168 32 59
		f 4 -1154 -1157 -1248 -1249
		mu 0 4 75 123 17 129
		f 4 1722 1723 1724 -1117
		mu 0 4 80 640 638 142
		f 4 -1723 -1195 1725 1726
		mu 0 4 640 80 140 639
		f 4 -1725 1727 1728 -1244
		mu 0 4 142 638 641 106
		f 4 1729 -1727 1730 -1191
		mu 0 4 111 1372 1373 153
		f 4 -1724 -1730 -1115 1731
		mu 0 4 1374 1375 111 151
		f 4 -1728 -1732 -1241 1732
		mu 0 4 641 1376 151 68
		f 4 -1308 -1309 -1729 -1733
		mu 0 4 68 222 106 641
		f 4 -1232 1733 1734 1735
		mu 0 4 83 47 642 643
		f 4 1736 -1227 1737 -1735
		mu 0 4 1377 128 30 1378
		f 4 -1734 -1256 1738 1739
		mu 0 4 642 47 23 644
		f 4 -1737 -1740 1740 -1257
		mu 0 4 128 1379 644 99
		f 4 -1169 -1164 -1741 -1739
		mu 0 4 23 166 99 644
		f 4 1741 1742 -1226 1743
		mu 0 4 646 645 67 126
		f 4 -1235 1744 -1742 1745
		mu 0 4 105 162 1380 1381
		f 4 -1745 -1252 1746 1747
		mu 0 4 1382 162 33 647
		f 4 -1254 -1743 -1748 1748
		mu 0 4 159 67 645 647
		f 4 -1747 -1318 -1316 -1749
		mu 0 4 647 33 223 159
		f 4 1749 1750 1751 1752
		mu 0 4 657 776 681 688
		f 4 1753 1754 1755 1756
		mu 0 4 754 773 767 734
		f 4 1757 1758 1759 1760
		mu 0 4 652 714 809 653
		f 4 1761 1762 1763 1764
		mu 0 4 806 816 783 752
		f 4 -1757 1765 -1753 1766
		mu 0 4 754 734 657 688
		f 4 1767 -1765 1768 -1761
		mu 0 4 653 806 752 652
		f 4 1769 1770 1771 1772
		mu 0 4 666 796 709 753
		f 4 1773 1774 1775 1776
		mu 0 4 771 729 785 738
		f 4 1777 1778 1779 1780
		mu 0 4 655 761 817 700
		f 4 -1781 1781 -1777 1782
		mu 0 4 655 700 771 738
		f 4 1783 -1773 1784 1785
		mu 0 4 797 666 753 689
		f 4 1786 1787 -1786 1788
		mu 0 4 672 807 797 689
		f 4 1789 1790 1791 -1785
		mu 0 4 753 695 674 689
		f 4 1792 1793 1794 -1780
		mu 0 4 817 659 724 700
		f 4 1795 1796 -1789 -1792
		mu 0 4 674 742 672 689
		f 4 1797 1798 -1790 -1772
		mu 0 4 709 661 695 753
		f 4 1799 1800 -1782 -1795
		mu 0 4 724 747 771 700
		f 4 -1801 1801 1802 -1774
		mu 0 4 771 747 673 729
		f 4 -1764 1803 -1802 1804
		mu 0 4 752 783 673 747
		f 4 1805 -1754 1806 -1799
		mu 0 4 661 773 754 695
		f 4 1807 -1752 1808 -1796
		mu 0 4 674 688 681 742
		f 4 -1769 -1805 -1800 1809
		mu 0 4 652 752 747 724
		f 4 -1758 -1810 -1794 1810
		mu 0 4 714 652 724 659
		f 4 -1807 -1767 -1808 -1791
		mu 0 4 695 754 688 674
		f 4 1811 1812 1813 1814
		mu 0 4 690 686 704 762
		f 4 1815 -1812 1816 1817
		mu 0 4 678 1383 1384 693
		f 4 1818 1819 1820 1821
		mu 0 4 763 803 691 748
		f 4 1822 1823 1824 1825
		mu 0 4 787 740 794 732
		f 4 1826 1827 1828 -1823
		mu 0 4 787 789 755 740
		f 4 1829 1830 1831 -1821
		mu 0 4 691 784 802 748
		f 4 1832 -1814 1833 -1831
		mu 0 4 784 762 704 802
		f 4 -1818 1834 -1828 1835
		mu 0 4 678 693 755 789
		f 4 1836 1837 1838 1839
		mu 0 4 820 684 760 819
		f 4 1840 -1806 1841 1842
		mu 0 4 715 773 661 745
		f 4 -1842 -1798 1843 1844
		mu 0 4 745 661 709 737
		f 4 -1771 1845 1846 -1844
		mu 0 4 709 796 750 737
		f 4 1847 -1775 1848 -1847
		mu 0 4 750 785 729 737
		f 4 -1803 1849 -1845 -1849
		mu 0 4 729 673 745 737
		f 4 -1804 1850 -1843 -1850
		mu 0 4 673 783 715 745
		f 4 1851 1852 -1755 -1841
		mu 0 4 715 658 767 773
		f 4 1853 -1852 -1851 -1763
		mu 0 4 816 658 715 783
		f 4 1854 1855 1856 1857
		mu 0 4 744 722 768 728
		f 4 1858 1859 -1858 1860
		mu 0 4 727 775 744 728
		f 4 -1860 1861 1862 1863
		mu 0 4 744 775 669 733
		f 4 -1855 -1864 1864 1865
		mu 0 4 722 744 733 725
		f 4 1866 -1840 1867 1868
		mu 0 4 677 820 819 730
		f 4 1869 1870 1871 1872
		mu 0 4 788 822 818 751
		f 4 1873 -1871 1874 1875
		mu 0 4 675 818 822 766
		f 4 1876 1877 1878 1879
		mu 0 4 708 795 696 656
		f 4 1880 1881 1882 1883
		mu 0 4 790 692 702 705
		f 4 1884 1885 1886 1887
		mu 0 4 663 716 670 736
		f 4 -1879 1888 1889 1890
		mu 0 4 656 696 717 668
		f 4 -1883 1891 1892 1893
		mu 0 4 705 702 770 671
		f 4 1894 1895 1896 -1832
		mu 0 4 802 813 769 748
		f 4 1897 1898 -1822 -1897
		mu 0 4 769 805 763 748
		f 4 1899 1900 1901 -1826
		mu 0 4 732 792 739 787
		f 4 -1902 1902 1903 -1827
		mu 0 4 787 739 706 789
		f 4 1904 1905 1906 -1813
		mu 0 4 686 731 726 704
		f 4 1907 -1905 -1816 1908
		mu 0 4 743 1385 1386 678
		f 4 1909 -1909 -1836 -1904
		mu 0 4 706 743 678 789
		f 4 -1907 1910 -1895 -1834
		mu 0 4 704 726 813 802
		f 4 1911 1912 1913 -1809
		mu 0 4 681 799 793 742
		f 4 1914 1915 1916 -1779
		mu 0 4 761 800 718 817
		f 4 1917 1918 -1797 -1914
		mu 0 4 793 703 672 742
		f 4 1919 1920 -1759 1921
		mu 0 4 679 764 809 714
		f 4 1922 -1922 -1811 1923
		mu 0 4 680 679 714 659
		f 4 1924 1925 -1912 -1751
		mu 0 4 776 741 799 681
		f 4 -1917 1926 -1924 -1793
		mu 0 4 817 718 680 659
		f 4 1927 1928 -1787 -1919
		mu 0 4 703 665 807 672
		f 4 1929 1930 1931 1932
		mu 0 4 719 778 804 667
		f 4 1933 1934 1935 1936
		mu 0 4 682 756 746 780
		f 4 1937 1938 1939 -1932
		mu 0 4 804 735 699 667
		f 4 -1935 1940 1941 1942
		mu 0 4 746 756 757 814
		f 4 -1863 1943 -1833 1944
		mu 0 4 733 669 762 784
		f 4 -1857 1945 -1829 1946
		mu 0 4 728 768 740 755
		f 4 1947 1948 -1820 1949
		mu 0 4 720 725 691 803
		f 4 -1946 1950 1951 -1824
		mu 0 4 740 768 758 794
		f 4 1952 -1861 -1947 -1835
		mu 0 4 693 727 728 755
		f 4 -1944 1953 1954 -1815
		mu 0 4 762 669 781 690
		f 4 -1865 -1945 -1830 -1949
		mu 0 4 725 733 784 691
		f 4 -1955 1955 -1953 -1817
		mu 0 4 1387 781 727 693
		f 4 1956 1957 1958 -1943
		mu 0 4 814 685 788 746
		f 4 1959 1960 -1933 1961
		mu 0 4 766 811 719 667
		f 4 1962 -1876 -1962 -1940
		mu 0 4 699 675 766 667
		f 4 -1873 1963 -1936 -1959
		mu 0 4 788 751 780 746
		f 4 1964 1965 1966 -1891
		mu 0 4 668 721 798 656
		f 4 1967 -1888 1968 -1838
		mu 0 4 684 663 736 760
		f 4 -1894 1969 1970 1971
		mu 0 4 705 671 711 677
		f 4 1972 1973 -1880 -1967
		mu 0 4 798 712 708 656
		f 4 -1972 -1869 1974 -1884
		mu 0 4 705 677 730 790
		f 4 1975 -1925 1976 1977
		mu 0 4 782 741 776 810
		f 4 -1766 1978 1979 1980
		mu 0 4 657 734 694 710
		f 4 -1981 1981 -1977 -1750
		mu 0 4 657 710 810 776
		f 4 1982 -1768 1983 -1980
		mu 0 4 1388 806 653 1389
		f 4 -1760 1984 -1982 -1984
		mu 0 4 653 809 1390 1391
		f 4 -1921 1985 -1978 -1985
		mu 0 4 809 764 1392 1393
		f 4 1986 1987 -1979 -1756
		mu 0 4 767 687 694 734
		f 4 -1988 1988 -1762 -1983
		mu 0 4 1394 687 816 806
		f 4 -1778 1989 1990 1991
		mu 0 4 761 655 791 808
		f 4 1992 -1770 1993 1994
		mu 0 4 801 796 666 664
		f 4 -1929 1995 1996 1997
		mu 0 4 807 665 707 1395
		f 4 -1994 -1784 1998 1999
		mu 0 4 664 666 797 1396
		f 4 2000 -1915 -1992 -1997
		mu 0 4 1397 800 761 808
		f 4 2001 -1995 2002 -1776
		mu 0 4 785 801 1398 738
		f 4 -1788 -1998 -1991 -1999
		mu 0 4 797 807 1399 1400
		f 4 -1783 -2003 -2000 -1990
		mu 0 4 655 738 1401 791
		f 4 2003 2004 2005 2006
		mu 0 4 676 683 662 723
		f 4 2007 2008 -2006 2009
		mu 0 4 697 654 723 662
		f 4 -1890 2010 -2007 2011
		mu 0 4 668 717 1402 1403
		f 4 2012 -1965 -2012 -2009
		mu 0 4 654 721 668 1404
		f 4 2013 2014 -1866 -1948
		mu 0 4 720 874 722 725
		f 4 -1856 -2015 2015 -1951
		mu 0 4 768 722 874 758
		f 4 2016 -1969 2017 -1974
		mu 0 4 712 760 736 708
		f 4 -1887 2018 -1877 -2018
		mu 0 4 736 670 795 708
		f 4 -1893 2019 -1885 2020
		mu 0 4 1405 1406 716 663
		f 4 -1970 -2021 -1968 2021
		mu 0 4 711 1407 663 684
		f 4 -1875 2022 2023 -1960
		mu 0 4 766 822 875 811
		f 4 2024 -2023 -1870 -1958
		mu 0 4 685 875 822 788
		f 4 2025 2026 2027 2028
		mu 0 4 879 774 765 878
		f 4 2029 2030 -2029 2031
		mu 0 4 777 698 879 878
		f 4 -2026 2032 2033 2034
		mu 0 4 774 879 876 660
		f 4 -2033 -2031 2035 2036
		mu 0 4 876 879 698 786
		f 4 2037 2038 2039 2040
		mu 0 4 884 889 888 885
		f 4 2041 -2039 2042 2043
		mu 0 4 881 888 889 880
		f 4 -2038 2044 2045 2046
		mu 0 4 889 884 812 877
		f 4 2047 -2028 2048 -2040
		mu 0 4 888 878 765 885
		f 4 2049 -2043 -2047 2050
		mu 0 4 713 880 889 877
		f 4 -2042 2051 -2032 -2048
		mu 0 4 888 881 777 878
		f 4 2052 2053 2054 2055
		mu 0 4 779 891 890 772
		f 4 2056 2057 2058 -2054
		mu 0 4 891 815 701 890
		f 4 2059 2060 -2059 2061
		mu 0 4 759 892 890 701
		f 4 2062 2063 -2055 -2061
		mu 0 4 892 749 772 890
		f 4 2064 2065 2066 2067
		mu 0 4 886 895 894 887
		f 4 2068 2069 2070 -2066
		mu 0 4 895 883 882 894
		f 4 -2067 2071 -2053 2072
		mu 0 4 887 894 891 779
		f 4 -2071 2073 -2057 -2072
		mu 0 4 894 882 815 891
		f 4 2074 2075 -2069 2076
		mu 0 4 893 873 883 895
		f 4 -2077 -2065 2077 2078
		mu 0 4 893 895 886 872
		f 4 -2027 2079 2080 2081
		mu 0 4 765 774 905 901
		f 4 2082 -2080 -2035 2083
		mu 0 4 897 905 774 660
		f 4 2084 -2058 2085 -2081
		mu 0 4 1408 701 815 1409
		f 4 -2083 2086 -2062 -2085
		mu 0 4 1410 1411 759 701
		f 4 2087 2088 2089 -2046
		mu 0 4 812 896 904 877
		f 4 2090 -2036 2091 2092
		mu 0 4 906 786 698 902
		f 4 -2064 2093 -2093 2094
		mu 0 4 772 749 1412 1413
		f 4 2095 2096 -2092 -2030
		mu 0 4 777 903 902 698
		f 4 -2097 2097 -2056 -2095
		mu 0 4 1414 1415 779 772
		f 4 -2090 2098 2099 -2051
		mu 0 4 877 904 898 713
		f 4 2100 -2068 2101 2102
		mu 0 4 908 886 887 907
		f 4 2103 -2070 2104 2105
		mu 0 4 899 882 883 900
		f 4 2106 -2044 2107 -2103
		mu 0 4 1416 881 880 908
		f 4 2108 -2041 2109 -2106
		mu 0 4 900 884 885 1417;
	setAttr ".fc[1000:1181]"
		f 4 -2049 -2082 2110 -2110
		mu 0 4 885 765 901 1418
		f 4 -2111 -2086 -2074 -2104
		mu 0 4 899 1419 815 882
		f 4 -2045 -2109 2111 -2088
		mu 0 4 812 884 900 896
		f 4 -2112 -2105 -2076 2112
		mu 0 4 896 900 883 873
		f 4 -2096 -2052 -2107 2113
		mu 0 4 903 777 881 1420
		f 4 -2098 -2114 -2102 -2073
		mu 0 4 779 1421 907 887
		f 4 -2108 -2050 -2100 2114
		mu 0 4 908 880 713 898
		f 4 -2101 -2115 2115 -2078
		mu 0 4 886 908 898 872
		f 4 2116 -2079 -2116 -2099
		mu 0 4 904 893 872 898
		f 4 -2089 -2113 -2075 -2117
		mu 0 4 904 896 873 893
		f 4 2117 2118 2119 2120
		mu 0 4 928 917 915 927
		f 4 2121 2122 -2121 2123
		mu 0 4 918 910 928 927
		f 4 -2118 2124 2125 2126
		mu 0 4 917 928 925 909
		f 4 -2125 -2123 2127 2128
		mu 0 4 925 928 910 920
		f 4 2129 2130 2131 2132
		mu 0 4 933 938 937 934
		f 4 2133 -2131 2134 2135
		mu 0 4 930 937 938 929
		f 4 -2130 2136 2137 2138
		mu 0 4 938 933 921 926
		f 4 2139 -2120 2140 -2132
		mu 0 4 937 927 915 934
		f 4 2141 -2135 -2139 2142
		mu 0 4 912 929 938 926
		f 4 -2134 2143 -2124 -2140
		mu 0 4 937 930 918 927
		f 4 2144 2145 2146 2147
		mu 0 4 919 940 939 916
		f 4 2148 2149 2150 -2146
		mu 0 4 940 922 911 939
		f 4 2151 2152 -2151 2153
		mu 0 4 914 941 939 911
		f 4 2154 2155 -2147 -2153
		mu 0 4 941 913 916 939
		f 4 2156 2157 2158 2159
		mu 0 4 935 944 943 936
		f 4 2160 2161 2162 -2158
		mu 0 4 944 932 931 943
		f 4 -2159 2163 -2145 2164
		mu 0 4 936 943 940 919
		f 4 -2163 2165 -2149 -2164
		mu 0 4 943 931 922 940
		f 4 2166 2167 -2161 2168
		mu 0 4 942 924 932 944
		f 4 -2169 -2157 2169 2170
		mu 0 4 942 944 935 923
		f 4 -2119 2171 2172 2173
		mu 0 4 915 917 954 950
		f 4 2174 -2172 -2127 2175
		mu 0 4 946 954 917 909
		f 4 2176 -2150 2177 -2173
		mu 0 4 1422 911 922 1423
		f 4 -2175 2178 -2154 -2177
		mu 0 4 1424 1425 914 911
		f 4 2179 2180 2181 -2138
		mu 0 4 921 945 953 926
		f 4 2182 -2128 2183 2184
		mu 0 4 955 920 910 951
		f 4 -2156 2185 -2185 2186
		mu 0 4 916 913 1426 1427
		f 4 2187 2188 -2184 -2122
		mu 0 4 918 952 951 910
		f 4 -2189 2189 -2148 -2187
		mu 0 4 1428 1429 919 916
		f 4 -2182 2190 2191 -2143
		mu 0 4 926 953 947 912
		f 4 2192 -2160 2193 2194
		mu 0 4 957 935 936 956
		f 4 2195 -2162 2196 2197
		mu 0 4 948 931 932 949
		f 4 2198 -2136 2199 -2195
		mu 0 4 1430 930 929 957
		f 4 2200 -2133 2201 -2198
		mu 0 4 949 933 934 1431
		f 4 -2141 -2174 2202 -2202
		mu 0 4 934 915 950 1432
		f 4 -2203 -2178 -2166 -2196
		mu 0 4 948 1433 922 931
		f 4 -2137 -2201 2203 -2180
		mu 0 4 921 933 949 945
		f 4 -2204 -2197 -2168 2204
		mu 0 4 945 949 932 924
		f 4 -2188 -2144 -2199 2205
		mu 0 4 952 918 930 1434
		f 4 -2190 -2206 -2194 -2165
		mu 0 4 919 1435 956 936
		f 4 -2200 -2142 -2192 2206
		mu 0 4 957 929 912 947
		f 4 -2193 -2207 2207 -2170
		mu 0 4 935 957 947 923
		f 4 2208 -2171 -2208 -2191
		mu 0 4 953 942 923 947
		f 4 -2181 -2205 -2167 -2209
		mu 0 4 953 945 924 942
		f 4 2209 2210 2211 2212
		mu 0 4 1233 1252 1243 1213
		f 4 2213 2214 2215 -2211
		mu 0 4 1252 1240 1247 1243
		f 4 2216 2217 2218 2219
		mu 0 4 1246 1241 1227 1250
		f 4 2220 2221 2222 -2217
		mu 0 4 1246 1216 1238 1241
		f 4 -2219 2223 2224 2225
		mu 0 4 1250 1227 1219 1218
		f 4 2226 2227 2228 -2224
		mu 0 4 1227 1230 1249 1219
		f 4 -2218 2229 2230 -2227
		mu 0 4 1227 1241 1244 1230
		f 4 2231 2232 -2230 -2223
		mu 0 4 1238 1221 1244 1241
		f 4 2233 2234 -2210 2235
		mu 0 4 1231 1215 1252 1233
		f 4 2236 2237 2238 2239
		mu 0 4 1223 1232 1235 1239
		f 4 2240 2241 2242 2243
		mu 0 4 1237 1242 1226 1236
		f 4 2244 -2244 2245 -2239
		mu 0 4 1235 1237 1236 1239
		f 4 2246 2247 2248 -2242
		mu 0 4 1242 1255 1254 1226
		f 4 2249 -2241 2250 2251
		mu 0 4 1214 1242 1237 1256
		f 4 -2216 2252 2253 2254
		mu 0 4 1436 1437 1256 1257
		f 4 -2251 -2245 2255 -2254
		mu 0 4 1256 1237 1235 1257
		f 4 -2256 -2238 2256 2257
		mu 0 4 1257 1235 1232 1258
		f 4 -2212 -2255 -2258 2258
		mu 0 4 1438 1439 1257 1258
		f 4 2259 -2252 -2253 2260
		mu 0 4 1259 1214 1256 1440
		f 4 2261 -2226 2262 2263
		mu 0 4 1261 1441 1442 1260
		f 4 -2240 2264 -2264 2265
		mu 0 4 1223 1239 1261 1260
		f 4 2266 2267 2268 -2243
		mu 0 4 1226 1253 1262 1236
		f 4 -2246 -2269 2269 -2265
		mu 0 4 1239 1236 1262 1261
		f 4 -2270 2270 -2220 -2262
		mu 0 4 1261 1262 1443 1444
		f 4 -2271 -2268 2271 -2221
		mu 0 4 1445 1262 1253 1216
		f 4 2272 2273 2274 -2225
		mu 0 4 1219 1229 1234 1218
		f 4 -2275 2275 2276 -2263
		mu 0 4 1446 1447 1224 1260
		f 4 2277 2278 2279 -2257
		mu 0 4 1232 1225 1251 1258
		f 4 2280 2281 -2236 2282
		mu 0 4 1220 1248 1231 1233
		f 4 2283 2284 -2273 -2229
		mu 0 4 1249 1222 1229 1219
		f 4 2285 2286 -2278 -2237
		mu 0 4 1223 1228 1225 1232
		f 4 2287 2288 -2259 -2280
		mu 0 4 1251 1212 1448 1258
		f 4 2289 -2283 -2213 -2289
		mu 0 4 1449 1220 1233 1213
		f 4 -2277 2290 -2286 -2266
		mu 0 4 1260 1224 1228 1223
		f 4 -2231 2291 2292 2293
		mu 0 4 1230 1244 1245 1215
		f 4 2294 2295 -2292 -2233
		mu 0 4 1221 1217 1245 1244
		f 4 -2294 -2234 2296 -2228
		mu 0 4 1230 1215 1231 1249
		f 4 2297 -2295 2298 -2248
		mu 0 4 1255 1217 1221 1254
		f 4 -2282 2299 -2284 -2297
		mu 0 4 1231 1248 1222 1249
		f 4 -2272 -2267 -2249 2300
		mu 0 4 1216 1253 1226 1254
		f 4 -2260 2301 -2247 -2250
		mu 0 4 1214 1259 1255 1242
		f 4 -2232 -2222 -2301 -2299
		mu 0 4 1221 1238 1216 1254
		f 4 -2293 2302 -2214 -2235
		mu 0 4 1215 1245 1240 1252
		f 4 2303 2304 -2303 -2296
		mu 0 4 1217 1263 1240 1245
		f 4 2305 -2304 -2298 -2302
		mu 0 4 1259 1263 1217 1255
		f 4 -2306 -2261 -2215 -2305
		mu 0 4 1263 1259 1247 1240
		f 4 2306 2307 2308 2309
		mu 0 4 832 829 836 835
		f 4 2310 2311 2312 2313
		mu 0 4 823 867 848 842
		f 4 2314 2315 2316 -2308
		mu 0 4 829 864 850 836
		f 4 2317 2318 2319 2320
		mu 0 4 870 869 847 864
		f 4 2321 2322 2323 -2311
		mu 0 4 823 859 866 867
		f 4 2324 2325 -2316 -2320
		mu 0 4 847 865 850 864
		f 4 2326 -2318 2327 -2324
		mu 0 4 866 869 870 867
		f 4 2328 2329 2330 -2325
		mu 0 4 847 828 840 865
		f 4 2331 -2323 2332 2333
		mu 0 4 834 866 859 857
		f 4 2334 2335 -2327 -2332
		mu 0 4 834 868 869 866
		f 4 -2336 2336 -2329 -2319
		mu 0 4 869 868 828 847
		f 4 2337 2338 -2312 -2328
		mu 0 4 870 871 848 867
		f 4 2339 -2338 -2321 -2315
		mu 0 4 829 871 870 864
		f 4 2340 2341 2342 2343
		mu 0 4 845 856 849 858
		f 4 2344 2345 2346 2347
		mu 0 4 824 855 838 841
		f 4 2348 2349 2350 2351
		mu 0 4 837 852 825 826
		f 4 -2346 2352 2353 2354
		mu 0 4 838 855 861 827
		f 4 -2354 2355 2356 2357
		mu 0 4 827 861 860 862
		f 4 2358 -2349 2359 -2342
		mu 0 4 856 852 837 849
		f 4 2360 -2348 2361 2362
		mu 0 4 843 824 841 844
		f 4 2363 2364 -2360 2365
		mu 0 4 830 853 849 837
		f 4 2366 -2362 2367 2368
		mu 0 4 833 844 841 839
		f 4 -2368 -2347 2369 2370
		mu 0 4 839 841 838 863
		f 4 2371 2372 -2343 -2365
		mu 0 4 853 851 858 849
		f 4 2373 2374 2375 -2358
		mu 0 4 862 846 854 827
		f 4 -2355 -2376 2376 -2370
		mu 0 4 838 827 854 863
		f 4 2377 2378 -2366 -2352
		mu 0 4 826 831 830 837
		f 4 -2375 2379 -2309 2380
		mu 0 4 854 846 835 836
		f 4 2381 -2371 2382 -2326
		mu 0 4 865 839 863 850
		f 4 -2377 -2381 -2317 -2383
		mu 0 4 863 854 836 850
		f 4 -2364 2383 -2322 2384
		mu 0 4 853 830 859 823
		f 4 -2331 2385 -2369 -2382
		mu 0 4 865 840 833 839
		f 4 -2379 2386 -2333 -2384
		mu 0 4 830 831 857 859
		f 4 2387 -2372 -2385 -2314
		mu 0 4 842 851 853 823
		f 4 2388 -2380 2389 -2388
		mu 0 4 842 1450 1451 851
		f 4 -2357 2390 -2344 2391
		mu 0 4 1452 1453 845 858
		f 4 -2374 -2392 -2373 -2390
		mu 0 4 1454 1455 858 851
		f 4 2392 -2310 -2389 -2313
		mu 0 4 848 832 1456 842
		f 4 -2307 -2393 -2339 -2340
		mu 0 4 829 832 848 871
		f 4 2393 2394 2395 -2330
		mu 0 4 828 1265 1264 840
		f 4 2396 -2334 2397 -2395
		mu 0 4 1265 834 857 1457
		f 4 2398 2399 2400 -2351
		mu 0 4 825 1267 1266 826
		f 4 2401 -2363 2402 -2400
		mu 0 4 1458 843 844 1459
		f 4 -2401 2403 2404 -2378
		mu 0 4 826 1266 1268 831
		f 4 -2403 -2367 2405 -2404
		mu 0 4 1460 844 833 1461
		f 4 -2396 2406 -2406 -2386
		mu 0 4 840 1264 1462 833
		f 4 -2398 -2387 -2405 -2407
		mu 0 4 1463 857 831 1268
		f 4 -2397 -2394 -2337 -2335
		mu 0 4 834 1265 828 868
		f 4 -1989 -1987 -1853 -1854
		mu 0 4 816 687 767 658
		f 4 -1846 -1993 -2002 -1848
		mu 0 4 750 796 801 785
		f 4 2407 2408 2409 2410
		mu 0 4 1273 1271 1272 1274
		f 4 2411 -2409 2412 2413
		mu 0 4 1275 1272 1271 1276
		f 4 2414 2415 2416 -2411
		mu 0 4 1274 1269 1270 1273
		f 4 2417 -1868 2418 -2413
		mu 0 4 1271 730 819 1276
		f 4 2419 -2417 2420 -1881
		mu 0 4 790 1273 1270 692
		f 4 -2420 -1975 -2418 -2408
		mu 0 4 1273 790 730 1271
		f 4 2421 2422 2423 -2412
		mu 0 4 1275 821 697 1272
		f 4 -2010 2424 -2410 -2424
		mu 0 4 697 662 1274 1272
		f 4 -2425 -2005 2425 -2415
		mu 0 4 1274 662 683 1269
		f 4 -2414 2426 -1973 2427
		mu 0 4 1275 1276 712 798
		f 4 -1966 2428 -2422 -2428
		mu 0 4 798 721 821 1275
		f 4 -2419 -1839 -2017 -2427
		mu 0 4 1276 819 760 712
		f 4 -2013 -2008 -2423 -2429
		mu 0 4 721 654 697 821
		f 4 -1971 -2022 -1837 -1867
		mu 0 4 677 711 684 820
		f 4 -1956 -1954 -1862 -1859
		mu 0 4 727 781 669 775
		f 4 -1825 2429 2430 2431
		mu 0 4 732 794 1277 1279
		f 4 2432 2433 -1900 -2432
		mu 0 4 1279 1278 792 732
		f 4 -1952 2434 2435 -2430
		mu 0 4 794 758 1280 1277
		f 4 -1899 2436 -2433 2437
		mu 0 4 763 805 1464 1465
		f 4 2438 -1819 -2438 -2431
		mu 0 4 1466 803 763 1467
		f 4 2439 -1950 -2439 -2436
		mu 0 4 1280 720 803 1468
		f 4 -2440 -2435 -2016 -2014
		mu 0 4 720 1280 758 874
		f 4 2440 2441 2442 -1939
		mu 0 4 735 1282 1281 699
		f 4 -2442 2443 -1937 2444
		mu 0 4 1469 1470 682 780
		f 4 2445 2446 -1963 -2443
		mu 0 4 1281 1283 675 699
		f 4 -1964 2447 -2446 -2445
		mu 0 4 780 751 1283 1471
		f 4 -2447 -2448 -1872 -1874
		mu 0 4 675 1283 751 818
		f 4 2448 -1930 2449 2450
		mu 0 4 1285 778 719 1284
		f 4 2451 -2451 2452 -1942
		mu 0 4 757 1472 1473 814
		f 4 2453 2454 -1957 -2453
		mu 0 4 1474 1286 685 814
		f 4 2455 -2454 -2450 -1961
		mu 0 4 811 1286 1284 719
		f 4 -2456 -2024 -2025 -2455
		mu 0 4 1286 811 875 685;
	setAttr ".cd" -type "dataPolyComponent" Index_Data Edge 0 ;
	setAttr ".cvd" -type "dataPolyComponent" Index_Data Vertex 0 ;
	setAttr ".hfd" -type "dataPolyComponent" Index_Data Face 0 ;
createNode transform -n "lowerTeeth" -p "headTopologyTeeth";
createNode mesh -n "lowerTeethShape" -p "lowerTeeth";
	setAttr -k off ".v";
	setAttr -s 18 ".iog[0].og";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".dr" 1;
createNode mesh -n "lowerTeethShapeOrig1" -p "lowerTeeth";
	setAttr -k off ".v";
	setAttr ".io" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr -s 1569 ".uvst[0].uvsp";
	setAttr ".uvst[0].uvsp[0:249]" -type "float2" 0.118055 0.36849299 0.117648
		 0.38529801 0.070197001 0.38332 0.134726 0.454721 0.10876 0.72678298 0.023902001 0.74014902
		 0.139035 0.75444001 0.064769998 0.65776002 0.083727002 0.68538702 0.095048003 0.366106
		 0.104188 0.349987 0.117154 0.69470602 0.092702001 0.352806 0.14426 0.63794702 0.095619
		 0.656124 0.104407 0.635001 0.042295001 0.32582301 0.131897 0.39592701 0.112784 0.45689401
		 0.122868 0.75529701 0.100446 0.77098203 0.117738 0.44064301 0.068444997 0.63700902
		 0.145409 0.65840203 0.106299 0.36844599 0.060297001 0.35102099 0.056896999 0.36822599
		 0.030820001 0.36616501 0.094870001 0.64497799 0.042165 0.64631402 0.096854001 0.32938001
		 0.034919001 0.34424701 0.091072999 0.63640702 0.044973001 0.70043802 0.094509996
		 0.66809899 0.070647001 0.482806 0.122796 0.456453 0.056473002 0.48845601 0.067562997
		 0.45519599 0.140174 0.72550303 0.141993 0.68232203 0.099367999 0.74155003 0.099812999
		 0.72779697 0.141826 0.69121802 0.033939 0.39197901 0.027396001 0.71376801 0.076050997
		 0.67806202 0.069894999 0.67954302 0.065916002 0.67007202 0.083021 0.62980902 0.107822
		 0.644373 0.100799 0.457753 0.043577 0.45091301 0.105173 0.68199402 0.056745999 0.65810001
		 0.070840999 0.65790999 0.058859002 0.72821599 0.091642 0.75081301 0.109062 0.32745501
		 0.058724001 0.74096203 0.123109 0.72625703 0.074809 0.63809401 0.126111 0.34686801
		 0.122088 0.71470302 0.044447001 0.72666001 0.094890997 0.74035299 0.028201001 0.70133603
		 0.120667 0.492071 0.071823001 0.66938603 0.143931 0.67142802 0.091562003 0.68854201
		 0.047671001 0.39075801 0.110422 0.49041301 0.025126001 0.72606403 0.023544 0.75393701
		 0.075704999 0.74100399 0.120105 0.68307799 0.135984 0.47297001 0.021919001 0.68031102
		 0.107263 0.75578803 0.101404 0.47017899 0.102458 0.70053601 0.121735 0.671184 0.14513201
		 0.646761 0.13060001 0.38373801 0.046394002 0.620902 0.104024 0.38545901 0.130759
		 0.43941301 0.040024001 0.43126601 0.118331 0.77089602 0.090778001 0.70639402 0.130271
		 0.36786601 0.019756 0.67002702 0.077624001 0.75027001 0.0515 0.435269 0.110982 0.44282001
		 0.070790999 0.718485 0.041117001 0.47015601 0.065581001 0.62168902 0.018933 0.64641601
		 0.058100998 0.67026299 0.100847 0.66889101 0.044344999 0.63439202 0.060812999 0.75524199
		 0.123402 0.74091798 0.081987001 0.39611301 0.117686 0.39998901 0.076135002 0.73153001
		 0.108446 0.65622598 0.097134002 0.67981601 0.067718998 0.72960198 0.096311003 0.44617301
		 0.108785 0.74176198 0.018355001 0.65822202 0.045286 0.71173799 0.043754 0.66965401
		 0.021629 0.76714402 0.075589001 0.62599802 0.043896001 0.48968801 0.066666 0.36656001
		 0.061763 0.438627 0.096704997 0.75382102 0.122657 0.65693903 0.107667 0.67005002
		 0.113599 0.471524 0.100947 0.69376802 0.020616001 0.635616 0.100781 0.40296 0.140092
		 0.74062502 0.138236 0.71380901 0.098613001 0.63647002 0.120042 0.63537502 0.059188999
		 0.39035401 0.050747 0.46997499 0.097030997 0.71632999 0.043949999 0.74122 0.060525
		 0.635153 0.064456001 0.64704001 0.045763999 0.36785001 0.053025 0.45217299 0.078806996
		 0.72059703 0.081542 0.33872101 0.071511 0.35216099 0.122208 0.645091 0.057009 0.64660501
		 0.062419999 0.68037701 0.032669 0.44731 0.070982002 0.64736903 0.067566 0.74040997
		 0.048057001 0.680282 0.095054001 0.72955197 0.088357002 0.76520801 0.020834001 0.62502003
		 0.073034003 0.445059 0.123966 0.472525 0.062001999 0.71579301 0.101728 0.65593803
		 0.090352997 0.67803401 0.044748001 0.75678402 0.091807 0.71936703 0.069715001 0.75163603
		 0.115665 0.34821501 0.048958 0.34781 0.101328 0.64457101 0.028828001 0.46878201 0.041972999
		 0.65803301 0.107068 0.71403301 0.096702002 0.48288301 0.065929003 0.46824801 0.13355801
		 0.49012801 0.030184001 0.487881 0.090747997 0.38249099 0.084237002 0.478838 0.085190997
		 0.44915 0.082280003 0.75989002 0.084273003 0.711528 0.083019003 0.64039999 0.083222002
		 0.674348 0.086980999 0.72349799 0.086716004 0.74710399 0.088136002 0.73996502 0.088110998
		 0.73127198 0.083283 0.66714001 0.083502002 0.657924 0.083307996 0.64792001 0.084660999
		 0.46903399 0.085565001 0.458114 0.079912998 0.37821499 0.080288 0.36754501 0.081607997
		 0.35723799 0.041666999 0.55783802 0.041653998 0.57472599 0.026574001 0.57714802 0.0306
		 0.59557003 0.045517001 0.59272599 0.107421 0.59744501 0.136592 0.585563 0.113873
		 0.565476 0.112235 0.58008999 0.13307901 0.59950203 0.123893 0.58315402 0.119809 0.59782499
		 0.082782 0.57566297 0.082194 0.58494598 0.065761 0.56276703 0.054896999 0.572451
		 0.054042999 0.55902499 0.066722997 0.57282102 0.059634998 0.58928502 0.071046002
		 0.58712298 0.099793002 0.57590598 0.093391001 0.58988702 0.101273 0.56581098 0.083471
		 0.56774402 0.031300001 0.53956902 0.035732001 0.53042603 0.049936 0.53182203 0.026726
		 0.55665803 0.045276001 0.54307801 0.134112 0.54392302 0.125237 0.56616402 0.122562
		 0.54639202 0.130024 0.52805102 0.13772701 0.56752503 0.118067 0.53062701 0.057558
		 0.54694498 0.062843002 0.53482002 0.074362002 0.541659 0.068371996 0.55300403 0.110902
		 0.54878998 0.093162999 0.54204899 0.105569 0.53460401 0.098831996 0.55386001 0.083830997
		 0.54705697 0.083981 0.55779701 0.79687703 0.23338 0.86783803 0.271871 0.89853197
		 0.29251 0.84189099 0.26449001 0.88890398 0.28061301 0.81734103 0.245453 0.82719499
		 0.234074 0.84456301 0.25026399 0.86535197 0.25491399 0.79687703 0.207504 0.81924403
		 0.19417199 0.84536099 0.23077799 0.019742001 0.93977302 0.040761001 0.96647203 0.114941
		 0.94106603;
	setAttr ".uvst[0].uvsp[250:499]" 0.076830998 0.96439999 0.138916 0.9648 0.138723
		 0.93716502 0.056111999 0.93811601 0.11567 0.96390003 0.041269999 0.93846703 0.055576999
		 0.96880901 0.100832 0.96860802 0.018794 0.963135 0.076659001 0.94393802 0.101465
		 0.93876201 0.083175004 0.963956 0.081913002 0.943519 0.019262001 0.95054102 0.075769
		 0.95349401 0.054533001 0.95342702 0.039937999 0.952389 0.073687002 0.96784598 0.066292003
		 0.97166502 0.091813996 0.93945497 0.084628001 0.94073999 0.073971003 0.941158 0.067202002
		 0.93742102 0.086474001 0.96652102 0.093116999 0.96779197 0.064487003 0.953834 0.071589001
		 0.95375103 0.120342 0.95265901 0.1051 0.95318401 0.14146 0.95142901 0.083130002 0.95371002
		 0.094241001 0.953134 0.087242998 0.95338702 0.079489999 0.942375 0.020679001 0.93023902
		 0.079852998 0.96573102 0.087026 0.93230802 0.079567 0.93645602 0.06154 0.92971802
		 0.043072 0.97968203 0.057560001 0.97586602 0.079331003 0.953327 0.046658002 0.92799002
		 0.016519001 0.97591102 0.084526002 0.97872603 0.077605002 0.97263402 0.021817001
		 0.865134 0.043708 0.89025098 0.111101 0.86249602 0.075939998 0.88566399 0.134065
		 0.88472801 0.13477901 0.86048502 0.056127999 0.86004502 0.111837 0.88401002 0.041965
		 0.86125302 0.057022002 0.89123702 0.097484 0.89027202 0.022807 0.88760102 0.076476999
		 0.86563498 0.099031001 0.85881603 0.081780002 0.88568199 0.081583999 0.865327 0.022523001
		 0.87587303 0.075205997 0.87544399 0.054885998 0.87579697 0.041556999 0.87588203 0.072920002
		 0.88902599 0.066476002 0.89259303 0.090383001 0.86066699 0.084326997 0.86246598 0.073939003
		 0.86269403 0.067226 0.85894603 0.084770001 0.88844699 0.090418003 0.88949198 0.064469002
		 0.87568498 0.071158998 0.87572199 0.115129 0.87317002 0.099117003 0.87375301 0.13549501
		 0.87323302 0.081975996 0.87577498 0.091216996 0.87479103 0.085786998 0.87553799 0.079342
		 0.864003 0.021785 0.85453701 0.078750998 0.88692802 0.086529002 0.85343403 0.079810001
		 0.85784 0.061519001 0.85068202 0.046929002 0.90376103 0.060159001 0.90005302 0.078580998
		 0.87518197 0.046459001 0.84967202 0.021081001 0.90092301 0.083425 0.89869702 0.076875001
		 0.89358002 0.137638 0.78169203 0.047345001 0.77560502 0.082976997 0.79594499 0.056501001
		 0.79710102 0.079029001 0.81401902 0.074318998 0.80181301 0.045938 0.83688003 0.042932998
		 0.82405698 0.023443 0.78661001 0.073838003 0.80833602 0.024847001 0.81265903 0.120587
		 0.80977398 0.138135 0.81649703 0.139632 0.80017102 0.081846997 0.80858302 0.058917999
		 0.82204998 0.139531 0.80771601 0.024319001 0.82542902 0.055119999 0.81135601 0.040895
		 0.79763699 0.120302 0.79839599 0.04301 0.78537899 0.023104001 0.83917302 0.101837
		 0.79764301 0.090410002 0.81064397 0.091066003 0.798711 0.074102998 0.81344497 0.101176
		 0.81108803 0.070175998 0.79128402 0.068001002 0.81836802 0.082572997 0.80101001 0.064934
		 0.77940202 0.065183997 0.81044102 0.066569 0.799411 0.072879001 0.82414103 0.073224001
		 0.78723001 0.024798 0.79888201 0.039774999 0.81195098 0.064295001 0.83099902 0.138686
		 0.79144502 0.060251001 0.786327 0.082339004 0.813636 0.078097999 0.80711198 0.078475997
		 0.80161601 0.089892 0.78844303 0.098149002 0.78574502 0.119433 0.78750801 0.078510001
		 0.79680502 0.115956 0.82024902 0.096110001 0.82299203 0.087802 0.82074702 0.075181998
		 0.79687899 0.084169 0.53263301 0.129886 0.51955301 0.100716 0.52270502 0.115589 0.51902002
		 0.080632001 0.59998602 0.064548001 0.60694802 0.048154 0.61082703 0.033075001 0.61128902
		 0.032212 0.50670803 0.047306001 0.50913602 0.082754001 0.49753401 0.063813001 0.50854301
		 0.060972001 0.420194 0.047586001 0.41637 0.086621001 0.43512401 0.073288001 0.42453101
		 0.81637698 0.059041999 0.80705899 0.072238997 0.810188 0.076612003 0.81634802 0.074286997
		 0.807872 0.086916 0.81285799 0.088596001 0.80388403 0.086350001 0.80293697 0.095147997
		 0.80638301 0.095661998 0.80994999 0.097157001 0.83955199 0.066077001 0.82694399 0.077830002
		 0.83096099 0.083862998 0.83887202 0.083021998 0.81968403 0.090305001 0.82631099 0.091518998
		 0.83280897 0.093959004 0.81834501 0.098535001 0.823291 0.098421998 0.82784599 0.100402
		 0.81361699 0.094720997 0.81545901 0.095073 0.81478602 0.099775001 0.81255698 0.099397004
		 0.86493897 0.075615004 0.84980398 0.086346 0.854312 0.091712996 0.862804 0.092386
		 0.84069598 0.095985003 0.84874803 0.098008998 0.85548699 0.10321 0.83823103 0.102959
		 0.84423202 0.10398 0.849231 0.108385 0.83258599 0.099803001 0.834728 0.10041 0.83055598
		 0.103904 0.83320397 0.104727 0.88980103 0.089123003 0.87280703 0.098233998 0.876508
		 0.104699 0.88489902 0.106696 0.86323601 0.107549 0.87101299 0.110478 0.87688297 0.116899
		 0.86015803 0.114257 0.86614197 0.116051 0.869923 0.121441 0.854289 0.110237 0.85645801
		 0.11142 0.85421503 0.11514 0.85140902 0.11361 0.91372299 0.107892 0.89504802 0.115043
		 0.89837599 0.123406 0.90818697 0.127334 0.883771 0.123624 0.891357 0.128967 0.897659
		 0.135867 0.87887198 0.130743 0.885342 0.13392401 0.88918799 0.139412 0.87480497 0.124451
		 0.876562 0.12638099 0.87330598 0.13017599 0.87127203 0.127694 0.93800497 0.12976199
		 0.90561497 0.143971 0.91107899 0.15080801 0.91846299 0.15707999 0.90038002 0.14864101
		 0.90704799 0.15396 0.91182899 0.16084599 0.89441502 0.141288 0.89672297 0.143209
		 0.890154 0.143898 0.89318699 0.14609499 0.957542 0.157096 0.92712998 0.16777501 0.93164903
		 0.175036 0.93754703 0.18136799 0.92172098 0.173315 0.92743599 0.178452 0.93129599
		 0.185013 0.91794199 0.16404299 0.92019498 0.166894 0.91655302 0.170164;
	setAttr ".uvst[0].uvsp[500:749]" 0.91403103 0.166794 0.97061801 0.18407699
		 0.94405401 0.19191401 0.94636601 0.198148 0.94984502 0.202196 0.93925899 0.19740801
		 0.94284397 0.20127 0.945768 0.20607901 0.93339801 0.191394 0.93700403 0.18875401
		 0.93870199 0.191617 0.93551999 0.19481499 0.80642599 0.137261 0.81092399 0.125083
		 0.80720299 0.123427 0.80452102 0.125542 0.80983102 0.116362 0.80666202 0.11773 0.80340302
		 0.118357 0.81955099 0.138947 0.82652599 0.127698 0.82258499 0.126417 0.81792402 0.126508
		 0.82677197 0.120738 0.82285303 0.12146 0.81859899 0.120026 0.81458497 0.12151 0.81260902
		 0.120578 0.812154 0.115011 0.81464303 0.116733 0.83067697 0.144961 0.83897799 0.132706
		 0.83580899 0.128369 0.83183497 0.128197 0.84144503 0.123542 0.83749402 0.122546 0.83386701
		 0.120862 0.83063298 0.123474 0.82918203 0.123498 0.831388 0.119225 0.82943702 0.119181
		 0.84317201 0.15657 0.85425198 0.147155 0.85032803 0.141885 0.84473401 0.138989 0.85815102
		 0.138723 0.85344398 0.136455 0.84874398 0.13204101 0.84392202 0.130518 0.84254402
		 0.12860499 0.84618402 0.12604199 0.84452403 0.1235 0.85819203 0.17097101 0.87202799
		 0.163357 0.86726898 0.157003 0.861036 0.153401 0.87620503 0.155406 0.87049502 0.15194499
		 0.86522901 0.14711601 0.86049998 0.14602999 0.85895401 0.14429 0.86316901 0.14194401
		 0.86153603 0.13947 0.87343901 0.192277 0.89066499 0.18587901 0.88615501 0.17707799
		 0.87970197 0.172006 0.89039803 0.171143 0.88536602 0.164602 0.89608198 0.176763 0.879206
		 0.162691 0.877473 0.16051701 0.88014799 0.155518 0.88198102 0.158053 0.89188898 0.21599799
		 0.90893501 0.20889699 0.90423602 0.20122799 0.89760202 0.196265 0.91493201 0.200664
		 0.90935397 0.195408 0.905375 0.188493 0.90000302 0.18658 0.89827299 0.18395799 0.90433502
		 0.181798 0.90224802 0.178784 0.91344601 0.233539 0.93156898 0.231783 0.92718703 0.22063801
		 0.92026001 0.216199 0.93536299 0.21939801 0.93017602 0.215435 0.926718 0.210344 0.92067498
		 0.20853999 0.91759801 0.206588 0.924622 0.20493101 0.921112 0.20226701 0.95045102
		 0.211014 0.95867002 0.21180101 0.94678402 0.210751 0.80264002 0.106742 0.80591702
		 0.106911 0.80944097 0.106725 0.81233501 0.107036 0.81453001 0.107929 0.81843603 0.109189
		 0.82288498 0.110028 0.82698202 0.110524 0.82989001 0.11122 0.83196402 0.111684 0.83588302
		 0.111545 0.86004102 0.125652 0.86414599 0.129444 0.86686599 0.132892 0.86845499 0.13515399
		 0.87191898 0.13813899 0.87780899 0.14209799 0.88274997 0.146657 0.88550901 0.149297
		 0.88792199 0.151412 0.89366102 0.155826 0.89955401 0.161714 0.90468901 0.168115 0.908674
		 0.172454 0.91041797 0.175282 0.913055 0.180388 0.91730899 0.187066 0.92251301 0.192718
		 0.92721802 0.196375 0.92983699 0.19907901 0.93270099 0.203376 0.93592203 0.20806
		 0.93968201 0.212689 0.85496002 0.122555 0.85061997 0.11996 0.84838301 0.118082 0.84522599
		 0.115373 0.84054297 0.112704 0.79687703 0.088105001 0.79687703 0.097525999 0.79687703
		 0.070556998 0.79687703 0.052173998 0.79687703 0.106202 0.79687703 0.115606 0.79687703
		 0.123165 0.79687703 0.13662601 0.80351198 0.053086001 0.80069202 0.070762999 0.799725
		 0.087278999 0.799546 0.096620999 0.79939097 0.106435 0.79996002 0.116877 0.80052203
		 0.12421 0.80063701 0.13379 0.97537798 0.211519 0.79687703 0.15785199 0.81251198 0.162717
		 0.822833 0.162883 0.82981402 0.176598 0.84309101 0.185426 0.99250197 0.189869 0.99652898
		 0.169624 0.98611897 0.138989 0.96623898 0.107243 0.94046497 0.080631003 0.91207999
		 0.059103001 0.88341397 0.042086001 0.85387897 0.029782001 0.82750899 0.023049001
		 0.80906099 0.020664001 0.79687703 0.019990001 0.94080502 0.16839001 0.95496702 0.19278499
		 0.92237598 0.14449 0.92584997 0.26801601 0.90044302 0.25902101 0.86956501 0.22979601
		 0.84728003 0.203354 0.95387203 0.236827 0.94354302 0.214479 0.94777697 0.226068 0.94371599
		 0.218647 0.88396901 0.362822 0.88476598 0.37946099 0.93210697 0.37686801 0.86714202
		 0.453361 0.89405298 0.718853 0.97825402 0.73243803 0.86303401 0.74630302 0.937401
		 0.65152001 0.918154 0.67855102 0.90669 0.35996699 0.89705902 0.344107 0.88468403
		 0.68772602 0.908409 0.34659201 0.85782802 0.63084102 0.90683401 0.64949501 0.89785498
		 0.628079 0.95890403 0.31926301 0.87022102 0.39066201 0.889548 0.45491999 0.88019001
		 0.74742699 0.90157503 0.76299602 0.88428497 0.43931001 0.93356401 0.63051701 0.85658902
		 0.65129602 0.89564198 0.36259601 0.94215697 0.34426901 0.94538498 0.361532 0.970945
		 0.35947201 0.90752399 0.63831699 0.95990199 0.63964301 0.90435201 0.32358101 0.96653903
		 0.337594 0.91123801 0.62968999 0.95732701 0.69263703 0.90820402 0.66170001 0.930484
		 0.48317301 0.87918699 0.45485601 0.94536501 0.48746699 0.933442 0.45280099 0.862001
		 0.71735603 0.85989702 0.67523301 0.90352398 0.73355001 0.90307403 0.71989399 0.86002702
		 0.68412602 0.96818203 0.38534501 0.97485602 0.70603502 0.926808 0.67234802 0.93272603
		 0.67320001 0.93662697 0.66373199 0.91910601 0.62298101 0.89456803 0.63751101 0.90028298
		 0.45494199 0.95826 0.44962099 0.89699101 0.67481202 0.94548899 0.65151501 0.93140602
		 0.65183097 0.94294202 0.721443 0.91121298 0.74276501 0.89210802 0.32182601 0.94324702
		 0.73360199 0.87961203 0.71843201 0.92729998 0.63163501 0.87532002 0.34149301 0.880732
		 0.70594698 0.957537 0.72057098 0.90799898 0.73237598 0.974096 0.69359702 0.881226
		 0.49076301 0.93090302 0.66346902;
	setAttr ".uvst[0].uvsp[750:999]" 0.85801101 0.66432703 0.91030401 0.68167102
		 0.954458 0.384177 0.89390898 0.48879001 0.97708303 0.71834302 0.97856301 0.74623102
		 0.92711198 0.733527 0.882007 0.67574698 0.86589003 0.47165 0.97998202 0.67375302
		 0.89556599 0.747774 0.90110999 0.46920699 0.89982402 0.69252002 0.88052797 0.663629
		 0.85691702 0.63965303 0.87134802 0.37844601 0.95577401 0.61423302 0.89839798 0.37963101
		 0.87111402 0.43801901 0.96204102 0.42982501 0.88368601 0.76284403 0.91148603 0.69842303
		 0.87145603 0.362562 0.98219103 0.66347802 0.92514402 0.742769 0.950463 0.43387601
		 0.88963997 0.43852201 0.93145603 0.71085298 0.96087301 0.46883401 0.93658 0.61493403
		 0.98312002 0.63986802 0.94422501 0.66366398 0.90179503 0.66181999 0.95778298 0.62777299
		 0.941333 0.74749303 0.87936699 0.732907 0.92016202 0.390147 0.88450003 0.394528 0.9267
		 0.72393698 0.89393502 0.649221 0.90507799 0.67292899 0.93450397 0.722233 0.90502602
		 0.442637 0.89411002 0.73376 0.98364502 0.65167803 0.95670998 0.70706999 0.95840299
		 0.66296703 0.98042703 0.75944901 0.92654902 0.61920202 0.95816398 0.48837301 0.93547899
		 0.35989401 0.93973398 0.43705601 0.90600902 0.74576497 0.87986398 0.65001303 0.89481997
		 0.66276699 0.88990498 0.47082001 0.90089703 0.68686002 0.98148602 0.62906402 0.90145302
		 0.397264 0.86202502 0.73248202 0.86398101 0.70566303 0.903696 0.62962002 0.88232201
		 0.628142 0.94303697 0.383863 0.95094001 0.46885699 0.905689 0.70851803 0.958076 0.733706
		 0.94151801 0.62856698 0.93738002 0.64078897 0.95648402 0.36110201 0.94769001 0.45117599
		 0.92410201 0.712798 0.91980702 0.33271301 0.93037403 0.34555101 0.88010901 0.63825899
		 0.94502997 0.64005202 0.93989998 0.67379099 0.969419 0.44590101 0.93100899 0.64116597
		 0.93473798 0.73300803 0.95410699 0.67361897 0.90788198 0.72167599 0.91368902 0.757263
		 0.98131102 0.61846602 0.92716497 0.44210601 0.87823099 0.471205 0.93990701 0.708821
		 0.90070403 0.64898998 0.91208303 0.67186099 0.95736802 0.74900901 0.91119099 0.71167898
		 0.93268001 0.74405402 0.88573098 0.34254 0.95418 0.34104401 0.90106797 0.63774198
		 0.97327501 0.467417 0.96013701 0.65132099 0.89557803 0.70602101 0.90769798 0.48359299
		 0.93527102 0.467197 0.86832601 0.48884201 0.97191602 0.486561 0.911623 0.376333 0.91790199
		 0.47825199 0.91550601 0.44583499 0.91978902 0.75196701 0.91797501 0.70358402 0.91921002
		 0.63391501 0.91979402 0.66931301 0.91621298 0.71577698 0.91626298 0.73922598 0.91480201
		 0.73209798 0.91492403 0.72346401 0.91952097 0.66186899 0.91890901 0.65200299 0.91892999
		 0.64165002 0.91635698 0.466665 0.91509497 0.45460901 0.92236799 0.37175301 0.92167503
		 0.36094901 0.91998798 0.350714 0.96074402 0.55333197 0.96060902 0.57040399 0.97547102
		 0.57237202 0.97137302 0.59077603 0.956801 0.58782202 0.89441103 0.59214997 0.86538303
		 0.58002102 0.887748 0.55981398 0.88946301 0.57467997 0.86877 0.59399003 0.87765402
		 0.57818198 0.88131702 0.59170902 0.91935599 0.57027 0.91989899 0.57962698 0.93663901
		 0.55788302 0.947519 0.56767797 0.94846898 0.55434698 0.93560499 0.56780702 0.94254702
		 0.58433002 0.93107998 0.58202499 0.90217 0.57037503 0.90857399 0.584535 0.90064299
		 0.56022102 0.91860098 0.56247097 0.97088802 0.53477502 0.96648502 0.52561301 0.95237601
		 0.52717698 0.97539902 0.55188203 0.95711201 0.53847301 0.86825401 0.53840202 0.87679702
		 0.56085199 0.87971902 0.54084003 0.872509 0.52256501 0.86441803 0.561975 0.88436598
		 0.52514797 0.94492799 0.54225999 0.93948197 0.53013402 0.92817301 0.536713 0.93411201
		 0.548145 0.89124 0.54308498 0.90947801 0.53661001 0.89691502 0.52916098 0.90345401
		 0.54827899 0.91874802 0.54181498 0.918365 0.55253398 0.72591603 0.271871 0.69522202
		 0.29251 0.751863 0.26449001 0.70485002 0.28061301 0.77641302 0.245453 0.766559 0.234074
		 0.74919099 0.25026399 0.72840202 0.25491399 0.77451003 0.19417199 0.748393 0.23077799
		 0.98207903 0.93616903 0.96113199 0.96271002 0.88710302 0.93741697 0.92535597 0.96025997
		 0.86294901 0.96091002 0.86337602 0.93328702 0.94490898 0.93545002 0.88640702 0.961366
		 0.96040601 0.93508601 0.94644701 0.96412599 0.90183401 0.96477401 0.98312002 0.959517
		 0.92470002 0.94094199 0.90058702 0.93525702 0.92011601 0.96009302 0.920057 0.94052702
		 0.98260301 0.94693202 0.925466 0.95017201 0.94670302 0.94986099 0.96164101 0.94872802
		 0.92841703 0.96367699 0.93562502 0.96827102 0.91035199 0.93610799 0.91736299 0.93745703
		 0.92743498 0.93818098 0.93435103 0.93491602 0.917261 0.96302497 0.91100401 0.96436
		 0.93647301 0.95067102 0.92947698 0.95051098 0.88250798 0.94920099 0.89802498 0.94981599
		 0.86051702 0.94752401 0.91959202 0.95016199 0.90876001 0.949853 0.91560698 0.94999701
		 0.92226899 0.93906897 0.98110402 0.92664301 0.92300099 0.96206999 0.91503203 0.92891002
		 0.92239302 0.93310302 0.94028503 0.92632103 0.95892602 0.97617102 0.94441003 0.97243702
		 0.922598 0.95005202 0.95513302 0.92452103 0.98545301 0.97227401 0.91724402 0.97531903
		 0.92426902 0.96926302 0.97954702 0.86252302 0.95797098 0.88750499 0.88971502 0.85908002
		 0.92611599 0.882608 0.867724 0.88288802 0.86678302 0.85868102 0.94506598 0.85781097
		 0.890342 0.88332897 0.959279 0.85929698 0.94441301 0.888744 0.90579897 0.88730103
		 0.97867101 0.88496202 0.92453301 0.86388499 0.90244102 0.85668403 0.92147797 0.88259202
		 0.92027599 0.86366999 0.97889602 0.87325001 0.92577797 0.87312698 0.94588703 0.87347198
		 0.95957798 0.87337399 0.928904 0.88593602 0.93511403 0.89015102 0.91170597 0.85889202
		 0.91766298 0.86071497 0.92698699 0.86096698;
	setAttr ".uvst[0].uvsp[1000:1249]" 0.93376398 0.85746801 0.91899103 0.88552701
		 0.913499 0.88677198 0.93614799 0.87353998 0.92953998 0.87341201 0.887052 0.871158
		 0.90243298 0.871571 0.86618602 0.87142301 0.92053401 0.87317199 0.91129398 0.87247801
		 0.91691703 0.873083 0.92216402 0.86198902 0.97952902 0.85193503 0.92405701 0.88424999
		 0.91490102 0.85117602 0.92162502 0.85552001 0.93982202 0.84826303 0.95464802 0.90122402
		 0.94143403 0.89757901 0.92317498 0.87293702 0.95485502 0.84718502 0.98045802 0.89826602
		 0.91838902 0.89635903 0.92481202 0.89119202 0.86403602 0.77219301 0.95384902 0.76552802
		 0.91858101 0.78640801 0.94499898 0.78708202 0.92280602 0.80435002 0.92713398 0.79197001
		 0.95621502 0.82674402 0.95915198 0.81380802 0.97791302 0.77614403 0.92780501 0.79861701
		 0.97692102 0.80220503 0.88103199 0.800264 0.86344701 0.80690402 0.86202103 0.79063803
		 0.91983998 0.79894102 0.94326699 0.81216198 0.86210501 0.79814702 0.97764802 0.81496203
		 0.94668603 0.80133402 0.96064103 0.78740299 0.88133299 0.788899 0.95833802 0.77520901
		 0.979074 0.82868302 0.899777 0.78815699 0.91131401 0.80103898 0.91052699 0.78919101
		 0.92769402 0.80379701 0.90048403 0.80157399 0.93128502 0.78154498 0.93394202 0.80883002
		 0.91896403 0.79143798 0.93633002 0.76960802 0.93652099 0.80063403 0.93490398 0.78948098
		 0.929084 0.81440902 0.92817402 0.77757502 0.976758 0.788432 0.96204102 0.80166501
		 0.937774 0.82114702 0.86297703 0.78193402 0.94112599 0.77642298 0.91950202 0.80396003
		 0.92352802 0.79744703 0.92299098 0.79198402 0.91170901 0.77897203 0.90346497 0.77629203
		 0.88221502 0.778032 0.92309302 0.78725302 0.88560402 0.81075603 0.90552503 0.81353801
		 0.91444802 0.81077802 0.92631501 0.78719097 0.91818303 0.52763802 0.87273902 0.514063
		 0.90183502 0.517551 0.88703603 0.513695 0.92125601 0.59497702 0.93735802 0.60202801
		 0.95376402 0.60597003 0.96884602 0.60647702 0.96989501 0.50542903 0.95476902 0.50786602
		 0.91923702 0.49625 0.93822402 0.50727701 0.94104302 0.41873601 0.95445901 0.4149
		 0.91534603 0.433705 0.92870402 0.423087 0.77737701 0.059041999 0.786695 0.072238997
		 0.783566 0.076612003 0.77740598 0.074286997 0.785882 0.086916 0.78089601 0.088596001
		 0.78987002 0.086350001 0.79081702 0.095147997 0.78737098 0.095661998 0.783804 0.097157001
		 0.75420201 0.066077001 0.76681 0.077830002 0.762793 0.083862998 0.75488198 0.083021998
		 0.77407002 0.090305001 0.767443 0.091518998 0.76094502 0.093959004 0.77540898 0.098535001
		 0.77046299 0.098421998 0.765908 0.100402 0.780137 0.094720997 0.77829498 0.095073
		 0.77896798 0.099775001 0.78119701 0.099397004 0.72881502 0.075615004 0.74395001 0.086346
		 0.73944199 0.091712996 0.73095 0.092386 0.75305802 0.095985003 0.74500602 0.098008998
		 0.738267 0.10321 0.75552303 0.102959 0.74952197 0.10398 0.74452299 0.108385 0.761168
		 0.099803001 0.75902599 0.10041 0.76319802 0.103904 0.76055002 0.104727 0.70395303
		 0.089123003 0.72094703 0.098233998 0.717246 0.104699 0.70885402 0.106696 0.73051798
		 0.107549 0.72274101 0.110478 0.71687001 0.116899 0.73359603 0.114257 0.72761202 0.116051
		 0.723831 0.121441 0.739465 0.110237 0.73729599 0.11142 0.73953903 0.11514 0.74234498
		 0.11361 0.680031 0.107892 0.69870597 0.115043 0.69537801 0.123406 0.68556702 0.127334
		 0.70998299 0.123624 0.70239699 0.128967 0.69609499 0.135867 0.71488202 0.130743 0.70841199
		 0.13392401 0.704566 0.139412 0.71894902 0.124451 0.71719199 0.12638099 0.72044802
		 0.13017599 0.72248203 0.127694 0.65574902 0.12976199 0.68813902 0.143971 0.682675
		 0.15080801 0.675291 0.15707999 0.69337398 0.14864101 0.68670601 0.15396 0.681925
		 0.16084599 0.69933897 0.141288 0.69703102 0.143209 0.70359999 0.143898 0.70056701
		 0.14609499 0.63621199 0.157096 0.66662401 0.16777501 0.66210502 0.175036 0.65620798
		 0.18136799 0.67203301 0.173315 0.666318 0.178452 0.662458 0.185013 0.67581201 0.16404299
		 0.67356002 0.166894 0.67720097 0.17016301 0.67972302 0.166794 0.62313598 0.18407699
		 0.64969999 0.19191401 0.64738899 0.198148 0.64390898 0.202196 0.654495 0.19740801
		 0.65091002 0.20127 0.64798599 0.20607901 0.660357 0.191394 0.65675002 0.18875401
		 0.65505201 0.191617 0.658234 0.19481499 0.787328 0.137261 0.78283 0.125082 0.786551
		 0.123427 0.78923303 0.125542 0.78392297 0.116362 0.78709197 0.11773 0.79035097 0.118357
		 0.77420199 0.138947 0.76722801 0.127698 0.77116901 0.126417 0.77582997 0.126508 0.76698202
		 0.120738 0.77090102 0.12146 0.77515501 0.120026 0.77916902 0.12151 0.78114599 0.120578
		 0.7816 0.115011 0.77911103 0.116733 0.76307797 0.144961 0.754776 0.132706 0.757945
		 0.128369 0.76191902 0.128197 0.75230902 0.123542 0.75625998 0.122546 0.75988698 0.120862
		 0.76312101 0.123474 0.76457202 0.123498 0.762366 0.119225 0.76431698 0.119181 0.75058198
		 0.15657 0.73950201 0.147155 0.74342602 0.141885 0.74901998 0.138989 0.73560297 0.138723
		 0.74031001 0.136455 0.74501002 0.13204101 0.74983197 0.130518 0.75120997 0.12860499
		 0.74756998 0.12604199 0.74923003 0.1235 0.73556203 0.17097101 0.721726 0.163357 0.72648501
		 0.157003 0.732719 0.153401 0.71754903 0.155406 0.72325897 0.15194499 0.72852498 0.14711601
		 0.73325402 0.14602999 0.73479998 0.14429 0.73058498 0.14194401 0.73221803 0.13947
		 0.72031498 0.192277 0.703089 0.18587901 0.70759898 0.17707799 0.71405202 0.172006
		 0.70335603 0.171143 0.70838797 0.164602 0.69767201 0.176763 0.71454799 0.162691;
	setAttr ".uvst[0].uvsp[1250:1499]" 0.716281 0.16051701 0.713606 0.155518 0.71177399
		 0.158053 0.70186502 0.21599799 0.68481898 0.20889699 0.68951797 0.20122799 0.69615299
		 0.196265 0.67882198 0.200664 0.68440002 0.195408 0.68837899 0.188493 0.69375098 0.18658
		 0.695481 0.18395799 0.68941897 0.181798 0.69150603 0.178784 0.68030798 0.233539 0.66218501
		 0.231783 0.66656703 0.22063801 0.67349398 0.216199 0.658391 0.21939801 0.66357899
		 0.215435 0.667036 0.210344 0.67307901 0.20853999 0.67615598 0.206588 0.66913199 0.20493101
		 0.67264199 0.20226701 0.64330298 0.211014 0.63508397 0.21180101 0.64697099 0.210751
		 0.79111397 0.106742 0.78783703 0.106911 0.78431302 0.106725 0.78141898 0.107036 0.77922398
		 0.107929 0.77531803 0.109189 0.770868 0.110028 0.76677197 0.110524 0.76386398 0.11122
		 0.76178998 0.111684 0.75787097 0.111545 0.73371297 0.125652 0.729608 0.129444 0.726888
		 0.132892 0.725299 0.13515399 0.72183502 0.13813899 0.71594501 0.14209799 0.71100402
		 0.146657 0.70824498 0.149297 0.705832 0.151412 0.70009297 0.155826 0.69420099 0.161714
		 0.68906498 0.168115 0.68507999 0.172454 0.68333602 0.175282 0.68069899 0.180388 0.67644501
		 0.187066 0.67124099 0.192718 0.66653597 0.196375 0.66391701 0.19907901 0.661053 0.203376
		 0.65783298 0.20806 0.65407199 0.212689 0.73879403 0.122555 0.74313402 0.11996 0.74537098
		 0.118082 0.748528 0.115373 0.75321102 0.112704 0.79024202 0.053086001 0.79306197
		 0.070762999 0.794029 0.087278999 0.79420799 0.096620999 0.79436302 0.106435 0.79379398
		 0.116877 0.79323202 0.12421 0.79311597 0.13379 0.61837602 0.211519 0.78124201 0.162717
		 0.77092099 0.162883 0.76393998 0.176598 0.75066298 0.185426 0.60125202 0.189869 0.59722501
		 0.169624 0.60763502 0.138989 0.62751502 0.107243 0.65328902 0.080631003 0.681674
		 0.059103001 0.71034002 0.042086001 0.73987502 0.029782001 0.76624501 0.023049001
		 0.784693 0.020664001 0.65294898 0.16839001 0.63878697 0.19278499 0.67137802 0.14449
		 0.66790402 0.26801601 0.69331098 0.259022 0.72418898 0.22979601 0.74647403 0.203354
		 0.63988203 0.236827 0.65021098 0.214479 0.64597702 0.226068 0.650038 0.218647 0.095394
		 0.92804402 0.109497 0.92288703 0.109497 0.92288703 0.13499001 0.916875 0.135379 0.98805702
		 0.10847 0.98500502 0.10847 0.98500502 0.093362004 0.98382503 0.067813002 0.97421002
		 0.070922002 0.93279803 0.070922002 0.93279803 0.095394 0.92804402 0.067813002 0.97421002
		 0.093362004 0.98382503 0.094765 0.84789902 0.108469 0.84242302 0.108469 0.84242302
		 0.13321801 0.83841503 0.130796 0.90816402 0.104717 0.90437901 0.104717 0.90437901
		 0.090975001 0.90264201 0.068580002 0.89702702 0.071757004 0.85365301 0.071757004
		 0.85365301 0.094765 0.84789902 0.068580002 0.89702702 0.090975001 0.90264201 0.094471
		 0.78123802 0.084691003 0.787615 0.112153 0.77944201 0.094471 0.78123802 0.084691003
		 0.787615 0.114068 0.82927102 0.095371 0.82790601 0.095371 0.82790601 0.085862003
		 0.82322103 0.085862003 0.82322103 0.114068 0.82927102 0.13651501 0.82685202 0.112153
		 0.77944201 0.021524001 0.77731901 0.059581 0.76937902 0.073316 0.76433003 0.063014001
		 0.70199603 0.075966001 0.70704103 0.073316 0.76433003 0.075966001 0.70704103 0.076590002
		 0.68951601 0.065444 0.69275802 0.048268002 0.69414002 0.076590002 0.68951601 0.065444
		 0.69275802 0.090919003 0.62647003 0.100846 0.62256902 0.090919003 0.62647003 0.119635
		 0.62150502 0.100846 0.62256902 0.063014001 0.70199603 0.040683001 0.76971102 0.059581
		 0.76937902 0.119791 0.69902003 0.040683001 0.76971102 0.119791 0.69902003 0.136234
		 0.70000702 0.137116 0.76860702 0.119635 0.62150502 0.144997 0.625431 0.048268002
		 0.69414002 0.022825001 0.692357 0.054659002 0.327378 0.066831 0.329916 0.054659002
		 0.327378 0.066831 0.329916 0.121146 0.326626 0.069700003 0.40874001 0.053226002 0.41181001
		 0.069700003 0.40874001 0.053226002 0.41181001 0.038244002 0.41171801 0.052995 0.52240902
		 0.067933001 0.52444702 0.067933001 0.52444702 0.052995 0.52240902 0.038626 0.52266002
		 0.096390001 0.61171103 0.096390001 0.61171103 0.114169 0.614591 0.114169 0.614591
		 0.12961 0.61438203 0.100689 0.50890201 0.116539 0.51051003 0.100689 0.50890201 0.116539
		 0.51051003 0.131484 0.508717 0.102081 0.428633 0.114526 0.42670301 0.102081 0.428633
		 0.114526 0.42670301 0.128196 0.42543301 0.89271402 0.91929102 0.90674102 0.92457902
		 0.86729902 0.91304302 0.89271402 0.91929102 0.86627102 0.98418802 0.89320201 0.98138702
		 0.90833402 0.98034102 0.89320201 0.98138702 0.93411702 0.97082102 0.93095702 0.92943603
		 0.93095702 0.92943603 0.90674102 0.92457902 0.93411702 0.97082102 0.90833402 0.98034102
		 0.89290202 0.84038001 0.90663701 0.84572202 0.86813402 0.83661401 0.89290202 0.84038001
		 0.87121302 0.90627402 0.89723003 0.90223801 0.91092002 0.90037102 0.89723003 0.90223801
		 0.93304503 0.89459002 0.92962003 0.85128701 0.92962003 0.85128701 0.90663701 0.84572202
		 0.93304503 0.89459002 0.91092002 0.90037102 0.90715402 0.77180302 0.91690803 0.77814001
		 0.88950503 0.76999003 0.90715402 0.77180302 0.91690803 0.77814001 0.905963 0.81857902
		 0.88740301 0.81978703 0.91606998 0.81410801 0.905963 0.81857902 0.91606998 0.81410801
		 0.88740301 0.81978703 0.86500102 0.81725502 0.88950503 0.76999003 0.97968203 0.76682502
		 0.94245702 0.76154202 0.92873901 0.75644302 0.92630202 0.69912702 0.93927503 0.69413
		 0.92873901 0.75644302 0.92630202 0.69912702 0.925273 0.68271202 0.93640202 0.68600702;
	setAttr ".uvst[0].uvsp[1500:1568]" 0.95357502 0.68746603 0.925273 0.68271202
		 0.93640202 0.68600702 0.91122103 0.61960602 0.90131003 0.61565602 0.91122103 0.61960602
		 0.88252801 0.61450702 0.90131003 0.61565602 0.93927503 0.69413 0.96135902 0.76194602
		 0.94245702 0.76154202 0.88248903 0.69094002 0.96135902 0.76194602 0.86603802 0.69186801
		 0.88248903 0.69094002 0.86490202 0.76048303 0.88252801 0.61450702 0.85714501 0.61832303
		 0.95357502 0.68746603 0.97902602 0.68579203 0.94655401 0.32098901 0.93440902 0.32369801
		 0.93440902 0.32369801 0.94655401 0.32098901 0.88000602 0.32116601 0.93263203 0.402612
		 0.94915903 0.40545601 0.93263203 0.402612 0.96414602 0.405155 0.94915903 0.40545601
		 0.93435901 0.51954502 0.94926101 0.51755202 0.93435901 0.51954502 0.94926101 0.51755202
		 0.96361202 0.517838 0.905348 0.60656202 0.905348 0.60656202 0.88752502 0.60926002
		 0.88752502 0.60926002 0.87209201 0.60890102 0.90126902 0.50764602 0.88538802 0.50926203
		 0.90126902 0.50764602 0.88538802 0.50926203 0.87041003 0.50747102 0.88737702 0.42527601
		 0.89985102 0.42720601 0.89985102 0.42720601 0.87367803 0.424005 0.88737702 0.42527601
		 0.039541002 0 0.21748404 0 0.17737132 1 0 0.98574728 0 0 0.25261468 4.1704637e-017
		 0.60827005 0.81475562 0.43651852 1 0 0 0.49942705 0 0.92797875 0.50765228 0.8441869
		 1 0.12716848 0 1 4.3701874e-017 0.72173506 0.90396851 0 0.41311911 0 0 1 0 0.54177999
		 0.84052467;
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr -s 1350 ".vt";
	setAttr ".vt[0:165]"  0.33941793 0.04410708 -0.43052062 0.34344327 0.035004258 -0.39341536
		 0.40413278 0.10786879 -0.37030849 0.33181864 -0.015005469 -0.3213217 0.23070465 -0.055000663 0.1076377
		 0.34026927 -0.13385713 0.17283472 0.2546255 -0.15539014 0.1559076 0.3475185 0.026124597 0.069232702
		 0.28242299 -0.023604751 0.094383344 0.33274829 0.10862505 -0.43693379 0.33770084 0.084842324 -0.47680563
		 0.29466951 -0.070762992 0.082732782 0.33463788 0.1233145 -0.46404877 0.29996991 -0.10927999 -0.017288983
		 0.26613897 0.018293023 0.031534702 0.28529173 -0.0089153051 -0.025235772 0.40402353 0.04255259 -0.5031091
		 0.38079348 0.0038353205 -0.38848948 0.31047574 0.04951632 -0.30997366 0.24089597 -0.11678541 0.17205691
		 0.26638502 -0.07364881 0.2168808 0.34210455 0.027435899 -0.34501565 0.34648961 0.01259768 0.010908633
		 0.27094257 -0.11760557 0.00087347627 0.32587987 0.075650811 -0.4248983 0.42811018 0.10844767 -0.45609668
		 0.43580431 0.097402215 -0.41190916 0.45458797 0.033235192 -0.4350448 0.27957043 0.0079838037 0.0034135282
		 0.37542701 -0.042505622 0.025964975 0.39164683 0.092107415 -0.50150216 0.44378877 0.043412805 -0.47901237
		 0.29638427 0.0012603998 -0.012302786 0.30724257 -0.090719581 0.096147805 0.25953776 -0.0059150457 0.059294164
		 0.39078355 0.082147241 -0.2081854 0.31985581 0.018737435 -0.31374159 0.40892899 0.035760522 -0.19842714
		 0.41114765 0.099005342 -0.28962424 0.25242823 -0.14605176 0.099965274 0.28630182 -0.12403142 0.043653518
		 0.22348851 -0.032554984 0.16060938 0.23506831 -0.025267959 0.11923857 0.30656147 -0.12273347 0.052154124
		 0.42688981 0.013425469 -0.39135587 0.3322705 -0.12717092 0.12316485 0.30522513 -0.016414046 0.095367372
		 0.31129169 -0.017502189 0.10308702 0.33223724 -0.0022720098 0.094833001 0.31874418 0.0044742823 -0.0079272985
		 0.26255816 -0.009559989 -0.01122874 0.3202911 0.081089616 -0.29993844 0.44283795 0.024611115 -0.30194682
		 0.25779942 -0.040628791 0.08103241 0.36179671 -0.0045493841 0.065289795 0.32925719 0.024572015 0.063582391
		 0.33589154 -0.028295875 0.15827261 0.24170718 -0.046226859 0.18255581 0.39784855 0.07095015 -0.50442874
		 0.32828522 -0.031570792 0.1913586 0.23718962 -0.099168181 0.10226189 0.33831796 0.0087391138 0.011802435
		 0.36115605 0.029338479 -0.4823024 0.26673737 -0.095964789 0.090762317 0.34632176 -0.072170615 0.15557639
		 0.23417653 -0.027852416 0.15792273 0.30852458 -0.13096845 0.10396004 0.32201988 -0.0074619055 -0.23110852
		 0.32230139 -0.0024436712 0.087120101 0.27179858 -0.12259901 0.025447011 0.2807554 -0.028215766 0.096637309
		 0.4260529 0.04410708 -0.370415 0.31534553 0.024826646 -0.21986207 0.34261554 -0.12814176 0.14648749
		 0.31510431 -0.14246976 0.18413787 0.2865642 -0.0051597357 0.18337412 0.2682724 -0.072614074 0.068324357
		 0.3268874 -0.025543571 -0.28641695 0.33515114 -0.11883962 0.054211646 0.23613727 -0.077848792 0.1839934
		 0.31666523 0.063487649 -0.26381609 0.30231845 -0.052113891 0.093931749 0.25238097 -0.065996528 0.043374389
		 0.28340676 -0.1123842 -0.015188098 0.35950482 0.0068584681 -0.40790692 0.32572138 -0.044247031 -0.018475771
		 0.32910585 0.066798806 -0.38037893 0.34981033 -0.0063432455 -0.34801239 0.41924119 -0.0024837255 -0.34120795
		 0.27326262 -0.11031568 0.20011902 0.29448268 -0.030348182 0.098983437 0.3530364 0.015444398 -0.43934694
		 0.35350931 -0.11527193 0.048968256 0.28106293 -0.025987983 0.19852516 0.42030522 0.032596231 -0.33814982
		 0.33782846 0.052769303 -0.34176582 0.31556553 -0.013464332 0.12376074 0.44101673 0.011197686 -0.25345206
		 0.32120636 -0.0061687231 -0.019535303 0.36502749 -0.10686147 0.015875459 0.34489328 -0.021396995 0.092290342
		 0.24200991 -0.0085872412 0.05815658 0.35416704 -0.03944242 0.002048552 0.30041689 -0.050348639 0.21573848
		 0.22368255 -0.10808122 0.13826634 0.36707789 0.089789033 -0.35236868 0.36994687 0.030427575 -0.36865029
		 0.29257882 -0.0049442053 0.15937482 0.24554583 -0.012658477 0.017069638 0.25438422 -0.025156379 0.084679306
		 0.32034072 -0.0074371099 0.15714948 0.34455258 0.083951592 -0.33201909 0.21717359 -0.064672828 0.15195067
		 0.36495873 -0.11060464 0.034377813 0.33397338 -0.076866508 0.12095101 0.35910991 -0.054596305 0.081843644
		 0.28336889 -0.1509155 0.1772058 0.318477 0.0047012568 -0.01262182 0.41437358 -0.0001758337 -0.21179259
		 0.41431934 0.11668265 -0.41911542 0.4164266 0.062984109 -0.33478871 0.23579198 -0.051748633 0.18764049
		 0.25117001 -0.054924369 0.0081461072 0.24293236 -0.028721213 0.053679466 0.30555856 0.037133813 -0.2689555
		 0.2866753 -0.042541862 0.097786888 0.35272655 -0.10523641 -0.0004440248 0.36196941 0.056838632 -0.34863415
		 0.24128841 -0.15225065 0.12866379 0.27581486 -0.1390537 0.094229877 0.28457525 0.0032907724 -0.018319547
		 0.29014504 -0.047811866 -0.025311083 0.4176257 0.072930932 -0.35669693 0.43343443 0.039236665 -0.24902213
		 0.26264802 -0.03230226 0.10072905 0.34191078 -0.076866508 0.18778008 0.35090053 0.00044119358 0.0045412779
		 0.35623884 0.022228837 0.039205015 0.45153242 0.070556283 -0.42106879 0.43636221 0.055693269 -0.29775774
		 0.29882991 -0.015005469 0.12615673 0.38192165 0.11891139 -0.48617646 0.40335923 0.12970507 -0.4532437
		 0.267241 -0.049792647 -0.0155406 0.36527121 -0.0010112524 0.035586447 0.32030991 -0.031545997 0.10311317
		 0.43766314 -0.0091680288 -0.31022939 0.33918595 0.018293023 0.036932111 0.31240559 -0.0077956915 0.18686269
		 0.33072603 -0.061203361 0.086840957 0.24314052 -0.023135543 0.13153027 0.26264554 -0.048468947 0.2143006
		 0.32973498 -0.1062454 -0.011579454 0.40088794 0.093648553 -0.32091251 0.31519651 0.0063911676 -0.2763629
		 0.32797062 -0.033627868 0.1217104 0.24668103 0.01263392 0.023228258 0.26822749 -0.021685004 0.082456291
		 0.3080847 -0.092271209 0.20421323 0.26472926 -0.029730201 0.11046644 0.29238254 -0.025876403 0.21017805
		 0.35017449 0.051760316 -0.48183367 0.44144973 0.080947518 -0.47062752 0.2638472 0.0061784983 -0.0058890581
		 0.43538111 -0.021536231 -0.26754808 0.37686491 -0.046907783 0.056496233;
	setAttr ".vt[166:331]" 0.26402682 -0.056410193 0.09193553 0.32487708 0.072209001 -0.22180164
		 0.40679371 0.071897149 -0.25523031 0.33435172 -0.034747481 -0.25227898 0.41298553 -0.032363296 -0.2366192
		 0.34179473 0.1089884 -0.37851092 0.3572225 0.055543542 -0.23004165 0.36798856 0.077052712 -0.32158205
		 0.26156244 -0.042330146 0.20472854 0.28853902 -0.025580764 0.10605314 0.31604558 -0.0070766211 0.0082404613
		 0.29206312 -0.025015235 0.072742492 0.27323899 -0.036162734 0.1266983 0.25907901 -0.044573188 0.17373122
		 0.25479335 -0.03821981 0.15882595 0.26154107 -0.040060401 0.143143 0.29774666 -0.020465255 0.0567469
		 0.29970717 -0.012849212 0.045791626 0.30862385 -0.015369773 0.026043028 0.36140168 0.029264092 -0.2695584
		 0.3608979 0.053060174 -0.29998112 0.37547183 0.087052941 -0.39066949 0.37976938 0.073799729 -0.42388877
		 0.37385443 0.10119593 -0.45020062 0.41862613 -0.014648795 -0.11597404 0.40983725 -0.027927756 -0.072613776
		 0.4062424 -0.06502378 -0.082928121 0.38087621 -0.073988318 -0.052581072 0.38521889 -0.038042426 -0.034442753
		 0.29025611 -0.020311713 -0.033775806 0.2975409 -0.06924665 -0.093079448 0.2792654 -1.3113022e-006 -0.11932048
		 0.27633032 -0.003542304 -0.080381036 0.31092271 -0.074240088 -0.071584731 0.27975973 -0.040521979 -0.082514226
		 0.29532954 -0.049013495 -0.05110991 0.33322597 0.0080085993 -0.080603242 0.33045647 0.016706109 -0.046878695
		 0.38905489 0.04926455 -0.096799761 0.39765 0.010501504 -0.080224991 0.40739644 0.0200001 -0.1106852
		 0.37833387 0.033603311 -0.08000502 0.3816686 0.006606698 -0.031543136 0.36557862 0.030498147 -0.037678748
		 0.28201121 0.049588799 -0.088656694 0.29384425 0.027186036 -0.034097433 0.28582397 0.025081277 -0.11226547
		 0.33626747 0.0036064386 -0.10216594 0.40084279 -0.046226859 -0.15525138 0.38969827 -0.043157935 -0.17022046
		 0.39307821 -0.0066817999 -0.17817456 0.41147143 -0.054090858 -0.12317547 0.40891492 -0.0071853399 -0.15566307
		 0.32184964 -0.04853189 -0.16608882 0.28269249 -0.031903625 -0.12346914 0.30791655 -0.020641685 -0.17192835
		 0.35207367 -0.042654395 -0.18182597 0.29867607 -0.060951591 -0.12469879 0.34363261 -0.013021827 -0.19453895
		 0.40247458 0.030287385 -0.14321798 0.38666362 0.026376367 -0.17237738 0.37365547 0.055719018 -0.16200167
		 0.38742304 0.064710259 -0.12201896 0.29656172 0.014070153 -0.16876343 0.32061493 0.058606744 -0.17257166
		 0.32977286 0.017981172 -0.19518435 0.29314172 0.055910707 -0.1423592 0.34443918 0.045576692 -0.15742061
		 0.33850017 0.017981172 -0.12728637 1.5832484e-007 -0.3157028 -0.22127178 0.14791319 -0.30270365 -0.49762115
		 0.17895557 -0.2752035 -0.63431871 0.075088389 -0.3177284 -0.41653976 0.18154305 -0.27581385 -0.56981683
		 0.034315884 -0.31955755 -0.2840994 0.10913683 -0.29221189 -0.24847786 0.12371794 -0.2976917 -0.34420204
		 0.18154064 -0.275967 -0.41684988 1.5832484e-007 -0.27827871 -0.073602088 0.082183808 -0.30371892 -0.032534279
		 0.16758397 -0.26410329 -0.26664197 0.071233235 -0.19970834 0.32214797 0.0099975755 -0.14106596 0.35864672
		 0.07728561 -0.10858381 0.27144673 0.015465757 -0.041609168 0.35733899 0.024294805 -0.16579473 0.24308649
		 0.073799416 -0.17026937 0.2497088 0.090376616 -0.10173833 0.34921256 0.01340333 -0.10544431 0.26823971
		 0.086566381 -0.14215314 0.34427163 0.0087322285 -0.10393178 0.36399454 0.0084034791 -0.078954101 0.30497012
		 0.018284962 -0.19917142 0.32965267 0.080031529 -0.042400718 0.34893078 0.08245343 -0.080326438 0.29860097
		 0.013831458 -0.041180015 0.34134114 0.079421297 -0.041529059 0.33677644 0.048662748 -0.20256269 0.33664635
		 0.050803229 -0.041357398 0.35676897 0.050372757 -0.10594976 0.36530489 0.049701083 -0.14731061 0.36180189
		 0.0090373307 -0.052790046 0.3595646 0.0088646468 -0.075210929 0.36287805 0.084669575 -0.064628959 0.31945422
		 0.085036136 -0.049183249 0.33084235 0.086003497 -0.052034736 0.3490113 0.088905498 -0.072887778 0.35006157
		 0.0080203284 -0.049982429 0.33445397 0.0079706591 -0.065385222 0.32331401 0.05055017 -0.076320052 0.36402506
		 0.050706223 -0.054302931 0.36033559 0.045528971 -0.10310435 0.24623206 0.044854894 -0.076763868 0.28385887
		 0.050152838 -0.16265523 0.22593209 0.046732828 -0.040661216 0.33883661 0.046101324 -0.063912749 0.31534132
		 0.046411157 -0.049625754 0.33004522 0.084404685 -0.038973689 0.34127024 0.079357475 -0.19003904 0.29971492
		 0.0088859666 -0.037571311 0.35100269 0.093675978 -0.068714499 0.33535263 0.091800451 -0.049435019 0.33833507
		 0.093822628 -0.090293288 0.32897618 0.0031103077 -0.12382066 0.32542393 0.0011401526 -0.095711112 0.34737694
		 0.049873747 -0.0341295 0.34840366 0.089203507 -0.12472475 0.31957728 0.010177325 -0.18855321 0.29781568
		 0.00094857719 -0.071375251 0.35260877 0.00098405406 -0.050045371 0.35175493 0.16642037 -0.19356859 0.30608419
		 0.10645473 -0.13475072 0.33841082 0.17195243 -0.10126054 0.24433771 0.11019167 -0.044165015 0.33573112
		 0.11159655 -0.15907228 0.22355771 0.16049096 -0.16366422 0.22643369 0.18831202 -0.098120093 0.32302552
		 0.10125387 -0.10028684 0.25009674 0.18750793 -0.13876188 0.32221445 0.10152818 -0.10061729 0.34349573
		 0.098564684 -0.077772975 0.28978583 0.11763003 -0.19195402 0.31464103 0.17165205 -0.038040757 0.31775346
		 0.17906673 -0.07398665 0.27109209 0.10665819 -0.043850541 0.3215923 0.16938853 -0.037424684 0.30568203
		 0.14557639 -0.19458044 0.32262594 0.14274062 -0.039512992 0.33096302 0.14697425 -0.10277212 0.3433871
		 0.14832944 -0.14171255 0.34295639 0.10473774 -0.055929542 0.33832553 0.10311046 -0.075461745 0.34179768
		 0.17768556 -0.057846427 0.28759822 0.17488518 -0.044496894 0.29820341 0.17906676 -0.046989799 0.31727138
		 0.18511203 -0.067345023 0.32064381 0.10027469 -0.053293586 0.31600109 0.098623894 -0.066837668 0.30612183
		 0.1458745 -0.073255897 0.34003803 0.14391613 -0.052427649 0.33532906 0.13622232 -0.098085761 0.2312609
		 0.13941292 -0.073219657 0.27317825 0.13503742 -0.15572011 0.21177706 0.13751128 -0.039049029 0.31453249
		 0.13814291 -0.059799552 0.2926856 0.1370123 -0.04788816 0.30518055;
	setAttr ".vt[332:497]" 0.17501289 -0.034572959 0.3091116 0.17217948 -0.18474233 0.27981004
		 0.10330679 -0.040987372 0.33067906 0.18799978 -0.06249845 0.30214623 0.1833854 -0.044633627 0.30445445
		 0.19154516 -0.086354136 0.29883984 0.092919186 -0.1191262 0.30582383 0.090575308 -0.092171431 0.32419631
		 0.14092658 -0.032154799 0.32281971 0.19001487 -0.12197757 0.2933335 0.10760653 -0.18278778 0.28095224
		 0.092654251 -0.072928309 0.33006418 0.095752575 -0.054783702 0.3315402 0.24063121 -0.16013074 0.20579076
		 0.26032805 -0.094765306 0.20985863 0.2491338 -0.02583158 0.2561942 0.2747032 -0.076143622 0.26718244
		 0.22144273 -0.032154918 0.29157153 0.24947207 -0.031939149 0.27727917 0.1876521 -0.1179055 0.2773928
		 0.21409646 -0.1284734 0.29957047 0.25947648 -0.16246521 0.22228047 0.23780949 -0.03543961 0.28918302
		 0.24522632 -0.1715948 0.27480754 0.1833404 -0.11054438 0.22275338 0.18058509 -0.16253889 0.22202957
		 0.19556834 -0.15492117 0.18829799 0.22600763 -0.027170539 0.28002033 0.21465489 -0.083799958 0.30661395
		 0.18160224 -0.15699041 0.2002067 0.21460044 -0.17434204 0.27337673 0.24547735 -0.089171052 0.29828376
		 0.27986643 -0.12211597 0.26137611 0.20866154 -0.10381925 0.2010673 0.27543417 -0.10715139 0.2279259
		 0.19049501 -0.16899431 0.24718967 0.22869202 -0.05965364 0.22630334 0.212706 -0.046147704 0.27178016
		 0.23593624 -0.038973212 0.24519876 0.2262558 -0.039108634 0.29489923 0.20131534 -0.067909002 0.25693658
		 0.26490435 -0.04081738 0.25175932 0.218536 -0.059360862 0.3035748 0.23962098 -0.025267959 0.2652432
		 0.2676194 -0.05333364 0.22165844 0.24132171 -0.06112802 0.29726222 0.26261243 -0.051204085 0.2739183
		 0.20294958 -0.052000403 0.30157617 0.26200956 -0.035757422 0.24157023 0.26683444 -0.16618383 0.2514402
		 0.24963731 -0.13366544 0.29285815 0.1921268 -0.074849486 0.29806614 0.21750224 -0.15759981 0.19410923
		 0.27590224 -0.061420798 0.23700336 0.21719027 -0.03273046 0.28856543 0.23492634 -0.02453649 0.28376916
		 0.2436631 -0.02457273 0.27371743 0.25629526 -0.03273046 0.23316464 0.25574186 -0.052152991 0.21169388
		 0.23825404 -0.10180128 0.19764283 0.25070882 -0.026552558 0.26390919 0.1779667 -0.11216676 0.25315729
		 0.18590897 -0.069284797 0.28416407 0.19951789 -0.047638297 0.29333851 0.25376457 -0.030563712 0.26633325
		 0.35329628 0.044709802 -0.18203887 0.37265018 -0.041897178 -0.17974949 0.36169976 0.01902926 -0.19087979
		 0.37041527 -0.0094588995 -0.19156837 0.33433282 0.01338923 -0.020051479 0.33805794 -0.015257239 -0.018837422
		 0.33765113 -0.048645377 -0.030554652 0.34211189 -0.07690084 -0.053591102 0.36978137 -0.039766669 -0.2244437
		 0.36358714 -0.012117743 -0.20091742 0.35561424 0.05670321 -0.19443238 0.35832691 0.017427087 -0.18800175
		 0.37838107 0.031758904 -0.35846379 0.3827377 -0.001662612 -0.35898662 0.37299812 0.080409646 -0.34379292
		 0.37488544 0.056007981 -0.35602993 0.073356971 -0.32942235 0.28401732 0.045020323 -0.2689327 0.31133696
		 0.061673217 -0.25008619 0.31880385 0.086961217 -0.26441705 0.30419216 0.058553584 -0.19780672 0.34527448
		 0.086485803 -0.19205701 0.33764216 0.036529467 -0.19747007 0.34898514 0.034973234 -0.16413343 0.35743818
		 0.056472279 -0.16377485 0.35103807 0.07867381 -0.15500295 0.34507811 0.17161165 -0.32033002 0.25537816
		 0.14019088 -0.2570194 0.29401499 0.1676075 -0.23544633 0.29853949 0.19676958 -0.25023496 0.27445769
		 0.12515806 -0.19116628 0.33186403 0.16044587 -0.19472921 0.32439989 0.19177198 -0.19046915 0.30508593
		 0.12542069 -0.1558708 0.34001437 0.15358701 -0.16647279 0.33285278 0.17910452 -0.15903699 0.31749585
		 0.09764453 -0.16160905 0.34093204 0.10893331 -0.16135728 0.33998117 0.10786188 -0.13771284 0.34002617
		 0.09459351 -0.13796461 0.34048265 0.26104671 -0.30519712 0.18051663 0.24417162 -0.25017202 0.23933747
		 0.26852298 -0.22912633 0.22912037 0.28581688 -0.23436391 0.19168255 0.22849315 -0.1922859 0.28206858
		 0.26414517 -0.19141805 0.25794169 0.29020667 -0.1726402 0.22516829 0.22547999 -0.16261899 0.28528506
		 0.2543819 -0.16571653 0.26728648 0.27725506 -0.14460123 0.24223271 0.19836617 -0.15889776 0.30157617
		 0.20821443 -0.15922582 0.29375929 0.19234915 -0.13616598 0.30295494 0.20322877 -0.13619077 0.2919195
		 0.33413881 -0.27971876 0.085031763 0.31905639 -0.21862829 0.14705995 0.33564073 -0.1926769 0.12908694
		 0.34502077 -0.19614542 0.089426205 0.32100055 -0.16145551 0.18902175 0.34255397 -0.15868032 0.15204293
		 0.34919536 -0.13633764 0.11325945 0.32085159 -0.13258207 0.19404799 0.34159619 -0.13688505 0.16451186
		 0.34386656 -0.11155355 0.13487452 0.29084516 -0.13374555 0.21966472 0.30025357 -0.13046491 0.209712
		 0.29187864 -0.10883749 0.2114149 0.28078622 -0.11269605 0.22340408 0.39703459 -0.24060667 -0.027810931
		 0.37233809 -0.17376173 0.034380078 0.38836658 -0.14067781 0.0083968341 0.40445173 -0.14558446 -0.041114718
		 0.36520013 -0.11379468 0.071171939 0.38768542 -0.099321723 0.032495201 0.39659268 -0.082753539 -0.011907607
		 0.35634512 -0.081128478 0.076843441 0.3781659 -0.079573035 0.041326344 0.37805235 -0.054636359 0.010650456
		 0.34379572 -0.093581557 0.10801145 0.34688219 -0.086355567 0.095544651 0.33557922 -0.062654853 0.10122116
		 0.33442032 -0.071302772 0.11654484 0.44985744 -0.20834005 -0.15838084 0.41666764 -0.066610694 -0.066897452
		 0.42491949 -0.053665519 -0.10871989 0.42711928 -0.051496863 -0.15958706 0.39974278 -0.043298125 -0.059943974
		 0.41354123 -0.040628791 -0.10407498 0.41170102 -0.027285933 -0.14818707 0.38598257 -0.050131202 -0.012726307
		 0.39215297 -0.04659307 -0.028153569 0.36993504 -0.03064096 -0.0040671825 0.37625962 -0.029161811 -0.022754967
		 0.46907204 -0.16478574 -0.30305803 0.44005874 -0.03308332 -0.22801 0.4539851 -0.015848517 -0.26754564
		 0.4571327 -0.0072482824 -0.31145668 0.42329264 -0.0079063177 -0.22683927 0.43832809 -0.0033515692 -0.26631132
		 0.43968987 0.014765382 -0.30447263 0.41683808 -0.026300788 -0.180509;
	setAttr ".vt[498:663]" 0.42056763 -0.021032691 -0.19874385 0.40629441 -0.0029281378 -0.19561034
		 0.40356982 -0.0081590414 -0.177174 0.47178251 -0.13005865 -0.42987433 0.45902753 0.014841676 -0.37908047
		 0.46638548 0.033019662 -0.41224423 0.47063303 0.031255364 -0.44297692 0.44202474 0.045692086 -0.38553271
		 0.45405346 0.049161553 -0.41643521 0.46225572 0.061355233 -0.45146087 0.43031222 0.046664834 -0.33677331
		 0.44620121 0.027827859 -0.33794644 0.44630992 0.034246087 -0.35752478 0.43096721 0.053447366 -0.35873339
		 0.052006867 -0.19021738 0.19409284 0.074700356 -0.13572156 0.23016593 0.056141168 -0.12399328 0.23287153
		 0.042846739 -0.13511884 0.22619697 0.072382517 -0.10440099 0.25920716 0.053764213 -0.10942113 0.24738643
		 0.035621252 -0.11407125 0.23999766 0.12448399 -0.17831647 0.18019754 0.14975785 -0.13604772 0.20431727
		 0.13004448 -0.13102949 0.20754096 0.11025308 -0.13310087 0.21651408 0.15379985 -0.11819398 0.22348669
		 0.13138554 -0.11573446 0.21768275 0.11105011 -0.11724699 0.23068827 0.092786565 -0.1198467 0.23832056
		 0.082980707 -0.11713636 0.2466127 0.084539309 -0.097730994 0.27235976 0.096050456 -0.10411489 0.25785217
		 0.17749144 -0.18964994 0.14054848 0.20395972 -0.13211858 0.16870287 0.19009042 -0.12348974 0.1887265
		 0.17498669 -0.13031042 0.19720307 0.2162132 -0.10368192 0.19675139 0.19555157 -0.10771692 0.20229733
		 0.18223113 -0.11612737 0.21711972 0.1695919 -0.11988294 0.21458188 0.162636 -0.12230718 0.21640286
		 0.17580986 -0.11194074 0.23081607 0.16744429 -0.11389768 0.233399 0.21678582 -0.17723119 0.062151343
		 0.23972513 -0.11219251 0.071723282 0.22524586 -0.11273515 0.099733338 0.21533605 -0.1275028 0.12716658
		 0.25531131 -0.090467811 0.09623073 0.23357812 -0.099321723 0.11156164 0.22223482 -0.10389745 0.14542556
		 0.22069281 -0.11064565 0.16846889 0.21933772 -0.11166227 0.18052624 0.23346224 -0.095774055 0.18614569
		 0.23343852 -0.096611381 0.19958472 0.2461324 -0.14485395 -0.03493008 0.26088586 -0.069536567 -0.039774239
		 0.24898702 -0.076029181 -0.0025750399 0.24369861 -0.095486045 0.029136449 0.27388474 -0.040123343 -0.020618737
		 0.25840259 -0.062038779 0.0094584227 0.2488924 -0.071352363 0.049467295 0.24909812 -0.083155036 0.064244568
		 0.25068277 -0.08859098 0.075367749 0.25947648 -0.067910552 0.080587536 0.26473171 -0.074516654 0.093189031
		 0.27835971 -0.1345371 -0.17025623 0.29283416 -0.049114585 -0.18804437 0.27720791 -0.039402366 -0.13892269
		 0.27156705 -0.055464149 -0.10031193 0.28877807 -0.019768119 -0.12581256 0.2852729 -0.025156379 -0.081210941
		 0.30433112 -0.012094855 -0.16860497 0.27731195 -0.037210822 -0.053169966 0.27702552 -0.040123343 -0.037704438
		 0.29468608 -0.022191405 -0.020110548 0.29198042 -0.022191405 -0.035114497 0.30996722 -0.12645757 -0.31639531
		 0.31652337 -0.033415198 -0.34047452 0.31087071 -0.03093183 -0.29437593 0.30663705 -0.049688697 -0.2541045
		 0.32176924 0.015634179 -0.32896587 0.3150807 -0.00029218197 -0.28694218 0.31547093 -0.0025962591 -0.24115065
		 0.31632948 -0.01605165 -0.21828949 0.31423396 -0.015486121 -0.20146161 0.33327097 0.0069977045 -0.20649955
		 0.33352399 0.0058151484 -0.19074255 0.31766582 -0.085173011 -0.45681086 0.33928075 -0.013632178 -0.4970229
		 0.32984617 0.025773644 -0.45051914 0.32550377 0.005564332 -0.41061804 0.34748524 0.055466294 -0.48331949
		 0.34047979 0.050197244 -0.44593158 0.34596467 0.039743066 -0.40950117 0.33577555 0.027034402 -0.37813699
		 0.33133858 0.017905831 -0.35790792 0.35490492 0.045541406 -0.37357453 0.34690583 0.039916635 -0.34970093
		 0.45599073 0.043813348 -0.49080321 0.45348591 -0.014866233 -0.50039506 0.45167869 0.065930963 -0.48346612
		 0.033130776 -0.13941419 0.29990897 0.054535232 -0.14009511 0.29588786 0.075625122 -0.1274246 0.30447349
		 0.090892091 -0.11157835 0.31147188 0.10197986 -0.11356771 0.30602017 0.11729641 -0.13178861 0.28854182
		 0.14062378 -0.14081609 0.27585527 0.16537248 -0.13151109 0.27359894 0.17963892 -0.11670721 0.27405328
		 0.18921065 -0.11674345 0.26712343 0.2028764 -0.13880002 0.25595024 0.29349896 -0.10866582 0.14716849
		 0.30236566 -0.087696433 0.12375382 0.3052488 -0.062075019 0.11118765 0.30486333 -0.05682981 0.098449096
		 0.30727816 -0.068237662 0.07377845 0.32467359 -0.064771056 0.033531159 0.33242655 -0.036919951 0.0025191903
		 0.33732247 -0.016918302 -0.010638013 0.34001642 -0.018903136 -0.025337458 0.35277617 -0.037094474 -0.068609834
		 0.36084354 -0.035251737 -0.11312394 0.36598074 -0.015234351 -0.15656909 0.3742373 0.0073982477 -0.18320268
		 0.37325585 0.010617852 -0.19549686 0.36667609 0.00071871281 -0.22779983 0.36232913 -0.00050580502 -0.27663249
		 0.37503922 0.019416451 -0.31821385 0.39004564 0.049920678 -0.34093341 0.39410913 0.05625999 -0.36033455
		 0.39537001 0.042770267 -0.39582822 0.39650065 0.040754199 -0.4331952 0.39876387 0.05625999 -0.46920922
		 0.27925128 -0.10913289 0.17687955 0.26930583 -0.095602155 0.2038134 0.26302636 -0.09808749 0.2167246
		 0.24953577 -0.12072027 0.22436872 0.22551778 -0.14048803 0.23938516 0 -0.18275487 0.34222573
		 0 -0.14706051 0.34753308 0 -0.27153337 0.30608183 0 -0.34883821 0.28688887 0 -0.1243633 0.31297356
		 0 -0.10642087 0.26554582 0 -0.1204685 0.24422875 0 -0.18765676 0.19564679 0.020798981 -0.34738576 0.28636131
		 0.015174702 -0.27174985 0.30674621 0.01438473 -0.18864 0.34418181 0.015046978 -0.15391386 0.35064092
		 0.013852581 -0.12984884 0.30802348 0.017263098 -0.10934007 0.25353071 0.019831639 -0.12554681 0.23587301
		 0.021371342 -0.17383993 0.20660916 0.45158672 -0.10454214 -0.52607155 0 -0.26121747 0.12842029
		 0.084267348 -0.25571859 0.1107004 0.13183726 -0.26304662 0.083127886 0.15818009 -0.27484739 0.0055942833
		 0.21347934 -0.25305879 -0.064194083 0.47082725 -0.21118677 -0.50917971 0.47805721 -0.27668703 -0.45702547
		 0.47885671 -0.32740247 -0.3457939 0.45774809 -0.37744653 -0.21305329 0.40156403 -0.41542375 -0.089500904;
	setAttr ".vt[664:829]" 0.33024114 -0.4466728 0.019553155 0.25257248 -0.47525251 0.11027475
		 0.1616379 -0.49442327 0.17916629 0.077259436 -0.50137937 0.21952972 0.024909573 -0.50192463 0.23472342
		 0 -0.50228894 0.23567626 0.45522922 -0.068884254 -0.28081626 0.46709988 -0.033415198 -0.41610947
		 0.43217856 -0.10945547 -0.13869104 0.30925289 -0.166942 -0.60714078 0.2667042 -0.20632207 -0.52470171
		 0.24810469 -0.24566114 -0.30976075 0.19413726 -0.27575815 -0.14701837 0.39310411 -0.058203101 -0.570153
		 0.41570055 0.070521951 -0.49349627 0.39683625 0.009378314 -0.53106928 0.39831197 0.061643481 -0.508816
		 -0.33941793 0.044107318 -0.43052062 -0.34344327 0.035004497 -0.39341536 -0.40413278 0.10786903 -0.37030849
		 -0.33181864 -0.015005231 -0.3213217 -0.23070465 -0.055000663 0.1076377 -0.34026927 -0.13385713 0.17283472
		 -0.2546255 -0.15539014 0.1559076 -0.3475185 0.026124597 0.069232702 -0.28242299 -0.023604751 0.094383344
		 -0.33274829 0.10862529 -0.43693379 -0.33770084 0.084842563 -0.4768056 -0.29466951 -0.070762813 0.082732812
		 -0.33463788 0.12331474 -0.46404877 -0.29996991 -0.10927981 -0.017288953 -0.26613897 0.018293262 0.031534716
		 -0.28529173 -0.0089150667 -0.025235742 -0.40402353 0.042552829 -0.5031091 -0.38079348 0.0038355589 -0.38848948
		 -0.31047574 0.049516559 -0.30997363 -0.24089597 -0.11678517 0.17205691 -0.26638502 -0.073648572 0.21688086
		 -0.34210455 0.027436137 -0.34501562 -0.34648961 0.012634158 0.010908663 -0.27094257 -0.11760539 0.00087350607
		 -0.32587987 0.07565105 -0.4248983 -0.42811018 0.10844791 -0.45609668 -0.43580431 0.097402453 -0.41190913
		 -0.45458797 0.033235431 -0.4350448 -0.27957043 0.0079840422 0.003413558 -0.37542701 -0.042505443 0.025965005
		 -0.39164683 0.092107654 -0.50150216 -0.44378877 0.043413043 -0.47901231 -0.29638427 0.0012606382 -0.012302741
		 -0.30724257 -0.090719402 0.096147835 -0.25953776 -0.0059148073 0.059294194 -0.39078355 0.082147479 -0.20818537
		 -0.31985581 0.018737674 -0.31374159 -0.40892899 0.03576076 -0.19842711 -0.41114765 0.09900558 -0.28962421
		 -0.25242823 -0.14605159 0.099965304 -0.28630182 -0.12403125 0.043653548 -0.22348851 -0.032554805 0.16060941
		 -0.23506831 -0.02526778 0.1192386 -0.30656147 -0.12273329 0.052154154 -0.42688981 0.013425708 -0.39135584
		 -0.3322705 -0.12717092 0.12316485 -0.30522513 -0.016414046 0.095367372 -0.31129169 -0.017502189 0.10308702
		 -0.33223724 -0.0022720098 0.094833001 -0.31874418 0.0044742823 -0.0079272985 -0.26255816 -0.009559989 -0.01122874
		 -0.3202911 0.081089616 -0.29993844 -0.44283795 0.024611115 -0.30194682 -0.25779942 -0.040628791 0.08103241
		 -0.36179671 -0.0045493841 0.065289795 -0.32925719 0.024572015 0.063582391 -0.33589154 -0.028295875 0.15827261
		 -0.24170718 -0.046226859 0.18255581 -0.39784855 0.07095015 -0.50442874 -0.32828522 -0.031570792 0.1913586
		 -0.23718962 -0.099168181 0.10226189 -0.33831796 0.0087391138 0.011802435 -0.36115605 0.029338479 -0.4823024
		 -0.26673737 -0.095964789 0.090762317 -0.34632176 -0.072170615 0.15557639 -0.23417653 -0.027852178 0.15792274
		 -0.30852458 -0.13096821 0.10396004 -0.32201988 -0.0074616671 -0.23110852 -0.32230139 -0.0024434328 0.087120116
		 -0.27179858 -0.12259877 0.025447026 -0.2807554 -0.028215528 0.096637309 -0.4260529 0.044107318 -0.37041226
		 -0.31534553 0.024826884 -0.21986207 -0.34261554 -0.12814152 0.1464875 -0.31510431 -0.14246953 0.18413788
		 -0.2865642 -0.0051594973 0.18337415 -0.2682724 -0.072613895 0.068324387 -0.3268874 -0.025543392 -0.28641692
		 -0.33515114 -0.11883962 0.054211646 -0.23613727 -0.077848792 0.1839934 -0.31666523 0.063487649 -0.26381609
		 -0.30231845 -0.052113891 0.093931749 -0.25238097 -0.065996528 0.043374389 -0.28340676 -0.1123842 -0.015188098
		 -0.35950482 0.0068584681 -0.40790692 -0.32572138 -0.044247031 -0.018475771 -0.32910585 0.066798806 -0.38037893
		 -0.34981033 -0.0063432455 -0.34801239 -0.41924119 -0.0024837255 -0.34120795 -0.27326262 -0.11031568 0.20011902
		 -0.29448268 -0.030348182 0.098983437 -0.3530364 0.015444398 -0.43934694 -0.35350931 -0.11527193 0.048968256
		 -0.28106293 -0.025987983 0.19852516 -0.42030522 0.032596231 -0.33814982 -0.33782846 0.052769303 -0.34176582
		 -0.31556553 -0.013464332 0.12376074 -0.44101673 0.011197686 -0.25345206 -0.32120636 -0.0061687231 -0.019535303
		 -0.36502749 -0.10686129 0.015875489 -0.34489328 -0.021396995 0.092290342 -0.24200991 -0.0085872412 0.05815658
		 -0.35416704 -0.03944242 0.002048552 -0.30041689 -0.050348401 0.21573848 -0.22368255 -0.10808104 0.13826637
		 -0.36707789 0.089789271 -0.35236868 -0.36994687 0.030427814 -0.36865029 -0.29257882 -0.0049439669 0.15937483
		 -0.24554583 -0.012658477 0.017069638 -0.25438422 -0.025156379 0.084679306 -0.32034072 -0.0074371099 0.15714948
		 -0.34455258 0.083951592 -0.33201909 -0.21717359 -0.064672828 0.15195067 -0.36495873 -0.11060464 0.034377813
		 -0.33397338 -0.076866508 0.12095101 -0.35910991 -0.054596305 0.081843644 -0.28336889 -0.15091527 0.1772058
		 -0.318477 0.0047014952 -0.01262179 -0.41437358 -0.0001758337 -0.21179259 -0.41431934 0.11668289 -0.4191153
		 -0.4164266 0.062984347 -0.33478859 -0.23579198 -0.051748395 0.18764055 -0.25117001 -0.05492419 0.008146137
		 -0.24293225 -0.028719306 0.053679466 -0.30555844 0.03717196 -0.26895547 -0.28667518 -0.042539954 0.097786888
		 -0.35272643 -0.1052345 -0.00044403225 -0.36196929 0.056840539 -0.34863415 -0.24128829 -0.15224874 0.12866382
		 -0.27581474 -0.1390518 0.094229907 -0.28457513 0.0032926798 -0.018319525 -0.29014492 -0.047809958 -0.02531106
		 -0.41762558 0.072932839 -0.35669693 -0.43343431 0.039238572 -0.2490221 -0.2626479 -0.032300353 0.10072908
		 -0.34191066 -0.0768646 0.18778011 -0.35090041 0.00044310093 0.0045413077 -0.35623872 0.022230387 0.039205045
		 -0.4515323 0.07055819 -0.42106879 -0.43636203 0.055695176 -0.29775769 -0.29882973 -0.015003562 0.12615682
		 -0.38192147 0.11891329 -0.4861764 -0.40335906 0.12970698 -0.45324364 -0.26724082 -0.04979074 -0.015540518
		 -0.36527103 -0.0010093451 0.035586536 -0.32030973 -0.031544566 0.10311326 -0.43766296 -0.0091661215 -0.3102293
		 -0.33918577 0.01829493 0.0369322 -0.31240541 -0.0077937841 0.18686278;
	setAttr ".vt[830:995]" -0.33072585 -0.061201453 0.086841047 -0.24314037 -0.023133636 0.13153036
		 -0.26264536 -0.04846704 0.21430066 -0.3297348 -0.10624385 -0.011579372 -0.40088782 0.09365046 -0.32091251
		 -0.31519639 0.0063928366 -0.2763629 -0.32797045 -0.03362596 0.12171049 -0.24668089 0.012635827 0.023228347
		 -0.26822737 -0.021683097 0.082456291 -0.30808458 -0.092269301 0.20421323 -0.26472914 -0.029728293 0.11046644
		 -0.29238236 -0.025874496 0.21017811 -0.35017431 0.051762223 -0.48183361 -0.44144955 0.080949426 -0.47062746
		 -0.2638472 0.0061787367 -0.0058890134 -0.43538111 -0.021536052 -0.26754805 -0.37686491 -0.046907544 0.056496277
		 -0.26402682 -0.056410015 0.09193556 -0.32487708 0.072209001 -0.22180164 -0.40679371 0.071897149 -0.25523031
		 -0.33435172 -0.034747481 -0.25227898 -0.41298553 -0.032363296 -0.2366192 -0.34179473 0.1089884 -0.37851092
		 -0.3572225 0.055543542 -0.23004165 -0.36798856 0.077052712 -0.32158205 -0.26156244 -0.042330146 0.20472854
		 -0.28853902 -0.025580764 0.10605314 -0.31604558 -0.0070766211 0.0082404613 -0.29206312 -0.025015235 0.072742492
		 -0.27323899 -0.036162734 0.1266983 -0.25907901 -0.044573188 0.17373122 -0.25479335 -0.03821981 0.15882595
		 -0.26154107 -0.040060401 0.143143 -0.29774666 -0.020465255 0.0567469 -0.29970717 -0.012849212 0.045791626
		 -0.30862385 -0.015369773 0.026043028 -0.36140168 0.029264092 -0.2695584 -0.3608979 0.053060174 -0.29998112
		 -0.37547183 0.087052941 -0.39066949 -0.37976938 0.073799729 -0.42388877 -0.37385443 0.10119593 -0.45020062
		 -0.41862613 -0.014648795 -0.11597404 -0.40983725 -0.027927756 -0.072613776 -0.4062424 -0.06502378 -0.082928121
		 -0.38087621 -0.073988318 -0.052581072 -0.38521889 -0.038042426 -0.034442753 -0.29025611 -0.020311713 -0.033775806
		 -0.2975409 -0.06924665 -0.093079448 -0.2792654 -1.3113022e-006 -0.11932048 -0.27633032 -0.003542304 -0.080381036
		 -0.31092271 -0.074240088 -0.071584731 -0.27975973 -0.040521979 -0.082514226 -0.29532954 -0.049013495 -0.05110991
		 -0.33322597 0.0080085993 -0.080603242 -0.33045647 0.016706109 -0.046878695 -0.38905489 0.04926455 -0.096799761
		 -0.39765 0.010501504 -0.080224991 -0.40739644 0.0200001 -0.1106852 -0.37833387 0.033603311 -0.08000502
		 -0.3816686 0.006606698 -0.031543136 -0.36557862 0.030498147 -0.037678748 -0.28201121 0.049588799 -0.088656694
		 -0.29384425 0.027186036 -0.034097433 -0.28582397 0.025081277 -0.11226547 -0.33626747 0.0036064386 -0.10216594
		 -0.40084279 -0.046226859 -0.15525138 -0.38969827 -0.043157935 -0.17022046 -0.39307821 -0.0066817999 -0.17817456
		 -0.41147143 -0.054090858 -0.12317547 -0.40891492 -0.0071853399 -0.15566307 -0.32184964 -0.04853189 -0.16608882
		 -0.28269249 -0.031903625 -0.12346914 -0.30791655 -0.020641685 -0.17192835 -0.35207367 -0.042654395 -0.18182597
		 -0.29867607 -0.060951591 -0.12469879 -0.34363261 -0.013021827 -0.19453895 -0.40247458 0.030287385 -0.14321798
		 -0.38666362 0.026376367 -0.17237738 -0.37365547 0.055719018 -0.16200167 -0.38742304 0.064710259 -0.12201896
		 -0.29656172 0.014070153 -0.16876343 -0.32061493 0.058606744 -0.17257166 -0.32977286 0.017981172 -0.19518435
		 -0.29314172 0.055910707 -0.1423592 -0.34443918 0.045576692 -0.15742061 -0.33850017 0.017981172 -0.12728637
		 -0.14791304 -0.30270556 -0.49762121 -0.17895542 -0.2752054 -0.63431883 -0.075088225 -0.31773031 -0.41653982
		 -0.1815429 -0.27581576 -0.56981695 -0.034315728 -0.31955945 -0.28409949 -0.10913666 -0.2922138 -0.24847795
		 -0.12371778 -0.29769361 -0.3442021 -0.18154049 -0.27596891 -0.41684994 -0.082183644 -0.30372083 -0.032534361
		 -0.16758382 -0.2641052 -0.26664209 -0.071233101 -0.19971025 0.32214797 -0.0099974172 -0.14106786 0.35864666
		 -0.077285446 -0.10858572 0.27144668 -0.015465599 -0.041611075 0.35733894 -0.024294648 -0.16579664 0.24308643
		 -0.073799253 -0.17027128 0.24970874 -0.090376452 -0.10174024 0.3492125 -0.013403201 -0.10544622 0.26823971
		 -0.086566217 -0.14215505 0.34427157 -0.0087320702 -0.10393369 0.36399448 -0.0084033208 -0.078956008 0.30497006
		 -0.018284835 -0.19917333 0.32965267 -0.080031365 -0.042402625 0.34893072 -0.082453266 -0.080328345 0.29860091
		 -0.0138313 -0.041181922 0.34134108 -0.079421163 -0.041530967 0.33677644 -0.048662592 -0.2025646 0.33664629
		 -0.050803073 -0.041359305 0.35676891 -0.050372601 -0.10595167 0.36530483 -0.049700927 -0.14731252 0.36180183
		 -0.0090371724 -0.052791953 0.35956454 -0.0088645183 -0.075212836 0.36287805 -0.084669411 -0.064630866 0.31945416
		 -0.085036002 -0.049185157 0.33084235 -0.086003333 -0.052036643 0.34901124 -0.088905334 -0.072889686 0.35006151
		 -0.00802017 -0.049984336 0.33445391 -0.0079705007 -0.06538713 0.32331395 -0.050550014 -0.076321959 0.364025
		 -0.050706066 -0.054304481 0.36033553 -0.045528814 -0.1031059 0.246232 -0.044854738 -0.076765418 0.28385881
		 -0.050152682 -0.16265714 0.22593203 -0.046732672 -0.040663123 0.33883655 -0.046101168 -0.063914657 0.31534126
		 -0.04641103 -0.049627662 0.33004522 -0.084404521 -0.03897512 0.34127018 -0.079357311 -0.19004095 0.29971486
		 -0.0088858083 -0.037573218 0.35100263 -0.093675815 -0.068716407 0.33535257 -0.091800287 -0.049436927 0.33833501
		 -0.093822464 -0.090295196 0.32897612 -0.0031101494 -0.12382257 0.32542387 -0.0011399942 -0.095713019 0.34737688
		 -0.04987359 -0.034131408 0.3484036 -0.089203343 -0.12472665 0.31957722 -0.010177166 -0.18855512 0.29781562
		 -0.00094841886 -0.071377158 0.35260871 -0.00098389573 -0.050047278 0.35175487 -0.16642022 -0.19357049 0.30608413
		 -0.10645457 -0.13475263 0.33841076 -0.17195231 -0.10126245 0.24433771 -0.11019153 -0.044166923 0.33573112
		 -0.11159642 -0.15907419 0.22355771 -0.16049084 -0.16366613 0.22643369 -0.1883119 -0.098122001 0.32302552
		 -0.10125373 -0.10028875 0.25009674 -0.18750781 -0.13876379 0.32221445 -0.10152805 -0.10061872 0.34349573
		 -0.09856455 -0.077774405 0.28978583 -0.1176299 -0.19195592 0.31464103 -0.17165193 -0.038042426 0.31775352
		 -0.17906661 -0.073988318 0.27109215 -0.10665806 -0.04385221 0.32159236 -0.16938841 -0.037426353 0.30568209
		 -0.14557627 -0.19458234 0.32262594 -0.1427405 -0.039514899 0.33096302 -0.14697413 -0.10277402 0.3433871
		 -0.14832932 -0.14171445 0.34295639 -0.10473758 -0.055931449 0.33832547;
	setAttr ".vt[996:1161]" -0.1031103 -0.075463653 0.34179762 -0.17768541 -0.057848334 0.28759816
		 -0.17488503 -0.044498801 0.29820335 -0.17906661 -0.046991706 0.31727132 -0.18511188 -0.067346931 0.32064375
		 -0.10027453 -0.053295493 0.31600103 -0.09862373 -0.066839576 0.30612177 -0.14587435 -0.073257804 0.34003797
		 -0.14391598 -0.052429557 0.335329 -0.13622217 -0.098087668 0.23126084 -0.13941278 -0.073221564 0.27317819
		 -0.13503727 -0.15572202 0.211777 -0.13751113 -0.03905046 0.31453243 -0.13814276 -0.059801459 0.29268554
		 -0.13701215 -0.047890067 0.30518049 -0.17501274 -0.034574866 0.30911154 -0.17217933 -0.18474424 0.27980998
		 -0.10330663 -0.04098928 0.330679 -0.18799967 -0.062500358 0.30214623 -0.1833854 -0.044634223 0.30445445
		 -0.19154501 -0.086355567 0.29883978 -0.092919022 -0.11912763 0.30582377 -0.090575144 -0.09217298 0.32419625
		 -0.14092644 -0.032156348 0.32281965 -0.19001475 -0.12197912 0.2933335 -0.1076064 -0.18278921 0.28095224
		 -0.092654116 -0.07292974 0.33006418 -0.095752411 -0.054785132 0.33154014 -0.24063109 -0.16013181 0.20579076
		 -0.26032794 -0.094766974 0.20985869 -0.24913368 -0.025833488 0.2561942 -0.27470309 -0.07614553 0.26718244
		 -0.22144261 -0.032156348 0.29157153 -0.24947195 -0.031940818 0.27727923 -0.18765198 -0.11790693 0.2773928
		 -0.21409646 -0.12847364 0.29957041 -0.25947648 -0.16246545 0.22228041 -0.23780946 -0.035439849 0.2891829
		 -0.24522632 -0.17159498 0.27480754 -0.1833404 -0.11054456 0.22275338 -0.18058497 -0.16254079 0.22202957
		 -0.19556822 -0.15492284 0.18829802 -0.22600751 -0.027172446 0.28002033 -0.21465477 -0.083801627 0.30661401
		 -0.18160212 -0.15699232 0.2002067 -0.21460032 -0.17434347 0.27337673 -0.24547723 -0.089172721 0.29828382
		 -0.27986631 -0.1221174 0.26137611 -0.20866142 -0.10382116 0.2010673 -0.27543405 -0.1071533 0.2279259
		 -0.19049489 -0.16899621 0.24718967 -0.22869191 -0.059655547 0.22630334 -0.21270588 -0.046149611 0.27178016
		 -0.23593612 -0.03897512 0.24519876 -0.22625569 -0.039110541 0.29489923 -0.20131522 -0.067910552 0.25693658
		 -0.26490435 -0.040817618 0.25175926 -0.218536 -0.059360862 0.3035748 -0.23962098 -0.025267959 0.2652432
		 -0.2676194 -0.05333364 0.22165844 -0.24132171 -0.06112802 0.29726222 -0.26261243 -0.051204085 0.2739183
		 -0.20294958 -0.052000403 0.30157617 -0.26200956 -0.035757422 0.24157023 -0.26683444 -0.16618383 0.2514402
		 -0.24963731 -0.13366544 0.29285815 -0.1921268 -0.074849486 0.29806614 -0.21750224 -0.15759981 0.19410923
		 -0.27590224 -0.061420798 0.23700336 -0.21719027 -0.03273046 0.28856543 -0.23492634 -0.02453649 0.28376916
		 -0.2436631 -0.02457273 0.27371743 -0.25629526 -0.03273046 0.23316464 -0.25574186 -0.052152991 0.21169388
		 -0.23825404 -0.10180128 0.19764283 -0.25070882 -0.026552558 0.26390919 -0.1779667 -0.11216676 0.25315729
		 -0.18590897 -0.069284797 0.28416407 -0.19951789 -0.047638297 0.29333851 -0.25376457 -0.030563712 0.26633325
		 -0.35329628 0.044709802 -0.18203887 -0.37265018 -0.041897178 -0.17974949 -0.36169976 0.01902926 -0.19087979
		 -0.37041527 -0.0094588995 -0.19156837 -0.33433282 0.013425469 -0.020051479 -0.33805776 -0.015255332 -0.01883734
		 -0.33765095 -0.04864347 -0.03055457 -0.34211171 -0.076898932 -0.05359102 -0.3697812 -0.039764762 -0.22444361
		 -0.36358696 -0.012115836 -0.20091733 -0.35561407 0.056705117 -0.19443229 -0.35832673 0.017428994 -0.18800166
		 -0.37838095 0.031760812 -0.35846379 -0.38273752 -0.0016607046 -0.35898656 -0.37299794 0.080411553 -0.34379286
		 -0.37488526 0.056009889 -0.35602987 -0.073356807 -0.32942045 0.28401738 -0.045020167 -0.26893079 0.31133702
		 -0.06167306 -0.25008428 0.31880391 -0.086961053 -0.26441514 0.30419222 -0.058553457 -0.19780481 0.34527448
		 -0.086485639 -0.19205511 0.33764222 -0.03652931 -0.19746816 0.3489852 -0.034973077 -0.16413152 0.35743824
		 -0.056472123 -0.16377294 0.35103813 -0.078673646 -0.15500104 0.34507817 -0.1716115 -0.32032812 0.25537822
		 -0.14019077 -0.25701749 0.29401499 -0.16760735 -0.23544443 0.29853955 -0.19676943 -0.25023305 0.27445775
		 -0.12515791 -0.19116437 0.33186409 -0.16044575 -0.1947273 0.32439989 -0.19177184 -0.19046724 0.30508599
		 -0.12542054 -0.15586889 0.34001443 -0.15358686 -0.16647089 0.33285284 -0.1791044 -0.15903509 0.31749585
		 -0.097644366 -0.16160715 0.3409321 -0.10893315 -0.16135538 0.33998123 -0.10786171 -0.13771093 0.34002623
		 -0.094593346 -0.1379627 0.34048271 -0.26104653 -0.30519521 0.18051672 -0.2441715 -0.25017011 0.23933747
		 -0.2685228 -0.22912443 0.22912043 -0.28581676 -0.23436201 0.19168255 -0.22849301 -0.19228399 0.28206864
		 -0.26414499 -0.19141614 0.25794175 -0.29020649 -0.1726383 0.22516835 -0.22547984 -0.16265523 0.28528512
		 -0.25438172 -0.16571462 0.26728654 -0.27725488 -0.14459968 0.24223277 -0.19836602 -0.15889621 0.30157623
		 -0.20821428 -0.15922427 0.29375935 -0.192349 -0.13616407 0.302955 -0.20322862 -0.13618886 0.29191956
		 -0.33413863 -0.27971685 0.085031852 -0.31905627 -0.21862638 0.14705995 -0.33564055 -0.19267547 0.12908703
		 -0.34502059 -0.19614351 0.089426294 -0.32100037 -0.1614536 0.18902184 -0.34255379 -0.15867841 0.15204301
		 -0.34919518 -0.13633573 0.11325954 -0.32085142 -0.13258016 0.19404805 -0.34159601 -0.13688314 0.16451195
		 -0.34386638 -0.11155164 0.13487461 -0.29084498 -0.13374364 0.21966478 -0.30025339 -0.130463 0.20971206
		 -0.29187846 -0.10883558 0.21141496 -0.28078604 -0.11269414 0.22340414 -0.39703441 -0.24060476 -0.027810849
		 -0.37233791 -0.17375982 0.034380168 -0.3883664 -0.1406759 0.0083969235 -0.40445161 -0.14554632 -0.041114695
		 -0.36520001 -0.11379278 0.071171939 -0.3876853 -0.099319816 0.032495201 -0.39659488 -0.082751632 -0.011907615
		 -0.356345 -0.081126571 0.076843441 -0.37816578 -0.079571128 0.041326344 -0.37805223 -0.054634452 0.010650456
		 -0.34379554 -0.09357965 0.10801154 -0.34688202 -0.08635366 0.095544741 -0.33557904 -0.062652946 0.10122125
		 -0.33442014 -0.071300864 0.11654493 -0.44985726 -0.20833814 -0.15838075 -0.41666752 -0.066608787 -0.06689743
		 -0.42491931 -0.053627849 -0.1087198 -0.42711911 -0.051495433 -0.15958697;
	setAttr ".vt[1162:1327]" -0.3997426 -0.043296576 -0.059943892 -0.41354111 -0.040627241 -0.10407496
		 -0.4117009 -0.027284503 -0.14818704 -0.38598245 -0.050129771 -0.012726314 -0.39215279 -0.04659164 -0.028153487
		 -0.36993492 -0.030639887 -0.0040671974 -0.37625951 -0.029160142 -0.022755004 -0.46907192 -0.16478384 -0.30305803
		 -0.44005862 -0.033081412 -0.22801 -0.45398498 -0.015847087 -0.26754564 -0.45713258 -0.0072466135 -0.31145674
		 -0.42329252 -0.0079048872 -0.22683927 -0.43832809 -0.0033513308 -0.26631126 -0.43968987 0.01476562 -0.3044726
		 -0.41683808 -0.02630055 -0.1805089 -0.42056763 -0.021032512 -0.19874382 -0.40629429 -0.0029262304 -0.19561034
		 -0.4035697 -0.0081573725 -0.17717403 -0.47178239 -0.13005674 -0.42987433 -0.45902741 0.014843345 -0.37908053
		 -0.46638536 0.033021569 -0.41224423 -0.47063291 0.031256795 -0.44297692 -0.44202462 0.045693755 -0.38553277
		 -0.45405334 0.049162984 -0.41643521 -0.4622556 0.061357141 -0.45146087 -0.4303121 0.046666741 -0.33677331
		 -0.44620109 0.027829766 -0.33794644 -0.4463098 0.034247994 -0.35752478 -0.43096709 0.053449273 -0.35873339
		 -0.05200674 -0.19021547 0.19409284 -0.074700221 -0.13571966 0.23016593 -0.056141041 -0.12399173 0.23287153
		 -0.042846739 -0.1351186 0.22619703 -0.072382517 -0.10440099 0.25920716 -0.053764213 -0.10942113 0.24738643
		 -0.035621252 -0.11407125 0.23999766 -0.12448399 -0.17831647 0.18019754 -0.14975785 -0.13604772 0.20431727
		 -0.13004448 -0.13102949 0.20754096 -0.11025308 -0.13310087 0.21651408 -0.15379985 -0.11819398 0.22348669
		 -0.13138554 -0.11573446 0.21768275 -0.11105011 -0.11724699 0.23068827 -0.092786565 -0.1198467 0.23832056
		 -0.082980707 -0.11713636 0.2466127 -0.084539309 -0.097730994 0.27235976 -0.096050456 -0.10411489 0.25785217
		 -0.17749144 -0.18964994 0.14054848 -0.20395972 -0.13211858 0.16870287 -0.19009042 -0.12348974 0.1887265
		 -0.17498669 -0.13031042 0.19720307 -0.2162132 -0.10368192 0.19675139 -0.19555157 -0.10771692 0.20229498
		 -0.18223113 -0.11612737 0.21711972 -0.1695919 -0.11988294 0.21458188 -0.162636 -0.12230718 0.21640286
		 -0.17580986 -0.11194074 0.23081607 -0.16744429 -0.11389768 0.233399 -0.21678582 -0.17723119 0.062151343
		 -0.23972513 -0.11219251 0.071723282 -0.22524586 -0.11273515 0.099733338 -0.21533605 -0.1275028 0.12716658
		 -0.25531131 -0.090467811 0.09623073 -0.23357812 -0.099321723 0.11156164 -0.22223482 -0.10389745 0.14542556
		 -0.22069281 -0.11064565 0.16846889 -0.21933772 -0.11166227 0.18052624 -0.23346224 -0.095774055 0.18614569
		 -0.23343852 -0.096611381 0.19958472 -0.2461324 -0.14485395 -0.03493008 -0.26088586 -0.069536567 -0.039774239
		 -0.24898702 -0.076029181 -0.0025750399 -0.24369861 -0.095486045 0.029136449 -0.27388474 -0.040123343 -0.020618737
		 -0.25840259 -0.062038779 0.0094584227 -0.2488924 -0.071352363 0.049467295 -0.24909812 -0.083155036 0.064244568
		 -0.25068277 -0.08859098 0.075367749 -0.25947648 -0.067910552 0.080587536 -0.26473171 -0.074516654 0.093189031
		 -0.27835971 -0.1345371 -0.17025623 -0.29283416 -0.049114585 -0.18804437 -0.27720791 -0.039402366 -0.13892269
		 -0.27156705 -0.055464149 -0.10031193 -0.28877807 -0.019768119 -0.12581256 -0.2852729 -0.025156379 -0.081210941
		 -0.30433112 -0.012094855 -0.16860497 -0.27731195 -0.037210822 -0.053169966 -0.27702552 -0.040123343 -0.037704438
		 -0.29468608 -0.022191405 -0.020110548 -0.29198042 -0.022191405 -0.035114497 -0.30996722 -0.12645757 -0.31639531
		 -0.31652337 -0.033415198 -0.34047452 -0.31087071 -0.03093183 -0.29437593 -0.30663705 -0.049688697 -0.2541045
		 -0.32176924 0.015634179 -0.32896587 -0.3150807 -0.00029218197 -0.28694218 -0.31547093 -0.0025962591 -0.24115065
		 -0.31632948 -0.01605165 -0.21828949 -0.31423396 -0.015486121 -0.20146161 -0.33327097 0.0069977045 -0.20649955
		 -0.33352399 0.0058151484 -0.19074255 -0.31766582 -0.085173011 -0.45681086 -0.33928075 -0.013632178 -0.4970229
		 -0.32984617 0.025773644 -0.45051914 -0.32550377 0.005564332 -0.41061804 -0.34748524 0.055466294 -0.48331949
		 -0.34047979 0.050197244 -0.44593158 -0.34596467 0.039743066 -0.40950117 -0.33577555 0.027034402 -0.37813699
		 -0.33133858 0.017905831 -0.35790792 -0.35490492 0.045541406 -0.37357453 -0.34690583 0.039916635 -0.34970093
		 -0.45599073 0.043813348 -0.49080321 -0.45348591 -0.014866233 -0.50039506 -0.45167869 0.065930963 -0.48346612
		 -0.033130776 -0.13941419 0.29990897 -0.054535232 -0.14009511 0.29588786 -0.075625122 -0.1274246 0.30447349
		 -0.090892091 -0.11157835 0.31147188 -0.10197986 -0.11356771 0.30602017 -0.11729641 -0.13178861 0.28854182
		 -0.14062378 -0.14077985 0.27585527 -0.16537248 -0.13151109 0.27359894 -0.17963892 -0.11670721 0.27405328
		 -0.18921065 -0.11674345 0.26712343 -0.2028764 -0.13880002 0.25595024 -0.29349896 -0.10866582 0.14716849
		 -0.30236566 -0.087696433 0.12375382 -0.3052488 -0.062075019 0.11118765 -0.30486333 -0.05682981 0.098449096
		 -0.30727816 -0.068237662 0.07377845 -0.32467359 -0.064771056 0.033531159 -0.33242655 -0.036919951 0.0025191903
		 -0.33732247 -0.01691854 -0.010638028 -0.34001642 -0.018903136 -0.025337458 -0.35277617 -0.037094474 -0.068609834
		 -0.36084354 -0.035251975 -0.11312395 -0.36598074 -0.015234351 -0.15656909 -0.3742373 0.0073982477 -0.18320268
		 -0.37325585 0.010617852 -0.19549686 -0.36667609 0.00071871281 -0.22779983 -0.36232913 -0.00050580502 -0.27663249
		 -0.37503922 0.019416451 -0.31821385 -0.39004564 0.049920678 -0.34093341 -0.39410913 0.056259751 -0.36033458
		 -0.39537001 0.042770028 -0.39582822 -0.39650065 0.040753961 -0.4331952 -0.39876387 0.056259751 -0.46920922
		 -0.27925128 -0.10913312 0.17687954 -0.26930583 -0.095602393 0.2038134 -0.26302636 -0.098087668 0.21672457
		 -0.24953577 -0.12072027 0.22436872 -0.22551778 -0.14048803 0.23938516 -0.020798981 -0.34738576 0.28636131
		 -0.015174702 -0.27174985 0.30674621 -0.01438473 -0.18864 0.34418181 -0.015046978 -0.15391386 0.35064092
		 -0.013852581 -0.12984884 0.30802348 -0.017263098 -0.10934007 0.25353071 -0.019831639 -0.12554681 0.23587301
		 -0.021371342 -0.17383993 0.20660916 -0.45158672 -0.10454214 -0.52607155 -0.084267348 -0.25571859 0.1107004
		 -0.13183726 -0.26304662 0.083127886 -0.15818009 -0.27484739 0.0055942833;
	setAttr ".vt[1328:1349]" -0.21347934 -0.25305879 -0.064194083 -0.47082725 -0.21118677 -0.50917971
		 -0.47805721 -0.27668703 -0.45702547 -0.47885671 -0.32740247 -0.3457939 -0.45774809 -0.37744653 -0.21305329
		 -0.40156403 -0.41542375 -0.089500904 -0.33024114 -0.4466728 0.019553155 -0.25257248 -0.47525251 0.11027475
		 -0.1616379 -0.49442327 0.17916629 -0.077259436 -0.50137937 0.21952972 -0.024909573 -0.50192487 0.23472342
		 -0.45522922 -0.068884254 -0.28081626 -0.46709988 -0.033415198 -0.41610947 -0.43217856 -0.10945547 -0.13869104
		 -0.30925289 -0.166942 -0.60714078 -0.2667042 -0.20632207 -0.52470171 -0.24810469 -0.24566114 -0.30976075
		 -0.19413726 -0.27575815 -0.14701837 -0.39310411 -0.058203101 -0.570153 -0.41570055 0.070521951 -0.49349627
		 -0.39683625 0.0093780756 -0.53106928 -0.39831197 0.061643243 -0.508816;
	setAttr -s 2574 ".ed";
	setAttr ".ed[0:165]"  241 246 1 246 243 1 243 242 1 242 241 1 414 413 1 413 416 1
		 416 415 1 415 414 1 417 415 1 416 418 1 418 417 1 419 414 1 417 419 1 417 421 1 421 420 1
		 420 419 1 422 421 1 418 422 1 424 423 1 423 426 1 426 425 1 425 424 1 427 424 1 425 428 1
		 428 427 1 426 429 1 429 428 1 430 427 1 428 431 1 431 430 1 429 432 1 432 431 1 416 424 1
		 427 418 1 413 423 1 433 418 1 427 434 1 434 433 1 435 434 1 430 435 1 435 436 1 436 433 1
		 436 422 1 438 437 1 437 440 1 440 439 1 439 438 1 439 442 1 442 441 1 441 438 1 443 442 1
		 440 443 1 442 445 1 445 444 1 444 441 1 446 445 1 443 446 1 441 429 1 426 438 1 423 437 1
		 447 429 1 441 448 1 448 447 1 449 447 1 448 450 1 450 449 1 444 450 1 449 432 1 452 451 1
		 451 454 1 454 453 1 453 452 1 453 456 1 456 455 1 455 452 1 457 456 1 454 457 1 456 459 1
		 459 458 1 458 455 1 457 460 1 460 459 1 461 443 1 443 455 1 455 462 1 462 461 1 463 462 1
		 458 463 1 461 464 1 464 446 1 463 464 1 440 452 1 437 451 1 466 465 1 465 468 1 468 467 1
		 467 466 1 467 470 1 470 469 1 469 466 1 468 471 1 471 470 1 470 473 1 473 472 1 472 469 1
		 471 474 1 474 473 1 454 466 1 469 457 1 451 465 1 475 457 1 469 476 1 476 475 1 477 476 1
		 472 477 1 475 478 1 478 460 1 477 478 1 480 481 1 481 484 1 484 483 1 483 480 1 481 482 1
		 482 485 1 485 484 1 471 480 1 480 487 1 487 486 1 486 471 1 486 488 1 488 474 1 489 487 1
		 483 489 1 489 488 1 491 492 1 492 495 1 495 494 1 494 491 1 492 493 1 493 496 1 496 495 1
		 497 482 1 482 491 1 491 498 1 498 497 1 494 499 1 499 498 1 497 500 1 500 485 1 499 500 1
		 502 503 1 503 506 1 506 505 1 505 502 1 507 506 1 503 504 1 504 507 1 508 496 1 493 509 1
		 509 508 1 510 509 1 493 502 1 502 510 1 505 511 1 511 510 1 511 508 1;
	setAttr ".ed[166:331]" 513 512 1 512 515 1 515 514 1 514 513 1 514 517 1 517 516 1
		 516 513 1 515 518 1 518 517 1 520 519 1 519 522 1 522 521 1 521 520 1 523 520 1 521 524 1
		 524 523 1 522 525 1 525 524 1 513 522 1 519 512 1 526 522 1 513 527 1 527 526 1 516 528 1
		 528 527 1 529 526 1 528 529 1 529 525 1 531 530 1 530 533 1 533 532 1 532 531 1 534 531 1
		 532 535 1 535 534 1 536 535 1 533 536 1 530 519 1 520 533 1 537 533 1 520 538 1 538 537 1
		 538 540 1 540 539 1 539 537 1 523 540 1 539 536 1 542 541 1 541 544 1 544 543 1 543 542 1
		 545 542 1 543 546 1 546 545 1 547 546 1 544 547 1 531 544 1 541 530 1 548 544 1 531 549 1
		 549 548 1 549 551 1 551 550 1 550 548 1 550 547 1 534 551 1 553 552 1 552 555 1 555 554 1
		 554 553 1 554 557 1 557 556 1 556 553 1 558 557 1 555 558 1 542 555 1 552 541 1 559 555 1
		 542 560 1 560 559 1 561 558 1 559 561 1 560 562 1 562 561 1 545 562 1 564 563 1 563 566 1
		 566 565 1 565 564 1 567 565 1 566 568 1 568 567 1 569 564 1 567 569 1 553 566 1 563 552 1
		 570 566 1 553 571 1 571 570 1 556 572 1 572 571 1 570 573 1 573 568 1 575 574 1 574 577 1
		 577 576 1 576 575 1 578 575 1 576 579 1 579 578 1 577 580 1 580 579 1 564 577 1 574 563 1
		 564 582 1 582 581 1 581 577 1 583 581 1 582 584 1 584 583 1 569 584 1 583 580 1 586 585 1
		 585 588 1 588 587 1 587 586 1 589 586 1 587 590 1 590 589 1 591 590 1 588 591 1 575 588 1
		 585 574 1 592 588 1 575 593 1 593 592 1 593 595 1 595 594 1 594 592 1 594 591 1 578 595 1
		 421 600 1 600 599 1 599 420 1 517 600 1 600 601 1 601 516 1 601 602 1 602 528 1 602 603 1
		 603 529 1 603 604 1 604 525 1 604 605 1 605 524 1 605 606 1 606 523 1 606 607 1 607 540 1
		 607 608 1 608 539 1 463 633 1 633 634 1 634 464 1 634 635 1 635 446 1;
	setAttr ".ed[332:497]" 635 636 1 636 445 1 608 609 1 609 536 1 458 632 1 632 633 1
		 459 610 1 610 632 1 460 611 1 611 610 1 545 611 1 611 612 1 612 562 1 612 613 1 613 561 1
		 613 614 1 614 558 1 614 615 1 615 557 1 615 616 1 616 556 1 616 617 1 617 572 1 617 618 1
		 618 573 1 573 572 1 618 619 1 619 568 1 619 620 1 620 567 1 620 621 1 621 569 1 621 622 1
		 622 584 1 622 623 1 623 583 1 623 624 1 624 580 1 624 625 1 625 579 1 625 626 1 626 578 1
		 626 627 1 627 595 1 627 628 1 628 594 1 628 629 1 629 591 1 629 630 1 630 590 1 630 631 1
		 631 589 1 504 596 1 596 598 1 598 507 1 518 599 1 422 601 1 436 602 1 435 603 1 430 604 1
		 431 605 1 432 606 1 449 607 1 450 608 1 444 609 1 546 610 1 478 612 1 477 613 1 472 614 1
		 473 615 1 474 616 1 488 617 1 489 618 1 483 619 1 484 620 1 485 621 1 500 622 1 499 623 1
		 494 624 1 495 625 1 496 626 1 508 627 1 511 628 1 505 629 1 506 630 1 507 631 1 547 632 1
		 550 633 1 551 634 1 534 635 1 535 636 1 636 609 1 420 648 1 648 647 1 647 419 1 647 646 1
		 646 414 1 646 645 1 645 413 1 599 649 1 649 648 1 515 651 1 651 650 1 650 518 1 512 652 1
		 652 651 1 650 649 1 640 645 1 646 639 1 639 640 1 647 637 1 637 639 1 648 638 1 638 637 1
		 649 641 1 641 638 1 642 650 1 651 643 1 643 642 1 652 644 1 644 643 1 654 644 1 512 654 1
		 655 654 1 519 655 1 530 656 1 656 655 1 541 657 1 657 656 1 552 658 1 658 657 1 653 501 1
		 501 660 1 660 659 0 659 653 0 501 490 1 490 661 1 661 660 0 490 479 1 479 662 1 662 661 0
		 479 465 1 465 663 1 663 662 0 451 664 1 664 663 0 437 665 1 665 664 0 423 666 1 666 665 0
		 413 667 1 667 666 0 645 668 1 668 667 0 640 669 1 669 668 0 468 672 1 672 480 1 479 672 1
		 670 491 1 482 672 1 672 670 1 490 670 1 671 502 1 493 670 1 670 671 1;
	setAttr ".ed[498:663]" 501 671 1 504 671 1 671 597 1 597 596 1 653 597 1 642 641 1
		 563 676 1 676 658 1 676 675 1 675 246 1 246 245 1 245 676 1 574 675 1 245 657 1 245 655 1
		 245 244 1 244 654 1 242 238 1 238 240 1 240 241 1 243 236 1 236 238 1 674 239 1 239 236 1
		 243 674 1 674 673 1 673 237 0 237 239 1 674 585 1 586 673 1 675 674 1 244 241 1 240 235 1
		 235 244 1 597 679 1 679 680 1 680 596 1 680 678 1 678 598 1 679 586 1 589 680 1 678 631 1
		 653 677 0 677 679 1 677 673 0 921 922 1 922 923 1 923 925 1 925 921 1 1093 1094 1
		 1094 1095 1 1095 1092 1 1092 1093 1 1096 1097 1 1097 1095 1 1094 1096 1 1098 1096 1
		 1093 1098 1 1098 1099 1 1099 1100 1 1100 1096 1 1101 1097 1 1100 1101 1 1103 1104 1
		 1104 1105 1 1105 1102 1 1102 1103 1 1106 1107 1 1107 1104 1 1103 1106 1 1107 1108 1
		 1108 1105 1 1109 1110 1 1110 1107 1 1106 1109 1 1110 1111 1 1111 1108 1 1097 1106 1
		 1103 1095 1 1102 1092 1 1112 1113 1 1113 1106 1 1097 1112 1 1114 1109 1 1113 1114 1
		 1112 1115 1 1115 1114 1 1101 1115 1 1117 1118 1 1118 1119 1 1119 1116 1 1116 1117 1
		 1117 1120 1 1120 1121 1 1121 1118 1 1122 1119 1 1121 1122 1 1120 1123 1 1123 1124 1
		 1124 1121 1 1125 1122 1 1124 1125 1 1117 1105 1 1108 1120 1 1116 1102 1 1126 1127 1
		 1127 1120 1 1108 1126 1 1128 1129 1 1129 1127 1 1126 1128 1 1129 1123 1 1111 1128 1
		 1131 1132 1 1132 1133 1 1133 1130 1 1130 1131 1 1131 1134 1 1134 1135 1 1135 1132 1
		 1136 1133 1 1135 1136 1 1134 1137 1 1137 1138 1 1138 1135 1 1138 1139 1 1139 1136 1
		 1140 1141 1 1141 1134 1 1134 1122 1 1122 1140 1 1142 1137 1 1141 1142 1 1125 1143 1
		 1143 1140 1 1143 1142 1 1131 1119 1 1130 1116 1 1145 1146 1 1146 1147 1 1147 1144 1
		 1144 1145 1 1145 1148 1 1148 1149 1 1149 1146 1 1149 1150 1 1150 1147 1 1148 1151 1
		 1151 1152 1 1152 1149 1 1152 1153 1 1153 1150 1 1136 1148 1 1145 1133 1 1144 1130 1
		 1154 1155 1 1155 1148 1 1136 1154 1 1156 1151 1 1155 1156 1 1139 1157 1 1157 1154 1
		 1157 1156 1 1159 1162 1 1162 1163 1 1163 1160 1;
	setAttr ".ed[664:829]" 1160 1159 1 1163 1164 1 1164 1161 1 1161 1160 1 1150 1165 1
		 1165 1166 1 1166 1159 1 1159 1150 1 1153 1167 1 1167 1165 1 1168 1162 1 1166 1168 1
		 1167 1168 1 1170 1173 1 1173 1174 1 1174 1171 1 1171 1170 1 1174 1175 1 1175 1172 1
		 1172 1171 1 1176 1177 1 1177 1170 1 1170 1161 1 1161 1176 1 1177 1178 1 1178 1173 1
		 1164 1179 1 1179 1176 1 1179 1178 1 1181 1184 1 1184 1185 1 1185 1182 1 1182 1181 1
		 1186 1183 1 1183 1182 1 1185 1186 1 1187 1188 1 1188 1172 1 1175 1187 1 1189 1181 1
		 1181 1172 1 1188 1189 1 1189 1190 1 1190 1184 1 1187 1190 1 1192 1193 1 1193 1194 1
		 1194 1191 1 1191 1192 1 1192 1195 1 1195 1196 1 1196 1193 1 1196 1197 1 1197 1194 1
		 1199 1200 1 1200 1201 1 1201 1198 1 1198 1199 1 1202 1203 1 1203 1200 1 1199 1202 1
		 1203 1204 1 1204 1201 1 1191 1198 1 1201 1192 1 1205 1206 1 1206 1192 1 1201 1205 1
		 1206 1207 1 1207 1195 1 1208 1207 1 1205 1208 1 1204 1208 1 1210 1211 1 1211 1212 1
		 1212 1209 1 1209 1210 1 1213 1214 1 1214 1211 1 1210 1213 1 1215 1212 1 1214 1215 1
		 1212 1199 1 1198 1209 1 1216 1217 1 1217 1199 1 1212 1216 1 1216 1218 1 1218 1219 1
		 1219 1217 1 1219 1202 1 1215 1218 1 1221 1222 1 1222 1223 1 1223 1220 1 1220 1221 1
		 1224 1225 1 1225 1222 1 1221 1224 1 1226 1223 1 1225 1226 1 1209 1220 1 1223 1210 1
		 1227 1228 1 1228 1210 1 1223 1227 1 1227 1229 1 1229 1230 1 1230 1228 1 1226 1229 1
		 1230 1213 1 1232 1233 1 1233 1234 1 1234 1231 1 1231 1232 1 1232 1235 1 1235 1236 1
		 1236 1233 1 1237 1234 1 1236 1237 1 1220 1231 1 1234 1221 1 1238 1239 1 1239 1221 1
		 1234 1238 1 1240 1238 1 1237 1240 1 1240 1241 1 1241 1239 1 1241 1224 1 1243 1244 1
		 1244 1245 1 1245 1242 1 1242 1243 1 1246 1247 1 1247 1245 1 1244 1246 1 1248 1246 1
		 1243 1248 1 1231 1242 1 1245 1232 1 1249 1250 1 1250 1232 1 1245 1249 1 1250 1251 1
		 1251 1235 1 1247 1252 1 1252 1249 1 1254 1255 1 1255 1256 1 1256 1253 1 1253 1254 1
		 1257 1258 1 1258 1255 1 1254 1257 1 1258 1259 1 1259 1256 1 1242 1253 1 1256 1243 1
		 1256 1260 1 1260 1261 1 1261 1243 1 1262 1263 1 1263 1261 1 1260 1262 1 1263 1248 1;
	setAttr ".ed[830:995]" 1259 1262 1 1265 1266 1 1266 1267 1 1267 1264 1 1264 1265 1
		 1268 1269 1 1269 1266 1 1265 1268 1 1270 1267 1 1269 1270 1 1253 1264 1 1267 1254 1
		 1271 1272 1 1272 1254 1 1267 1271 1 1271 1273 1 1273 1274 1 1274 1272 1 1270 1273 1
		 1274 1257 1 1099 1278 1 1278 1279 1 1279 1100 1 1195 1280 1 1280 1279 1 1279 1196 1
		 1207 1281 1 1281 1280 1 1208 1282 1 1282 1281 1 1204 1283 1 1283 1282 1 1203 1284 1
		 1284 1283 1 1202 1285 1 1285 1284 1 1219 1286 1 1286 1285 1 1218 1287 1 1287 1286 1
		 1143 1313 1 1313 1312 1 1312 1142 1 1125 1314 1 1314 1313 1 1124 1315 1 1315 1314 1
		 1215 1288 1 1288 1287 1 1312 1311 1 1311 1137 1 1311 1289 1 1289 1138 1 1289 1290 1
		 1290 1139 1 1241 1291 1 1291 1290 1 1290 1224 1 1240 1292 1 1292 1291 1 1237 1293 1
		 1293 1292 1 1236 1294 1 1294 1293 1 1235 1295 1 1295 1294 1 1251 1296 1 1296 1295 1
		 1251 1252 1 1252 1297 1 1297 1296 1 1247 1298 1 1298 1297 1 1246 1299 1 1299 1298 1
		 1248 1300 1 1300 1299 1 1263 1301 1 1301 1300 1 1262 1302 1 1302 1301 1 1259 1303 1
		 1303 1302 1 1258 1304 1 1304 1303 1 1257 1305 1 1305 1304 1 1274 1306 1 1306 1305 1
		 1273 1307 1 1307 1306 1 1270 1308 1 1308 1307 1 1269 1309 1 1309 1308 1 1268 1310 1
		 1310 1309 1 1186 1277 1 1277 1275 1 1275 1183 1 1278 1197 1 1280 1101 1 1281 1115 1
		 1282 1114 1 1283 1109 1 1284 1110 1 1285 1111 1 1286 1128 1 1287 1129 1 1288 1123 1
		 1289 1225 1 1291 1157 1 1292 1156 1 1293 1151 1 1294 1152 1 1295 1153 1 1296 1167 1
		 1297 1168 1 1298 1162 1 1299 1163 1 1300 1164 1 1301 1179 1 1302 1178 1 1303 1173 1
		 1304 1174 1 1305 1175 1 1306 1187 1 1307 1190 1 1308 1184 1 1309 1185 1 1310 1186 1
		 1311 1226 1 1312 1229 1 1313 1230 1 1314 1213 1 1315 1214 1 1288 1315 1 1098 1318 1
		 1318 1319 1 1319 1099 1 1093 1317 1 1317 1318 1 1092 1316 1 1316 1317 1 1319 1320 1
		 1320 1278 1 1197 1321 1 1321 1322 1 1322 1194 1 1322 1323 1 1323 1191 1 1320 1321 1
		 639 1317 1 1316 640 1 637 1318 1 638 1319 1 641 1320 1 643 1322 1 1321 642 1 644 1323 1
		 654 1191 1 1325 1198 1 654 1325 1 1325 1326 1 1326 1209 1 1326 1327 1;
	setAttr ".ed[996:1161]" 1327 1220 1 1327 1328 1 1328 1231 1 1324 1329 0 1329 1330 0
		 1330 1180 1 1180 1324 1 1330 1331 0 1331 1169 1 1169 1180 1 1331 1332 0 1332 1158 1
		 1158 1169 1 1332 1333 0 1333 1144 1 1144 1158 1 1333 1334 0 1334 1130 1 1334 1335 0
		 1335 1116 1 1335 1336 0 1336 1102 1 1336 1337 0 1337 1092 1 1337 1338 0 1338 1316 1
		 1338 669 0 1159 1341 1 1341 1147 1 1341 1158 1 1339 1341 1 1341 1161 1 1170 1339 1
		 1339 1169 1 1340 1339 1 1339 1172 1 1181 1340 1 1340 1180 1 1275 1276 1 1276 1340 1
		 1340 1183 1 1276 1324 1 1328 1345 1 1345 1242 1 1345 924 1 924 925 1 925 1344 1 1344 1345 1
		 1344 1253 1 1327 924 1 1325 924 1 244 924 1 921 920 1 920 918 1 918 922 1 918 916 1
		 916 923 1 1343 923 1 916 919 1 919 1343 1 919 917 1 917 1342 0 1342 1343 1 1342 1265 1
		 1264 1343 1 1343 1344 1 235 920 1 921 244 1 1275 1349 1 1349 1348 1 1348 1276 1 1277 1347 1
		 1347 1349 1 1349 1268 1 1265 1348 1 1310 1347 1 1348 1346 1 1346 1324 0 1342 1346 0
		 253 265 1 265 266 1 266 255 1 255 253 1 265 256 1 256 248 1 248 266 1 263 247 0 247 255 1
		 266 263 1 248 258 1 258 263 0 275 272 1 272 271 1 271 276 1 276 275 1 276 267 1 267 268 1
		 268 275 1 259 264 1 264 276 1 271 259 1 253 272 1 275 265 1 264 250 1 250 267 1 268 256 1
		 277 254 1 254 257 1 257 278 1 278 277 1 249 277 1 278 260 1 260 249 1 249 252 1 252 279 0
		 279 277 1 279 251 0 251 254 1 281 274 1 274 273 1 273 282 1 282 281 1 269 281 1 282 270 1
		 270 269 1 257 274 1 281 278 1 269 260 1 282 280 1 280 262 1 262 270 1 273 261 1 261 280 1
		 292 288 1 288 253 1 255 292 1 247 284 0 284 292 1 260 288 1 292 249 1 284 252 0 291 264 1
		 259 283 1 283 291 1 248 289 1 289 293 1 293 258 0 289 254 1 251 293 0 256 290 1 290 289 1
		 290 257 1 285 250 1 291 285 1 274 294 1 294 295 1 295 273 1 270 287 1 287 286 1 286 269 1
		 267 295 1 294 268 1 272 286 1 287 271 1 288 286 1 287 283 1;
	setAttr ".ed[1162:1327]" 262 283 1 294 290 1 285 295 1 285 261 1 291 280 1 302 314 1
		 314 315 1 315 304 1 304 302 1 314 305 1 305 297 1 297 315 1 312 296 0 296 304 1 315 312 1
		 297 307 1 307 312 0 324 321 1 321 320 1 320 325 1 325 324 1 325 316 1 316 317 1 317 324 1
		 308 313 1 313 325 1 320 308 1 302 321 1 324 314 1 313 299 1 299 316 1 317 305 1 326 303 1
		 303 306 1 306 327 1 327 326 1 298 326 1 327 309 1 309 298 1 298 301 1 301 328 0 328 326 1
		 328 300 0 300 303 1 330 323 1 323 322 1 322 331 1 331 330 1 318 330 1 331 319 1 319 318 1
		 306 323 1 330 327 1 318 309 1 331 329 1 329 311 1 311 319 1 322 310 1 310 329 1 341 337 1
		 337 302 1 304 341 1 296 333 0 333 341 1 309 337 1 341 298 1 333 301 0 340 313 1 308 332 1
		 332 340 1 297 338 1 338 342 1 342 307 0 338 303 1 300 342 0 305 339 1 339 338 1 339 306 1
		 334 299 1 340 334 1 323 343 1 343 344 1 344 322 1 319 336 1 336 335 1 335 318 1 316 344 1
		 343 317 1 321 335 1 336 320 1 337 335 1 336 332 1 311 332 1 343 339 1 334 344 1 334 310 1
		 340 329 1 366 346 1 346 376 1 376 385 1 385 366 1 376 380 1 380 373 1 373 385 1 379 383 1
		 383 360 1 360 374 1 374 379 1 374 371 1 371 349 1 349 379 1 383 351 1 351 352 1 352 360 1
		 352 382 1 382 363 1 363 360 1 363 377 1 377 374 1 377 354 1 354 371 1 364 366 1 385 348 1
		 348 364 1 356 372 1 372 368 1 368 365 1 365 356 1 370 369 1 369 359 1 359 375 1 375 370 1
		 372 369 1 370 368 1 359 387 1 387 388 1 388 375 1 347 389 1 389 370 1 375 347 1 376 390 1
		 390 389 1 389 380 1 390 368 1 390 391 1 391 365 1 346 391 1 392 380 1 347 392 1 394 393 1
		 393 351 1 383 394 1 356 393 1 394 372 1 369 395 1 395 386 1 386 359 1 394 395 1 379 395 1
		 349 386 1 351 367 1 367 362 0 362 352 1 393 357 1 357 367 0 391 384 1;
	setAttr ".ed[1328:1493]" 384 358 0 358 365 1 353 366 1 364 381 1 381 353 0 362 355 0
		 355 382 1 358 361 0 361 356 1 346 345 1 345 384 0 353 345 0 361 357 0 363 348 1 348 378 1
		 378 377 1 378 350 1 350 354 1 382 364 1 387 354 1 350 388 1 355 381 0 349 387 1 388 392 1
		 373 378 1 373 396 1 396 350 1 396 392 1 32 130 1 130 163 1 163 28 1 28 32 1 130 15 1
		 15 50 1 50 163 1 143 50 1 15 131 1 131 143 1 147 137 1 137 22 1 22 61 1 61 147 1
		 137 144 1 144 136 1 136 22 1 157 109 1 109 70 1 70 8 1 8 157 1 109 53 1 53 125 1
		 125 70 1 76 11 1 11 125 1 53 76 1 46 47 1 47 48 1 48 68 1 68 46 1 47 145 1 145 100 1
		 100 48 1 115 100 1 145 149 1 149 115 1 121 57 1 57 65 1 65 41 1 41 121 1 160 148 1
		 148 75 1 75 93 1 93 160 1 42 41 1 65 150 1 150 42 1 148 110 1 110 107 1 107 75 1
		 159 134 1 134 42 1 150 159 1 110 96 1 96 140 1 140 107 1 110 56 1 56 155 1 155 96 1
		 41 112 1 112 79 1 79 121 1 4 112 1 42 4 1 166 4 1 134 166 1 148 59 1 59 56 1 79 20 1
		 20 151 1 151 121 1 81 166 1 134 90 1 90 81 1 20 103 1 103 160 1 160 151 1 103 59 1
		 155 81 1 90 96 1 102 136 1 144 29 1 29 102 1 138 162 1 162 25 1 25 26 1 26 138 1
		 25 142 1 142 119 1 119 26 1 1 86 1 86 24 1 24 0 1 0 1 1 86 171 1 171 9 1 9 24 1 106 127 1
		 127 86 1 1 106 1 127 105 1 105 171 1 52 94 1 94 120 1 120 139 1 139 52 1 18 95 1
		 95 21 1 21 36 1 36 18 1 124 18 1 36 154 1 154 124 1 139 133 1 133 97 1 97 52 1 72 124 1
		 154 67 1 67 72 1 133 37 1 37 118 1 118 97 1 151 174 1 174 57 1 93 174 1 90 175 1
		 175 140 1 159 175 1 70 47 1 46 8 1 11 149 1 145 125 1 53 123 1 123 82 1 82 76 1 101 123 1
		 109 101 1;
	setAttr ".ed[1494:1659]" 34 101 1 157 34 1 7 48 1 100 54 1 54 7 1 115 165 1 165 54 1
		 156 101 1 34 14 1 14 156 1 122 82 1 123 108 1 108 122 1 156 108 1 7 55 1 55 68 1
		 137 7 1 54 144 1 108 50 1 143 122 1 156 163 1 147 55 1 165 29 1 14 28 1 136 98 1
		 98 117 1 117 22 1 117 49 1 49 61 1 102 85 1 85 98 1 117 130 1 32 49 1 98 15 1 85 131 1
		 38 168 1 168 133 1 139 38 1 51 111 1 111 95 1 18 51 1 72 167 1 167 80 1 80 124 1
		 120 153 1 153 38 1 168 35 1 35 37 1 80 51 1 84 1 1 0 91 1 91 84 0 138 27 1 27 31 0
		 31 162 1 17 106 1 84 17 0 3 36 1 21 87 1 87 3 0 3 77 0 77 154 1 52 146 1 146 88 0
		 88 94 1 77 169 0 169 67 1 118 170 1 170 164 0 164 97 1 164 146 0 155 114 1 114 33 1
		 33 81 1 56 64 1 64 114 1 158 135 1 135 59 1 103 158 1 135 64 1 112 104 1 104 19 1
		 19 79 1 19 89 1 89 20 1 89 158 1 33 63 1 63 166 1 60 4 1 63 60 1 60 104 1 135 5 1
		 5 73 0 73 64 1 116 74 0 74 158 1 89 116 1 74 5 0 128 104 1 60 39 1 39 128 0 114 45 1
		 45 66 0 66 33 1 128 6 0 6 19 1 63 129 1 129 39 0 66 129 0 6 116 0 73 45 0 40 43 0
		 43 11 1 76 40 1 102 126 1 126 152 0 152 85 1 83 143 1 131 13 1 13 83 0 152 13 0 122 23 1
		 23 69 0 69 82 1 165 113 1 113 99 0 99 29 1 43 78 0 78 149 1 69 40 0 115 92 1 92 113 0
		 78 92 0 83 23 0 99 126 0 75 180 1 180 179 1 179 93 1 180 65 1 57 179 1 150 181 1
		 181 178 1 178 159 1 181 107 1 140 178 1 181 180 1 68 182 1 182 177 1 177 46 1 182 34 1
		 157 177 1 182 183 1 183 14 1 55 183 1 28 184 1 184 176 1 176 32 1 184 147 1 61 176 1
		 184 183 1 187 188 1 188 9 1 171 187 1 189 188 1 188 119 1 142 189 1;
	setAttr ".ed[1660:1825]" 51 186 1 186 173 1 173 111 1 186 38 1 153 173 1 186 185 1
		 185 168 1 80 185 1 185 172 1 172 35 1 167 172 1 209 208 1 208 205 1 205 207 1 207 209 1
		 208 194 1 194 191 1 191 205 1 195 211 1 211 210 1 210 198 1 198 195 1 201 195 1 198 200 1
		 200 201 1 205 206 1 206 204 1 204 207 1 197 198 1 210 212 1 212 197 1 220 200 1 197 220 1
		 191 190 1 190 206 1 206 225 1 225 228 1 228 204 1 190 218 1 218 225 1 221 220 1 197 229 1
		 229 221 1 212 232 1 232 229 1 226 225 1 218 216 1 216 226 1 224 221 1 229 231 1 231 224 1
		 226 227 1 227 228 1 232 230 1 230 231 1 190 217 1 217 214 0 214 218 1 200 196 1 196 199 0
		 199 201 1 224 222 1 222 219 0 219 221 1 223 220 1 219 223 0 194 193 1 193 192 0 192 191 1
		 214 215 0 215 216 1 223 196 0 192 217 0 204 213 1 213 202 1 202 207 1 210 202 1 213 212 1
		 211 203 1 203 202 1 203 209 1 234 232 1 213 234 1 228 234 1 234 233 1 233 230 1 227 233 1
		 58 161 1 161 10 1 10 30 1 30 58 1 58 162 1 31 16 0 16 58 1 25 30 1 30 141 1 141 142 1
		 10 12 1 12 141 1 16 62 0 62 161 1 127 132 1 132 2 1 2 105 1 106 71 1 71 132 1 44 71 1
		 17 44 0 10 24 1 9 12 1 138 71 1 44 27 0 26 132 1 161 0 1 189 12 1 187 2 1 2 119 1
		 62 91 0 400 224 1 231 399 1 399 400 1 216 400 1 399 226 1 399 397 1 397 227 1 230 397 1
		 400 398 1 398 222 0 215 398 0 402 208 1 209 401 1 401 402 1 195 402 1 401 211 1 402 403 1
		 403 194 1 201 403 1 403 404 1 404 193 0 199 404 0 37 408 1 408 406 1 406 118 1 408 72 1
		 67 406 1 35 407 1 407 408 1 407 167 1 406 405 1 405 170 0 169 405 0 409 412 1 412 120 1
		 94 409 1 95 412 1 409 21 1 111 411 1 411 412 1 411 153 1 88 410 0 410 409 1 410 87 0
		 932 934 1 934 945 1 945 944 1 944 932 1;
	setAttr ".ed[1826:1991]" 945 927 1 927 935 1 935 944 1 942 945 1 934 926 1 926 942 0
		 942 937 0 937 927 1 954 955 1 955 950 1 950 951 1 951 954 1 954 947 1 947 946 1 946 955 1
		 938 950 1 955 943 1 943 938 1 944 954 1 951 932 1 946 929 1 929 943 1 935 947 1 956 957 1
		 957 936 1 936 933 1 933 956 1 928 939 1 939 957 1 956 928 1 956 958 1 958 931 0 931 928 1
		 933 930 1 930 958 0 960 961 1 961 952 1 952 953 1 953 960 1 948 949 1 949 961 1 960 948 1
		 957 960 1 953 936 1 939 948 1 949 941 1 941 959 1 959 961 1 959 940 1 940 952 1 971 934 1
		 932 967 1 967 971 1 971 963 1 963 926 0 928 971 1 967 939 1 931 963 0 970 962 1 962 938 1
		 943 970 1 937 972 0 972 968 1 968 927 1 972 930 0 933 968 1 968 969 1 969 935 1 936 969 1
		 964 970 1 929 964 1 952 974 1 974 973 1 973 953 1 948 965 1 965 966 1 966 949 1 947 973 1
		 974 946 1 950 966 1 965 951 1 965 967 1 962 966 1 962 941 1 969 973 1 974 964 1 940 964 1
		 959 970 1 981 983 1 983 994 1 994 993 1 993 981 1 994 976 1 976 984 1 984 993 1 991 994 1
		 983 975 1 975 991 0 991 986 0 986 976 1 1003 1004 1 1004 999 1 999 1000 1 1000 1003 1
		 1003 996 1 996 995 1 995 1004 1 987 999 1 1004 992 1 992 987 1 993 1003 1 1000 981 1
		 995 978 1 978 992 1 984 996 1 1005 1006 1 1006 985 1 985 982 1 982 1005 1 977 988 1
		 988 1006 1 1005 977 1 1005 1007 1 1007 980 0 980 977 1 982 979 1 979 1007 0 1009 1010 1
		 1010 1001 1 1001 1002 1 1002 1009 1 997 998 1 998 1010 1 1009 997 1 1006 1009 1 1002 985 1
		 988 997 1 998 990 1 990 1008 1 1008 1010 1 1008 989 1 989 1001 1 1020 983 1 981 1016 1
		 1016 1020 1 1020 1012 1 1012 975 0 977 1020 1 1016 988 1 980 1012 0 1019 1011 1 1011 987 1
		 992 1019 1 986 1021 0 1021 1017 1 1017 976 1 1021 979 0 982 1017 1 1017 1018 1 1018 984 1
		 985 1018 1 1013 1019 1 978 1013 1 1001 1023 1 1023 1022 1 1022 1002 1;
	setAttr ".ed[1992:2157]" 997 1014 1 1014 1015 1 1015 998 1 996 1022 1 1023 995 1
		 999 1015 1 1014 1000 1 1014 1016 1 1011 1015 1 1011 990 1 1018 1022 1 1023 1013 1
		 989 1013 1 1008 1019 1 1045 1064 1 1064 1055 1 1055 1025 1 1025 1045 1 1064 1052 1
		 1052 1059 1 1059 1055 1 1058 1053 1 1053 1039 1 1039 1062 1 1062 1058 1 1058 1028 1
		 1028 1050 1 1050 1053 1 1039 1031 1 1031 1030 1 1030 1062 1 1039 1042 1 1042 1061 1
		 1061 1031 1 1053 1056 1 1056 1042 1 1050 1033 1 1033 1056 1 1043 1027 1 1027 1064 1
		 1045 1043 1 1035 1044 1 1044 1047 1 1047 1051 1 1051 1035 1 1049 1054 1 1054 1038 1
		 1038 1048 1 1048 1049 1 1047 1049 1 1048 1051 1 1054 1067 1 1067 1066 1 1066 1038 1
		 1026 1054 1 1049 1068 1 1068 1026 1 1059 1068 1 1068 1069 1 1069 1055 1 1047 1069 1
		 1044 1070 1 1070 1069 1 1070 1025 1 1071 1026 1 1059 1071 1 1073 1062 1 1030 1072 1
		 1072 1073 1 1051 1073 1 1072 1035 1 1038 1065 1 1065 1074 1 1074 1048 1 1074 1073 1
		 1074 1058 1 1065 1028 1 1031 1041 1 1041 1046 0 1046 1030 1 1046 1036 0 1036 1072 1
		 1044 1037 1 1037 1063 0 1063 1070 1 1032 1060 0 1060 1043 1 1045 1032 1 1061 1034 1
		 1034 1041 0 1035 1040 1 1040 1037 0 1063 1024 0 1024 1025 1 1024 1032 0 1036 1040 0
		 1056 1057 1 1057 1027 1 1027 1042 1 1033 1029 1 1029 1057 1 1043 1061 1 1067 1029 1
		 1033 1066 1 1060 1034 0 1066 1028 1 1071 1067 1 1057 1052 1 1029 1075 1 1075 1052 1
		 1071 1075 1 713 709 1 709 844 1 844 811 1 811 713 1 844 731 1 731 696 1 696 811 1
		 824 812 1 812 696 1 731 824 1 828 742 1 742 703 1 703 818 1 818 828 1 703 817 1 817 825 1
		 825 818 1 838 689 1 689 751 1 751 790 1 790 838 1 751 806 1 806 734 1 734 790 1 757 734 1
		 806 692 1 692 757 1 727 749 1 749 729 1 729 728 1 728 727 1 729 781 1 781 826 1 826 728 1
		 796 830 1 830 826 1 781 796 1 802 722 1 722 746 1 746 738 1 738 802 1 841 774 1 774 756 1
		 756 829 1 829 841 1 723 831 1 831 746 1 722 723 1 756 788 1 788 791 1 791 829 1 840 831 1
		 723 815 1 815 840 1 788 821 1;
	setAttr ".ed[2158:2323]" 821 777 1 777 791 1 777 836 1 836 737 1 737 791 1 802 760 1
		 760 793 1 793 722 1 685 723 1 793 685 1 847 815 1 685 847 1 737 740 1 740 829 1 802 832 1
		 832 701 1 701 760 1 762 771 1 771 815 1 847 762 1 832 841 1 841 784 1 784 701 1 740 784 1
		 777 771 1 762 836 1 783 710 1 710 825 1 817 783 1 819 707 1 707 706 1 706 843 1 843 819 1
		 707 800 1 800 823 1 823 706 1 682 681 1 681 705 1 705 767 1 767 682 1 705 690 1 690 852 1
		 852 767 1 787 682 1 767 808 1 808 787 1 852 786 1 786 808 1 733 820 1 820 801 1 801 775 1
		 775 733 1 699 717 1 717 702 1 702 776 1 776 699 1 805 835 1 835 717 1 699 805 1 733 778 1
		 778 814 1 814 820 1 753 748 1 748 835 1 805 753 1 778 799 1 799 718 1 718 814 1 738 855 1
		 855 832 1 855 774 1 821 856 1 856 771 1 856 840 1 689 727 1 728 751 1 806 826 1 830 692 1
		 757 763 1 763 804 1 804 734 1 782 790 1 804 782 1 715 838 1 782 715 1 688 735 1 735 781 1
		 729 688 1 735 846 1 846 796 1 837 695 1 695 715 1 782 837 1 803 789 1 789 804 1 763 803 1
		 789 837 1 749 736 1 736 688 1 825 735 1 688 818 1 803 824 1 731 789 1 844 837 1 736 828 1
		 710 846 1 709 695 1 703 798 1 798 779 1 779 817 1 742 730 1 730 798 1 779 766 1 766 783 1
		 730 713 1 811 798 1 696 779 1 812 766 1 719 820 1 814 849 1 849 719 1 732 699 1 776 792 1
		 792 732 1 805 761 1 761 848 1 848 753 1 719 834 1 834 801 1 718 716 1 716 849 1 732 761 1
		 765 772 0 772 681 1 682 765 1 843 712 1 712 708 0 708 819 1 698 765 0 787 698 1 684 768 0
		 768 702 1 717 684 1 835 758 1 758 684 0 775 769 1 769 827 0 827 733 1 748 850 1 850 758 0
		 778 845 1 845 851 0 851 799 1 827 845 0 762 714 1 714 795 1 795 836 1 795 745 1 745 737 1
		 839 784 1 740 816 1 816 839 1 745 816 1 760 700 1 700 785 1 785 793 1;
	setAttr ".ed[2324:2489]" 701 770 1 770 700 1 839 770 1 847 744 1 744 714 1 741 744 1
		 685 741 1 785 741 1 745 754 1 754 686 0 686 816 1 797 770 1 839 755 1 755 797 0 686 755 0
		 809 720 0 720 741 1 785 809 1 714 747 1 747 726 0 726 795 1 700 687 1 687 809 0 720 810 0
		 810 744 1 810 747 0 797 687 0 726 754 0 721 757 1 692 724 1 724 721 0 766 833 1 833 807 0
		 807 783 1 764 694 0 694 812 1 824 764 1 694 833 0 763 750 1 750 704 0 704 803 1 710 780 1
		 780 794 0 794 846 1 830 759 1 759 724 0 721 750 0 794 773 0 773 796 1 773 759 0 704 764 0
		 807 780 0 774 860 1 860 861 1 861 756 1 860 738 1 746 861 1 840 859 1 859 862 1 862 831 1
		 859 821 1 788 862 1 861 862 1 727 858 1 858 863 1 863 749 1 858 838 1 715 863 1 695 864 1
		 864 863 1 864 736 1 713 857 1 857 865 1 865 709 1 857 742 1 828 865 1 864 865 1 868 852 1
		 690 869 1 869 868 1 870 823 1 800 869 1 869 870 1 792 854 1 854 867 1 867 732 1 854 834 1
		 719 867 1 849 866 1 866 867 1 866 761 1 716 853 1 853 866 1 853 848 1 890 888 1 888 886 1
		 886 889 1 889 890 1 886 872 1 872 875 1 875 889 1 876 879 1 879 891 1 891 892 1 892 876 1
		 882 881 1 881 879 1 876 882 1 888 885 1 885 887 1 887 886 1 878 893 1 893 891 1 879 878 1
		 901 878 1 881 901 1 887 871 1 871 872 1 885 909 1 909 906 1 906 887 1 906 899 1 899 871 1
		 902 910 1 910 878 1 901 902 1 910 913 1 913 893 1 907 897 1 897 899 1 906 907 1 905 912 1
		 912 910 1 902 905 1 909 908 1 908 907 1 912 911 1 911 913 1 899 895 1 895 898 0 898 871 1
		 882 880 1 880 877 0 877 881 1 902 900 1 900 903 0 903 905 1 904 900 0 901 904 1 872 873 1
		 873 874 0 874 875 1 897 896 1 896 895 0 877 904 0 898 873 0 888 883 1 883 894 1 894 885 1
		 893 894 1 883 891 1 883 884 1 884 892 1 890 884 1 915 894 1 913 915 1;
	setAttr ".ed[2490:2573]" 915 909 1 911 914 1 914 915 1 914 908 1 739 711 1 711 691 1
		 691 842 1 842 739 1 739 697 1 697 712 0 843 739 1 823 822 1 822 711 1 711 706 1 822 693 1
		 693 691 1 842 743 1 743 697 0 786 683 1 683 813 1 813 808 1 813 752 1 752 787 1 725 698 0
		 752 725 1 693 690 1 705 691 1 708 725 0 752 819 1 813 707 1 681 842 1 693 870 1 800 683 1
		 683 868 1 772 743 0 1079 1078 1 1078 912 1 905 1079 1 907 1078 1 1079 897 1 908 1076 1
		 1076 1078 1 1076 911 1 903 1077 0 1077 1079 1 1077 896 0 1081 1080 1 1080 890 1 889 1081 1
		 892 1080 1 1081 876 1 875 1082 1 1082 1081 1 1082 882 1 874 1083 0 1083 1082 1 1083 880 0
		 799 1085 1 1085 1087 1 1087 718 1 1085 748 1 753 1087 1 1087 1086 1 1086 716 1 848 1086 1
		 851 1084 0 1084 1085 1 1084 850 0 1088 775 1 801 1091 1 1091 1088 1 702 1088 1 1091 776 1
		 1091 1090 1 1090 792 1 834 1090 1 1088 1089 1 1089 769 0 768 1089 0 919 239 1 237 917 0
		 916 236 1 918 238 0 920 240 1;
	setAttr -s 1241 ".fc";
	setAttr ".fc[0:499]" -type "polyFaces" 
		f 4 0 1 2 3
		mu 0 4 241 246 243 242
		f 4 4 5 6 7
		mu 0 4 414 413 416 415
		f 4 8 -7 9 10
		mu 0 4 417 415 416 418
		f 4 11 -8 -9 12
		mu 0 4 419 414 415 417
		f 4 -13 13 14 15
		mu 0 4 419 417 421 420
		f 4 16 -14 -11 17
		mu 0 4 422 421 417 418
		f 4 18 19 20 21
		mu 0 4 424 423 426 425
		f 4 22 -22 23 24
		mu 0 4 427 424 425 428
		f 4 -21 25 26 -24
		mu 0 4 425 426 429 428
		f 4 27 -25 28 29
		mu 0 4 430 427 428 431
		f 4 -27 30 31 -29
		mu 0 4 428 429 432 431
		f 4 32 -23 33 -10
		mu 0 4 416 424 427 418
		f 4 -6 34 -19 -33
		mu 0 4 416 413 423 424
		f 4 35 -34 36 37
		mu 0 4 433 418 427 434
		f 4 38 -37 -28 39
		mu 0 4 435 434 427 430
		f 4 -39 40 41 -38
		mu 0 4 434 435 436 433
		f 4 -42 42 -18 -36
		mu 0 4 433 436 422 418
		f 4 43 44 45 46
		mu 0 4 438 437 440 439
		f 4 -47 47 48 49
		mu 0 4 438 439 442 441
		f 4 50 -48 -46 51
		mu 0 4 443 442 439 440
		f 4 -49 52 53 54
		mu 0 4 441 442 445 444
		f 4 55 -53 -51 56
		mu 0 4 446 445 442 443
		f 4 -50 57 -26 58
		mu 0 4 438 441 429 426
		f 4 59 -44 -59 -20
		mu 0 4 423 437 438 426
		f 4 60 -58 61 62
		mu 0 4 447 429 441 448
		f 4 63 -63 64 65
		mu 0 4 449 447 448 450
		f 4 66 -65 -62 -55
		mu 0 4 444 450 448 441
		f 4 -64 67 -31 -61
		mu 0 4 447 449 432 429
		f 4 68 69 70 71
		mu 0 4 452 451 454 453
		f 4 -72 72 73 74
		mu 0 4 452 453 456 455
		f 4 75 -73 -71 76
		mu 0 4 457 456 453 454
		f 4 -74 77 78 79
		mu 0 4 455 456 459 458
		f 4 -76 80 81 -78
		mu 0 4 456 457 460 459
		f 4 82 83 84 85
		mu 0 4 461 443 455 462
		f 4 86 -85 -80 87
		mu 0 4 463 462 455 458
		f 4 -83 88 89 -57
		mu 0 4 443 461 464 446
		f 4 90 -89 -86 -87
		mu 0 4 463 464 461 462
		f 4 -52 91 -75 -84
		mu 0 4 443 440 452 455
		f 4 92 -69 -92 -45
		mu 0 4 437 451 452 440
		f 4 93 94 95 96
		mu 0 4 466 465 468 467
		f 4 -97 97 98 99
		mu 0 4 466 467 470 469
		f 4 -96 100 101 -98
		mu 0 4 467 468 471 470
		f 4 -99 102 103 104
		mu 0 4 469 470 473 472
		f 4 -102 105 106 -103
		mu 0 4 470 471 474 473
		f 4 107 -100 108 -77
		mu 0 4 454 466 469 457
		f 4 109 -94 -108 -70
		mu 0 4 451 465 466 454
		f 4 110 -109 111 112
		mu 0 4 475 457 469 476
		f 4 113 -112 -105 114
		mu 0 4 477 476 469 472
		f 4 -111 115 116 -81
		mu 0 4 457 475 478 460
		f 4 117 -116 -113 -114
		mu 0 4 477 478 475 476
		f 4 118 119 120 121
		mu 0 4 480 481 484 483
		f 4 122 123 124 -120
		mu 0 4 481 482 485 484
		f 4 125 126 127 128
		mu 0 4 471 480 487 486
		f 4 -129 129 130 -106
		mu 0 4 471 486 488 474
		f 4 131 -127 -122 132
		mu 0 4 489 487 480 483
		f 4 133 -130 -128 -132
		mu 0 4 489 488 486 487
		f 4 134 135 136 137
		mu 0 4 491 492 495 494
		f 4 138 139 140 -136
		mu 0 4 492 493 496 495
		f 4 141 142 143 144
		mu 0 4 497 482 491 498
		f 4 -138 145 146 -144
		mu 0 4 491 494 499 498
		f 4 -142 147 148 -124
		mu 0 4 482 497 500 485
		f 4 149 -148 -145 -147
		mu 0 4 499 500 497 498
		f 4 150 151 152 153
		mu 0 4 502 503 506 505
		f 4 154 -152 155 156
		mu 0 4 507 506 503 504
		f 4 157 -140 158 159
		mu 0 4 508 496 493 509
		f 4 160 -159 161 162
		mu 0 4 510 509 493 502
		f 4 -154 163 164 -163
		mu 0 4 502 505 511 510
		f 4 -165 165 -160 -161
		mu 0 4 510 511 508 509
		f 4 166 167 168 169
		mu 0 4 513 512 515 514
		f 4 -170 170 171 172
		mu 0 4 513 514 517 516
		f 4 -169 173 174 -171
		mu 0 4 514 515 518 517
		f 4 175 176 177 178
		mu 0 4 520 519 522 521
		f 4 179 -179 180 181
		mu 0 4 523 520 521 524
		f 4 -178 182 183 -181
		mu 0 4 521 522 525 524
		f 4 184 -177 185 -167
		mu 0 4 513 522 519 512
		f 4 186 -185 187 188
		mu 0 4 526 522 513 527
		f 4 -173 189 190 -188
		mu 0 4 513 516 528 527
		f 4 191 -189 -191 192
		mu 0 4 529 526 527 528
		f 4 -192 193 -183 -187
		mu 0 4 526 529 525 522
		f 4 194 195 196 197
		mu 0 4 531 530 533 532
		f 4 198 -198 199 200
		mu 0 4 534 531 532 535
		f 4 201 -200 -197 202
		mu 0 4 536 535 532 533
		f 4 -196 203 -176 204
		mu 0 4 533 530 519 520
		f 4 205 -205 206 207
		mu 0 4 537 533 520 538
		f 4 -208 208 209 210
		mu 0 4 537 538 540 539
		f 4 211 -209 -207 -180
		mu 0 4 523 540 538 520
		f 4 -211 212 -203 -206
		mu 0 4 537 539 536 533
		f 4 213 214 215 216
		mu 0 4 542 541 544 543
		f 4 217 -217 218 219
		mu 0 4 545 542 543 546
		f 4 220 -219 -216 221
		mu 0 4 547 546 543 544
		f 4 222 -215 223 -195
		mu 0 4 531 544 541 530
		f 4 224 -223 225 226
		mu 0 4 548 544 531 549
		f 4 -227 227 228 229
		mu 0 4 548 549 551 550
		f 4 -230 230 -222 -225
		mu 0 4 548 550 547 544
		f 4 231 -228 -226 -199
		mu 0 4 534 551 549 531
		f 4 232 233 234 235
		mu 0 4 553 552 555 554
		f 4 -236 236 237 238
		mu 0 4 553 554 557 556
		f 4 239 -237 -235 240
		mu 0 4 558 557 554 555
		f 4 241 -234 242 -214
		mu 0 4 542 555 552 541
		f 4 243 -242 244 245
		mu 0 4 559 555 542 560
		f 4 246 -241 -244 247
		mu 0 4 561 558 555 559
		f 4 -246 248 249 -248
		mu 0 4 559 560 562 561
		f 4 250 -249 -245 -218
		mu 0 4 545 562 560 542
		f 4 251 252 253 254
		mu 0 4 564 563 566 565
		f 4 255 -254 256 257
		mu 0 4 567 565 566 568
		f 4 258 -255 -256 259
		mu 0 4 569 564 565 567
		f 4 260 -253 261 -233
		mu 0 4 553 566 563 552
		f 4 262 -261 263 264
		mu 0 4 570 566 553 571
		f 4 -239 265 266 -264
		mu 0 4 553 556 572 571
		f 4 -263 267 268 -257
		mu 0 4 566 570 573 568
		f 4 269 270 271 272
		mu 0 4 575 574 577 576
		f 4 273 -273 274 275
		mu 0 4 578 575 576 579
		f 4 -272 276 277 -275
		mu 0 4 576 577 580 579
		f 4 278 -271 279 -252
		mu 0 4 564 577 574 563
		f 4 -279 280 281 282
		mu 0 4 577 564 582 581
		f 4 283 -282 284 285
		mu 0 4 583 581 582 584
		f 4 286 -285 -281 -259
		mu 0 4 569 584 582 564
		f 4 -284 287 -277 -283
		mu 0 4 581 583 580 577
		f 4 288 289 290 291
		mu 0 4 586 585 588 587
		f 4 292 -292 293 294
		mu 0 4 589 586 587 590
		f 4 295 -294 -291 296
		mu 0 4 591 590 587 588
		f 4 -270 297 -290 298
		mu 0 4 574 575 588 585
		f 4 299 -298 300 301
		mu 0 4 592 588 575 593
		f 4 -302 302 303 304
		mu 0 4 592 593 595 594
		f 4 -305 305 -297 -300
		mu 0 4 592 594 591 588
		f 4 306 -303 -301 -274
		mu 0 4 578 595 593 575
		f 4 307 308 309 -15
		mu 0 4 421 600 599 420
		f 4 310 311 312 -172
		mu 0 4 517 600 601 516
		f 4 -313 313 314 -190
		mu 0 4 516 601 602 528
		f 4 -315 315 316 -193
		mu 0 4 528 602 603 529
		f 4 -317 317 318 -194
		mu 0 4 529 603 604 525
		f 4 -319 319 320 -184
		mu 0 4 525 604 605 524
		f 4 -321 321 322 -182
		mu 0 4 524 605 606 523
		f 4 -323 323 324 -212
		mu 0 4 523 606 607 540
		f 4 -325 325 326 -210
		mu 0 4 540 607 608 539
		f 4 327 328 329 -91
		mu 0 4 463 633 634 464
		f 4 -330 330 331 -90
		mu 0 4 464 634 635 446
		f 4 -332 332 333 -56
		mu 0 4 446 635 636 445
		f 4 -327 334 335 -213
		mu 0 4 539 608 609 536
		f 4 336 337 -328 -88
		mu 0 4 458 632 633 463
		f 4 338 339 -337 -79
		mu 0 4 459 610 632 458
		f 4 340 341 -339 -82
		mu 0 4 460 611 610 459
		f 4 342 343 344 -251
		mu 0 4 545 611 612 562
		f 4 -345 345 346 -250
		mu 0 4 562 612 613 561
		f 4 -347 347 348 -247
		mu 0 4 561 613 614 558
		f 4 -349 349 350 -240
		mu 0 4 558 614 615 557
		f 4 -351 351 352 -238
		mu 0 4 557 615 616 556
		f 4 -353 353 354 -266
		mu 0 4 556 616 617 572
		f 4 -355 355 356 357
		mu 0 4 572 617 618 573
		f 4 -357 358 359 -269
		mu 0 4 573 618 619 568
		f 4 -360 360 361 -258
		mu 0 4 568 619 620 567
		f 4 -362 362 363 -260
		mu 0 4 567 620 621 569
		f 4 -364 364 365 -287
		mu 0 4 569 621 622 584
		f 4 -366 366 367 -286
		mu 0 4 584 622 623 583
		f 4 -368 368 369 -288
		mu 0 4 583 623 624 580
		f 4 -370 370 371 -278
		mu 0 4 580 624 625 579
		f 4 -372 372 373 -276
		mu 0 4 579 625 626 578
		f 4 -374 374 375 -307
		mu 0 4 578 626 627 595
		f 4 -376 376 377 -304
		mu 0 4 595 627 628 594
		f 4 -378 378 379 -306
		mu 0 4 594 628 629 591
		f 4 -380 380 381 -296
		mu 0 4 591 629 630 590
		f 4 -382 382 383 -295
		mu 0 4 590 630 631 589
		f 4 384 385 386 -157
		mu 0 4 504 596 598 507
		f 4 387 -309 -311 -175
		mu 0 4 518 599 600 517
		f 4 388 -312 -308 -17
		mu 0 4 422 601 600 421
		f 4 389 -314 -389 -43
		mu 0 4 436 602 601 422
		f 4 390 -316 -390 -41
		mu 0 4 435 603 602 436
		f 4 391 -318 -391 -40
		mu 0 4 430 604 603 435
		f 4 392 -320 -392 -30
		mu 0 4 431 605 604 430
		f 4 393 -322 -393 -32
		mu 0 4 432 606 605 431
		f 4 394 -324 -394 -68
		mu 0 4 449 607 606 432
		f 4 395 -326 -395 -66
		mu 0 4 450 608 607 449
		f 4 396 -335 -396 -67
		mu 0 4 444 609 608 450
		f 4 397 -342 -343 -220
		mu 0 4 546 610 611 545
		f 4 398 -344 -341 -117
		mu 0 4 478 612 611 460
		f 4 399 -346 -399 -118
		mu 0 4 477 613 612 478
		f 4 400 -348 -400 -115
		mu 0 4 472 614 613 477
		f 4 401 -350 -401 -104
		mu 0 4 473 615 614 472
		f 4 402 -352 -402 -107
		mu 0 4 474 616 615 473
		f 4 403 -354 -403 -131
		mu 0 4 488 617 616 474
		f 4 404 -356 -404 -134
		mu 0 4 489 618 617 488
		f 4 405 -359 -405 -133
		mu 0 4 483 619 618 489
		f 4 406 -361 -406 -121
		mu 0 4 484 620 619 483
		f 4 407 -363 -407 -125
		mu 0 4 485 621 620 484
		f 4 408 -365 -408 -149
		mu 0 4 500 622 621 485
		f 4 409 -367 -409 -150
		mu 0 4 499 623 622 500
		f 4 410 -369 -410 -146
		mu 0 4 494 624 623 499
		f 4 411 -371 -411 -137
		mu 0 4 495 625 624 494
		f 4 412 -373 -412 -141
		mu 0 4 496 626 625 495
		f 4 413 -375 -413 -158
		mu 0 4 508 627 626 496
		f 4 414 -377 -414 -166
		mu 0 4 511 628 627 508
		f 4 415 -379 -415 -164
		mu 0 4 505 629 628 511
		f 4 416 -381 -416 -153
		mu 0 4 506 630 629 505
		f 4 417 -383 -417 -155
		mu 0 4 507 631 630 506
		f 4 418 -340 -398 -221
		mu 0 4 547 632 610 546
		f 4 419 -338 -419 -231
		mu 0 4 550 633 632 547
		f 4 420 -329 -420 -229
		mu 0 4 551 634 633 550
		f 4 421 -331 -421 -232
		mu 0 4 534 635 634 551
		f 4 -201 422 -333 -422
		mu 0 4 534 535 636 635
		f 4 423 -397 -54 -334
		mu 0 4 636 609 444 445
		f 4 -424 -423 -202 -336
		mu 0 4 609 636 535 536
		f 4 424 425 426 -16
		mu 0 4 420 648 647 419
		f 4 -427 427 428 -12
		mu 0 4 419 647 646 414
		f 4 -429 429 430 -5
		mu 0 4 414 646 645 413
		f 4 431 432 -425 -310
		mu 0 4 599 649 648 420
		f 4 433 434 435 -174
		mu 0 4 515 651 650 518
		f 4 436 437 -434 -168
		mu 0 4 512 652 651 515
		f 4 -436 438 -432 -388
		mu 0 4 518 650 649 599
		f 4 439 -430 440 441
		mu 0 4 640 645 646 639
		f 4 -441 -428 442 443
		mu 0 4 639 646 647 637
		f 4 -443 -426 444 445
		mu 0 4 637 647 648 638
		f 4 -445 -433 446 447
		mu 0 4 638 648 649 641
		f 4 448 -435 449 450
		mu 0 4 642 650 651 643
		f 4 -450 -438 451 452
		mu 0 4 643 651 652 644
		f 4 453 -452 -437 454
		mu 0 4 654 644 652 512
		f 4 455 -455 -186 456
		mu 0 4 655 654 512 519
		f 4 -457 -204 457 458
		mu 0 4 655 519 530 656
		f 4 -458 -224 459 460
		mu 0 4 656 530 541 657
		f 4 -460 -243 461 462
		mu 0 4 657 541 552 658
		f 4 463 464 465 466
		mu 0 4 653 501 660 659
		f 4 -465 467 468 469
		mu 0 4 660 501 490 661
		f 4 470 471 472 -469
		mu 0 4 490 479 662 661
		f 4 -472 473 474 475
		mu 0 4 662 479 465 663
		f 4 -475 -110 476 477
		mu 0 4 663 465 451 664
		f 4 -477 -93 478 479
		mu 0 4 664 451 437 665
		f 4 -479 -60 480 481
		mu 0 4 665 437 423 666
		f 4 -481 -35 482 483
		mu 0 4 666 423 413 667
		f 4 -483 -431 484 485
		mu 0 4 667 413 645 668
		f 4 -485 -440 486 487
		mu 0 4 668 645 640 669
		f 4 488 489 -126 -101
		mu 0 4 468 672 480 471
		f 4 -474 490 -489 -95
		mu 0 4 465 479 672 468
		f 4 491 -143 492 493
		mu 0 4 670 491 482 672
		f 4 -471 494 -494 -491
		mu 0 4 479 490 670 672
		f 4 495 -162 496 497
		mu 0 4 671 502 493 670
		f 4 -468 498 -498 -495
		mu 0 4 490 501 671 670
		f 4 499 500 501 -385
		mu 0 4 504 671 597 596
		f 4 -499 -464 502 -501
		mu 0 4 671 501 653 597
		f 4 -490 -493 -123 -119
		mu 0 4 480 672 482 481
		f 4 -492 -497 -139 -135
		mu 0 4 491 670 493 492
		f 4 -496 -500 -156 -151
		mu 0 4 502 671 504 503
		f 4 -447 -439 -449 503
		mu 0 4 641 649 650 642
		f 4 -462 -262 504 505
		mu 0 4 658 552 563 676
		f 4 506 507 508 509
		mu 0 4 676 675 246 245
		f 4 -505 -280 510 -507
		mu 0 4 676 563 574 675
		f 4 -463 -506 -510 511
		mu 0 4 657 658 676 245
		f 4 -461 -512 512 -459
		mu 0 4 656 657 245 655
		f 4 -513 513 514 -456
		mu 0 4 655 245 244 654
		f 4 515 516 517 -4
		mu 0 4 242 238 240 241
		f 4 518 519 -516 -3
		mu 0 4 243 236 238 242
		f 4 520 521 -519 522
		mu 0 4 674 239 236 243
		f 4 523 524 525 -521
		mu 0 4 674 673 237 239
		f 4 526 -289 527 -524
		mu 0 4 674 585 586 673
		f 4 528 -523 -2 -508
		mu 0 4 675 674 243 246
		f 4 -511 -299 -527 -529
		mu 0 4 675 574 585 674
		f 4 529 -518 530 531
		mu 0 4 244 241 240 235
		f 4 -502 532 533 534
		mu 0 4 596 597 679 680
		f 4 -535 535 536 -386
		mu 0 4 596 680 678 598
		f 4 -534 537 -293 538
		mu 0 4 680 679 586 589
		f 4 -537 539 -418 -387
		mu 0 4 598 678 631 507
		f 4 -384 -540 -536 -539
		mu 0 4 589 631 678 680
		f 4 -533 -503 540 541
		mu 0 4 679 597 653 677
		f 4 -528 -538 -542 542
		mu 0 4 673 586 679 677
		f 4 -530 -514 -509 -1
		mu 0 4 241 244 245 246
		f 4 -265 -267 -358 -268
		mu 0 4 570 571 572 573
		f 4 543 544 545 546
		mu 0 4 921 922 923 925
		f 4 547 548 549 550
		mu 0 4 1093 1094 1095 1092
		f 4 551 552 -549 553
		mu 0 4 1096 1097 1095 1094
		f 4 554 -554 -548 555
		mu 0 4 1098 1096 1094 1093
		f 4 556 557 558 -555
		mu 0 4 1098 1099 1100 1096
		f 4 559 -552 -559 560
		mu 0 4 1101 1097 1096 1100
		f 4 561 562 563 564
		mu 0 4 1103 1104 1105 1102
		f 4 565 566 -562 567
		mu 0 4 1106 1107 1104 1103
		f 4 -567 568 569 -563
		mu 0 4 1104 1107 1108 1105
		f 4 570 571 -566 572
		mu 0 4 1109 1110 1107 1106
		f 4 -572 573 574 -569
		mu 0 4 1107 1110 1111 1108
		f 4 -553 575 -568 576
		mu 0 4 1095 1097 1106 1103
		f 4 -577 -565 577 -550
		mu 0 4 1095 1103 1102 1092
		f 4 578 579 -576 580
		mu 0 4 1112 1113 1106 1097
		f 4 581 -573 -580 582
		mu 0 4 1114 1109 1106 1113
		f 4 -579 583 584 -583
		mu 0 4 1113 1112 1115 1114
		f 4 -581 -560 585 -584
		mu 0 4 1112 1097 1101 1115
		f 4 586 587 588 589
		mu 0 4 1117 1118 1119 1116
		f 4 590 591 592 -587
		mu 0 4 1117 1120 1121 1118
		f 4 593 -588 -593 594
		mu 0 4 1122 1119 1118 1121
		f 4 595 596 597 -592
		mu 0 4 1120 1123 1124 1121
		f 4 598 -595 -598 599
		mu 0 4 1125 1122 1121 1124
		f 4 600 -570 601 -591
		mu 0 4 1117 1105 1108 1120
		f 4 -564 -601 -590 602
		mu 0 4 1102 1105 1117 1116
		f 4 603 604 -602 605
		mu 0 4 1126 1127 1120 1108
		f 4 606 607 -604 608
		mu 0 4 1128 1129 1127 1126
		f 4 -596 -605 -608 609
		mu 0 4 1123 1120 1127 1129
		f 4 -606 -575 610 -609
		mu 0 4 1126 1108 1111 1128
		f 4 611 612 613 614
		mu 0 4 1131 1132 1133 1130
		f 4 615 616 617 -612
		mu 0 4 1131 1134 1135 1132
		f 4 618 -613 -618 619
		mu 0 4 1136 1133 1132 1135
		f 4 620 621 622 -617
		mu 0 4 1134 1137 1138 1135
		f 4 -623 623 624 -620
		mu 0 4 1135 1138 1139 1136
		f 4 625 626 627 628
		mu 0 4 1140 1141 1134 1122
		f 4 629 -621 -627 630
		mu 0 4 1142 1137 1134 1141
		f 4 -599 631 632 -629
		mu 0 4 1122 1125 1143 1140
		f 4 -631 -626 -633 633
		mu 0 4 1142 1141 1140 1143
		f 4 -628 -616 634 -594
		mu 0 4 1122 1134 1131 1119
		f 4 -589 -635 -615 635
		mu 0 4 1116 1119 1131 1130
		f 4 636 637 638 639
		mu 0 4 1145 1146 1147 1144
		f 4 640 641 642 -637
		mu 0 4 1145 1148 1149 1146
		f 4 -643 643 644 -638
		mu 0 4 1146 1149 1150 1147
		f 4 645 646 647 -642
		mu 0 4 1148 1151 1152 1149
		f 4 -648 648 649 -644
		mu 0 4 1149 1152 1153 1150
		f 4 -619 650 -641 651
		mu 0 4 1133 1136 1148 1145
		f 4 -614 -652 -640 652
		mu 0 4 1130 1133 1145 1144
		f 4 653 654 -651 655
		mu 0 4 1154 1155 1148 1136
		f 4 656 -646 -655 657
		mu 0 4 1156 1151 1148 1155
		f 4 -625 658 659 -656
		mu 0 4 1136 1139 1157 1154
		f 4 -658 -654 -660 660
		mu 0 4 1156 1155 1154 1157
		f 4 661 662 663 664
		mu 0 4 1159 1162 1163 1160
		f 4 -664 665 666 667
		mu 0 4 1160 1163 1164 1161
		f 4 668 669 670 671
		mu 0 4 1150 1165 1166 1159
		f 4 -650 672 673 -669
		mu 0 4 1150 1153 1167 1165
		f 4 674 -662 -671 675
		mu 0 4 1168 1162 1159 1166
		f 4 -676 -670 -674 676
		mu 0 4 1168 1166 1165 1167
		f 4 677 678 679 680
		mu 0 4 1170 1173 1174 1171
		f 4 -680 681 682 683
		mu 0 4 1171 1174 1175 1172
		f 4 684 685 686 687
		mu 0 4 1176 1177 1170 1161
		f 4 -686 688 689 -678
		mu 0 4 1170 1177 1178 1173
		f 4 -667 690 691 -688
		mu 0 4 1161 1164 1179 1176
		f 4 -689 -685 -692 692
		mu 0 4 1178 1177 1176 1179
		f 4 693 694 695 696
		mu 0 4 1181 1184 1185 1182
		f 4 697 698 -696 699
		mu 0 4 1186 1183 1182 1185
		f 4 700 701 -683 702
		mu 0 4 1187 1188 1172 1175
		f 4 703 704 -702 705
		mu 0 4 1189 1181 1172 1188
		f 4 -704 706 707 -694
		mu 0 4 1181 1189 1190 1184
		f 4 -706 -701 708 -707
		mu 0 4 1189 1188 1187 1190
		f 4 709 710 711 712
		mu 0 4 1192 1193 1194 1191
		f 4 713 714 715 -710
		mu 0 4 1192 1195 1196 1193
		f 4 -716 716 717 -711
		mu 0 4 1193 1196 1197 1194
		f 4 718 719 720 721
		mu 0 4 1199 1200 1201 1198
		f 4 722 723 -719 724
		mu 0 4 1202 1203 1200 1199
		f 4 -724 725 726 -720
		mu 0 4 1200 1203 1204 1201
		f 4 -713 727 -721 728
		mu 0 4 1192 1191 1198 1201
		f 4 729 730 -729 731
		mu 0 4 1205 1206 1192 1201
		f 4 -731 732 733 -714
		mu 0 4 1192 1206 1207 1195
		f 4 734 -733 -730 735
		mu 0 4 1208 1207 1206 1205
		f 4 -732 -727 736 -736
		mu 0 4 1205 1201 1204 1208
		f 4 737 738 739 740
		mu 0 4 1210 1211 1212 1209
		f 4 741 742 -738 743
		mu 0 4 1213 1214 1211 1210
		f 4 744 -739 -743 745
		mu 0 4 1215 1212 1211 1214
		f 4 746 -722 747 -740
		mu 0 4 1212 1199 1198 1209
		f 4 748 749 -747 750
		mu 0 4 1216 1217 1199 1212
		f 4 751 752 753 -749
		mu 0 4 1216 1218 1219 1217
		f 4 -725 -750 -754 754
		mu 0 4 1202 1199 1217 1219
		f 4 -751 -745 755 -752
		mu 0 4 1216 1212 1215 1218
		f 4 756 757 758 759
		mu 0 4 1221 1222 1223 1220
		f 4 760 761 -757 762
		mu 0 4 1224 1225 1222 1221
		f 4 763 -758 -762 764
		mu 0 4 1226 1223 1222 1225
		f 4 -741 765 -759 766
		mu 0 4 1210 1209 1220 1223
		f 4 767 768 -767 769
		mu 0 4 1227 1228 1210 1223
		f 4 770 771 772 -768
		mu 0 4 1227 1229 1230 1228
		f 4 -770 -764 773 -771
		mu 0 4 1227 1223 1226 1229
		f 4 -744 -769 -773 774
		mu 0 4 1213 1210 1228 1230
		f 4 775 776 777 778
		mu 0 4 1232 1233 1234 1231
		f 4 779 780 781 -776
		mu 0 4 1232 1235 1236 1233
		f 4 782 -777 -782 783
		mu 0 4 1237 1234 1233 1236
		f 4 -760 784 -778 785
		mu 0 4 1221 1220 1231 1234
		f 4 786 787 -786 788
		mu 0 4 1238 1239 1221 1234
		f 4 789 -789 -783 790
		mu 0 4 1240 1238 1234 1237
		f 4 -790 791 792 -787
		mu 0 4 1238 1240 1241 1239
		f 4 -763 -788 -793 793
		mu 0 4 1224 1221 1239 1241
		f 4 794 795 796 797
		mu 0 4 1243 1244 1245 1242
		f 4 798 799 -796 800
		mu 0 4 1246 1247 1245 1244
		f 4 801 -801 -795 802
		mu 0 4 1248 1246 1244 1243
		f 4 -779 803 -797 804
		mu 0 4 1232 1231 1242 1245
		f 4 805 806 -805 807
		mu 0 4 1249 1250 1232 1245
		f 4 -807 808 809 -780
		mu 0 4 1232 1250 1251 1235
		f 4 -800 810 811 -808
		mu 0 4 1245 1247 1252 1249
		f 4 812 813 814 815
		mu 0 4 1254 1255 1256 1253
		f 4 816 817 -813 818
		mu 0 4 1257 1258 1255 1254
		f 4 -818 819 820 -814
		mu 0 4 1255 1258 1259 1256
		f 4 -798 821 -815 822
		mu 0 4 1243 1242 1253 1256
		f 4 823 824 825 -823
		mu 0 4 1256 1260 1261 1243
		f 4 826 827 -825 828
		mu 0 4 1262 1263 1261 1260
		f 4 -803 -826 -828 829
		mu 0 4 1248 1243 1261 1263
		f 4 -824 -821 830 -829
		mu 0 4 1260 1256 1259 1262
		f 4 831 832 833 834
		mu 0 4 1265 1266 1267 1264
		f 4 835 836 -832 837
		mu 0 4 1268 1269 1266 1265
		f 4 838 -833 -837 839
		mu 0 4 1270 1267 1266 1269
		f 4 840 -834 841 -816
		mu 0 4 1253 1264 1267 1254
		f 4 842 843 -842 844
		mu 0 4 1271 1272 1254 1267
		f 4 845 846 847 -843
		mu 0 4 1271 1273 1274 1272
		f 4 -845 -839 848 -846
		mu 0 4 1271 1267 1270 1273
		f 4 -819 -844 -848 849
		mu 0 4 1257 1254 1272 1274
		f 4 -558 850 851 852
		mu 0 4 1100 1099 1278 1279
		f 4 -715 853 854 855
		mu 0 4 1196 1195 1280 1279
		f 4 -734 856 857 -854
		mu 0 4 1195 1207 1281 1280
		f 4 -735 858 859 -857
		mu 0 4 1207 1208 1282 1281
		f 4 -737 860 861 -859
		mu 0 4 1208 1204 1283 1282
		f 4 -726 862 863 -861
		mu 0 4 1204 1203 1284 1283
		f 4 -723 864 865 -863
		mu 0 4 1203 1202 1285 1284
		f 4 -755 866 867 -865
		mu 0 4 1202 1219 1286 1285
		f 4 -753 868 869 -867
		mu 0 4 1219 1218 1287 1286
		f 4 -634 870 871 872
		mu 0 4 1142 1143 1313 1312
		f 4 -632 873 874 -871
		mu 0 4 1143 1125 1314 1313
		f 4 -600 875 876 -874
		mu 0 4 1125 1124 1315 1314
		f 4 -756 877 878 -869
		mu 0 4 1218 1215 1288 1287
		f 4 -630 -873 879 880
		mu 0 4 1137 1142 1312 1311
		f 4 -622 -881 881 882
		mu 0 4 1138 1137 1311 1289
		f 4 -624 -883 883 884
		mu 0 4 1139 1138 1289 1290
		f 4 -794 885 886 887
		mu 0 4 1224 1241 1291 1290
		f 4 -792 888 889 -886
		mu 0 4 1241 1240 1292 1291
		f 4 -791 890 891 -889
		mu 0 4 1240 1237 1293 1292
		f 4 -784 892 893 -891
		mu 0 4 1237 1236 1294 1293
		f 4 -781 894 895 -893
		mu 0 4 1236 1235 1295 1294
		f 4 -810 896 897 -895
		mu 0 4 1235 1251 1296 1295
		f 4 898 899 900 -897
		mu 0 4 1251 1252 1297 1296
		f 4 -811 901 902 -900
		mu 0 4 1252 1247 1298 1297
		f 4 -799 903 904 -902
		mu 0 4 1247 1246 1299 1298
		f 4 -802 905 906 -904
		mu 0 4 1246 1248 1300 1299
		f 4 -830 907 908 -906
		mu 0 4 1248 1263 1301 1300
		f 4 -827 909 910 -908
		mu 0 4 1263 1262 1302 1301
		f 4 -831 911 912 -910
		mu 0 4 1262 1259 1303 1302
		f 4 -820 913 914 -912
		mu 0 4 1259 1258 1304 1303
		f 4 -817 915 916 -914
		mu 0 4 1258 1257 1305 1304
		f 4 -850 917 918 -916
		mu 0 4 1257 1274 1306 1305
		f 4 -847 919 920 -918
		mu 0 4 1274 1273 1307 1306
		f 4 -849 921 922 -920
		mu 0 4 1273 1270 1308 1307
		f 4 -840 923 924 -922
		mu 0 4 1270 1269 1309 1308
		f 4 -836 925 926 -924
		mu 0 4 1269 1268 1310 1309
		f 4 -698 927 928 929
		mu 0 4 1183 1186 1277 1275
		f 4 -717 -856 -852 930
		mu 0 4 1197 1196 1279 1278
		f 4 -561 -853 -855 931
		mu 0 4 1101 1100 1279 1280
		f 4 -586 -932 -858 932
		mu 0 4 1115 1101 1280 1281
		f 4 -585 -933 -860 933
		mu 0 4 1114 1115 1281 1282
		f 4 -582 -934 -862 934
		mu 0 4 1109 1114 1282 1283
		f 4 -571 -935 -864 935
		mu 0 4 1110 1109 1283 1284
		f 4 -574 -936 -866 936
		mu 0 4 1111 1110 1284 1285
		f 4 -611 -937 -868 937
		mu 0 4 1128 1111 1285 1286
		f 4 -607 -938 -870 938
		mu 0 4 1129 1128 1286 1287
		f 4 -610 -939 -879 939
		mu 0 4 1123 1129 1287 1288
		f 4 -761 -888 -884 940
		mu 0 4 1225 1224 1290 1289
		f 4 -659 -885 -887 941
		mu 0 4 1157 1139 1290 1291
		f 4 -661 -942 -890 942
		mu 0 4 1156 1157 1291 1292
		f 4 -657 -943 -892 943
		mu 0 4 1151 1156 1292 1293
		f 4 -647 -944 -894 944
		mu 0 4 1152 1151 1293 1294
		f 4 -649 -945 -896 945
		mu 0 4 1153 1152 1294 1295
		f 4 -673 -946 -898 946
		mu 0 4 1167 1153 1295 1296
		f 4 -677 -947 -901 947
		mu 0 4 1168 1167 1296 1297
		f 4 -675 -948 -903 948
		mu 0 4 1162 1168 1297 1298
		f 4 -663 -949 -905 949
		mu 0 4 1163 1162 1298 1299
		f 4 -666 -950 -907 950
		mu 0 4 1164 1163 1299 1300
		f 4 -691 -951 -909 951
		mu 0 4 1179 1164 1300 1301
		f 4 -693 -952 -911 952
		mu 0 4 1178 1179 1301 1302
		f 4 -690 -953 -913 953
		mu 0 4 1173 1178 1302 1303
		f 4 -679 -954 -915 954
		mu 0 4 1174 1173 1303 1304
		f 4 -682 -955 -917 955
		mu 0 4 1175 1174 1304 1305
		f 4 -703 -956 -919 956
		mu 0 4 1187 1175 1305 1306
		f 4 -709 -957 -921 957
		mu 0 4 1190 1187 1306 1307
		f 4 -708 -958 -923 958
		mu 0 4 1184 1190 1307 1308
		f 4 -695 -959 -925 959
		mu 0 4 1185 1184 1308 1309
		f 4 -700 -960 -927 960
		mu 0 4 1186 1185 1309 1310
		f 4 -765 -941 -882 961
		mu 0 4 1226 1225 1289 1311
		f 4 -774 -962 -880 962
		mu 0 4 1229 1226 1311 1312
		f 4 -772 -963 -872 963
		mu 0 4 1230 1229 1312 1313
		f 4 -775 -964 -875 964
		mu 0 4 1213 1230 1313 1314
		f 4 -965 -877 965 -742
		mu 0 4 1213 1314 1315 1214
		f 4 -876 -597 -940 966
		mu 0 4 1315 1124 1123 1288
		f 4 -878 -746 -966 -967
		mu 0 4 1288 1215 1214 1315
		f 4 -557 967 968 969
		mu 0 4 1099 1098 1318 1319
		f 4 -556 970 971 -968
		mu 0 4 1098 1093 1317 1318
		f 4 -551 972 973 -971
		mu 0 4 1093 1092 1316 1317
		f 4 -851 -970 974 975
		mu 0 4 1278 1099 1319 1320
		f 4 -718 976 977 978
		mu 0 4 1194 1197 1321 1322
		f 4 -712 -979 979 980
		mu 0 4 1191 1194 1322 1323
		f 4 -931 -976 981 -977
		mu 0 4 1197 1278 1320 1321
		f 4 -442 982 -974 983
		mu 0 4 640 639 1317 1316
		f 4 -444 984 -972 -983
		mu 0 4 639 637 1318 1317
		f 4 -446 985 -969 -985
		mu 0 4 637 638 1319 1318
		f 4 -448 986 -975 -986
		mu 0 4 638 641 1320 1319
		f 4 -451 987 -978 988
		mu 0 4 642 643 1322 1321
		f 4 -453 989 -980 -988
		mu 0 4 643 644 1323 1322
		f 4 990 -981 -990 -454
		mu 0 4 654 1191 1323 644
		f 4 991 -728 -991 992
		mu 0 4 1325 1198 1191 654
		f 4 993 994 -748 -992
		mu 0 4 1325 1326 1209 1198
		f 4 995 996 -766 -995
		mu 0 4 1326 1327 1220 1209
		f 4 997 998 -785 -997
		mu 0 4 1327 1328 1231 1220
		f 4 999 1000 1001 1002
		mu 0 4 1324 1329 1330 1180
		f 4 1003 1004 1005 -1002
		mu 0 4 1330 1331 1169 1180
		f 4 -1005 1006 1007 1008
		mu 0 4 1169 1331 1332 1158
		f 4 1009 1010 1011 -1008
		mu 0 4 1332 1333 1144 1158
		f 4 1012 1013 -653 -1011
		mu 0 4 1333 1334 1130 1144
		f 4 1014 1015 -636 -1014
		mu 0 4 1334 1335 1116 1130
		f 4 1016 1017 -603 -1016
		mu 0 4 1335 1336 1102 1116
		f 4 1018 1019 -578 -1018
		mu 0 4 1336 1337 1092 1102
		f 4 1020 1021 -973 -1020
		mu 0 4 1337 1338 1316 1092
		f 4 1022 -487 -984 -1022
		mu 0 4 1338 669 640 1316
		f 4 -645 -672 1023 1024
		mu 0 4 1147 1150 1159 1341
		f 4 -639 -1025 1025 -1012
		mu 0 4 1144 1147 1341 1158
		f 4 1026 1027 -687 1028
		mu 0 4 1339 1341 1161 1170
		f 4 -1026 -1027 1029 -1009
		mu 0 4 1158 1341 1339 1169
		f 4 1030 1031 -705 1032
		mu 0 4 1340 1339 1172 1181
		f 4 -1030 -1031 1033 -1006
		mu 0 4 1169 1339 1340 1180
		f 4 -930 1034 1035 1036
		mu 0 4 1183 1275 1276 1340;
	setAttr ".fc[500:999]"
		f 4 -1036 1037 -1003 -1034
		mu 0 4 1340 1276 1324 1180
		f 4 -665 -668 -1028 -1024
		mu 0 4 1159 1160 1161 1341
		f 4 -681 -684 -1032 -1029
		mu 0 4 1170 1171 1172 1339
		f 4 -697 -699 -1037 -1033
		mu 0 4 1181 1182 1183 1340
		f 4 -504 -989 -982 -987
		mu 0 4 641 642 1321 1320
		f 4 1038 1039 -804 -999
		mu 0 4 1328 1345 1242 1231
		f 4 1040 1041 1042 1043
		mu 0 4 1345 924 925 1344
		f 4 -1044 1044 -822 -1040
		mu 0 4 1345 1344 1253 1242
		f 4 1045 -1041 -1039 -998
		mu 0 4 1327 924 1345 1328
		f 4 -994 1046 -1046 -996
		mu 0 4 1326 1325 924 1327
		f 4 -993 -515 1047 -1047
		mu 0 4 1325 654 244 924
		f 4 -544 1048 1049 1050
		mu 0 4 922 921 920 918
		f 4 -545 -1051 1051 1052
		mu 0 4 923 922 918 916
		f 4 1053 -1053 1054 1055
		mu 0 4 1343 923 916 919
		f 4 -1056 1056 1057 1058
		mu 0 4 1343 919 917 1342
		f 4 -1059 1059 -835 1060
		mu 0 4 1343 1342 1265 1264
		f 4 -1043 -546 -1054 1061
		mu 0 4 1344 925 923 1343
		f 4 -1062 -1061 -841 -1045
		mu 0 4 1344 1343 1264 1253
		f 4 -532 1062 -1049 1063
		mu 0 4 244 235 920 921
		f 4 1064 1065 1066 -1035
		mu 0 4 1275 1349 1348 1276
		f 4 -929 1067 1068 -1065
		mu 0 4 1275 1277 1347 1349
		f 4 1069 -838 1070 -1066
		mu 0 4 1349 1268 1265 1348
		f 4 -928 -961 1071 -1068
		mu 0 4 1277 1186 1310 1347
		f 4 -1070 -1069 -1072 -926
		mu 0 4 1268 1349 1347 1310
		f 4 1072 1073 -1038 -1067
		mu 0 4 1348 1346 1324 1276
		f 4 1074 -1073 -1071 -1060
		mu 0 4 1342 1346 1348 1265
		f 4 -547 -1042 -1048 -1064
		mu 0 4 921 925 924 244
		f 4 -812 -899 -809 -806
		mu 0 4 1249 1252 1251 1250
		f 4 1075 1076 1077 1078
		mu 0 4 253 265 266 255
		f 4 -1077 1079 1080 1081
		mu 0 4 266 265 256 248
		f 4 1082 1083 -1078 1084
		mu 0 4 263 247 255 266
		f 4 1085 1086 -1085 -1082
		mu 0 4 248 258 263 266
		f 4 1087 1088 1089 1090
		mu 0 4 275 272 271 276
		f 4 1091 1092 1093 -1091
		mu 0 4 276 267 268 275
		f 4 1094 1095 -1090 1096
		mu 0 4 259 264 276 271
		f 4 1097 -1088 1098 -1076
		mu 0 4 253 272 275 265
		f 4 -1096 1099 1100 -1092
		mu 0 4 276 264 250 267
		f 4 -1080 -1099 -1094 1101
		mu 0 4 256 265 275 268
		f 4 1102 1103 1104 1105
		mu 0 4 277 254 257 278
		f 4 1106 -1106 1107 1108
		mu 0 4 249 277 278 260
		f 4 -1107 1109 1110 1111
		mu 0 4 277 249 252 279
		f 4 -1103 -1112 1112 1113
		mu 0 4 254 277 279 251
		f 4 1114 1115 1116 1117
		mu 0 4 281 274 273 282
		f 4 1118 -1118 1119 1120
		mu 0 4 269 281 282 270
		f 4 -1105 1121 -1115 1122
		mu 0 4 278 257 274 281
		f 4 -1108 -1123 -1119 1123
		mu 0 4 260 278 281 269
		f 4 -1120 1124 1125 1126
		mu 0 4 270 282 280 262
		f 4 1127 1128 -1125 -1117
		mu 0 4 273 261 280 282
		f 4 1129 1130 -1079 1131
		mu 0 4 292 288 253 255
		f 4 -1084 1132 1133 -1132
		mu 0 4 255 247 284 292
		f 4 1134 -1130 1135 -1109
		mu 0 4 260 1350 1351 249
		f 4 -1110 -1136 -1134 1136
		mu 0 4 252 249 1352 1353
		f 4 1137 -1095 1138 1139
		mu 0 4 291 264 259 283
		f 4 1140 1141 1142 -1086
		mu 0 4 248 289 293 258
		f 4 -1142 1143 -1114 1144
		mu 0 4 1354 1355 254 251
		f 4 -1141 -1081 1145 1146
		mu 0 4 289 248 256 290
		f 4 -1104 -1144 -1147 1147
		mu 0 4 257 254 1356 1357
		f 4 1148 -1100 -1138 1149
		mu 0 4 285 250 264 291
		f 4 1150 1151 1152 -1116
		mu 0 4 274 294 295 273
		f 4 1153 1154 1155 -1121
		mu 0 4 270 287 286 269
		f 4 1156 -1152 1157 -1093
		mu 0 4 267 295 1358 268
		f 4 1158 -1155 1159 -1089
		mu 0 4 272 1359 287 271
		f 4 1160 -1159 -1098 -1131
		mu 0 4 288 1360 272 253
		f 4 -1124 -1156 -1161 -1135
		mu 0 4 260 269 286 1361
		f 4 1161 -1139 -1097 -1160
		mu 0 4 287 283 259 271
		f 4 -1127 1162 -1162 -1154
		mu 0 4 270 262 283 287
		f 4 -1158 1163 -1146 -1102
		mu 0 4 268 1362 290 256
		f 4 -1151 -1122 -1148 -1164
		mu 0 4 294 274 257 1363
		f 4 -1149 1164 -1157 -1101
		mu 0 4 250 285 295 267
		f 4 1165 -1128 -1153 -1165
		mu 0 4 285 261 273 295
		f 4 -1166 -1150 1166 -1129
		mu 0 4 261 285 291 280
		f 4 -1126 -1167 -1140 -1163
		mu 0 4 262 280 291 283
		f 4 1167 1168 1169 1170
		mu 0 4 302 314 315 304
		f 4 -1169 1171 1172 1173
		mu 0 4 315 314 305 297
		f 4 1174 1175 -1170 1176
		mu 0 4 312 296 304 315
		f 4 1177 1178 -1177 -1174
		mu 0 4 297 307 312 315
		f 4 1179 1180 1181 1182
		mu 0 4 324 321 320 325
		f 4 1183 1184 1185 -1183
		mu 0 4 325 316 317 324
		f 4 1186 1187 -1182 1188
		mu 0 4 308 313 325 320
		f 4 1189 -1180 1190 -1168
		mu 0 4 302 321 324 314
		f 4 -1188 1191 1192 -1184
		mu 0 4 325 313 299 316
		f 4 -1172 -1191 -1186 1193
		mu 0 4 305 314 324 317
		f 4 1194 1195 1196 1197
		mu 0 4 326 303 306 327
		f 4 1198 -1198 1199 1200
		mu 0 4 298 326 327 309
		f 4 -1199 1201 1202 1203
		mu 0 4 326 298 301 328
		f 4 -1195 -1204 1204 1205
		mu 0 4 303 326 328 300
		f 4 1206 1207 1208 1209
		mu 0 4 330 323 322 331
		f 4 1210 -1210 1211 1212
		mu 0 4 318 330 331 319
		f 4 -1197 1213 -1207 1214
		mu 0 4 327 306 323 330
		f 4 -1200 -1215 -1211 1215
		mu 0 4 309 327 330 318
		f 4 -1212 1216 1217 1218
		mu 0 4 319 331 329 311
		f 4 1219 1220 -1217 -1209
		mu 0 4 322 310 329 331
		f 4 1221 1222 -1171 1223
		mu 0 4 341 337 302 304
		f 4 -1176 1224 1225 -1224
		mu 0 4 304 296 333 341
		f 4 1226 -1222 1227 -1201
		mu 0 4 309 1364 1365 298
		f 4 -1202 -1228 -1226 1228
		mu 0 4 301 298 1366 1367
		f 4 1229 -1187 1230 1231
		mu 0 4 340 313 308 332
		f 4 1232 1233 1234 -1178
		mu 0 4 297 338 342 307
		f 4 -1234 1235 -1206 1236
		mu 0 4 1368 1369 303 300
		f 4 -1233 -1173 1237 1238
		mu 0 4 338 297 305 339
		f 4 -1196 -1236 -1239 1239
		mu 0 4 306 303 1370 1371
		f 4 1240 -1192 -1230 1241
		mu 0 4 334 299 313 340
		f 4 1242 1243 1244 -1208
		mu 0 4 323 343 344 322
		f 4 1245 1246 1247 -1213
		mu 0 4 319 336 335 318
		f 4 1248 -1244 1249 -1185
		mu 0 4 316 344 1372 317
		f 4 1250 -1247 1251 -1181
		mu 0 4 321 1373 336 320
		f 4 1252 -1251 -1190 -1223
		mu 0 4 337 1374 321 302
		f 4 -1216 -1248 -1253 -1227
		mu 0 4 309 318 335 1375
		f 4 1253 -1231 -1189 -1252
		mu 0 4 336 332 308 320
		f 4 -1219 1254 -1254 -1246
		mu 0 4 319 311 332 336
		f 4 -1250 1255 -1238 -1194
		mu 0 4 317 1376 339 305
		f 4 -1243 -1214 -1240 -1256
		mu 0 4 343 323 306 1377
		f 4 -1241 1256 -1249 -1193
		mu 0 4 299 334 344 316
		f 4 1257 -1220 -1245 -1257
		mu 0 4 334 310 322 344
		f 4 -1258 -1242 1258 -1221
		mu 0 4 310 334 340 329
		f 4 -1218 -1259 -1232 -1255
		mu 0 4 311 329 340 332
		f 4 1259 1260 1261 1262
		mu 0 4 366 346 376 385
		f 4 -1262 1263 1264 1265
		mu 0 4 385 376 380 373
		f 4 1266 1267 1268 1269
		mu 0 4 379 383 360 374
		f 4 -1270 1270 1271 1272
		mu 0 4 379 374 371 349
		f 4 1273 1274 1275 -1268
		mu 0 4 383 351 352 360
		f 4 -1276 1276 1277 1278
		mu 0 4 360 352 382 363
		f 4 -1279 1279 1280 -1269
		mu 0 4 360 363 377 374
		f 4 -1271 -1281 1281 1282
		mu 0 4 371 374 377 354
		f 4 1283 -1263 1284 1285
		mu 0 4 364 366 385 348
		f 4 1286 1287 1288 1289
		mu 0 4 356 372 368 365
		f 4 1290 1291 1292 1293
		mu 0 4 370 369 359 375
		f 4 -1288 1294 -1291 1295
		mu 0 4 368 372 369 370
		f 4 -1293 1296 1297 1298
		mu 0 4 375 359 387 388
		f 4 1299 1300 -1294 1301
		mu 0 4 347 389 370 375
		f 4 1302 1303 1304 -1264
		mu 0 4 1378 390 389 1379
		f 4 -1304 1305 -1296 -1301
		mu 0 4 389 390 368 370
		f 4 1306 1307 -1289 -1306
		mu 0 4 390 391 365 368
		f 4 1308 -1307 -1303 -1261
		mu 0 4 1380 391 390 1381
		f 4 1309 -1305 -1300 1310
		mu 0 4 392 1382 389 347
		f 4 1311 1312 -1274 1313
		mu 0 4 394 393 1383 1384
		f 4 1314 -1312 1315 -1287
		mu 0 4 356 393 394 372
		f 4 -1292 1316 1317 1318
		mu 0 4 359 369 395 386
		f 4 -1316 1319 -1317 -1295
		mu 0 4 372 394 395 369
		f 4 -1314 -1267 1320 -1320
		mu 0 4 394 1385 1386 395
		f 4 -1273 1321 -1318 -1321
		mu 0 4 1387 349 386 395
		f 4 -1275 1322 1323 1324
		mu 0 4 352 351 367 362
		f 4 -1313 1325 1326 -1323
		mu 0 4 1388 393 357 1389
		f 4 -1308 1327 1328 1329
		mu 0 4 365 391 384 358
		f 4 1330 -1284 1331 1332
		mu 0 4 353 366 364 381
		f 4 -1277 -1325 1333 1334
		mu 0 4 382 352 362 355
		f 4 -1290 -1330 1335 1336
		mu 0 4 356 365 358 361
		f 4 -1328 -1309 1337 1338
		mu 0 4 384 391 1390 345
		f 4 -1338 -1260 -1331 1339
		mu 0 4 1391 346 366 353
		f 4 -1315 -1337 1340 -1326
		mu 0 4 393 356 361 357
		f 4 1341 1342 1343 -1280
		mu 0 4 363 348 378 377
		f 4 -1282 -1344 1344 1345
		mu 0 4 354 377 378 350
		f 4 -1278 1346 -1286 -1342
		mu 0 4 363 382 364 348
		f 4 -1298 1347 -1346 1348
		mu 0 4 388 387 354 350
		f 4 -1347 -1335 1349 -1332
		mu 0 4 364 382 355 381
		f 4 1350 -1297 -1319 -1322
		mu 0 4 349 387 359 386
		f 4 -1302 -1299 1351 -1311
		mu 0 4 347 375 388 392
		f 4 -1348 -1351 -1272 -1283
		mu 0 4 354 387 349 371
		f 4 -1285 -1266 1352 -1343
		mu 0 4 348 385 373 378
		f 4 -1345 -1353 1353 1354
		mu 0 4 350 378 373 396
		f 4 -1352 -1349 -1355 1355
		mu 0 4 392 388 350 396
		f 4 -1354 -1265 -1310 -1356
		mu 0 4 396 373 380 392
		f 4 1356 1357 1358 1359
		mu 0 4 32 130 163 28
		f 4 1360 1361 1362 -1358
		mu 0 4 130 15 50 163
		f 4 1363 -1362 1364 1365
		mu 0 4 143 50 15 131
		f 4 1366 1367 1368 1369
		mu 0 4 147 137 22 61
		f 4 1370 1371 1372 -1368
		mu 0 4 137 144 136 22
		f 4 1373 1374 1375 1376
		mu 0 4 157 109 70 8
		f 4 1377 1378 1379 -1375
		mu 0 4 109 53 125 70
		f 4 1380 1381 -1379 1382
		mu 0 4 76 11 125 53
		f 4 1383 1384 1385 1386
		mu 0 4 46 47 48 68
		f 4 1387 1388 1389 -1385
		mu 0 4 47 145 100 48
		f 4 1390 -1389 1391 1392
		mu 0 4 115 100 145 149
		f 4 1393 1394 1395 1396
		mu 0 4 121 57 65 41
		f 4 1397 1398 1399 1400
		mu 0 4 160 148 75 93
		f 4 1401 -1396 1402 1403
		mu 0 4 42 41 65 150
		f 4 -1399 1404 1405 1406
		mu 0 4 75 148 110 107
		f 4 1407 1408 -1404 1409
		mu 0 4 159 134 42 150
		f 4 -1406 1410 1411 1412
		mu 0 4 107 110 96 140
		f 4 1413 1414 1415 -1411
		mu 0 4 110 56 155 96
		f 4 1416 1417 1418 -1397
		mu 0 4 41 112 79 121
		f 4 1419 -1417 -1402 1420
		mu 0 4 4 112 41 42
		f 4 1421 -1421 -1409 1422
		mu 0 4 166 4 42 134
		f 4 1423 1424 -1414 -1405
		mu 0 4 148 59 56 110
		f 4 1425 1426 1427 -1419
		mu 0 4 79 20 151 121
		f 4 1428 -1423 1429 1430
		mu 0 4 81 166 134 90
		f 4 1431 1432 1433 -1427
		mu 0 4 1392 103 160 1393
		f 4 1434 -1424 -1398 -1433
		mu 0 4 103 59 148 160
		f 4 -1416 1435 -1431 1436
		mu 0 4 96 155 1394 1395
		f 4 1437 -1372 1438 1439
		mu 0 4 102 136 144 29
		f 4 1440 1441 1442 1443
		mu 0 4 138 162 25 26
		f 4 -1443 1444 1445 1446
		mu 0 4 26 25 142 119
		f 4 1447 1448 1449 1450
		mu 0 4 1 86 24 0
		f 4 -1449 1451 1452 1453
		mu 0 4 24 86 171 9
		f 4 1454 1455 -1448 1456
		mu 0 4 106 127 86 1
		f 4 1457 1458 -1452 -1456
		mu 0 4 127 105 171 86
		f 4 1459 1460 1461 1462
		mu 0 4 52 94 120 139
		f 4 1463 1464 1465 1466
		mu 0 4 18 95 21 36
		f 4 1467 -1467 1468 1469
		mu 0 4 124 18 36 154
		f 4 -1463 1470 1471 1472
		mu 0 4 52 139 133 97
		f 4 1473 -1470 1474 1475
		mu 0 4 72 124 154 67
		f 4 -1472 1476 1477 1478
		mu 0 4 97 133 37 118
		f 4 -1394 -1428 1479 1480
		mu 0 4 57 121 151 174
		f 4 -1480 -1434 -1401 1481
		mu 0 4 174 1396 160 93
		f 4 -1412 -1437 1482 1483
		mu 0 4 140 96 1397 175
		f 4 -1483 -1430 -1408 1484
		mu 0 4 175 90 134 159
		f 4 -1376 1485 -1384 1486
		mu 0 4 8 1398 47 46
		f 4 -1382 1487 -1392 1488
		mu 0 4 1399 1400 149 145
		f 4 -1380 -1489 -1388 -1486
		mu 0 4 1401 1402 145 47
		f 4 1489 1490 1491 -1383
		mu 0 4 53 123 82 76
		f 4 1492 -1490 -1378 1493
		mu 0 4 101 123 53 109
		f 4 1494 -1494 -1374 1495
		mu 0 4 34 101 109 157
		f 4 1496 -1390 1497 1498
		mu 0 4 7 48 100 54
		f 4 -1498 -1391 1499 1500
		mu 0 4 54 100 115 165
		f 4 1501 -1495 1502 1503
		mu 0 4 156 101 34 14
		f 4 1504 -1491 1505 1506
		mu 0 4 122 82 123 108
		f 4 -1506 -1493 -1502 1507
		mu 0 4 108 123 101 156
		f 4 -1386 -1497 1508 1509
		mu 0 4 68 48 7 55
		f 4 1510 -1499 1511 -1371
		mu 0 4 137 7 54 144
		f 4 1512 -1364 1513 -1507
		mu 0 4 108 50 143 122
		f 4 -1513 -1508 1514 -1363
		mu 0 4 50 108 156 163
		f 4 1515 -1509 -1511 -1367
		mu 0 4 147 55 7 137
		f 4 1516 -1439 -1512 -1501
		mu 0 4 165 29 144 54
		f 4 -1515 -1504 1517 -1359
		mu 0 4 163 156 14 28
		f 4 1518 1519 1520 -1373
		mu 0 4 136 98 117 22
		f 4 -1369 -1521 1521 1522
		mu 0 4 61 22 117 49
		f 4 1523 1524 -1519 -1438
		mu 0 4 102 85 98 136
		f 4 1525 -1357 1526 -1522
		mu 0 4 1403 130 32 49
		f 4 1527 -1361 -1526 -1520
		mu 0 4 1404 15 130 1405
		f 4 1528 -1365 -1528 -1525
		mu 0 4 1406 131 15 1407
		f 4 1529 1530 -1471 1531
		mu 0 4 38 168 133 139
		f 4 1532 1533 -1464 1534
		mu 0 4 51 111 95 18
		f 4 1535 1536 1537 -1474
		mu 0 4 72 167 80 124
		f 4 -1532 -1462 1538 1539
		mu 0 4 38 139 120 153
		f 4 1540 1541 -1477 -1531
		mu 0 4 168 35 37 133
		f 4 1542 -1535 -1468 -1538
		mu 0 4 80 51 18 124
		f 4 1543 -1451 1544 1545
		mu 0 4 84 1 0 91
		f 4 -1441 1546 1547 1548
		mu 0 4 162 138 27 31
		f 4 1549 -1457 -1544 1550
		mu 0 4 17 106 1 84
		f 4 1551 -1466 1552 1553
		mu 0 4 3 36 21 87
		f 4 -1469 -1552 1554 1555
		mu 0 4 154 36 3 77
		f 4 -1460 1556 1557 1558
		mu 0 4 94 52 146 88
		f 4 -1475 -1556 1559 1560
		mu 0 4 67 154 77 169
		f 4 -1479 1561 1562 1563
		mu 0 4 97 118 170 164
		f 4 -1473 -1564 1564 -1557
		mu 0 4 52 97 164 146
		f 4 1565 1566 1567 -1436
		mu 0 4 155 114 33 1408
		f 4 1568 1569 -1566 -1415
		mu 0 4 56 64 114 155
		f 4 1570 1571 -1435 1572
		mu 0 4 158 135 59 103
		f 4 -1572 1573 -1569 -1425
		mu 0 4 59 135 64 56
		f 4 1574 1575 1576 -1418
		mu 0 4 112 104 19 79
		f 4 -1577 1577 1578 -1426
		mu 0 4 79 19 89 20
		f 4 1579 -1573 -1432 -1579
		mu 0 4 1409 158 103 1410
		f 4 -1568 1580 1581 -1429
		mu 0 4 81 1411 63 166
		f 4 1582 -1422 -1582 1583
		mu 0 4 60 4 166 63
		f 4 -1575 -1420 -1583 1584
		mu 0 4 104 112 4 60
		f 4 1585 1586 1587 -1574
		mu 0 4 135 5 73 64
		f 4 1588 1589 -1580 1590
		mu 0 4 116 74 158 1412
		f 4 -1586 -1571 -1590 1591
		mu 0 4 5 135 158 74
		f 4 1592 -1585 1593 1594
		mu 0 4 128 104 60 39
		f 4 1595 1596 1597 -1567
		mu 0 4 114 45 66 33
		f 4 -1576 -1593 1598 1599
		mu 0 4 19 104 128 6
		f 4 -1594 -1584 1600 1601
		mu 0 4 39 60 63 129
		f 4 -1601 -1581 -1598 1602
		mu 0 4 129 63 1413 1414
		f 4 -1578 -1600 1603 -1591
		mu 0 4 89 19 6 1415
		f 4 -1570 -1588 1604 -1596
		mu 0 4 114 64 73 45
		f 4 1605 1606 -1381 1607
		mu 0 4 40 43 11 76
		f 4 1608 1609 1610 -1524
		mu 0 4 102 126 152 85
		f 4 1611 -1366 1612 1613
		mu 0 4 83 143 131 13
		f 4 -1611 1614 -1613 -1529
		mu 0 4 1416 1417 13 131
		f 4 -1505 1615 1616 1617
		mu 0 4 82 122 23 69
		f 4 -1517 1618 1619 1620
		mu 0 4 29 165 113 99
		f 4 -1607 1621 1622 -1488
		mu 0 4 1418 1419 78 149
		f 4 -1492 -1618 1623 -1608
		mu 0 4 76 82 69 40
		f 4 -1619 -1500 1624 1625
		mu 0 4 113 165 115 92
		f 4 -1625 -1393 -1623 1626
		mu 0 4 92 115 149 78
		f 4 1627 -1616 -1514 -1612
		mu 0 4 83 23 122 143
		f 4 -1440 -1621 1628 -1609
		mu 0 4 102 29 99 126
		f 4 -1400 1629 1630 1631
		mu 0 4 93 75 180 179
		f 4 1632 -1395 1633 -1631
		mu 0 4 180 65 57 179
		f 4 -1410 1634 1635 1636
		mu 0 4 159 150 181 178
		f 4 1637 -1413 1638 -1636
		mu 0 4 181 107 140 178
		f 4 -1407 -1638 1639 -1630
		mu 0 4 75 107 181 180
		f 4 -1635 -1403 -1633 -1640
		mu 0 4 181 150 65 180
		f 4 1640 1641 1642 -1387
		mu 0 4 68 182 177 46
		f 4 1643 -1496 1644 -1642
		mu 0 4 182 34 157 177
		f 4 -1503 -1644 1645 1646
		mu 0 4 14 34 182 183
		f 4 -1641 -1510 1647 -1646
		mu 0 4 182 68 55 183
		f 4 -1360 1648 1649 1650
		mu 0 4 32 28 184 176
		f 4 1651 -1370 1652 -1650
		mu 0 4 184 147 61 176
		f 4 -1648 -1516 -1652 1653
		mu 0 4 183 55 147 184
		f 4 -1649 -1518 -1647 -1654
		mu 0 4 184 28 14 183
		f 4 1654 1655 -1453 1656
		mu 0 4 187 188 9 171
		f 4 1657 1658 -1446 1659
		mu 0 4 189 188 119 142
		f 4 1660 1661 1662 -1533
		mu 0 4 51 186 173 111
		f 4 -1662 1663 -1540 1664
		mu 0 4 173 186 38 153
		f 4 -1664 1665 1666 -1530
		mu 0 4 38 186 185 168
		f 4 1667 -1666 -1661 -1543
		mu 0 4 80 185 186 51
		f 4 -1667 1668 1669 -1541
		mu 0 4 168 185 172 35
		f 4 -1669 -1668 -1537 1670
		mu 0 4 172 185 80 167
		f 4 1671 1672 1673 1674
		mu 0 4 209 208 205 207
		f 4 -1673 1675 1676 1677
		mu 0 4 205 208 194 191
		f 4 1678 1679 1680 1681
		mu 0 4 195 211 210 198
		f 4 1682 -1682 1683 1684
		mu 0 4 201 195 198 200
		f 4 -1674 1685 1686 1687
		mu 0 4 207 205 206 204
		f 4 1688 -1681 1689 1690
		mu 0 4 197 198 210 212
		f 4 1691 -1684 -1689 1692
		mu 0 4 220 200 198 197
		f 4 -1678 1693 1694 -1686
		mu 0 4 205 191 190 206
		f 4 -1687 1695 1696 1697
		mu 0 4 204 206 225 228
		f 4 -1696 -1695 1698 1699
		mu 0 4 225 206 190 218
		f 4 1700 -1693 1701 1702
		mu 0 4 221 220 197 229
		f 4 -1702 -1691 1703 1704
		mu 0 4 229 197 212 232
		f 4 1705 -1700 1706 1707
		mu 0 4 226 225 218 216
		f 4 1708 -1703 1709 1710
		mu 0 4 224 221 229 231
		f 4 -1697 -1706 1711 1712
		mu 0 4 228 225 226 227
		f 4 -1710 -1705 1713 1714
		mu 0 4 231 229 232 230
		f 4 -1699 1715 1716 1717
		mu 0 4 218 190 217 214
		f 4 -1685 1718 1719 1720
		mu 0 4 201 200 196 199
		f 4 -1709 1721 1722 1723
		mu 0 4 221 224 222 219
		f 4 1724 -1701 -1724 1725
		mu 0 4 223 220 221 219
		f 4 -1677 1726 1727 1728
		mu 0 4 191 194 193 192
		f 4 -1707 -1718 1729 1730
		mu 0 4 216 218 214 215
		f 4 -1719 -1692 -1725 1731
		mu 0 4 196 200 220 223
		f 4 -1694 -1729 1732 -1716
		mu 0 4 190 191 192 217
		f 4 1733 1734 1735 -1688
		mu 0 4 204 213 202 207
		f 4 -1690 1736 -1735 1737
		mu 0 4 212 210 202 213
		f 4 -1737 -1680 1738 1739
		mu 0 4 202 210 211 203
		f 4 -1740 1740 -1675 -1736
		mu 0 4 202 203 209 207
		f 4 1741 -1704 -1738 1742
		mu 0 4 234 232 212 213
		f 4 -1743 -1734 -1698 1743
		mu 0 4 234 213 204 228
		f 4 -1742 1744 1745 -1714
		mu 0 4 232 234 233 230
		f 4 -1745 -1744 -1713 1746
		mu 0 4 233 234 228 227
		f 4 1747 1748 1749 1750
		mu 0 4 58 161 10 30
		f 4 1751 -1549 1752 1753
		mu 0 4 1420 162 31 16
		f 4 1754 1755 1756 -1445
		mu 0 4 25 1421 141 142
		f 4 -1442 -1752 -1751 -1755
		mu 0 4 25 162 1422 1423
		f 4 1757 1758 -1756 -1750
		mu 0 4 10 12 141 30
		f 4 -1748 -1754 1759 1760
		mu 0 4 161 58 1424 62
		f 4 1761 1762 1763 -1458
		mu 0 4 1425 132 2 105
		f 4 1764 1765 -1762 -1455
		mu 0 4 1426 71 132 1427
		f 4 1766 -1765 -1550 1767
		mu 0 4 44 71 1428 1429
		f 4 -1758 1768 -1454 1769
		mu 0 4 12 10 24 9
		f 4 1770 -1767 1771 -1547
		mu 0 4 138 71 44 27
		f 4 1772 -1766 -1771 -1444
		mu 0 4 26 132 71 138
		f 4 -1749 1773 -1450 -1769
		mu 0 4 10 161 0 24
		f 4 -1656 -1658 1774 -1770
		mu 0 4 9 188 189 12
		f 4 -1659 -1655 1775 1776
		mu 0 4 119 188 187 2
		f 4 -1774 -1761 1777 -1545
		mu 0 4 0 161 62 91
		f 4 -1763 -1773 -1447 -1777
		mu 0 4 2 132 26 119
		f 4 -1639 -1484 -1485 -1637
		mu 0 4 178 140 175 159
		f 4 -1482 -1632 -1634 -1481
		mu 0 4 174 93 179 57
		f 4 -1651 -1653 -1523 -1527
		mu 0 4 32 176 61 49
		f 4 -1643 -1645 -1377 -1487
		mu 0 4 46 177 157 8
		f 4 1778 -1711 1779 1780
		mu 0 4 400 224 231 399
		f 4 1781 -1781 1782 -1708
		mu 0 4 216 1430 1431 226
		f 4 -1712 -1783 1783 1784
		mu 0 4 227 226 1432 397
		f 4 -1715 1785 -1784 -1780
		mu 0 4 231 230 397 399
		f 4 -1779 1786 1787 -1722
		mu 0 4 224 400 398 222
		f 4 -1782 -1731 1788 -1787
		mu 0 4 1433 216 215 1434
		f 4 -1786 -1746 -1747 -1785
		mu 0 4 397 230 233 227
		f 4 1789 -1672 1790 1791
		mu 0 4 402 208 209 401
		f 4 -1679 1792 -1792 1793
		mu 0 4 211 195 1435 401
		f 4 -1676 -1790 1794 1795
		mu 0 4 194 208 402 403
		f 4 -1793 -1683 1796 -1795
		mu 0 4 1436 195 201 1437
		f 4 -1727 -1796 1797 1798
		mu 0 4 193 194 403 404
		f 4 -1797 -1721 1799 -1798
		mu 0 4 1438 201 199 1439
		f 4 -1741 -1739 -1794 -1791
		mu 0 4 209 203 211 401
		f 4 -1478 1800 1801 1802
		mu 0 4 118 37 408 406
		f 4 1803 -1476 1804 -1802
		mu 0 4 1440 72 67 1441
		f 4 -1542 1805 1806 -1801
		mu 0 4 37 35 407 408
		f 4 1807 -1536 -1804 -1807
		mu 0 4 407 167 72 1442
		f 4 -1562 -1803 1808 1809
		mu 0 4 170 118 406 405
		f 4 -1805 -1561 1810 -1809
		mu 0 4 1443 67 169 1444
		f 4 1811 1812 -1461 1813
		mu 0 4 409 412 120 94
		f 4 1814 -1812 1815 -1465
		mu 0 4 95 1445 1446 21
		f 4 -1534 1816 1817 -1815
		mu 0 4 95 111 411 1447
		f 4 -1818 1818 -1539 -1813
		mu 0 4 412 411 153 120
		f 4 -1814 -1559 1819 1820
		mu 0 4 409 94 88 410
		f 4 -1553 -1816 -1821 1821
		mu 0 4 87 21 1448 1449
		f 4 -1663 -1665 -1819 -1817
		mu 0 4 111 173 153 411
		f 4 -1806 -1670 -1671 -1808
		mu 0 4 407 35 172 167
		f 4 -1657 -1459 -1764 -1776
		mu 0 4 187 171 105 2
		f 4 -1759 -1775 -1660 -1757
		mu 0 4 141 12 189 142
		f 4 1822 1823 1824 1825
		mu 0 4 932 934 945 944
		f 4 1826 1827 1828 -1825
		mu 0 4 945 927 935 944
		f 4 1829 -1824 1830 1831
		mu 0 4 942 945 934 926
		f 4 -1827 -1830 1832 1833
		mu 0 4 927 945 942 937
		f 4 1834 1835 1836 1837
		mu 0 4 954 955 950 951
		f 4 -1835 1838 1839 1840
		mu 0 4 955 954 947 946
		f 4 1841 -1836 1842 1843
		mu 0 4 938 950 955 943
		f 4 -1826 1844 -1838 1845
		mu 0 4 932 944 954 951
		f 4 -1841 1846 1847 -1843
		mu 0 4 955 946 929 943
		f 4 1848 -1839 -1845 -1829
		mu 0 4 935 947 954 944
		f 4 1849 1850 1851 1852
		mu 0 4 956 957 936 933
		f 4 1853 1854 -1850 1855
		mu 0 4 928 939 957 956
		f 4 1856 1857 1858 -1856
		mu 0 4 956 958 931 928
		f 4 1859 1860 -1857 -1853
		mu 0 4 933 930 958 956
		f 4 1861 1862 1863 1864
		mu 0 4 960 961 952 953
		f 4 1865 1866 -1862 1867
		mu 0 4 948 949 961 960
		f 4 1868 -1865 1869 -1851
		mu 0 4 957 960 953 936
		f 4 1870 -1868 -1869 -1855
		mu 0 4 939 948 960 957
		f 4 1871 1872 1873 -1867
		mu 0 4 949 941 959 961
		f 4 -1863 -1874 1874 1875
		mu 0 4 952 961 959 940
		f 4 1876 -1823 1877 1878
		mu 0 4 971 934 932 967
		f 4 -1877 1879 1880 -1831
		mu 0 4 934 971 963 926
		f 4 -1854 1881 -1879 1882
		mu 0 4 939 928 1450 1451
		f 4 1883 -1880 -1882 -1859
		mu 0 4 931 1452 1453 928
		f 4 1884 1885 -1844 1886
		mu 0 4 970 962 938 943
		f 4 -1834 1887 1888 1889
		mu 0 4 927 937 972 968
		f 4 1890 -1860 1891 -1889
		mu 0 4 1454 930 933 1455
		f 4 1892 1893 -1828 -1890
		mu 0 4 968 969 935 927
		f 4 1894 -1893 -1892 -1852
		mu 0 4 936 1456 1457 933
		f 4 1895 -1887 -1848 1896
		mu 0 4 964 970 943 929
		f 4 -1864 1897 1898 1899
		mu 0 4 953 952 974 973
		f 4 -1866 1900 1901 1902
		mu 0 4 949 948 965 966
		f 4 -1840 1903 -1899 1904
		mu 0 4 946 947 1458 974
		f 4 -1837 1905 -1902 1906
		mu 0 4 951 950 966 1459
		f 4 -1878 -1846 -1907 1907
		mu 0 4 967 932 951 1460
		f 4 -1883 -1908 -1901 -1871
		mu 0 4 939 1461 965 948
		f 4 -1906 -1842 -1886 1908
		mu 0 4 966 950 938 962
		f 4 -1903 -1909 1909 -1872
		mu 0 4 949 966 962 941
		f 4 -1849 -1894 1910 -1904
		mu 0 4 947 935 969 1462
		f 4 -1911 -1895 -1870 -1900
		mu 0 4 973 1463 936 953
		f 4 -1847 -1905 1911 -1897
		mu 0 4 929 946 974 964
		f 4 -1912 -1898 -1876 1912
		mu 0 4 964 974 952 940
		f 4 -1875 1913 -1896 -1913
		mu 0 4 940 959 970 964
		f 4 -1910 -1885 -1914 -1873
		mu 0 4 941 962 970 959
		f 4 1914 1915 1916 1917
		mu 0 4 981 983 994 993
		f 4 1918 1919 1920 -1917
		mu 0 4 994 976 984 993
		f 4 1921 -1916 1922 1923
		mu 0 4 991 994 983 975
		f 4 -1919 -1922 1924 1925
		mu 0 4 976 994 991 986
		f 4 1926 1927 1928 1929
		mu 0 4 1003 1004 999 1000
		f 4 -1927 1930 1931 1932
		mu 0 4 1004 1003 996 995
		f 4 1933 -1928 1934 1935
		mu 0 4 987 999 1004 992
		f 4 -1918 1936 -1930 1937
		mu 0 4 981 993 1003 1000
		f 4 -1933 1938 1939 -1935
		mu 0 4 1004 995 978 992
		f 4 1940 -1931 -1937 -1921
		mu 0 4 984 996 1003 993
		f 4 1941 1942 1943 1944
		mu 0 4 1005 1006 985 982
		f 4 1945 1946 -1942 1947
		mu 0 4 977 988 1006 1005
		f 4 1948 1949 1950 -1948
		mu 0 4 1005 1007 980 977
		f 4 1951 1952 -1949 -1945
		mu 0 4 982 979 1007 1005
		f 4 1953 1954 1955 1956
		mu 0 4 1009 1010 1001 1002
		f 4 1957 1958 -1954 1959
		mu 0 4 997 998 1010 1009
		f 4 1960 -1957 1961 -1943
		mu 0 4 1006 1009 1002 985
		f 4 1962 -1960 -1961 -1947
		mu 0 4 988 997 1009 1006
		f 4 1963 1964 1965 -1959
		mu 0 4 998 990 1008 1010
		f 4 -1955 -1966 1966 1967
		mu 0 4 1001 1010 1008 989
		f 4 1968 -1915 1969 1970
		mu 0 4 1020 983 981 1016
		f 4 -1969 1971 1972 -1923
		mu 0 4 983 1020 1012 975
		f 4 -1946 1973 -1971 1974
		mu 0 4 988 977 1464 1465
		f 4 1975 -1972 -1974 -1951
		mu 0 4 980 1466 1467 977
		f 4 1976 1977 -1936 1978
		mu 0 4 1019 1011 987 992
		f 4 -1926 1979 1980 1981
		mu 0 4 976 986 1021 1017
		f 4 1982 -1952 1983 -1981
		mu 0 4 1468 979 982 1469
		f 4 1984 1985 -1920 -1982
		mu 0 4 1017 1018 984 976
		f 4 1986 -1985 -1984 -1944
		mu 0 4 985 1470 1471 982
		f 4 1987 -1979 -1940 1988
		mu 0 4 1013 1019 992 978
		f 4 -1956 1989 1990 1991
		mu 0 4 1002 1001 1023 1022
		f 4 -1958 1992 1993 1994
		mu 0 4 998 997 1014 1015
		f 4 -1932 1995 -1991 1996
		mu 0 4 995 996 1472 1023
		f 4 -1929 1997 -1994 1998
		mu 0 4 1000 999 1015 1473
		f 4 -1970 -1938 -1999 1999
		mu 0 4 1016 981 1000 1474
		f 4 -1975 -2000 -1993 -1963
		mu 0 4 988 1475 1014 997
		f 4 -1998 -1934 -1978 2000
		mu 0 4 1015 999 987 1011
		f 4 -1995 -2001 2001 -1964
		mu 0 4 998 1015 1011 990
		f 4 -1941 -1986 2002 -1996
		mu 0 4 996 984 1018 1476
		f 4 -2003 -1987 -1962 -1992
		mu 0 4 1022 1477 985 1002
		f 4 -1939 -1997 2003 -1989
		mu 0 4 978 995 1023 1013
		f 4 -2004 -1990 -1968 2004
		mu 0 4 1013 1023 1001 989
		f 4 -1967 2005 -1988 -2005
		mu 0 4 989 1008 1019 1013
		f 4 -2002 -1977 -2006 -1965
		mu 0 4 990 1011 1019 1008
		f 4 2006 2007 2008 2009
		mu 0 4 1045 1064 1055 1025
		f 4 2010 2011 2012 -2008
		mu 0 4 1064 1052 1059 1055
		f 4 2013 2014 2015 2016
		mu 0 4 1058 1053 1039 1062
		f 4 2017 2018 2019 -2014
		mu 0 4 1058 1028 1050 1053
		f 4 -2016 2020 2021 2022
		mu 0 4 1062 1039 1031 1030
		f 4 2023 2024 2025 -2021
		mu 0 4 1039 1042 1061 1031
		f 4 -2015 2026 2027 -2024
		mu 0 4 1039 1053 1056 1042
		f 4 2028 2029 -2027 -2020
		mu 0 4 1050 1033 1056 1053
		f 4 2030 2031 -2007 2032
		mu 0 4 1043 1027 1064 1045
		f 4 2033 2034 2035 2036
		mu 0 4 1035 1044 1047 1051
		f 4 2037 2038 2039 2040
		mu 0 4 1049 1054 1038 1048
		f 4 2041 -2041 2042 -2036
		mu 0 4 1047 1049 1048 1051
		f 4 2043 2044 2045 -2039
		mu 0 4 1054 1067 1066 1038
		f 4 2046 -2038 2047 2048
		mu 0 4 1026 1054 1049 1068
		f 4 -2013 2049 2050 2051
		mu 0 4 1478 1479 1068 1069
		f 4 -2048 -2042 2052 -2051
		mu 0 4 1068 1049 1047 1069
		f 4 -2053 -2035 2053 2054
		mu 0 4 1069 1047 1044 1070
		f 4 -2009 -2052 -2055 2055
		mu 0 4 1480 1481 1069 1070
		f 4 2056 -2049 -2050 2057
		mu 0 4 1071 1026 1068 1482
		f 4 2058 -2023 2059 2060
		mu 0 4 1073 1483 1484 1072
		f 4 -2037 2061 -2061 2062
		mu 0 4 1035 1051 1073 1072
		f 4 2063 2064 2065 -2040
		mu 0 4 1038 1065 1074 1048
		f 4 -2043 -2066 2066 -2062
		mu 0 4 1051 1048 1074 1073
		f 4 -2067 2067 -2017 -2059
		mu 0 4 1073 1074 1485 1486
		f 4 -2068 -2065 2068 -2018
		mu 0 4 1487 1074 1065 1028
		f 4 2069 2070 2071 -2022
		mu 0 4 1031 1041 1046 1030
		f 4 -2072 2072 2073 -2060
		mu 0 4 1488 1489 1036 1072
		f 4 2074 2075 2076 -2054
		mu 0 4 1044 1037 1063 1070
		f 4 2077 2078 -2033 2079
		mu 0 4 1032 1060 1043 1045
		f 4 2080 2081 -2070 -2026
		mu 0 4 1061 1034 1041 1031;
	setAttr ".fc[1000:1240]"
		f 4 2082 2083 -2075 -2034
		mu 0 4 1035 1040 1037 1044
		f 4 2084 2085 -2056 -2077
		mu 0 4 1063 1024 1490 1070
		f 4 2086 -2080 -2010 -2086
		mu 0 4 1491 1032 1045 1025
		f 4 -2074 2087 -2083 -2063
		mu 0 4 1072 1036 1040 1035
		f 4 -2028 2088 2089 2090
		mu 0 4 1042 1056 1057 1027
		f 4 2091 2092 -2089 -2030
		mu 0 4 1033 1029 1057 1056
		f 4 -2091 -2031 2093 -2025
		mu 0 4 1042 1027 1043 1061
		f 4 2094 -2092 2095 -2045
		mu 0 4 1067 1029 1033 1066
		f 4 -2079 2096 -2081 -2094
		mu 0 4 1043 1060 1034 1061
		f 4 -2069 -2064 -2046 2097
		mu 0 4 1028 1065 1038 1066
		f 4 -2057 2098 -2044 -2047
		mu 0 4 1026 1071 1067 1054
		f 4 -2029 -2019 -2098 -2096
		mu 0 4 1033 1050 1028 1066
		f 4 -2090 2099 -2011 -2032
		mu 0 4 1027 1057 1052 1064
		f 4 2100 2101 -2100 -2093
		mu 0 4 1029 1075 1052 1057
		f 4 2102 -2101 -2095 -2099
		mu 0 4 1071 1075 1029 1067
		f 4 -2103 -2058 -2012 -2102
		mu 0 4 1075 1071 1059 1052
		f 4 2103 2104 2105 2106
		mu 0 4 713 709 844 811
		f 4 -2106 2107 2108 2109
		mu 0 4 811 844 731 696
		f 4 2110 2111 -2109 2112
		mu 0 4 824 812 696 731
		f 4 2113 2114 2115 2116
		mu 0 4 828 742 703 818
		f 4 -2116 2117 2118 2119
		mu 0 4 818 703 817 825
		f 4 2120 2121 2122 2123
		mu 0 4 838 689 751 790
		f 4 -2123 2124 2125 2126
		mu 0 4 790 751 806 734
		f 4 2127 -2126 2128 2129
		mu 0 4 757 734 806 692
		f 4 2130 2131 2132 2133
		mu 0 4 727 749 729 728
		f 4 -2133 2134 2135 2136
		mu 0 4 728 729 781 826
		f 4 2137 2138 -2136 2139
		mu 0 4 796 830 826 781
		f 4 2140 2141 2142 2143
		mu 0 4 802 722 746 738
		f 4 2144 2145 2146 2147
		mu 0 4 841 774 756 829
		f 4 2148 2149 -2142 2150
		mu 0 4 723 831 746 722
		f 4 2151 2152 2153 -2147
		mu 0 4 756 788 791 829
		f 4 2154 -2149 2155 2156
		mu 0 4 840 831 723 815
		f 4 2157 2158 2159 -2153
		mu 0 4 788 821 777 791
		f 4 -2160 2160 2161 2162
		mu 0 4 791 777 836 737
		f 4 -2141 2163 2164 2165
		mu 0 4 722 802 760 793
		f 4 2166 -2151 -2166 2167
		mu 0 4 685 723 722 793
		f 4 2168 -2156 -2167 2169
		mu 0 4 847 815 723 685
		f 4 -2154 -2163 2170 2171
		mu 0 4 829 791 737 740
		f 4 -2164 2172 2173 2174
		mu 0 4 760 802 832 701
		f 4 2175 2176 -2169 2177
		mu 0 4 762 771 815 847
		f 4 -2174 2178 2179 2180
		mu 0 4 1492 1493 841 784
		f 4 -2180 -2148 -2172 2181
		mu 0 4 784 841 829 740
		f 4 2182 -2176 2183 -2161
		mu 0 4 777 1494 1495 836
		f 4 2184 2185 -2119 2186
		mu 0 4 783 710 825 817
		f 4 2187 2188 2189 2190
		mu 0 4 819 707 706 843
		f 4 2191 2192 2193 -2189
		mu 0 4 707 800 823 706
		f 4 2194 2195 2196 2197
		mu 0 4 682 681 705 767
		f 4 2198 2199 2200 -2197
		mu 0 4 705 690 852 767
		f 4 2201 -2198 2202 2203
		mu 0 4 787 682 767 808
		f 4 -2203 -2201 2204 2205
		mu 0 4 808 767 852 786
		f 4 2206 2207 2208 2209
		mu 0 4 733 820 801 775
		f 4 2210 2211 2212 2213
		mu 0 4 699 717 702 776
		f 4 2214 2215 -2211 2216
		mu 0 4 805 835 717 699
		f 4 2217 2218 2219 -2207
		mu 0 4 733 778 814 820
		f 4 2220 2221 -2215 2222
		mu 0 4 753 748 835 805
		f 4 2223 2224 2225 -2219
		mu 0 4 778 799 718 814
		f 4 2226 2227 -2173 -2144
		mu 0 4 738 855 832 802
		f 4 2228 -2145 -2179 -2228
		mu 0 4 855 774 841 1496
		f 4 2229 2230 -2183 -2159
		mu 0 4 821 856 1497 777
		f 4 2231 -2157 -2177 -2231
		mu 0 4 856 840 815 771
		f 4 2232 -2134 2233 -2122
		mu 0 4 689 727 728 1498
		f 4 2234 -2139 2235 -2129
		mu 0 4 1499 826 830 1500
		f 4 -2234 -2137 -2235 -2125
		mu 0 4 1501 728 826 1502
		f 4 -2128 2236 2237 2238
		mu 0 4 734 757 763 804
		f 4 2239 -2127 -2239 2240
		mu 0 4 782 790 734 804
		f 4 2241 -2124 -2240 2242
		mu 0 4 715 838 790 782
		f 4 2243 2244 -2135 2245
		mu 0 4 688 735 781 729
		f 4 2246 2247 -2140 -2245
		mu 0 4 735 846 796 781
		f 4 2248 2249 -2243 2250
		mu 0 4 837 695 715 782
		f 4 2251 2252 -2238 2253
		mu 0 4 803 789 804 763
		f 4 2254 -2251 -2241 -2253
		mu 0 4 789 837 782 804
		f 4 2255 2256 -2246 -2132
		mu 0 4 749 736 688 729
		f 4 -2120 2257 -2244 2258
		mu 0 4 818 825 735 688
		f 4 -2252 2259 -2113 2260
		mu 0 4 789 803 824 731
		f 4 -2108 2261 -2255 -2261
		mu 0 4 731 844 837 789
		f 4 -2117 -2259 -2257 2262
		mu 0 4 828 818 688 736
		f 4 -2247 -2258 -2186 2263
		mu 0 4 846 735 825 710
		f 4 -2105 2264 -2249 -2262
		mu 0 4 844 709 695 837
		f 4 -2118 2265 2266 2267
		mu 0 4 817 703 798 779
		f 4 2268 2269 -2266 -2115
		mu 0 4 742 730 798 703
		f 4 -2187 -2268 2270 2271
		mu 0 4 783 817 779 766
		f 4 -2270 2272 -2107 2273
		mu 0 4 1503 730 713 811
		f 4 -2267 -2274 -2110 2274
		mu 0 4 1504 1505 811 696
		f 4 -2271 -2275 -2112 2275
		mu 0 4 1506 1507 696 812
		f 4 2276 -2220 2277 2278
		mu 0 4 719 820 814 849
		f 4 2279 -2214 2280 2281
		mu 0 4 732 699 776 792
		f 4 -2223 2282 2283 2284
		mu 0 4 753 805 761 848
		f 4 2285 2286 -2208 -2277
		mu 0 4 719 834 801 820
		f 4 -2278 -2226 2287 2288
		mu 0 4 849 814 718 716
		f 4 -2283 -2217 -2280 2289
		mu 0 4 761 805 699 732
		f 4 2290 2291 -2195 2292
		mu 0 4 765 772 681 682
		f 4 2293 2294 2295 -2191
		mu 0 4 843 712 708 819
		f 4 2296 -2293 -2202 2297
		mu 0 4 698 765 682 787
		f 4 2298 2299 -2212 2300
		mu 0 4 684 768 702 717
		f 4 2301 2302 -2301 -2216
		mu 0 4 835 758 684 717
		f 4 2303 2304 2305 -2210
		mu 0 4 775 769 827 733
		f 4 2306 2307 -2302 -2222
		mu 0 4 748 850 758 835
		f 4 2308 2309 2310 -2224
		mu 0 4 778 845 851 799
		f 4 -2306 2311 -2309 -2218
		mu 0 4 733 827 845 778
		f 4 -2184 2312 2313 2314
		mu 0 4 836 1508 714 795
		f 4 -2162 -2315 2315 2316
		mu 0 4 737 836 795 745
		f 4 2317 -2182 2318 2319
		mu 0 4 839 784 740 816
		f 4 -2171 -2317 2320 -2319
		mu 0 4 740 737 745 816
		f 4 -2165 2321 2322 2323
		mu 0 4 793 760 700 785
		f 4 -2175 2324 2325 -2322
		mu 0 4 760 701 770 700
		f 4 -2325 -2181 -2318 2326
		mu 0 4 1509 1510 784 839
		f 4 -2178 2327 2328 -2313
		mu 0 4 762 847 744 1511
		f 4 2329 -2328 -2170 2330
		mu 0 4 741 744 847 685
		f 4 2331 -2331 -2168 -2324
		mu 0 4 785 741 685 793
		f 4 -2321 2332 2333 2334
		mu 0 4 816 745 754 686
		f 4 2335 -2327 2336 2337
		mu 0 4 797 1512 839 755
		f 4 2338 -2337 -2320 -2335
		mu 0 4 686 755 839 816
		f 4 2339 2340 -2332 2341
		mu 0 4 809 720 741 785
		f 4 -2314 2342 2343 2344
		mu 0 4 795 714 747 726
		f 4 2345 2346 -2342 -2323
		mu 0 4 700 687 809 785
		f 4 2347 2348 -2330 -2341
		mu 0 4 720 810 744 741
		f 4 2349 -2343 -2329 -2349
		mu 0 4 810 1513 1514 744
		f 4 -2336 2350 -2346 -2326
		mu 0 4 770 1515 687 700
		f 4 -2345 2351 -2333 -2316
		mu 0 4 795 726 754 745
		f 4 2352 -2130 2353 2354
		mu 0 4 721 757 692 724
		f 4 -2272 2355 2356 2357
		mu 0 4 783 766 833 807
		f 4 2358 2359 -2111 2360
		mu 0 4 764 694 812 824
		f 4 -2276 -2360 2361 -2356
		mu 0 4 1516 812 694 1517
		f 4 2362 2363 2364 -2254
		mu 0 4 763 750 704 803
		f 4 2365 2366 2367 -2264
		mu 0 4 710 780 794 846
		f 4 -2236 2368 2369 -2354
		mu 0 4 1518 830 759 1519
		f 4 -2353 2370 -2363 -2237
		mu 0 4 757 721 750 763
		f 4 2371 2372 -2248 -2368
		mu 0 4 794 773 796 846
		f 4 2373 -2369 -2138 -2373
		mu 0 4 773 759 830 796
		f 4 -2361 -2260 -2365 2374
		mu 0 4 764 824 803 704
		f 4 -2358 2375 -2366 -2185
		mu 0 4 783 807 780 710
		f 4 2376 2377 2378 -2146
		mu 0 4 774 860 861 756
		f 4 -2378 2379 -2143 2380
		mu 0 4 861 860 738 746
		f 4 2381 2382 2383 -2155
		mu 0 4 840 859 862 831
		f 4 -2383 2384 -2158 2385
		mu 0 4 862 859 821 788
		f 4 -2379 2386 -2386 -2152
		mu 0 4 756 861 862 788
		f 4 -2387 -2381 -2150 -2384
		mu 0 4 862 861 746 831
		f 4 -2131 2387 2388 2389
		mu 0 4 749 727 858 863
		f 4 -2389 2390 -2242 2391
		mu 0 4 863 858 838 715
		f 4 2392 2393 -2392 -2250
		mu 0 4 695 864 863 715
		f 4 -2394 2394 -2256 -2390
		mu 0 4 863 864 736 749
		f 4 2395 2396 2397 -2104
		mu 0 4 713 857 865 709
		f 4 -2397 2398 -2114 2399
		mu 0 4 865 857 742 828
		f 4 2400 -2400 -2263 -2395
		mu 0 4 864 865 828 736
		f 4 -2401 -2393 -2265 -2398
		mu 0 4 865 864 695 709
		f 4 2401 -2200 2402 2403
		mu 0 4 868 852 690 869
		f 4 2404 -2193 2405 2406
		mu 0 4 870 823 800 869
		f 4 -2282 2407 2408 2409
		mu 0 4 732 792 854 867
		f 4 2410 -2286 2411 -2409
		mu 0 4 854 834 719 867
		f 4 -2279 2412 2413 -2412
		mu 0 4 719 849 866 867
		f 4 -2290 -2410 -2414 2414
		mu 0 4 761 732 867 866
		f 4 -2289 2415 2416 -2413
		mu 0 4 849 716 853 866
		f 4 2417 -2284 -2415 -2417
		mu 0 4 853 848 761 866
		f 4 2418 2419 2420 2421
		mu 0 4 890 888 886 889
		f 4 2422 2423 2424 -2421
		mu 0 4 886 872 875 889
		f 4 2425 2426 2427 2428
		mu 0 4 876 879 891 892
		f 4 2429 2430 -2426 2431
		mu 0 4 882 881 879 876
		f 4 2432 2433 2434 -2420
		mu 0 4 888 885 887 886
		f 4 2435 2436 -2427 2437
		mu 0 4 878 893 891 879
		f 4 2438 -2438 -2431 2439
		mu 0 4 901 878 879 881
		f 4 -2435 2440 2441 -2423
		mu 0 4 886 887 871 872
		f 4 2442 2443 2444 -2434
		mu 0 4 885 909 906 887
		f 4 2445 2446 -2441 -2445
		mu 0 4 906 899 871 887
		f 4 2447 2448 -2439 2449
		mu 0 4 902 910 878 901
		f 4 2450 2451 -2436 -2449
		mu 0 4 910 913 893 878
		f 4 2452 2453 -2446 2454
		mu 0 4 907 897 899 906
		f 4 2455 2456 -2448 2457
		mu 0 4 905 912 910 902
		f 4 2458 2459 -2455 -2444
		mu 0 4 909 908 907 906
		f 4 2460 2461 -2451 -2457
		mu 0 4 912 911 913 910
		f 4 2462 2463 2464 -2447
		mu 0 4 899 895 898 871
		f 4 2465 2466 2467 -2430
		mu 0 4 882 880 877 881
		f 4 2468 2469 2470 -2458
		mu 0 4 902 900 903 905
		f 4 2471 -2469 -2450 2472
		mu 0 4 904 900 902 901
		f 4 2473 2474 2475 -2424
		mu 0 4 872 873 874 875
		f 4 2476 2477 -2463 -2454
		mu 0 4 897 896 895 899
		f 4 2478 -2473 -2440 -2468
		mu 0 4 877 904 901 881
		f 4 -2465 2479 -2474 -2442
		mu 0 4 871 898 873 872
		f 4 -2433 2480 2481 2482
		mu 0 4 885 888 883 894
		f 4 2483 -2482 2484 -2437
		mu 0 4 893 894 883 891
		f 4 2485 2486 -2428 -2485
		mu 0 4 883 884 892 891
		f 4 -2481 -2419 2487 -2486
		mu 0 4 883 888 890 884
		f 4 2488 -2484 -2452 2489
		mu 0 4 915 894 893 913
		f 4 2490 -2443 -2483 -2489
		mu 0 4 915 909 885 894
		f 4 -2462 2491 2492 -2490
		mu 0 4 913 911 914 915
		f 4 2493 -2459 -2491 -2493
		mu 0 4 914 908 909 915
		f 4 2494 2495 2496 2497
		mu 0 4 739 711 691 842
		f 4 2498 2499 -2294 2500
		mu 0 4 1520 697 712 843
		f 4 -2194 2501 2502 2503
		mu 0 4 706 823 822 1521
		f 4 -2504 -2495 -2501 -2190
		mu 0 4 706 1522 1523 843
		f 4 -2496 -2503 2504 2505
		mu 0 4 691 711 822 693
		f 4 2506 2507 -2499 -2498
		mu 0 4 842 743 1524 739
		f 4 -2206 2508 2509 2510
		mu 0 4 1525 786 683 813
		f 4 -2204 -2511 2511 2512
		mu 0 4 1526 1527 813 752
		f 4 2513 -2298 -2513 2514
		mu 0 4 725 1528 1529 752
		f 4 2515 -2199 2516 -2506
		mu 0 4 693 690 705 691
		f 4 -2296 2517 -2515 2518
		mu 0 4 819 708 725 752
		f 4 -2188 -2519 -2512 2519
		mu 0 4 707 819 752 813
		f 4 -2517 -2196 2520 -2497
		mu 0 4 691 705 681 842
		f 4 -2516 2521 -2407 -2403
		mu 0 4 690 693 870 869
		f 4 2522 2523 -2404 -2406
		mu 0 4 800 683 868 869
		f 4 -2292 2524 -2507 -2521
		mu 0 4 681 772 743 842
		f 4 -2523 -2192 -2520 -2510
		mu 0 4 683 800 707 813
		f 4 -2382 -2232 -2230 -2385
		mu 0 4 859 840 856 821
		f 4 -2227 -2380 -2377 -2229
		mu 0 4 855 738 860 774
		f 4 -2273 -2269 -2399 -2396
		mu 0 4 713 730 742 857
		f 4 -2233 -2121 -2391 -2388
		mu 0 4 727 689 838 858
		f 4 2525 2526 -2456 2527
		mu 0 4 1079 1078 912 905
		f 4 -2453 2528 -2526 2529
		mu 0 4 897 907 1530 1531
		f 4 2530 2531 -2529 -2460
		mu 0 4 908 1076 1532 907
		f 4 -2527 -2532 2532 -2461
		mu 0 4 912 1078 1076 911
		f 4 -2471 2533 2534 -2528
		mu 0 4 905 903 1077 1079
		f 4 -2535 2535 -2477 -2530
		mu 0 4 1533 1534 896 897
		f 4 -2531 -2494 -2492 -2533
		mu 0 4 1076 908 914 911
		f 4 2536 2537 -2422 2538
		mu 0 4 1081 1080 890 889
		f 4 2539 -2537 2540 -2429
		mu 0 4 892 1080 1535 876
		f 4 2541 2542 -2539 -2425
		mu 0 4 875 1082 1081 889
		f 4 -2543 2543 -2432 -2541
		mu 0 4 1536 1537 882 876
		f 4 2544 2545 -2542 -2476
		mu 0 4 874 1083 1082 875
		f 4 -2546 2546 -2466 -2544
		mu 0 4 1538 1539 880 882
		f 4 -2538 -2540 -2487 -2488
		mu 0 4 890 1080 892 884
		f 4 2547 2548 2549 -2225
		mu 0 4 799 1085 1087 718
		f 4 -2549 2550 -2221 2551
		mu 0 4 1540 1541 748 753
		f 4 -2550 2552 2553 -2288
		mu 0 4 718 1087 1086 716
		f 4 -2553 -2552 -2285 2554
		mu 0 4 1086 1542 753 848
		f 4 2555 2556 -2548 -2311
		mu 0 4 851 1084 1085 799
		f 4 -2557 2557 -2307 -2551
		mu 0 4 1543 1544 850 748
		f 4 2558 -2209 2559 2560
		mu 0 4 1088 775 801 1091
		f 4 -2213 2561 -2561 2562
		mu 0 4 776 702 1545 1546
		f 4 -2563 2563 2564 -2281
		mu 0 4 776 1547 1090 792
		f 4 -2560 -2287 2565 -2564
		mu 0 4 1091 801 834 1090
		f 4 2566 2567 -2304 -2559
		mu 0 4 1088 1089 769 775
		f 4 2568 -2567 -2562 -2300
		mu 0 4 768 1548 1549 702
		f 4 -2565 -2566 -2411 -2408
		mu 0 4 792 1090 834 854
		f 4 -2555 -2418 -2416 -2554
		mu 0 4 1086 848 853 716
		f 4 -2524 -2509 -2205 -2402
		mu 0 4 868 683 786 852
		f 4 -2502 -2405 -2522 -2505
		mu 0 4 822 823 870 693
		f 4 -1057 2569 -526 2570
		mu 0 4 1550 1551 1552 1553
		f 4 -1055 2571 -522 -2570
		mu 0 4 1554 1555 1556 1557
		f 4 -1052 2572 -520 -2572
		mu 0 4 1558 1559 1560 1561
		f 4 -517 -2573 -1050 2573
		mu 0 4 1562 1563 1564 1565
		f 3 -531 -2574 -1063
		mu 0 3 1566 1567 1568;
	setAttr ".cd" -type "dataPolyComponent" Index_Data Edge 0 ;
	setAttr ".cvd" -type "dataPolyComponent" Index_Data Vertex 0 ;
	setAttr ".hfd" -type "dataPolyComponent" Index_Data Face 0 ;
createNode transform -n "tongue" -p "headTopologyTeeth";
createNode mesh -n "tongueShape" -p "tongue";
	setAttr -k off ".v";
	setAttr -s 12 ".iog[0].og";
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".dr" 1;
createNode mesh -n "tongueShapeOrig1" -p "tongue";
	setAttr -k off ".v";
	setAttr ".io" yes;
	setAttr ".vir" yes;
	setAttr ".vif" yes;
	setAttr ".uvst[0].uvsn" -type "string" "map1";
	setAttr -s 410 ".uvst[0].uvsp";
	setAttr ".uvst[0].uvsp[0:249]" -type "float2" 0.53324199 0.313131 0.515221
		 0.31187299 0.51855397 0.297001 0.51831102 0.327739 0.57103801 0.32763299 0.58481902
		 0.32043201 0.59939301 0.33344701 0.51557201 0.22443099 0.57351899 0.350099 0.57585597
		 0.363296 0.56854999 0.33691701 0.57169199 0.317229 0.61375302 0.34870401 0.58213902
		 0.34121701 0.58954501 0.35261899 0.51262701 0.191173 0.577393 0.37825 0.62480801
		 0.36691001 0.59627998 0.36614299 0.57815701 0.39448199 0.635333 0.39475101 0.60170102
		 0.38203999 0.51685297 0.172189 0.58124298 0.417867 0.60899597 0.406831 0.64485103
		 0.430444 0.52056998 0.14954901 0.58493501 0.44824499 0.616247 0.43958199 0.65076703
		 0.46754301 0.52307099 0.13109501 0.58651102 0.48112199 0.61996502 0.47477701 0.65108103
		 0.50377202 0.58381599 0.51331002 0.61791402 0.50867498 0.52417099 0.112331 0.64658302
		 0.54409701 0.52452397 0.093125001 0.57664502 0.55178201 0.610861 0.54695201 0.64075702
		 0.57857901 0.57184398 0.589894 0.60571098 0.58081597 0.63445902 0.63741702 0.57956702
		 0.65188402 0.61227697 0.63441002 0.57380003 0.62088102 0.60797501 0.60899901 0.640293
		 0.60799301 0.52460402 0.077067003 0.54251403 0.69842702 0.55883402 0.36112201 0.55647701
		 0.37872899 0.55348599 0.39793599 0.55226398 0.42192301 0.55189002 0.45162299 0.55095798
		 0.483069 0.54825401 0.514238 0.54286301 0.55338901 0.53992897 0.59627002 0.541071
		 0.63067102 0.56064498 0.345559 0.55995601 0.33165899 0.56057799 0.32023701 0.56194502
		 0.30962899 0.56827301 0.29543701 0.55349201 0.175217 0.55598098 0.15008301 0.55654597
		 0.129256 0.55284899 0.107627 0.552338 0.088555001 0.55140102 0.071258999 0.49218601
		 0.066780999 0.49228001 0.080389 0.49229899 0.093859002 0.49233299 0.109093 0.49229199
		 0.126913 0.49226999 0.147053 0.49222001 0.169579 0.49217701 0.19763801 0.49217999
		 0.228026 0.49219501 0.25833401 0.492192 0.281618 0.49217799 0.297746 0.49215999 0.311506
		 0.492156 0.325385 0.492167 0.34424099 0.49217901 0.368168 0.492181 0.39377701 0.49217901
		 0.41953301 0.49217099 0.44921601 0.49216399 0.48068899 0.492163 0.51301003 0.492163
		 0.555641 0.49212399 0.600824 0.491954 0.63709801 0.49219301 0.70598602 0.55072898
		 0.32541701 0.549281 0.31687099 0.55133998 0.30878201 0.54117602 0.29894701 0.54121
		 0.32997301 0.54515702 0.27842101 0.491862 0.66649002 0.54278302 0.660586 0.54264301
		 0.053993002 0.524939 0.062593997 0.499771 0.12706999 0.500597 0.108455 0.498638 0.094366997
		 0.49767601 0.081069998 0.499558 0.146713 0.49713901 0.068342999 0.50243402 0.196375
		 0.50431597 0.171505 0.51204401 0.106999 0.51098001 0.127938 0.51126099 0.092675999
		 0.511199 0.078612 0.50865698 0.148275 0.51530403 0.062329002 0.52541298 0.198689
		 0.53377599 0.173931 0.53834802 0.149968 0.53948897 0.130493 0.53891098 0.074602999
		 0.53948402 0.091297001 0.53932798 0.110038 0.53280902 0.057101 0.52412599 0.25689599
		 0.524782 0.27901301 0.54443997 0.255983 0.54687101 0.20240501 0.54238099 0.229105
		 0.56625402 0.276573 0.56444103 0.25281501 0.56712502 0.17539001 0.565566 0.205174
		 0.56812 0.1489 0.56427002 0.23158 0.57399702 0.128479 0.56922603 0.103445 0.56834602
		 0.068278998 0.56197703 0.047122002 0.57342303 0.083926 0.52239102 0.34991401 0.54037899
		 0.35427701 0.53761399 0.37458599 0.52279198 0.36749101 0.52059603 0.450789 0.51250601
		 0.635607 0.51555902 0.70451301 0.51214999 0.59981602 0.51332903 0.55512899 0.524602
		 0.39380401 0.51635998 0.51375401 0.52264899 0.42114201 0.518628 0.48203099 0.51342499
		 0.66501701 0.496667 0.197386 0.49747401 0.170343 0.50417101 0.32652 0.50268 0.31164101
		 0.50205499 0.227322 0.50774097 0.27994999 0.50742298 0.25742 0.50435299 0.29708499
		 0.50952101 0.36956599 0.50690103 0.34672901 0.45104 0.313122 0.46908301 0.31186101
		 0.46582001 0.29697499 0.46599701 0.32773501 0.413331 0.32768101 0.39955401 0.320434
		 0.38497901 0.33344901 0.46878999 0.22449 0.41084501 0.35014701 0.40850499 0.36333901
		 0.415813 0.336979 0.412682 0.31725401 0.370617 0.348708 0.40222701 0.34125 0.39482
		 0.352651 0.47174001 0.19129901 0.40696901 0.378295 0.359566 0.366914 0.38808501 0.36617199
		 0.40620199 0.394526 0.34904301 0.39475501 0.382664 0.38206899 0.46756199 0.172434
		 0.40311199 0.41791299 0.375368 0.40686199 0.33952501 0.43044701 0.46390599 0.150004
		 0.399414 0.44829699 0.36811501 0.439619 0.333608 0.46754801 0.461375 0.13183101 0.39782599
		 0.48119399 0.36439401 0.474828 0.333294 0.50377703 0.400498 0.51341403 0.366429 0.50876302
		 0.46016201 0.113593 0.33779401 0.54410303 0.460051 0.094985001 0.407671 0.55193001
		 0.37344599 0.54713899 0.34362301 0.57858503 0.41255 0.59006 0.37852001 0.58112699
		 0.34992 0.63742501 0.404183 0.65202701 0.371885 0.63445097 0.41004899 0.62131703
		 0.376331 0.60914999 0.344087 0.60799903 0.460044 0.079322003 0.44187 0.69843102 0.425529
		 0.36117399 0.42788401 0.378775 0.43087199 0.39797699 0.43208799 0.42196301 0.43244901
		 0.45166799 0.43336499 0.48311701 0.43605599 0.51428503 0.441461 0.55340302 0.44439101
		 0.59622198 0.44298601 0.63044202 0.42371601 0.34562501 0.42440301 0.33175001 0.42379501
		 0.320306 0.422438 0.30964801 0.41610101 0.29543701 0.43086299 0.17557301 0.42837301
		 0.150699 0.42773899 0.13023899 0.431247 0.109137 0.43183899 0.090993002 0.432798
		 0.075028002 0.43362099 0.325526 0.435076 0.316989 0.43305001 0.30880401 0.44326001
		 0.298897 0.44313401 0.330037 0.439253 0.278404 0.44062099 0.66056001;
	setAttr ".uvst[0].uvsp[250:409]" 0.44074401 0.061409 0.45924199 0.064838998
		 0.48479101 0.127257 0.484038 0.108784 0.48597401 0.094656996 0.48689899 0.081361003
		 0.48497599 0.146846 0.487239 0.068507001 0.48192099 0.19642501 0.48011199 0.171629
		 0.47233799 0.10792 0.473515 0.128429 0.473427 0.093910001 0.47341999 0.079920001
		 0.47585699 0.14855801 0.468766 0.063997999 0.45894799 0.198851 0.45061299 0.17426801
		 0.44606799 0.150565 0.44487801 0.13146199 0.44567901 0.078249998 0.44492301 0.093942001
		 0.444819 0.111896 0.45091501 0.061124001 0.46027699 0.25690401 0.45962599 0.27899101
		 0.43996099 0.256006 0.43748599 0.202592 0.44197601 0.229201 0.418125 0.27657601 0.419862
		 0.25287101 0.417216 0.175696 0.41879201 0.205331 0.416197 0.149443 0.420127 0.231657
		 0.410299 0.12911201 0.41481301 0.104464 0.41563001 0.069944002 0.421864 0.049871001
		 0.41059101 0.084976003 0.46195999 0.34993899 0.44398201 0.35432401 0.446749 0.37462401
		 0.46156999 0.36751699 0.46374401 0.450809 0.47143099 0.63551801 0.46882501 0.70451301
		 0.472123 0.59974998 0.47099599 0.555116 0.45975599 0.39382699 0.46796301 0.51376802
		 0.461705 0.42116001 0.4657 0.48205301 0.470218 0.66498703 0.48768699 0.19740801 0.48696399
		 0.17040201 0.48013899 0.32652399 0.48163599 0.31163499 0.482306 0.22734299 0.476652
		 0.27993801 0.47697201 0.25742599 0.48000601 0.29706901 0.47483999 0.36957699 0.47743899
		 0.34673601 0.568102 0.68470502 0.58622903 0.66745001 0.568102 0.68470502 0.58622903
		 0.66745001 0.618797 0.66516 0.53682703 0.032044001 0.569341 0.012764 0.53682703 0.032044001
		 0.58295202 0.249603 0.58168101 0.26847401 0.58168101 0.26847401 0.58096802 0.28107801
		 0.59096903 0.176357 0.58756202 0.205586 0.59379101 0.149167 0.59096903 0.176357 0.58756202
		 0.205586 0.58435303 0.230616 0.58435303 0.230616 0.58295202 0.249603 0.59701502 0.125532
		 0.59379101 0.149167 0.59943902 0.099114001 0.59701502 0.125532 0.57250702 0.042364001
		 0.58507103 0.063533001 0.59943902 0.099114001 0.59684902 0.078395002 0.59684902 0.078395002
		 0.58507103 0.063533001 0.569341 0.012764 0.57250702 0.042364001 0.41628301 0.68471003
		 0.41628301 0.68471003 0.398155 0.667454 0.398155 0.667454 0.36558801 0.66516501 0.447548
		 0.032044001 0.41503501 0.012764 0.447548 0.032044001 0.40269101 0.26847601 0.40141901
		 0.249605 0.40340501 0.28108001 0.40269101 0.26847601 0.396806 0.205586 0.39340001
		 0.176357 0.39340001 0.176357 0.39058101 0.149166 0.40001601 0.230618 0.396806 0.205586
		 0.40141901 0.249605 0.40001601 0.230618 0.38735801 0.12553 0.39058101 0.149166 0.38735801
		 0.12553 0.384938 0.099109001 0.41186801 0.042364001 0.399304 0.063533001 0.384938
		 0.099109001 0.38752601 0.078395002 0.38752601 0.078395002 0.399304 0.063533001 0.41186801
		 0.042364001 0.41503501 0.012764 0.044983733 0 0.18660232 0 0.049198378 0.98504013
		 0 1.000000119209 0.029486135 4.1321046e-016 0.20386121 0 0.096375383 0.92577273 0
		 1 0.68487585 0.066152111 1 0.066152111 0.0038479271 0.23020147 0 0 0 0 0.33719614
		 0 0.69930577 0.17250594 1 0.56238151 0 0 0.1416184 0 0.18660221 1 0.13740386 0.98504049
		 0 0 0.17437522 0 0.20386277 1 0.10748663 0.92577326 0 0 0.21641028 0 0.11967741 1
		 0.11479355 0.7327522 0.66280359 0 0.99999994 0 0 0.56238055 0.30069423 0.1725052;
	setAttr ".cuvs" -type "string" "map1";
	setAttr ".dcc" -type "string" "Ambient+Diffuse";
	setAttr ".covm[0]"  0 1 1;
	setAttr ".cdvm[0]"  0 1 1;
	setAttr -s 322 ".vt";
	setAttr ".vt[0:165]"  0.12850949 -0.050131202 0.30859813 0.074721649 -0.043705344 0.33566478
		 0.079657674 -0.088312507 0.31970716 0.078598097 -0.015294433 0.29473841 0.21141687 -0.057888389 0.22180751
		 0.22001654 -0.092131019 0.20815569 0.22688484 -0.10540807 0.18269041 0.058499202 -0.17296636 0.11213158
		 0.21940872 -0.028154731 0.17966565 0.22499517 -0.016709685 0.14974876 0.20838711 -0.038355231 0.21112409
		 0.20834222 -0.078666091 0.23012084 0.23277162 -0.12118757 0.14349313 0.23039941 -0.0591892 0.18550281
		 0.24097157 -0.060523391 0.15954523 0.048296027 -0.19422567 0.021180451 0.22947231 -0.0043224096 0.11292849
		 0.23721811 -0.12752759 0.10047352 0.25048426 -0.061342597 0.12709333 0.2334953 0.010833383 0.073385656
		 0.24450029 -0.12616193 0.034129143 0.25963962 -0.057672858 0.090464354 0.059227683 -0.20313966 -0.034615397
		 0.24640912 0.026342034 0.010941356 0.27401248 -0.053739905 0.028211564 0.25054342 -0.12371576 -0.048593372
		 0.065275334 -0.2158035 -0.099829376 0.26260313 0.036454797 -0.075735956 0.28912091 -0.052072883 -0.056386501
		 0.25324187 -0.12475049 -0.12814263 0.070719838 -0.22101057 -0.15036273 0.27542225 0.039342523 -0.17280343
		 0.30000529 -0.052656531 -0.14579797 0.25494948 -0.12818182 -0.19895691 0.27895325 0.035089135 -0.26937163
		 0.30541664 -0.054556251 -0.23095021 0.075731561 -0.23255956 -0.20140696 0.25563079 -0.14341581 -0.27782685
		 0.079014376 -0.23808229 -0.25417811 0.26786554 0.0068584681 -0.39143667 0.30417973 -0.070658088 -0.33524308
		 0.24473934 -0.16860425 -0.33553839 0.24812837 -0.057888389 -0.49676099 0.28880638 -0.11541212 -0.41575021
		 0.17800468 -0.25774038 -0.40839705 0.18179116 -0.22310102 -0.56920731 0.20540467 -0.24172628 -0.48387983
		 0.21862586 -0.14074075 -0.53972542 0.25278309 -0.17913663 -0.44709975 0.21571192 -0.20971429 -0.36588573
		 0.075530529 -0.2573837 -0.29238418 0.084974006 -0.26652503 -0.59485865 0.18355557 0.0020653009 0.16435601
		 0.17655241 0.020650506 0.11874676 0.17043856 0.040925622 0.067534298 0.17193808 0.064633012 0.00026282668
		 0.17680548 0.082324624 -0.090366513 0.18038161 0.087596536 -0.1895718 0.17841387 0.077985406 -0.28576672
		 0.1662263 0.041511178 -0.40688047 0.15170911 -0.030851722 -0.52236247 0.13468722 -0.11912763 -0.57478404
		 0.1909017 -0.015546203 0.20160669 0.19077161 -0.034284949 0.23307511 0.19178148 -0.055906653 0.24950787
		 0.19180506 -0.080076575 0.25429001 0.19333066 -0.10527456 0.24079463 0.15631868 -0.16878545 -0.04166624
		 0.15677992 -0.17098558 -0.11168361 0.15304074 -0.17622411 -0.16781741 0.14125521 -0.19696653 -0.22638038
		 0.13417867 -0.21159112 -0.27832615 0.13060026 -0.2325958 -0.32273349 1.8139232e-008 -0.26836807 -0.3255496
		 0 -0.23688161 -0.28597969 0 -0.21054113 -0.26366442 0 -0.20512235 -0.22129807 0 -0.18314207 -0.16932181
		 0 -0.18409193 -0.10788271 0 -0.18062246 -0.041034758 0 -0.17943799 0.038909137 0 -0.17290723 0.12633657
		 0 -0.14558446 0.21929297 0 -0.11511648 0.29290786 0 -0.082600951 0.33662757 0 -0.03969419 0.34929273
		 0 -0.013859153 0.31390554 0 0.000867486 0.24839833 0 0.017209649 0.16437007 0 0.039664865 0.082640737
		 0 0.064859986 0.0043996274 0 0.084817529 -0.088602245 0 0.090482354 -0.18785712 0 0.079185128 -0.28380588
		 0 0.041177392 -0.40749356 0 -0.032217383 -0.51861 0 -0.1199888 -0.5721302 1.4494775e-008 -0.26846075 -0.59614491
		 0.17012636 -0.035869956 0.25816151 0.16766664 -0.052213073 0.2729201 0.17095183 -0.073625922 0.27488104
		 0.14316384 -0.093434691 0.29079819 0.14508668 -0.021321654 0.26230803 0.14287539 -0.13056409 0.24304873
		 0.12206633 -0.27744836 -0.46668538 0.1354049 -0.25471023 -0.58010864 0 -0.19570291 -0.59913778
		 0.11842702 -0.20069253 -0.60279143 0.12652747 -0.27052534 -0.37427697 0.065836735 -0.29609603 -0.41340858
		 0.068191484 -0.28030622 -0.3309575 0.14103223 -0.25932419 -0.52613181 0.018757869 -0.18476903 -0.16946381
		 0.013580592 -0.21696031 -0.22051993 0.014765523 -0.21331632 -0.26072454 0.010565048 -0.23529375 -0.28720248
		 0.019956999 -0.18469465 -0.10840791 0.013060086 -0.26675662 -0.33019319 0.020600315 -0.18790853 0.036101758
		 0.02674965 -0.1940788 -0.034850001 0.045822084 -0.22421396 -0.22011754 0.042156145 -0.20941865 -0.16036281
		 0.047766246 -0.22760618 -0.25785565 0.044485763 -0.24789274 -0.28713119 0.037981659 -0.20267713 -0.1027576
		 0.040302269 -0.2736088 -0.34855777 0.084179848 -0.18527257 0.035990745 0.10405883 -0.19032896 -0.036812723
		 0.11207657 -0.1958164 -0.10539472 0.11195125 -0.2015146 -0.15764496 0.10929991 -0.24911344 -0.30719733
		 0.11633617 -0.23133314 -0.26348495 0.11592457 -0.21757734 -0.21241683 0.093900502 -0.2828306 -0.35836205
		 0.083477378 -0.15199888 0.20467219 0.089600697 -0.12525785 0.26653445 0.13784707 -0.15430677 0.1873593
		 0.14243068 -0.17246282 0.037553877 0.13152033 -0.16835248 0.11420557 0.19240125 -0.12624204 0.21085197
		 0.1909561 -0.14721143 0.16362771 0.19367597 -0.15463293 -0.044362485 0.19252416 -0.1575588 0.036234409
		 0.19287434 -0.15640104 -0.11756337 0.1900882 -0.15734899 0.10760453 0.19574311 -0.15875852 -0.17982522
		 0.18790507 -0.17755926 -0.24552175 0.17190737 -0.22104871 -0.33848083 0.1568225 -0.25579965 -0.37197599
		 0.1851213 -0.19250047 -0.30080903 0.084151447 0.0036064386 0.2187632 0.13463992 0.00418818 0.1934258
		 0.12543251 0.023177743 0.1340822 0.083465531 0.017728448 0.16082706 0.083302334 0.089862466 -0.091735929
		 0.054121345 -0.11724699 -0.57584822 0.038225118 -0.26727396 -0.59786558 0.060701139 -0.028910041 -0.52195132
		 0.066954553 0.045117021 -0.40910831 0.089922316 0.041391969 0.07942149 0.075367317 0.083723664 -0.28655422
		 0.086594597 0.069686532 0.00074085593 0.08002191 0.095349908 -0.19099832 0.048778504 -0.19443929 -0.60304689
		 0.0085073793 -0.1828903 0.038431585 0.011984134 -0.18488729 -0.038307697;
	setAttr ".vt[166:321]" 0.036962308 -0.013994575 0.30756709 0.035119865 -0.040628791 0.34528393
		 0.025127158 -0.17283285 0.12340598 0.043721862 -0.11912763 0.28265741 0.040219098 -0.14839017 0.21391478
		 0.037806656 -0.084379554 0.33112845 0.046231255 0.018795609 0.15816626 0.040938087 0.0019813776 0.23677406
		 -0.12850949 -0.050131202 0.30859813 -0.074721649 -0.043705344 0.33566478 -0.079657674 -0.088312507 0.31970716
		 -0.078598097 -0.015294433 0.29473841 -0.21141687 -0.057888389 0.22180751 -0.22001654 -0.092131019 0.20815569
		 -0.22688484 -0.10540807 0.18269041 -0.058499202 -0.17296636 0.11213158 -0.21940872 -0.028154731 0.17966565
		 -0.22499517 -0.016709685 0.14974876 -0.20838711 -0.038355231 0.21112409 -0.20834222 -0.078703284 0.23012084
		 -0.23277162 -0.12118757 0.14349313 -0.23039941 -0.0591892 0.18550281 -0.24097157 -0.060523391 0.15954523
		 -0.048296027 -0.19422567 0.021180451 -0.22947231 -0.0043224096 0.11292849 -0.23721811 -0.12752759 0.10047352
		 -0.25048426 -0.061342597 0.12709333 -0.2334953 0.010833383 0.073385656 -0.24450029 -0.12616193 0.034129143
		 -0.25963962 -0.057672858 0.090464354 -0.059227683 -0.20313966 -0.034615397 -0.24640912 0.026342034 0.010941356
		 -0.27401248 -0.053739905 0.028211564 -0.25054342 -0.12371576 -0.048593372 -0.065275334 -0.2158035 -0.099829376
		 -0.26260313 0.036454797 -0.075735956 -0.28912091 -0.052072883 -0.056386501 -0.25324187 -0.12475049 -0.12814263
		 -0.070719838 -0.22101057 -0.15036273 -0.27542225 0.039342523 -0.17280343 -0.30000529 -0.052656531 -0.14579797
		 -0.25494948 -0.12818182 -0.19895691 -0.27895325 0.035089135 -0.26937163 -0.30541664 -0.054556251 -0.23095021
		 -0.075731561 -0.23255956 -0.20140696 -0.25563079 -0.14341581 -0.27782685 -0.079014376 -0.23808229 -0.25417811
		 -0.26786554 0.0068241358 -0.39143667 -0.30417973 -0.070658088 -0.33524308 -0.24473934 -0.16860425 -0.33553839
		 -0.24812837 -0.057888389 -0.49676099 -0.28880638 -0.11541212 -0.41575021 -0.17800468 -0.25774038 -0.40839705
		 -0.18179116 -0.22310102 -0.56920731 -0.20540467 -0.24172628 -0.48387983 -0.21862586 -0.14074075 -0.53972542
		 -0.25278309 -0.17913663 -0.44709975 -0.21571192 -0.20971429 -0.36588573 -0.075530529 -0.2573837 -0.29238418
		 -0.084973961 -0.26652479 -0.59485877 -0.18355557 0.0020653009 0.16435601 -0.17655241 0.020650506 0.11874676
		 -0.17043856 0.040925622 0.067534298 -0.17193808 0.064633012 0.00026282668 -0.17680548 0.082324624 -0.090366513
		 -0.18038161 0.087596536 -0.1895718 -0.17841387 0.077985406 -0.28576672 -0.1662263 0.041511178 -0.40688047
		 -0.15170911 -0.030851722 -0.52236247 -0.13468707 -0.11912572 -0.57478392 -0.19090155 -0.015544295 0.20160675
		 -0.19077146 -0.034283042 0.23307517 -0.19178133 -0.055904746 0.24950793 -0.19180492 -0.080074668 0.25429007
		 -0.19333051 -0.10527265 0.24079469 -0.15631853 -0.16878355 -0.041666158 -0.1567798 -0.17098367 -0.11168361
		 -0.15304059 -0.17622221 -0.16781732 -0.14125507 -0.19696462 -0.22638029 -0.13417852 -0.21158922 -0.27832603
		 -0.13060011 -0.23255765 -0.32273343 -0.17012621 -0.035868049 0.25816157 -0.16766649 -0.052211165 0.27292016
		 -0.17095168 -0.073624015 0.27488109 -0.14316373 -0.093432784 0.29079819 -0.14508653 -0.021319747 0.26230809
		 -0.14287524 -0.13056219 0.24304879 -0.12206621 -0.27744704 -0.46668532 -0.13540478 -0.25470904 -0.58010852
		 -0.11842686 -0.20069063 -0.60279131 -0.12652732 -0.27052343 -0.37427691 -0.065836616 -0.29609463 -0.41340852
		 -0.06819132 -0.28030431 -0.33095744 -0.14103213 -0.25932312 -0.52613175 -0.018757712 -0.18476713 -0.16946372
		 -0.013580464 -0.2169584 -0.22051993 -0.014765365 -0.21331441 -0.26072443 -0.01056489 -0.23529184 -0.28720239
		 -0.019956842 -0.18469274 -0.10840783 -0.01305996 -0.26675522 -0.3301931 -0.020600159 -0.18790662 0.036101848
		 -0.026749492 -0.1940769 -0.034849919 -0.045821927 -0.22421205 -0.22011745 -0.042155989 -0.20941675 -0.16036272
		 -0.047766089 -0.22760427 -0.25785556 -0.044485636 -0.24789083 -0.28712925 -0.037981503 -0.20267522 -0.10275752
		 -0.040302146 -0.27360728 -0.34855756 -0.084179685 -0.18527067 0.035990834 -0.10405867 -0.19032705 -0.036812641
		 -0.1120764 -0.19581449 -0.10539464 -0.11195108 -0.20151269 -0.15764487 -0.10929975 -0.24911153 -0.30719727
		 -0.116336 -0.23133159 -0.2634849 -0.1159244 -0.21757579 -0.21241674 -0.093900338 -0.28282905 -0.35836199
		 -0.083477214 -0.15199697 0.20467225 -0.089600533 -0.12525594 0.26653451 -0.13784692 -0.15430486 0.18735939
		 -0.14243056 -0.17246091 0.037553877 -0.13152018 -0.16835105 0.11420566 -0.1924011 -0.12624013 0.21085203
		 -0.19095595 -0.14720953 0.1636278 -0.19367582 -0.15463102 -0.044362403 -0.19252402 -0.15755689 0.036234498
		 -0.19287419 -0.15639913 -0.11756328 -0.19008805 -0.15734708 0.10760462 -0.19574296 -0.15875661 -0.17982513
		 -0.18790492 -0.17755735 -0.24552166 -0.17190722 -0.22104681 -0.33848077 -0.15682235 -0.25579774 -0.37197593
		 -0.18512115 -0.19249856 -0.30080894 -0.084151283 0.003608346 0.21876326 -0.13463977 0.0041900873 0.19342586
		 -0.12543236 0.02317965 0.13408229 -0.083465397 0.017730355 0.16082709 -0.0833022 0.089864373 -0.091735937
		 -0.054121219 -0.11724508 -0.57584822 -0.038224995 -0.26727262 -0.59786558 -0.060701013 -0.028908134 -0.52195132
		 -0.066954419 0.045118928 -0.40910831 -0.089922182 0.041393876 0.07942149 -0.075367182 0.083725095 -0.28655663
		 -0.086594462 0.069687963 0.00074084848 -0.080021776 0.095351815 -0.19099832 -0.048778377 -0.19443762 -0.60304689
		 -0.0085072508 -0.18288863 0.038431555 -0.011984006 -0.18488562 -0.038307734 -0.036962181 -0.013992906 0.30756703
		 -0.035119738 -0.040626884 0.34528393 -0.025127031 -0.17279279 0.12340598 -0.043721735 -0.11912572 0.28265741
		 -0.040218972 -0.14838827 0.21391478 -0.0378065 -0.084377646 0.33112851 -0.046231098 0.018797517 0.15816635
		 -0.04093793 0.001983285 0.23677412;
	setAttr -s 640 ".ed";
	setAttr ".ed[0:165]"  104 109 1 111 104 1 105 111 1 257 253 1 253 259 1 259 254 1
		 16 53 1 53 52 1 52 9 1 9 16 1 18 16 1 9 14 1 14 18 1 12 18 1 14 6 1 6 12 1 52 62 1
		 62 8 1 8 9 1 8 13 1 13 14 1 5 6 1 13 5 1 27 56 1 56 55 1 55 23 1 23 27 1 31 57 1
		 57 56 1 27 31 1 28 27 1 23 24 1 24 28 1 32 31 1 28 32 1 24 20 1 20 25 1 25 28 1 29 32 1
		 25 29 1 118 119 1 119 22 1 22 15 1 15 118 1 119 124 1 124 26 1 26 22 1 22 127 1 127 126 1
		 126 15 1 26 128 1 128 127 1 63 64 1 64 4 1 4 10 1 10 63 1 11 4 1 64 65 1 65 11 1
		 13 4 1 11 5 1 8 10 1 62 63 1 99 64 1 63 98 1 98 99 1 99 100 1 100 65 1 101 66 1 66 65 1
		 100 101 1 99 0 1 0 101 1 2 101 1 0 1 1 1 2 1 98 102 1 102 0 1 62 102 1 102 3 1 3 1 1
		 55 54 1 54 19 1 19 23 1 54 53 1 16 19 1 19 21 1 21 24 1 18 21 1 21 17 1 17 20 1 12 17 1
		 126 7 1 7 118 1 34 58 1 58 57 1 31 34 1 59 58 1 34 39 1 39 59 1 35 34 1 32 35 1 35 40 1
		 40 39 1 29 33 1 33 35 1 37 40 1 33 37 1 113 112 1 112 77 1 77 76 1 76 113 1 112 116 1
		 116 78 1 78 77 1 113 120 1 120 121 1 121 112 1 120 36 1 36 30 1 30 121 1 121 124 1
		 124 116 1 30 26 1 129 128 1 30 129 1 69 68 1 68 128 1 129 69 1 132 129 1 36 132 1
		 70 69 1 132 70 1 61 47 1 47 45 1 45 107 1 107 61 1 47 48 1 48 46 1 46 45 1 48 49 1
		 49 44 1 44 46 1 117 115 1 115 74 1 74 73 1 73 117 1 125 123 1 123 115 1 117 125 1
		 110 50 1 50 123 1 125 110 1 72 130 1 130 133 1 133 108 1 108 72 1 130 50 1 110 133 1
		 60 59 1 39 42 1 42 60 1 40 43 1 43 42 1 41 43 1 37 41 1;
	setAttr ".ed[166:331]" 114 113 1 76 75 1 75 114 1 122 120 1 114 122 1 38 36 1
		 122 38 1 38 131 1 131 132 1 131 71 1 71 70 1 61 60 1 42 47 1 43 48 1 41 49 1 115 114 1
		 75 74 1 123 122 1 50 38 1 130 131 1 72 71 1 51 107 1 45 105 1 105 51 1 46 111 1 44 104 1
		 109 125 1 104 108 1 133 109 1 103 101 1 2 135 1 135 103 1 67 137 1 137 126 1 127 67 1
		 68 67 1 138 7 1 137 138 1 135 134 1 134 136 1 136 103 1 134 7 1 138 136 1 139 140 1
		 140 12 1 6 139 1 66 139 1 5 66 1 141 25 1 20 142 1 142 141 1 143 29 1 141 143 1 68 143 1
		 141 67 1 144 142 1 17 144 1 140 144 1 143 145 1 145 33 1 69 145 1 146 37 1 145 146 1
		 70 146 1 49 147 1 147 148 1 148 44 1 147 72 1 108 148 1 146 149 1 149 41 1 71 149 1
		 149 147 1 142 137 1 144 138 1 140 136 1 139 103 1 152 153 1 153 150 1 150 151 1 151 152 1
		 151 52 1 53 152 1 150 3 1 102 151 1 91 154 1 154 162 1 162 92 1 92 91 1 90 161 1
		 161 154 1 91 90 1 57 162 1 154 56 1 161 55 1 89 159 1 159 161 1 90 89 1 159 152 1
		 54 159 1 162 160 1 160 93 1 93 92 1 160 158 1 158 94 1 94 93 1 58 160 1 59 158 1
		 96 155 1 155 163 1 163 106 1 106 96 1 107 163 1 155 61 1 158 157 1 157 95 1 95 94 1
		 60 157 1 157 155 1 96 95 1 163 156 1 156 97 1 97 106 1 51 156 1 165 164 1 164 80 1
		 80 79 1 79 165 1 166 167 1 167 1 1 3 166 1 166 86 1 86 85 1 85 167 1 168 164 1 164 118 1
		 7 168 1 168 81 1 81 80 1 169 170 1 170 134 1 135 169 1 169 83 1 83 82 1 82 170 1
		 171 169 1 2 171 1 171 84 1 84 83 1 167 171 1 85 84 1 170 168 1 82 81 1 173 172 1
		 172 88 1 88 87 1 87 173 1 173 166 1 150 173 1 87 86 1 165 119 1 165 116 1 79 78 1
		 153 172 1 89 88 1 172 159 1;
	setAttr ".ed[332:497]" 190 183 1 183 226 1 226 227 1 227 190 1 192 188 1 188 183 1
		 190 192 1 186 180 1 180 188 1 192 186 1 183 182 1 182 236 1 236 226 1 188 187 1 187 182 1
		 179 187 1 180 179 1 201 197 1 197 229 1 229 230 1 230 201 1 205 201 1 230 231 1 231 205 1
		 202 198 1 198 197 1 201 202 1 206 202 1 205 206 1 202 199 1 199 194 1 194 198 1 203 199 1
		 206 203 1 266 189 1 189 196 1 196 267 1 267 266 1 196 200 1 200 272 1 272 267 1 189 274 1
		 274 275 1 275 196 1 275 276 1 276 200 1 237 184 1 184 178 1 178 238 1 238 237 1 185 239 1
		 239 238 1 178 185 1 179 185 1 178 187 1 184 182 1 237 236 1 248 247 1 247 237 1 238 248 1
		 239 249 1 249 248 1 250 249 1 239 240 1 240 250 1 250 174 1 174 248 1 176 175 1 175 174 1
		 250 176 1 174 251 1 251 247 1 251 236 1 175 177 1 177 251 1 197 193 1 193 228 1 228 229 1
		 193 190 1 227 228 1 198 195 1 195 193 1 195 192 1 194 191 1 191 195 1 191 186 1 266 181 1
		 181 274 1 208 205 1 231 232 1 232 208 1 233 213 1 213 208 1 232 233 1 209 206 1 208 209 1
		 213 214 1 214 209 1 209 207 1 207 203 1 211 207 1 214 211 1 261 76 1 77 260 1 260 261 1
		 78 264 1 264 260 1 260 269 1 269 268 1 268 261 1 269 204 1 204 210 1 210 268 1 264 272 1
		 272 269 1 200 204 1 277 204 1 276 277 1 243 277 1 276 242 1 242 243 1 280 210 1 277 280 1
		 244 280 1 243 244 1 235 255 1 255 219 1 219 221 1 221 235 1 219 220 1 220 222 1 222 221 1
		 220 218 1 218 223 1 223 222 1 265 73 1 74 263 1 263 265 1 273 265 1 263 271 1 271 273 1
		 258 273 1 271 224 1 224 258 1 246 256 1 256 281 1 281 278 1 278 246 1 281 258 1 224 278 1
		 234 216 1 216 213 1 233 234 1 216 217 1 217 214 1 215 211 1 217 215 1 262 75 1 261 262 1
		 270 262 1 268 270 1 212 270 1 210 212 1 280 279 1 279 212 1 244 245 1;
	setAttr ".ed[498:639]" 245 279 1 221 216 1 234 235 1 222 217 1 223 215 1 262 263 1
		 270 271 1 212 224 1 279 278 1 245 246 1 225 254 1 254 219 1 255 225 1 259 220 1 253 218 1
		 273 257 1 257 281 1 256 253 1 252 283 1 283 176 1 250 252 1 241 275 1 274 285 1 285 241 1
		 241 242 1 286 285 1 181 286 1 252 284 1 284 282 1 282 283 1 284 286 1 181 282 1 287 180 1
		 186 288 1 288 287 1 240 179 1 287 240 1 289 290 1 290 194 1 199 289 1 291 289 1 203 291 1
		 241 289 1 291 242 1 292 191 1 290 292 1 292 288 1 207 293 1 293 291 1 293 243 1 294 293 1
		 211 294 1 294 244 1 218 296 1 296 295 1 295 223 1 296 256 1 246 295 1 215 297 1 297 294 1
		 297 245 1 295 297 1 285 290 1 286 292 1 284 288 1 252 287 1 300 299 1 299 298 1 298 301 1
		 301 300 1 300 227 1 226 299 1 299 251 1 177 298 1 92 310 1 310 302 1 302 91 1 302 309 1
		 309 90 1 230 302 1 310 231 1 229 309 1 309 307 1 307 89 1 307 228 1 300 307 1 93 308 1
		 308 310 1 94 306 1 306 308 1 308 232 1 306 233 1 106 311 1 311 303 1 303 96 1 235 303 1
		 311 255 1 95 305 1 305 306 1 305 234 1 303 305 1 97 304 1 304 311 1 304 225 1 313 79 1
		 80 312 1 312 313 1 314 177 1 175 315 1 315 314 1 315 85 1 86 314 1 316 181 1 266 312 1
		 312 316 1 81 316 1 317 283 1 282 318 1 318 317 1 318 82 1 83 317 1 319 176 1 317 319 1
		 84 319 1 319 315 1 316 318 1 321 87 1 88 320 1 320 321 1 321 298 1 314 321 1 267 313 1
		 264 313 1 320 301 1 307 320 1 156 117 1 73 97 1 51 125 1 105 109 1 265 304 1 273 225 1
		 257 254 1;
	setAttr -s 320 ".fc[0:319]" -type "polyFaces" 
		f 4 6 7 8 9
		mu 0 4 16 53 52 9
		f 4 10 -10 11 12
		mu 0 4 18 16 9 14
		f 4 13 -13 14 15
		mu 0 4 12 18 14 6
		f 4 -9 16 17 18
		mu 0 4 9 52 62 8
		f 4 -12 -19 19 20
		mu 0 4 14 9 8 13
		f 4 21 -15 -21 22
		mu 0 4 5 6 14 13
		f 4 23 24 25 26
		mu 0 4 27 56 55 23
		f 4 27 28 -24 29
		mu 0 4 31 57 56 27
		f 4 30 -27 31 32
		mu 0 4 28 27 23 24
		f 4 33 -30 -31 34
		mu 0 4 32 31 27 28
		f 4 -33 35 36 37
		mu 0 4 28 24 20 25
		f 4 38 -35 -38 39
		mu 0 4 29 32 28 25
		f 4 40 41 42 43
		mu 0 4 114 115 22 15
		f 4 -42 44 45 46
		mu 0 4 22 115 120 26
		f 4 47 48 49 -43
		mu 0 4 22 123 122 15
		f 4 50 51 -48 -47
		mu 0 4 26 124 123 22
		f 4 52 53 54 55
		mu 0 4 63 64 4 10
		f 4 56 -54 57 58
		mu 0 4 11 4 64 65
		f 4 59 -57 60 -23
		mu 0 4 13 4 11 5
		f 4 -55 -60 -20 61
		mu 0 4 10 4 13 8
		f 4 -18 62 -56 -62
		mu 0 4 8 62 63 10
		f 4 63 -53 64 65
		mu 0 4 99 64 63 98
		f 4 -58 -64 66 67
		mu 0 4 65 64 99 100
		f 4 68 69 -68 70
		mu 0 4 101 66 65 100
		f 4 71 72 -71 -67
		mu 0 4 99 0 101 100
		f 4 73 -73 74 75
		mu 0 4 2 101 0 1
		f 4 76 77 -72 -66
		mu 0 4 98 102 0 99
		f 4 78 -77 -65 -63
		mu 0 4 62 102 98 63
		f 4 79 80 -75 -78
		mu 0 4 102 3 1 0
		f 4 -26 81 82 83
		mu 0 4 23 55 54 19
		f 4 -83 84 -7 85
		mu 0 4 19 54 53 16
		f 4 -32 -84 86 87
		mu 0 4 24 23 19 21
		f 4 -87 -86 -11 88
		mu 0 4 21 19 16 18
		f 4 -36 -88 89 90
		mu 0 4 20 24 21 17
		f 4 -90 -89 -14 91
		mu 0 4 17 21 18 12
		f 4 92 93 -44 -50
		mu 0 4 122 7 114 15
		f 4 94 95 -28 96
		mu 0 4 34 58 57 31
		f 4 97 -95 98 99
		mu 0 4 59 58 34 39
		f 4 100 -97 -34 101
		mu 0 4 35 34 31 32
		f 4 -99 -101 102 103
		mu 0 4 39 34 35 40
		f 4 104 105 -102 -39
		mu 0 4 29 33 35 32
		f 4 106 -103 -106 107
		mu 0 4 37 40 35 33
		f 4 108 109 110 111
		mu 0 4 109 108 77 76
		f 4 -110 112 113 114
		mu 0 4 77 108 112 78
		f 4 115 116 117 -109
		mu 0 4 109 116 117 108
		f 4 118 119 120 -117
		mu 0 4 116 36 30 117
		f 4 -113 -118 121 122
		mu 0 4 112 108 117 120
		f 4 -122 -121 123 -46
		mu 0 4 120 117 30 26
		f 4 124 -51 -124 125
		mu 0 4 125 124 26 30
		f 4 126 127 -125 128
		mu 0 4 69 68 124 125
		f 4 129 -126 -120 130
		mu 0 4 128 125 30 36
		f 4 131 -129 -130 132
		mu 0 4 70 69 125 128
		f 4 133 134 135 136
		mu 0 4 61 47 45 105
		f 4 -135 137 138 139
		mu 0 4 45 47 48 46
		f 4 -139 140 141 142
		mu 0 4 46 48 49 44
		f 4 143 144 145 146
		mu 0 4 113 111 74 73
		f 4 147 148 -144 149
		mu 0 4 121 119 111 113
		f 4 150 151 -148 152
		mu 0 4 107 50 119 121
		f 4 153 154 155 156
		mu 0 4 72 126 129 106
		f 4 157 -151 158 -155
		mu 0 4 126 50 107 129
		f 4 159 -100 160 161
		mu 0 4 60 59 39 42
		f 4 -161 -104 162 163
		mu 0 4 42 39 40 43
		f 4 164 -163 -107 165
		mu 0 4 41 43 40 37
		f 4 166 -112 167 168
		mu 0 4 110 109 76 75
		f 4 169 -116 -167 170
		mu 0 4 118 116 109 110
		f 4 171 -119 -170 172
		mu 0 4 38 36 116 118
		f 4 -172 173 174 -131
		mu 0 4 36 38 127 128
		f 4 -175 175 176 -133
		mu 0 4 128 127 71 70
		f 4 177 -162 178 -134
		mu 0 4 61 60 42 47
		f 4 -138 -179 -164 179
		mu 0 4 48 47 42 43
		f 4 -180 -165 180 -141
		mu 0 4 48 43 41 49
		f 4 181 -169 182 -145
		mu 0 4 111 110 75 74
		f 4 -149 183 -171 -182
		mu 0 4 111 119 118 110
		f 4 184 -173 -184 -152
		mu 0 4 50 38 118 119
		f 4 -158 185 -174 -185
		mu 0 4 50 126 127 38
		f 4 -154 186 -176 -186
		mu 0 4 126 72 71 127
		f 4 187 -136 188 189
		mu 0 4 51 105 45 314
		f 4 -140 190 -3 -189
		mu 0 4 45 46 315 316
		f 4 -191 -143 191 -2
		mu 0 4 317 46 44 318
		f 4 -1 193 -156 194
		mu 0 4 319 320 106 129
		f 4 -193 -195 -159 -153
		mu 0 4 121 321 129 107
		f 4 195 -74 196 197
		mu 0 4 103 101 2 131
		f 4 198 199 -49 200
		mu 0 4 67 133 122 123
		f 4 -201 -52 -128 201
		mu 0 4 67 123 124 68
		f 4 202 -93 -200 203
		mu 0 4 134 7 122 133
		f 4 -198 204 205 206
		mu 0 4 103 131 130 132
		f 4 207 -203 208 -206
		mu 0 4 130 7 134 132
		f 4 209 210 -16 211
		mu 0 4 135 136 322 323
		f 4 212 -212 -22 213
		mu 0 4 66 135 324 325
		f 4 214 -37 215 216
		mu 0 4 137 326 327 138
		f 4 217 -40 -215 218
		mu 0 4 139 328 329 137
		f 4 219 -219 220 -202
		mu 0 4 68 139 137 67
		f 4 -70 -214 -61 -59
		mu 0 4 65 66 5 11
		f 4 221 -216 -91 222
		mu 0 4 140 138 330 331
		f 4 223 -223 -92 -211
		mu 0 4 136 140 332 333
		f 4 -105 -218 224 225
		mu 0 4 334 335 139 141
		f 4 226 -225 -220 -127
		mu 0 4 69 141 139 68
		f 4 227 -108 -226 228
		mu 0 4 142 336 337 141
		f 4 229 -229 -227 -132
		mu 0 4 70 142 141 69
		f 4 -142 230 231 232
		mu 0 4 338 339 143 144
		f 4 -232 233 -157 234
		mu 0 4 144 143 72 106
		f 4 -228 235 236 -166
		mu 0 4 340 142 145 341
		f 4 -177 237 -236 -230
		mu 0 4 70 71 145 142
		f 4 -237 238 -231 -181
		mu 0 4 342 145 143 343
		f 4 -238 -187 -234 -239
		mu 0 4 145 71 72 143
		f 4 -194 -192 -233 -235
		mu 0 4 106 344 345 144
		f 4 -217 239 -199 -221
		mu 0 4 137 138 133 67
		f 4 240 -204 -240 -222
		mu 0 4 140 134 133 138
		f 4 241 -209 -241 -224
		mu 0 4 136 132 134 140
		f 4 242 -207 -242 -210
		mu 0 4 135 103 132 136
		f 4 -69 -196 -243 -213
		mu 0 4 66 101 103 135
		f 4 243 244 245 246
		mu 0 4 148 149 146 147
		f 4 -247 247 -8 248
		mu 0 4 148 147 52 53
		f 4 249 -80 250 -246
		mu 0 4 146 3 102 147
		f 4 -251 -79 -17 -248
		mu 0 4 147 102 62 52
		f 4 251 252 253 254
		mu 0 4 91 150 158 92
		f 4 255 256 -252 257
		mu 0 4 90 157 150 91
		f 4 258 -253 259 -29
		mu 0 4 57 158 150 56
		f 4 -260 -257 260 -25
		mu 0 4 56 150 157 55
		f 4 261 262 -256 263
		mu 0 4 89 155 157 90
		f 4 264 -249 -85 265
		mu 0 4 155 148 53 54
		f 4 -261 -263 -266 -82
		mu 0 4 55 157 155 54
		f 4 -254 266 267 268
		mu 0 4 92 158 156 93
		f 4 -268 269 270 271
		mu 0 4 93 156 154 94
		f 4 272 -267 -259 -96
		mu 0 4 58 156 158 57
		f 4 -98 273 -270 -273
		mu 0 4 58 59 154 156
		f 4 274 275 276 277
		mu 0 4 96 151 159 104
		f 4 -137 278 -276 279
		mu 0 4 61 105 159 151
		f 4 -271 280 281 282
		mu 0 4 94 154 153 95
		f 4 -160 283 -281 -274
		mu 0 4 59 60 153 154
		f 4 -282 284 -275 285
		mu 0 4 95 153 151 96
		f 4 -284 -178 -280 -285
		mu 0 4 153 60 61 151
		f 4 286 287 288 -277
		mu 0 4 159 152 97 104
		f 4 -279 -188 289 -287
		mu 0 4 159 105 51 152
		f 4 290 291 292 293
		mu 0 4 161 160 80 79
		f 4 294 295 -81 296
		mu 0 4 162 163 1 3
		f 4 -295 297 298 299
		mu 0 4 163 162 86 85
		f 4 300 301 -94 302
		mu 0 4 164 160 114 7
		f 4 -301 303 304 -292
		mu 0 4 160 164 81 80
		f 4 305 306 -205 307
		mu 0 4 165 166 130 131
		f 4 -306 308 309 310
		mu 0 4 166 165 83 82
		f 4 311 -308 -197 312
		mu 0 4 167 165 131 2
		f 4 -312 313 314 -309
		mu 0 4 165 167 84 83
		f 4 315 -313 -76 -296
		mu 0 4 163 167 2 1
		f 4 -316 -300 316 -314
		mu 0 4 167 163 85 84
		f 4 317 -303 -208 -307
		mu 0 4 166 164 7 130
		f 4 -318 -311 318 -304
		mu 0 4 164 166 82 81
		f 4 319 320 321 322
		mu 0 4 169 168 88 87
		f 4 323 -297 -250 324
		mu 0 4 169 162 3 146
		f 4 -324 -323 325 -298
		mu 0 4 162 169 87 86
		f 4 -291 326 -41 -302
		mu 0 4 160 161 115 114
		f 4 -123 -45 -327 327
		mu 0 4 112 120 115 161
		f 4 328 -114 -328 -294
		mu 0 4 79 78 112 161
		f 4 -320 -325 -245 329
		mu 0 4 168 169 146 149
		f 4 -262 330 -321 331
		mu 0 4 155 89 88 168
		f 4 -265 -332 -330 -244
		mu 0 4 148 155 168 149
		f 4 332 333 334 335
		mu 0 4 186 179 222 223
		f 4 336 337 -333 338
		mu 0 4 188 184 179 186
		f 4 339 340 -337 341
		mu 0 4 182 176 184 188
		f 4 342 343 344 -334
		mu 0 4 179 178 232 222
		f 4 345 346 -343 -338
		mu 0 4 184 183 178 179
		f 4 347 -346 -341 348
		mu 0 4 175 183 184 176
		f 4 349 350 351 352
		mu 0 4 197 193 225 226
		f 4 353 -353 354 355
		mu 0 4 201 197 226 227
		f 4 356 357 -350 358
		mu 0 4 198 194 193 197
		f 4 359 -359 -354 360
		mu 0 4 202 198 197 201
		f 4 361 362 363 -357
		mu 0 4 198 195 190 194
		f 4 364 -362 -360 365
		mu 0 4 199 195 198 202
		f 4 366 367 368 369
		mu 0 4 258 185 192 259
		f 4 370 371 372 -369
		mu 0 4 192 196 264 259
		f 4 -368 373 374 375
		mu 0 4 192 185 266 267
		f 4 -371 -376 376 377
		mu 0 4 196 192 267 268
		f 4 378 379 380 381
		mu 0 4 233 180 174 234
		f 4 382 383 -381 384
		mu 0 4 181 235 234 174
		f 4 -348 385 -385 386
		mu 0 4 183 175 181 174
		f 4 387 -347 -387 -380
		mu 0 4 180 178 183 174
		f 4 -388 -379 388 -344
		mu 0 4 178 180 233 232
		f 4 389 390 -382 391
		mu 0 4 244 243 233 234
		f 4 392 393 -392 -384
		mu 0 4 235 245 244 234
		f 4 394 -393 395 396
		mu 0 4 246 245 235 236
		f 4 -394 -395 397 398
		mu 0 4 244 245 246 170
		f 4 399 400 -398 401
		mu 0 4 172 171 170 246
		f 4 -390 -399 402 403
		mu 0 4 243 244 170 247
		f 4 -389 -391 -404 404
		mu 0 4 232 233 243 247
		f 4 -403 -401 405 406
		mu 0 4 247 170 171 173
		f 4 407 408 409 -351
		mu 0 4 193 189 224 225
		f 4 410 -336 411 -409
		mu 0 4 189 186 223 224
		f 4 412 413 -408 -358
		mu 0 4 194 191 189 193
		f 4 414 -339 -411 -414
		mu 0 4 191 188 186 189
		f 4 415 416 -413 -364
		mu 0 4 190 187 191 194
		f 4 417 -342 -415 -417
		mu 0 4 187 182 188 191
		f 4 -374 -367 418 419
		mu 0 4 266 185 258 177
		f 4 420 -356 421 422
		mu 0 4 204 201 227 228
		f 4 423 424 -423 425
		mu 0 4 229 209 204 228
		f 4 426 -361 -421 427
		mu 0 4 205 202 201 204
		f 4 428 429 -428 -425
		mu 0 4 209 210 205 204
		f 4 -366 -427 430 431
		mu 0 4 199 202 205 203
		f 4 432 -431 -430 433
		mu 0 4 207 203 205 210
		f 4 434 -111 435 436
		mu 0 4 253 76 77 252
		f 4 -115 437 438 -436
		mu 0 4 77 78 256 252
		f 4 -437 439 440 441
		mu 0 4 253 252 261 260
		f 4 -441 442 443 444
		mu 0 4 260 261 200 206
		f 4 445 446 -440 -439
		mu 0 4 256 264 261 252
		f 4 -372 447 -443 -447
		mu 0 4 264 196 200 261
		f 4 448 -448 -378 449
		mu 0 4 269 200 196 268
		f 4 450 -450 451 452
		mu 0 4 239 269 268 238
		f 4 453 -444 -449 454
		mu 0 4 272 206 200 269
		f 4 455 -455 -451 456
		mu 0 4 240 272 269 239
		f 4 457 458 459 460
		mu 0 4 231 249 215 217
		f 4 461 462 463 -460
		mu 0 4 215 216 218 217
		f 4 464 465 466 -463
		mu 0 4 216 214 219 218
		f 4 467 -146 468 469
		mu 0 4 257 73 74 255
		f 4 470 -470 471 472
		mu 0 4 265 257 255 263
		f 4 473 -473 474 475
		mu 0 4 251 265 263 220
		f 4 476 477 478 479
		mu 0 4 242 250 273 270
		f 4 -479 480 -476 481
		mu 0 4 270 273 251 220
		f 4 482 483 -424 484
		mu 0 4 230 212 209 229
		f 4 485 486 -429 -484
		mu 0 4 212 213 210 209
		f 4 487 -434 -487 488
		mu 0 4 211 207 210 213
		f 4 489 -168 -435 490
		mu 0 4 254 75 76 253
		f 4 491 -491 -442 492
		mu 0 4 262 254 253 260
		f 4 493 -493 -445 494
		mu 0 4 208 262 260 206
		f 4 -454 495 496 -495
		mu 0 4 206 272 271 208
		f 4 -456 497 498 -496
		mu 0 4 272 240 241 271
		f 4 -461 499 -483 500
		mu 0 4 231 217 212 230
		f 4 501 -486 -500 -464
		mu 0 4 218 213 212 217
		f 4 -467 502 -489 -502
		mu 0 4 218 219 211 213
		f 4 -469 -183 -490 503
		mu 0 4 255 74 75 254
		f 4 -504 -492 504 -472
		mu 0 4 255 254 262 263
		f 4 -475 -505 -494 505
		mu 0 4 220 263 262 208
		f 4 -506 -497 506 -482
		mu 0 4 220 208 271 270
		f 4 -507 -499 507 -480
		mu 0 4 270 271 241 242
		f 4 508 509 -459 510
		mu 0 4 221 346 215 249
		f 4 -510 -6 511 -462
		mu 0 4 215 347 348 216
		f 4 -5 512 -465 -512
		mu 0 4 349 350 214 216
		f 4 514 -478 515 -4
		mu 0 4 351 273 250 352
		f 4 -474 -481 -515 -514
		mu 0 4 265 251 273 353
		f 4 516 517 -402 518
		mu 0 4 248 275 172 246
		f 4 519 -375 520 521
		mu 0 4 237 267 266 277
		f 4 522 -452 -377 -520
		mu 0 4 237 238 268 267
		f 4 523 -521 -420 524
		mu 0 4 278 277 266 177
		f 4 525 526 527 -517
		mu 0 4 248 276 274 275
		f 4 -527 528 -525 529
		mu 0 4 274 276 278 177
		f 4 530 -340 531 532
		mu 0 4 279 354 355 280
		f 4 533 -349 -531 534
		mu 0 4 236 356 357 279
		f 4 535 536 -363 537
		mu 0 4 281 282 358 359
		f 4 538 -538 -365 539
		mu 0 4 283 281 360 361
		f 4 -523 540 -539 541
		mu 0 4 238 237 281 283
		f 4 -383 -386 -534 -396
		mu 0 4 235 181 175 236
		f 4 542 -416 -537 543
		mu 0 4 284 362 363 282
		f 4 -532 -418 -543 544
		mu 0 4 280 364 365 284
		f 4 545 546 -540 -432
		mu 0 4 366 285 283 367
		f 4 -453 -542 -547 547
		mu 0 4 239 238 283 285
		f 4 548 -546 -433 549
		mu 0 4 286 285 368 369
		f 4 -457 -548 -549 550
		mu 0 4 240 239 285 286
		f 4 551 552 553 -466
		mu 0 4 370 288 287 371
		f 4 554 -477 555 -553
		mu 0 4 288 250 242 287
		f 4 -488 556 557 -550
		mu 0 4 372 373 289 286
		f 4 -551 -558 558 -498
		mu 0 4 240 286 289 241
		f 4 -503 -554 559 -557
		mu 0 4 374 375 287 289
		f 4 -560 -556 -508 -559
		mu 0 4 289 287 242 241
		f 4 -555 -552 -513 -516
		mu 0 4 250 288 376 377
		f 4 -541 -522 560 -536
		mu 0 4 281 237 277 282
		f 4 -544 -561 -524 561
		mu 0 4 284 282 277 278
		f 4 -545 -562 -529 562
		mu 0 4 280 284 278 276
		f 4 -533 -563 -526 563
		mu 0 4 279 280 276 248
		f 4 -535 -564 -519 -397
		mu 0 4 236 279 248 246
		f 4 564 565 566 567
		mu 0 4 292 291 290 293
		f 4 568 -335 569 -565
		mu 0 4 292 223 222 291
		f 4 -566 570 -407 571
		mu 0 4 290 291 247 173
		f 4 -570 -345 -405 -571
		mu 0 4 291 222 232 247
		f 4 -255 572 573 574
		mu 0 4 91 92 302 294
		f 4 -258 -575 575 576
		mu 0 4 90 91 294 301
		f 4 -355 577 -574 578
		mu 0 4 227 226 294 302
		f 4 -352 579 -576 -578
		mu 0 4 226 225 301 294
		f 4 -264 -577 580 581
		mu 0 4 89 90 301 299
		f 4 582 -412 -569 583
		mu 0 4 299 224 223 292
		f 4 -410 -583 -581 -580
		mu 0 4 225 224 299 301
		f 4 -269 584 585 -573
		mu 0 4 92 93 300 302
		f 4 -272 586 587 -585
		mu 0 4 93 94 298 300
		f 4 -422 -579 -586 588
		mu 0 4 228 227 302 300
		f 4 -589 -588 589 -426
		mu 0 4 228 300 298 229
		f 4 -278 590 591 592
		mu 0 4 96 104 303 295
		f 4 593 -592 594 -458
		mu 0 4 231 295 303 249
		f 4 -283 595 596 -587
		mu 0 4 94 95 297 298
		f 4 -590 -597 597 -485
		mu 0 4 229 298 297 230
		f 4 -286 -593 598 -596
		mu 0 4 95 96 295 297
		f 4 -599 -594 -501 -598
		mu 0 4 297 295 231 230
		f 4 -591 -289 599 600
		mu 0 4 303 104 97 296
		f 4 -601 601 -511 -595
		mu 0 4 303 296 221 249
		f 4 602 -293 603 604
		mu 0 4 305 79 80 304
		f 4 605 -406 606 607
		mu 0 4 306 173 171 307
		f 4 608 -299 609 -608
		mu 0 4 307 85 86 306
		f 4 610 -419 611 612
		mu 0 4 308 177 258 304
		f 4 -604 -305 613 -613
		mu 0 4 304 80 81 308
		f 4 614 -528 615 616
		mu 0 4 309 275 274 310
		f 4 617 -310 618 -617
		mu 0 4 310 82 83 309
		f 4 619 -518 -615 620
		mu 0 4 311 172 275 309
		f 4 -619 -315 621 -621
		mu 0 4 309 83 84 311
		f 4 -607 -400 -620 622
		mu 0 4 307 171 172 311
		f 4 -622 -317 -609 -623
		mu 0 4 311 84 85 307
		f 4 -616 -530 -611 623
		mu 0 4 310 274 177 308
		f 4 -614 -319 -618 -624
		mu 0 4 308 81 82 310
		f 4 624 -322 625 626
		mu 0 4 313 87 88 312
		f 4 627 -572 -606 628
		mu 0 4 313 290 173 306
		f 4 -610 -326 -625 -629
		mu 0 4 306 86 87 313
		f 4 -612 -370 629 -605
		mu 0 4 304 258 259 305
		f 4 630 -630 -373 -446
		mu 0 4 256 305 259 264
		f 4 -603 -631 -438 -329
		mu 0 4 79 305 256 78
		f 4 631 -567 -628 -627
		mu 0 4 312 293 290 313
		f 4 632 -626 -331 -582
		mu 0 4 299 312 88 89
		f 4 -568 -632 -633 -584
		mu 0 4 292 293 312 299
		f 4 -288 633 -147 634
		mu 0 4 378 379 380 381
		f 4 -290 635 -150 -634
		mu 0 4 382 383 384 385
		f 4 192 -636 -190 636
		mu 0 4 386 387 388 389
		f 4 2 1 0 -637
		mu 0 4 390 391 392 393
		f 4 -600 -635 -468 637
		mu 0 4 394 395 396 397
		f 4 -602 -638 -471 638
		mu 0 4 398 399 400 401
		f 4 -509 -639 513 639
		mu 0 4 402 403 404 405
		f 4 5 -640 3 4
		mu 0 4 406 407 408 409;
	setAttr ".cd" -type "dataPolyComponent" Index_Data Edge 0 ;
	setAttr ".cvd" -type "dataPolyComponent" Index_Data Vertex 0 ;
	setAttr ".hfd" -type "dataPolyComponent" Index_Data Face 0 ;
createNode transform -n "teethClusterHandle" -p "headTopologyTeeth";
	setAttr ".v" no;
createNode clusterHandle -n "teethClusterHandleShape" -p "teethClusterHandle";
	setAttr ".ihi" 0;
	setAttr -k off ".v";
createNode parentConstraint -n "cluster1Handle_parentConstraint1" -p "teethClusterHandle";
	addAttr -ci true -k true -sn "w0" -ln "locator1W0" -dv 1 -min 0 -at "double";
	setAttr -k on ".nds";
	setAttr -k off ".v";
	setAttr -k off ".tx";
	setAttr -k off ".ty";
	setAttr -k off ".tz";
	setAttr -k off ".rx";
	setAttr -k off ".ry";
	setAttr -k off ".rz";
	setAttr -k off ".sx";
	setAttr -k off ".sy";
	setAttr -k off ".sz";
	setAttr ".erp" yes;
	setAttr -k on ".w0";
createNode scaleConstraint -n "teethClusterHandle_scaleConstraint1" -p "teethClusterHandle";
	addAttr -ci true -k true -sn "w0" -ln "teethLocatorW0" -dv 1 -min 0 -at "double";
	setAttr -k on ".nds";
	setAttr -k off ".v";
	setAttr -k off ".tx";
	setAttr -k off ".ty";
	setAttr -k off ".tz";
	setAttr -k off ".rx";
	setAttr -k off ".ry";
	setAttr -k off ".rz";
	setAttr -k off ".sx";
	setAttr -k off ".sy";
	setAttr -k off ".sz";
	setAttr ".erp" yes;
	setAttr -k on ".w0";
createNode transform -n "teethLocator" -p "headTopologyTeeth";
	addAttr -ci true -sn "bend1" -ln "bend1" -at "double";
	addAttr -ci true -sn "bend2" -ln "bend2" -at "double";
	addAttr -ci true -sn "bend3" -ln "bend3" -at "double";
	setAttr -l on ".tx";
	setAttr -l on ".rz";
	setAttr -l on ".ry";
	setAttr -k on ".bend1";
	setAttr -k on ".bend2";
	setAttr -k on ".bend3";
createNode locator -n "teethLocatorShape" -p "teethLocator";
	setAttr -k off ".v";
createNode transform -n "bend1Handle" -p "teethLocator";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 0 -0.018005609512329102 0.38981786062396329 ;
	setAttr ".r" -type "double3" -90 89.999999999999986 0 ;
	setAttr ".s" -type "double3" 0.53307615220546722 0.53307615220546722 0.53307615220546722 ;
	setAttr ".smd" 7;
createNode deformBend -n "bend1HandleShape" -p "bend1Handle";
	setAttr -k off ".v";
	setAttr ".dd" -type "doubleArray" 3 -1 1 0 ;
	setAttr ".hw" 0.53271166086196908;
createNode transform -n "bend2Handle" -p "teethLocator";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 0 -0.018005609512329102 -0.069105222821235657 ;
	setAttr ".r" -type "double3" 0 0 90 ;
	setAttr ".s" -type "double3" 1.2065026760101318 1.2065026760101318 1.2065026760101318 ;
	setAttr ".smd" 7;
createNode deformBend -n "bend2HandleShape" -p "bend2Handle";
	setAttr -k off ".v";
	setAttr ".dd" -type "doubleArray" 3 -1 1 0 ;
	setAttr ".hw" 0.58303581625223166;
createNode transform -n "bend3Handle" -p "teethLocator";
	setAttr ".v" no;
	setAttr ".t" -type "double3" 0 0.096633344888687134 -0.069105222821235657 ;
	setAttr ".r" -type "double3" 0 90 0 ;
	setAttr ".s" -type "double3" 1.2313569784164429 1.2313569784164429 1.2313569784164429 ;
	setAttr ".smd" 7;
createNode deformBend -n "bend3HandleShape" -p "bend3Handle";
	setAttr -k off ".v";
	setAttr ".dd" -type "doubleArray" 3 -1 1 0 ;
	setAttr ".hw" 0.65881451070308694;
createNode lightLinker -s -n "lightLinker1";
	setAttr -s 8 ".lnk";
	setAttr -s 8 ".slnk";
createNode displayLayerManager -n "layerManager";
createNode displayLayer -n "defaultLayer";
createNode renderLayerManager -n "renderLayerManager";
createNode renderLayer -n "defaultRenderLayer";
	setAttr ".g" yes;
createNode shadingEngine -n "gumSG";
	setAttr ".ihi" 0;
	setAttr -s 5 ".dsm";
	setAttr ".ro" yes;
	setAttr -s 5 ".gn";
createNode materialInfo -n "materialInfo8";
createNode lambert -n "gumLambert";
	setAttr ".c" -type "float3" 0.73333335 0.40784314 0.40784314 ;
createNode shadingEngine -n "teethSG";
	setAttr ".ihi" 0;
	setAttr -s 2 ".dsm";
	setAttr ".ro" yes;
	setAttr -s 2 ".gn";
createNode materialInfo -n "materialInfo9";
createNode lambert -n "teethLambert";
	setAttr ".c" -type "float3" 0.78225374 0.78225374 0.78225374 ;
	setAttr ".ambc" -type "float3" 0.57257956 0.57257956 0.57257956 ;

createNode script -n "sceneConfigurationScriptNode";
	setAttr ".b" -type "string" "playbackOptions -min 1 -max 24 -ast 1 -aet 48 ";
	setAttr ".st" 6;
createNode cluster -n "teethCluster";
	setAttr -s 3 ".ip";
	setAttr -s 3 ".og";
	setAttr ".rel" yes;
	setAttr -s 3 ".gm";
	setAttr ".gm[0]" -type "matrix" 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1;
	setAttr ".gm[1]" -type "matrix" 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1;
	setAttr ".gm[2]" -type "matrix" 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1;
createNode tweak -n "tweak1";
	setAttr -s 21 ".vl[0].vt";
	setAttr ".vl[0].vt[557:567]" -type "float3" -0.10772739 -0.085858658 -0.0014588712  
		-0.10942882 -0.087008461 -0.003403258  -0.10962172 -0.087244555 -0.0067141191  -0.10478228 
		-0.087255552 -0.010564404  -0.091916189 -0.087046728 -0.014065789  -0.075575218 -0.086804762 
		-0.017136833  -0.057794981 -0.086665884 -0.019714167  -0.036965597 -0.086500272 -0.021648096  
		-0.017637772 -0.086297289 -0.0227431  -0.0056595155 -0.086153522 -0.023163877  0 
		-0.086118385 -0.023271726 ;
	setAttr ".vl[0].vt[1199:1208]" -type "float3" 0.10772739 -0.085858658 -0.0014588712  
		0.10942882 -0.087008461 -0.003403258  0.10962172 -0.087244555 -0.0067141191  0.10478228 
		-0.087255552 -0.010564404  0.091916189 -0.087046728 -0.014065789  0.075575218 -0.086804762 
		-0.017136833  0.057794981 -0.086665884 -0.019714167  0.036965597 -0.086500272 -0.021648096  
		0.017637772 -0.086297289 -0.0227431  0.0056595155 -0.086153522 -0.023163877 ;
createNode tweak -n "tweak2";
	setAttr -s 29 ".vl[0].vt";
	setAttr ".vl[0].vt[237]" -type "float3" 0 1.1641532e-010 1.4901161e-008 ;
	setAttr ".vl[0].vt[653]" -type "float3" -0.097843811 -3.7252903e-009 -7.4505806e-009 ;
	setAttr ".vl[0].vt[659:669]" -type "float3" -0.10201257 0.046369866 -0.0061826641  
		-0.078607053 0.077283122 -0.012365306  -0.078738466 0.077283122 -0.012365295  -0.075267561 
		0.077283129 -0.012365298  -0.066029213 0.077283129 -0.012365302  -0.054301582 0.077283129 
		-0.012365302  -0.041530553 0.077283114 -0.012365306  -0.026578136 0.077283114 -0.012365306  
		-0.012703776 0.077283137 -0.012365269  -0.0040958854 0.077283122 -0.012365313  -9.1835496e-041 
		0.077283122 -0.012365306 ;
	setAttr ".vl[0].vt[673]" -type "float3" -0.067004807 1.8626451e-009 7.4505806e-009 ;
	setAttr ".vl[0].vt[677]" -type "float3" -0.085172527 -1.1175871e-008 -7.4505806e-009 ;
	setAttr ".vl[0].vt[917]" -type "float3" 3.7252903e-009 1.1641532e-010 7.4505806e-009 ;
	setAttr ".vl[0].vt[1324]" -type "float3" 0.097843811 -3.7252903e-009 -7.4505806e-009 ;
	setAttr ".vl[0].vt[1329:1338]" -type "float3" 0.10201257 0.046369866 -0.0061826641  
		0.078607053 0.077283122 -0.012365306  0.078738466 0.077283122 -0.012365295  0.075267561 
		0.077283129 -0.012365298  0.066029213 0.077283129 -0.012365302  0.054301582 0.077283129 
		-0.012365302  0.041530553 0.077283114 -0.012365306  0.026578136 0.077283114 -0.012365306  
		0.012703776 0.077283137 -0.012365269  0.0040958854 0.077283114 -0.012365313 ;
	setAttr ".vl[0].vt[1342]" -type "float3" 0.067004807 1.8626451e-009 7.4505806e-009 ;
	setAttr ".vl[0].vt[1346]" -type "float3" 0.085172527 -1.1175871e-008 -7.4505806e-009 ;
createNode tweak -n "tweak3";
createNode objectSet -n "cluster1Set";
	setAttr ".ihi" 0;
	setAttr -s 3 ".dsm";
	setAttr ".vo" yes;
	setAttr -s 3 ".gn";
createNode groupId -n "cluster1GroupId";
	setAttr ".ihi" 0;
createNode groupParts -n "cluster1GroupParts";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode groupId -n "cluster1GroupId1";
	setAttr ".ihi" 0;
createNode groupParts -n "cluster1GroupParts1";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode groupId -n "cluster1GroupId2";
	setAttr ".ihi" 0;
createNode groupParts -n "cluster1GroupParts2";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode objectSet -n "tweakSet1";
	setAttr ".ihi" 0;
	setAttr ".vo" yes;
createNode groupId -n "groupId26";
	setAttr ".ihi" 0;
createNode groupParts -n "groupParts11";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode objectSet -n "tweakSet2";
	setAttr ".ihi" 0;
	setAttr ".vo" yes;
createNode groupId -n "groupId27";
	setAttr ".ihi" 0;
createNode groupParts -n "groupParts12";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode objectSet -n "tweakSet3";
	setAttr ".ihi" 0;
	setAttr ".vo" yes;
createNode groupId -n "groupId28";
	setAttr ".ihi" 0;
createNode groupParts -n "groupParts13";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode nonLinear -n "bend1";
	addAttr -is true -ci true -k true -sn "cur" -ln "curvature" -smn -4 -smx 4 -at "double";
	addAttr -is true -ci true -k true -sn "lb" -ln "lowBound" -dv -1 -max 0 -smn -10 
		-smx 0 -at "double";
	addAttr -is true -ci true -k true -sn "hb" -ln "highBound" -dv 1 -min 0 -smn 0 -smx 
		10 -at "double";
	setAttr -s 3 ".ip";
	setAttr -s 3 ".og";
	setAttr -k on ".cur";
	setAttr -k on ".lb";
	setAttr -k on ".hb";
createNode objectSet -n "bend1Set";
	setAttr ".ihi" 0;
	setAttr -s 3 ".dsm";
	setAttr ".vo" yes;
	setAttr -s 3 ".gn";
createNode groupId -n "bend1GroupId";
	setAttr ".ihi" 0;
createNode groupParts -n "bend1GroupParts";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode groupId -n "bend1GroupId1";
	setAttr ".ihi" 0;
createNode groupParts -n "bend1GroupParts1";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode groupId -n "bend1GroupId2";
	setAttr ".ihi" 0;
createNode groupParts -n "bend1GroupParts2";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode nonLinear -n "bend2";
	addAttr -is true -ci true -k true -sn "cur" -ln "curvature" -smn -4 -smx 4 -at "double";
	addAttr -is true -ci true -k true -sn "lb" -ln "lowBound" -dv -1 -max 0 -smn -10 
		-smx 0 -at "double";
	addAttr -is true -ci true -k true -sn "hb" -ln "highBound" -dv 1 -min 0 -smn 0 -smx 
		10 -at "double";
	setAttr -s 3 ".ip";
	setAttr -s 3 ".og";
	setAttr -k on ".cur";
	setAttr -k on ".lb";
	setAttr -k on ".hb";
createNode objectSet -n "bend2Set";
	setAttr ".ihi" 0;
	setAttr -s 3 ".dsm";
	setAttr ".vo" yes;
	setAttr -s 3 ".gn";
createNode groupId -n "bend2GroupId";
	setAttr ".ihi" 0;
createNode groupParts -n "bend2GroupParts";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode groupId -n "bend2GroupId1";
	setAttr ".ihi" 0;
createNode groupParts -n "bend2GroupParts1";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode groupId -n "bend2GroupId2";
	setAttr ".ihi" 0;
createNode groupParts -n "bend2GroupParts2";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode nonLinear -n "bend3";
	addAttr -is true -ci true -k true -sn "cur" -ln "curvature" -smn -4 -smx 4 -at "double";
	addAttr -is true -ci true -k true -sn "lb" -ln "lowBound" -dv -1 -max 0 -smn -10 
		-smx 0 -at "double";
	addAttr -is true -ci true -k true -sn "hb" -ln "highBound" -dv 1 -min 0 -smn 0 -smx 
		10 -at "double";
	setAttr -s 3 ".ip";
	setAttr -s 3 ".og";
	setAttr -k on ".cur";
	setAttr -k on ".lb";
	setAttr -k on ".hb";
createNode objectSet -n "bend3Set";
	setAttr ".ihi" 0;
	setAttr -s 3 ".dsm";
	setAttr ".vo" yes;
	setAttr -s 3 ".gn";
createNode groupId -n "bend3GroupId";
	setAttr ".ihi" 0;
createNode groupParts -n "bend3GroupParts";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode groupId -n "bend3GroupId1";
	setAttr ".ihi" 0;
createNode groupParts -n "bend3GroupParts1";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode groupId -n "bend3GroupId2";
	setAttr ".ihi" 0;
createNode groupParts -n "bend3GroupParts2";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "vtx[*]";
createNode groupId -n "groupId29";
	setAttr ".ihi" 0;
createNode groupParts -n "groupParts14";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "f[0:513]";
createNode groupId -n "groupId30";
	setAttr ".ihi" 0;
createNode groupParts -n "groupParts15";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "f[514:1181]";
createNode groupId -n "groupId31";
	setAttr ".ihi" 0;
createNode groupParts -n "groupParts16";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "f[0:527]";
createNode groupId -n "groupId32";
	setAttr ".ihi" 0;
createNode groupParts -n "groupParts17";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "f[528:1235]";
createNode groupId -n "groupId33";
	setAttr ".ihi" 0;
createNode groupParts -n "groupParts18";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "f[1236:1238]";
createNode groupId -n "groupId34";
	setAttr ".ihi" 0;
createNode groupParts -n "groupParts19";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "f[1239:1240]";
createNode groupId -n "groupId35";
	setAttr ".ihi" 0;
createNode groupParts -n "groupParts20";
	setAttr ".ihi" 0;
	setAttr ".ic" -type "componentList" 1 "f[0:319]";
select -ne :time1;
	setAttr -av -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".o" 1;
	setAttr -av ".unw" 1;
	setAttr -k on ".etw";
	setAttr -k on ".tps";
	setAttr -k on ".tms";
lockNode -l 1 ;
select -ne :renderPartition;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 4 ".st";
	setAttr -cb on ".an";
	setAttr -cb on ".pt";
lockNode -l 1 ;
select -ne :initialShadingGroup;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".mwc";
	setAttr -cb on ".an";
	setAttr -cb on ".il";
	setAttr -cb on ".vo";
	setAttr -cb on ".eo";
	setAttr -cb on ".fo";
	setAttr -cb on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :initialParticleSE;
	setAttr -av -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".mwc";
	setAttr -cb on ".an";
	setAttr -cb on ".il";
	setAttr -cb on ".vo";
	setAttr -cb on ".eo";
	setAttr -cb on ".fo";
	setAttr -cb on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :defaultShaderList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 4 ".s";
lockNode -l 1 ;
select -ne :postProcessList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -s 2 ".p";
lockNode -l 1 ;
select -ne :defaultRenderingList1;
lockNode -l 1 ;
select -ne :renderGlobalsList1;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
lockNode -l 1 ;
select -ne :defaultRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k on ".macc";
	setAttr -k on ".macd";
	setAttr -k on ".macq";
	setAttr -k on ".mcfr";
	setAttr -cb on ".ifg";
	setAttr -k on ".clip";
	setAttr -k on ".edm";
	setAttr -k on ".edl";
	setAttr -cb on ".ren";
	setAttr -av -k on ".esr";
	setAttr -k on ".ors";
	setAttr -cb on ".sdf";
	setAttr -av -k on ".outf";
	setAttr -cb on ".imfkey";
	setAttr -k on ".gama";
	setAttr -k on ".an";
	setAttr -cb on ".ar";
	setAttr -k on ".fs" 1;
	setAttr -k on ".ef" 10;
	setAttr -av -k on ".bfs";
	setAttr -cb on ".me";
	setAttr -cb on ".se";
	setAttr -k on ".be";
	setAttr -cb on ".ep";
	setAttr -k on ".fec";
	setAttr -av -k on ".ofc";
	setAttr -cb on ".ofe";
	setAttr -cb on ".efe";
	setAttr -cb on ".oft";
	setAttr -cb on ".umfn";
	setAttr -cb on ".ufe";
	setAttr -cb on ".pff";
	setAttr -cb on ".peie";
	setAttr -cb on ".ifp";
	setAttr -k on ".comp";
	setAttr -k on ".cth";
	setAttr -k on ".soll";
	setAttr -k on ".sosl";
	setAttr -k on ".rd";
	setAttr -k on ".lp";
	setAttr -av -k on ".sp";
	setAttr -k on ".shs";
	setAttr -av -k on ".lpr";
	setAttr -cb on ".gv";
	setAttr -cb on ".sv";
	setAttr -k on ".mm";
	setAttr -k on ".npu";
	setAttr -k on ".itf";
	setAttr -k on ".shp";
	setAttr -cb on ".isp";
	setAttr -k on ".uf";
	setAttr -k on ".oi";
	setAttr -k on ".rut";
	setAttr -cb on ".mb";
	setAttr -av -k on ".mbf";
	setAttr -k on ".afp";
	setAttr -k on ".pfb";
	setAttr -k on ".pram";
	setAttr -k on ".poam";
	setAttr -k on ".prlm";
	setAttr -k on ".polm";
	setAttr -cb on ".prm";
	setAttr -cb on ".pom";
	setAttr -cb on ".pfrm";
	setAttr -cb on ".pfom";
	setAttr -av -k on ".bll";
	setAttr -k on ".bls";
	setAttr -av -k on ".smv";
	setAttr -k on ".ubc";
	setAttr -k on ".mbc";
	setAttr -cb on ".mbt";
	setAttr -k on ".udbx";
	setAttr -k on ".smc";
	setAttr -k on ".kmv";
	setAttr -cb on ".isl";
	setAttr -cb on ".ism";
	setAttr -cb on ".imb";
	setAttr -k on ".rlen";
	setAttr -av -k on ".frts";
	setAttr -k on ".tlwd";
	setAttr -k on ".tlht";
	setAttr -k on ".jfc";
	setAttr -cb on ".rsb";
	setAttr -k on ".ope";
	setAttr -k on ".oppf";
	setAttr -cb on ".hbl";
select -ne :defaultLightSet;
	setAttr -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -av -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -k on ".mwc";
	setAttr -k on ".an";
	setAttr -k on ".il";
	setAttr -k on ".vo";
	setAttr -k on ".eo";
	setAttr -k on ".fo";
	setAttr -k on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :defaultObjectSet;
	setAttr -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -k on ".mwc";
	setAttr -k on ".an";
	setAttr -k on ".il";
	setAttr -k on ".vo";
	setAttr -k on ".eo";
	setAttr -k on ".fo";
	setAttr -k on ".epo";
	setAttr -k on ".ro" yes;
lockNode -l 1 ;
select -ne :hardwareRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -k off ".ctrs" 256;
	setAttr -av -k off ".btrs" 512;
	setAttr -k off ".fbfm";
	setAttr -k off -cb on ".ehql";
	setAttr -k off -cb on ".eams";
	setAttr -k off -cb on ".eeaa";
	setAttr -k off -cb on ".engm";
	setAttr -k off -cb on ".mes";
	setAttr -k off -cb on ".emb";
	setAttr -av -k off -cb on ".mbbf";
	setAttr -k off -cb on ".mbs";
	setAttr -k off -cb on ".trm";
	setAttr -k off -cb on ".tshc";
	setAttr -k off ".enpt";
	setAttr -k off -cb on ".clmt";
	setAttr -k off -cb on ".tcov";
	setAttr -k off -cb on ".lith";
	setAttr -k off -cb on ".sobc";
	setAttr -k off -cb on ".cuth";
	setAttr -k off -cb on ".hgcd";
	setAttr -k off -cb on ".hgci";
	setAttr -k off -cb on ".mgcs";
	setAttr -k off -cb on ".twa";
	setAttr -k off -cb on ".twz";
	setAttr -k on ".hwcc";
	setAttr -k on ".hwdp";
	setAttr -k on ".hwql";
	setAttr -k on ".hwfr";
	setAttr -k on ".soll";
	setAttr -k on ".sosl";
	setAttr -k on ".bswa";
	setAttr -k on ".shml";
	setAttr -k on ".hwel";
lockNode -l 1 ;
select -ne :defaultHardwareRenderGlobals;
	setAttr -k on ".cch";
	setAttr -cb on ".ihi";
	setAttr -k on ".nds";
	setAttr -cb on ".bnm";
	setAttr -av -k on ".rp";
	setAttr -k on ".cai";
	setAttr -k on ".coi";
	setAttr -cb on ".bc";
	setAttr -av -k on ".bcb";
	setAttr -av -k on ".bcg";
	setAttr -av -k on ".bcr";
	setAttr -k on ".ei";
	setAttr -av -k on ".ex";
	setAttr -av -k on ".es";
	setAttr -av -cb on ".ef";
	setAttr -av -cb on ".bf";
	setAttr -k on ".fii";
	setAttr -av -cb on ".sf";
	setAttr -k on ".gr";
	setAttr -k on ".li";
	setAttr -k on ".ls";
	setAttr -av -k on ".mb";
	setAttr -k on ".ti";
	setAttr -k on ".txt";
	setAttr -k on ".mpr";
	setAttr -k on ".wzd";
	setAttr -k on ".fn" -type "string" "im";
	setAttr -k on ".if";
	setAttr -k on ".res" -type "string" "ntsc_4d 646 485 1.333";
	setAttr -k on ".as";
	setAttr -k on ".ds";
	setAttr -k on ".lm";
	setAttr -av -k on ".fir";
	setAttr -k on ".aap";
	setAttr -av -k on ".gh";
	setAttr -cb on ".sd";
lockNode -l 1 ;
select -ne :ikSystem;
	setAttr -k on ".cch";
	setAttr -k on ".ihi";
	setAttr -k on ".nds";
	setAttr -k on ".bnm";
	setAttr -av ".gsn";
	setAttr -k on ".gsv";
	setAttr -s 4 ".sol";
connectAttr "groupId29.id" "upperTeethShape.iog.og[3].gid";
connectAttr "gumSG.mwc" "upperTeethShape.iog.og[3].gco";
connectAttr "groupId30.id" "upperTeethShape.iog.og[4].gid";
connectAttr "teethSG.mwc" "upperTeethShape.iog.og[4].gco";
connectAttr "cluster1GroupId.id" "upperTeethShape.iog.og[10].gid";
connectAttr "cluster1Set.mwc" "upperTeethShape.iog.og[10].gco";
connectAttr "groupId26.id" "upperTeethShape.iog.og[11].gid";
connectAttr "tweakSet1.mwc" "upperTeethShape.iog.og[11].gco";
connectAttr "bend1GroupId.id" "upperTeethShape.iog.og[15].gid";
connectAttr "bend1Set.mwc" "upperTeethShape.iog.og[15].gco";
connectAttr "bend2GroupId.id" "upperTeethShape.iog.og[16].gid";
connectAttr "bend2Set.mwc" "upperTeethShape.iog.og[16].gco";
connectAttr "bend3GroupId.id" "upperTeethShape.iog.og[17].gid";
connectAttr "bend3Set.mwc" "upperTeethShape.iog.og[17].gco";
connectAttr "groupParts15.og" "upperTeethShape.i";
connectAttr "tweak1.vl[0].vt[0]" "upperTeethShape.twl";
connectAttr "groupId31.id" "lowerTeethShape.iog.og[3].gid";
connectAttr "gumSG.mwc" "lowerTeethShape.iog.og[3].gco";
connectAttr "groupId32.id" "lowerTeethShape.iog.og[4].gid";
connectAttr "teethSG.mwc" "lowerTeethShape.iog.og[4].gco";
connectAttr "groupId33.id" "lowerTeethShape.iog.og[9].gid";
connectAttr "gumSG.mwc" "lowerTeethShape.iog.og[9].gco";
connectAttr "groupId34.id" "lowerTeethShape.iog.og[10].gid";
connectAttr "gumSG.mwc" "lowerTeethShape.iog.og[10].gco";
connectAttr "cluster1GroupId1.id" "lowerTeethShape.iog.og[13].gid";
connectAttr "cluster1Set.mwc" "lowerTeethShape.iog.og[13].gco";
connectAttr "groupId27.id" "lowerTeethShape.iog.og[14].gid";
connectAttr "tweakSet2.mwc" "lowerTeethShape.iog.og[14].gco";
connectAttr "bend1GroupId1.id" "lowerTeethShape.iog.og[17].gid";
connectAttr "bend1Set.mwc" "lowerTeethShape.iog.og[17].gco";
connectAttr "bend2GroupId1.id" "lowerTeethShape.iog.og[18].gid";
connectAttr "bend2Set.mwc" "lowerTeethShape.iog.og[18].gco";
connectAttr "bend3GroupId1.id" "lowerTeethShape.iog.og[19].gid";
connectAttr "bend3Set.mwc" "lowerTeethShape.iog.og[19].gco";
connectAttr "groupParts19.og" "lowerTeethShape.i";
connectAttr "tweak2.vl[0].vt[0]" "lowerTeethShape.twl";
connectAttr "groupId35.id" "tongueShape.iog.og[3].gid";
connectAttr "gumSG.mwc" "tongueShape.iog.og[3].gco";
connectAttr "cluster1GroupId2.id" "tongueShape.iog.og[10].gid";
connectAttr "cluster1Set.mwc" "tongueShape.iog.og[10].gco";
connectAttr "groupId28.id" "tongueShape.iog.og[11].gid";
connectAttr "tweakSet3.mwc" "tongueShape.iog.og[11].gco";
connectAttr "bend1GroupId2.id" "tongueShape.iog.og[14].gid";
connectAttr "bend1Set.mwc" "tongueShape.iog.og[14].gco";
connectAttr "bend2GroupId2.id" "tongueShape.iog.og[15].gid";
connectAttr "bend2Set.mwc" "tongueShape.iog.og[15].gco";
connectAttr "bend3GroupId2.id" "tongueShape.iog.og[16].gid";
connectAttr "bend3Set.mwc" "tongueShape.iog.og[16].gco";
connectAttr "groupParts20.og" "tongueShape.i";
connectAttr "tweak3.vl[0].vt[0]" "tongueShape.twl";
connectAttr "cluster1Handle_parentConstraint1.ctx" "teethClusterHandle.tx";
connectAttr "cluster1Handle_parentConstraint1.cty" "teethClusterHandle.ty";
connectAttr "cluster1Handle_parentConstraint1.ctz" "teethClusterHandle.tz";
connectAttr "cluster1Handle_parentConstraint1.crx" "teethClusterHandle.rx";
connectAttr "cluster1Handle_parentConstraint1.cry" "teethClusterHandle.ry";
connectAttr "cluster1Handle_parentConstraint1.crz" "teethClusterHandle.rz";
connectAttr "teethClusterHandle_scaleConstraint1.csx" "teethClusterHandle.sx";
connectAttr "teethClusterHandle_scaleConstraint1.csy" "teethClusterHandle.sy";
connectAttr "teethClusterHandle_scaleConstraint1.csz" "teethClusterHandle.sz";
connectAttr "teethClusterHandle.ro" "cluster1Handle_parentConstraint1.cro";
connectAttr "teethClusterHandle.pim" "cluster1Handle_parentConstraint1.cpim";
connectAttr "teethClusterHandle.rp" "cluster1Handle_parentConstraint1.crp";
connectAttr "teethClusterHandle.rpt" "cluster1Handle_parentConstraint1.crt";
connectAttr "teethLocator.t" "cluster1Handle_parentConstraint1.tg[0].tt";
connectAttr "teethLocator.rp" "cluster1Handle_parentConstraint1.tg[0].trp";
connectAttr "teethLocator.rpt" "cluster1Handle_parentConstraint1.tg[0].trt";
connectAttr "teethLocator.r" "cluster1Handle_parentConstraint1.tg[0].tr";
connectAttr "teethLocator.ro" "cluster1Handle_parentConstraint1.tg[0].tro";
connectAttr "teethLocator.s" "cluster1Handle_parentConstraint1.tg[0].ts";
connectAttr "teethLocator.pm" "cluster1Handle_parentConstraint1.tg[0].tpm";
connectAttr "cluster1Handle_parentConstraint1.w0" "cluster1Handle_parentConstraint1.tg[0].tw"
		;
connectAttr "teethClusterHandle.pim" "teethClusterHandle_scaleConstraint1.cpim";
connectAttr "teethLocator.s" "teethClusterHandle_scaleConstraint1.tg[0].ts";
connectAttr "teethLocator.pm" "teethClusterHandle_scaleConstraint1.tg[0].tpm";
connectAttr "teethClusterHandle_scaleConstraint1.w0" "teethClusterHandle_scaleConstraint1.tg[0].tw"
		;
connectAttr "bend1.msg" "bend1Handle.sml";
connectAttr "bend1.cur" "bend1HandleShape.cur";
connectAttr "bend1.lb" "bend1HandleShape.lb";
connectAttr "bend1.hb" "bend1HandleShape.hb";
connectAttr "bend2.msg" "bend2Handle.sml";
connectAttr "bend2.cur" "bend2HandleShape.cur";
connectAttr "bend2.lb" "bend2HandleShape.lb";
connectAttr "bend2.hb" "bend2HandleShape.hb";
connectAttr "bend3.msg" "bend3Handle.sml";
connectAttr "bend3.cur" "bend3HandleShape.cur";
connectAttr "bend3.lb" "bend3HandleShape.lb";
connectAttr "bend3.hb" "bend3HandleShape.hb";
relationship "link" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "gumSG.message" ":defaultLightSet.message";
relationship "link" ":lightLinker1" "teethSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialShadingGroup.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" ":initialParticleSE.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "gumSG.message" ":defaultLightSet.message";
relationship "shadowLink" ":lightLinker1" "teethSG.message" ":defaultLightSet.message";
connectAttr "layerManager.dli[0]" "defaultLayer.id";
connectAttr "renderLayerManager.rlmi[0]" "defaultRenderLayer.rlid";
connectAttr "gumLambert.oc" "gumSG.ss";
connectAttr "groupId29.msg" "gumSG.gn" -na;
connectAttr "groupId31.msg" "gumSG.gn" -na;
connectAttr "groupId33.msg" "gumSG.gn" -na;
connectAttr "groupId34.msg" "gumSG.gn" -na;
connectAttr "groupId35.msg" "gumSG.gn" -na;
connectAttr "upperTeethShape.iog.og[3]" "gumSG.dsm" -na;
connectAttr "lowerTeethShape.iog.og[3]" "gumSG.dsm" -na;
connectAttr "lowerTeethShape.iog.og[9]" "gumSG.dsm" -na;
connectAttr "lowerTeethShape.iog.og[10]" "gumSG.dsm" -na;
connectAttr "tongueShape.iog.og[3]" "gumSG.dsm" -na;
connectAttr "gumSG.msg" "materialInfo8.sg";
connectAttr "gumLambert.msg" "materialInfo8.m";
connectAttr "teethLambert.oc" "teethSG.ss";
connectAttr "groupId30.msg" "teethSG.gn" -na;
connectAttr "groupId32.msg" "teethSG.gn" -na;
connectAttr "upperTeethShape.iog.og[4]" "teethSG.dsm" -na;
connectAttr "lowerTeethShape.iog.og[4]" "teethSG.dsm" -na;
connectAttr "teethSG.msg" "materialInfo9.sg";
connectAttr "teethLambert.msg" "materialInfo9.m";
connectAttr "cluster1GroupParts.og" "teethCluster.ip[0].ig";
connectAttr "cluster1GroupId.id" "teethCluster.ip[0].gi";
connectAttr "cluster1GroupParts1.og" "teethCluster.ip[1].ig";
connectAttr "cluster1GroupId1.id" "teethCluster.ip[1].gi";
connectAttr "cluster1GroupParts2.og" "teethCluster.ip[2].ig";
connectAttr "cluster1GroupId2.id" "teethCluster.ip[2].gi";
connectAttr "teethClusterHandle.wm" "teethCluster.ma";
connectAttr "teethClusterHandleShape.x" "teethCluster.x";
connectAttr "groupParts11.og" "tweak1.ip[0].ig";
connectAttr "groupId26.id" "tweak1.ip[0].gi";
connectAttr "groupParts12.og" "tweak2.ip[0].ig";
connectAttr "groupId27.id" "tweak2.ip[0].gi";
connectAttr "groupParts13.og" "tweak3.ip[0].ig";
connectAttr "groupId28.id" "tweak3.ip[0].gi";
connectAttr "cluster1GroupId.msg" "cluster1Set.gn" -na;
connectAttr "cluster1GroupId1.msg" "cluster1Set.gn" -na;
connectAttr "cluster1GroupId2.msg" "cluster1Set.gn" -na;
connectAttr "upperTeethShape.iog.og[10]" "cluster1Set.dsm" -na;
connectAttr "lowerTeethShape.iog.og[13]" "cluster1Set.dsm" -na;
connectAttr "tongueShape.iog.og[10]" "cluster1Set.dsm" -na;
connectAttr "teethCluster.msg" "cluster1Set.ub[0]";
connectAttr "tweak1.og[0]" "cluster1GroupParts.ig";
connectAttr "cluster1GroupId.id" "cluster1GroupParts.gi";
connectAttr "tweak2.og[0]" "cluster1GroupParts1.ig";
connectAttr "cluster1GroupId1.id" "cluster1GroupParts1.gi";
connectAttr "tweak3.og[0]" "cluster1GroupParts2.ig";
connectAttr "cluster1GroupId2.id" "cluster1GroupParts2.gi";
connectAttr "groupId26.msg" "tweakSet1.gn" -na;
connectAttr "upperTeethShape.iog.og[11]" "tweakSet1.dsm" -na;
connectAttr "tweak1.msg" "tweakSet1.ub[0]";
connectAttr "upperTeethShapeOrig1.w" "groupParts11.ig";
connectAttr "groupId26.id" "groupParts11.gi";
connectAttr "groupId27.msg" "tweakSet2.gn" -na;
connectAttr "lowerTeethShape.iog.og[14]" "tweakSet2.dsm" -na;
connectAttr "tweak2.msg" "tweakSet2.ub[0]";
connectAttr "lowerTeethShapeOrig1.w" "groupParts12.ig";
connectAttr "groupId27.id" "groupParts12.gi";
connectAttr "groupId28.msg" "tweakSet3.gn" -na;
connectAttr "tongueShape.iog.og[11]" "tweakSet3.dsm" -na;
connectAttr "tweak3.msg" "tweakSet3.ub[0]";
connectAttr "tongueShapeOrig1.w" "groupParts13.ig";
connectAttr "groupId28.id" "groupParts13.gi";
connectAttr "bend1GroupParts.og" "bend1.ip[0].ig";
connectAttr "bend1GroupId.id" "bend1.ip[0].gi";
connectAttr "bend1GroupParts1.og" "bend1.ip[1].ig";
connectAttr "bend1GroupId1.id" "bend1.ip[1].gi";
connectAttr "bend1GroupParts2.og" "bend1.ip[2].ig";
connectAttr "bend1GroupId2.id" "bend1.ip[2].gi";
connectAttr "teethLocator.bend1" "bend1.cur";
connectAttr "bend1HandleShape.dd" "bend1.dd";
connectAttr "bend1Handle.wm" "bend1.ma";
connectAttr "bend1GroupId.msg" "bend1Set.gn" -na;
connectAttr "bend1GroupId1.msg" "bend1Set.gn" -na;
connectAttr "bend1GroupId2.msg" "bend1Set.gn" -na;
connectAttr "upperTeethShape.iog.og[15]" "bend1Set.dsm" -na;
connectAttr "lowerTeethShape.iog.og[17]" "bend1Set.dsm" -na;
connectAttr "tongueShape.iog.og[14]" "bend1Set.dsm" -na;
connectAttr "bend1.msg" "bend1Set.ub[0]";
connectAttr "teethCluster.og[0]" "bend1GroupParts.ig";
connectAttr "bend1GroupId.id" "bend1GroupParts.gi";
connectAttr "teethCluster.og[1]" "bend1GroupParts1.ig";
connectAttr "bend1GroupId1.id" "bend1GroupParts1.gi";
connectAttr "teethCluster.og[2]" "bend1GroupParts2.ig";
connectAttr "bend1GroupId2.id" "bend1GroupParts2.gi";
connectAttr "bend2GroupParts.og" "bend2.ip[0].ig";
connectAttr "bend2GroupId.id" "bend2.ip[0].gi";
connectAttr "bend2GroupParts1.og" "bend2.ip[1].ig";
connectAttr "bend2GroupId1.id" "bend2.ip[1].gi";
connectAttr "bend2GroupParts2.og" "bend2.ip[2].ig";
connectAttr "bend2GroupId2.id" "bend2.ip[2].gi";
connectAttr "teethLocator.bend2" "bend2.cur";
connectAttr "bend2HandleShape.dd" "bend2.dd";
connectAttr "bend2Handle.wm" "bend2.ma";
connectAttr "bend2GroupId.msg" "bend2Set.gn" -na;
connectAttr "bend2GroupId1.msg" "bend2Set.gn" -na;
connectAttr "bend2GroupId2.msg" "bend2Set.gn" -na;
connectAttr "upperTeethShape.iog.og[16]" "bend2Set.dsm" -na;
connectAttr "lowerTeethShape.iog.og[18]" "bend2Set.dsm" -na;
connectAttr "tongueShape.iog.og[15]" "bend2Set.dsm" -na;
connectAttr "bend2.msg" "bend2Set.ub[0]";
connectAttr "bend1.og[0]" "bend2GroupParts.ig";
connectAttr "bend2GroupId.id" "bend2GroupParts.gi";
connectAttr "bend1.og[1]" "bend2GroupParts1.ig";
connectAttr "bend2GroupId1.id" "bend2GroupParts1.gi";
connectAttr "bend1.og[2]" "bend2GroupParts2.ig";
connectAttr "bend2GroupId2.id" "bend2GroupParts2.gi";
connectAttr "bend3GroupParts.og" "bend3.ip[0].ig";
connectAttr "bend3GroupId.id" "bend3.ip[0].gi";
connectAttr "bend3GroupParts1.og" "bend3.ip[1].ig";
connectAttr "bend3GroupId1.id" "bend3.ip[1].gi";
connectAttr "bend3GroupParts2.og" "bend3.ip[2].ig";
connectAttr "bend3GroupId2.id" "bend3.ip[2].gi";
connectAttr "teethLocator.bend3" "bend3.cur";
connectAttr "bend3HandleShape.dd" "bend3.dd";
connectAttr "bend3Handle.wm" "bend3.ma";
connectAttr "bend3GroupId.msg" "bend3Set.gn" -na;
connectAttr "bend3GroupId1.msg" "bend3Set.gn" -na;
connectAttr "bend3GroupId2.msg" "bend3Set.gn" -na;
connectAttr "upperTeethShape.iog.og[17]" "bend3Set.dsm" -na;
connectAttr "lowerTeethShape.iog.og[19]" "bend3Set.dsm" -na;
connectAttr "tongueShape.iog.og[16]" "bend3Set.dsm" -na;
connectAttr "bend3.msg" "bend3Set.ub[0]";
connectAttr "bend2.og[0]" "bend3GroupParts.ig";
connectAttr "bend3GroupId.id" "bend3GroupParts.gi";
connectAttr "bend2.og[1]" "bend3GroupParts1.ig";
connectAttr "bend3GroupId1.id" "bend3GroupParts1.gi";
connectAttr "bend2.og[2]" "bend3GroupParts2.ig";
connectAttr "bend3GroupId2.id" "bend3GroupParts2.gi";
connectAttr "bend3.og[0]" "groupParts14.ig";
connectAttr "groupId29.id" "groupParts14.gi";
connectAttr "groupParts14.og" "groupParts15.ig";
connectAttr "groupId30.id" "groupParts15.gi";
connectAttr "bend3.og[1]" "groupParts16.ig";
connectAttr "groupId31.id" "groupParts16.gi";
connectAttr "groupParts16.og" "groupParts17.ig";
connectAttr "groupId32.id" "groupParts17.gi";
connectAttr "groupParts17.og" "groupParts18.ig";
connectAttr "groupId33.id" "groupParts18.gi";
connectAttr "groupParts18.og" "groupParts19.ig";
connectAttr "groupId34.id" "groupParts19.gi";
connectAttr "bend3.og[2]" "groupParts20.ig";
connectAttr "groupId35.id" "groupParts20.gi";
connectAttr "gumSG.pa" ":renderPartition.st" -na;
connectAttr "teethSG.pa" ":renderPartition.st" -na;
connectAttr "gumLambert.msg" ":defaultShaderList1.s" -na;
connectAttr "teethLambert.msg" ":defaultShaderList1.s" -na;
connectAttr "defaultRenderLayer.msg" ":defaultRenderingList1.r" -na;
// End of headTopologyTeeth.ma
