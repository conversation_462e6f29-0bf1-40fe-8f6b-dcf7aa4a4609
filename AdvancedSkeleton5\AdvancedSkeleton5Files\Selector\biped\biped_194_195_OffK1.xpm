/* XPM */
static char *biped_194_195_OffK1[] = {
/* columns rows colors chars-per-pixel */
"23 23 33 1",
"  c #FF4444",
". c #FF4747",
"X c #FF4848",
"o c #FF4949",
"O c #FF4A4A",
"+ c #FF4C4C",
"@ c #FF5555",
"# c #FF5858",
"$ c #FF5959",
"% c #FF5A5A",
"& c #FF5B5B",
"* c #EA7F7F",
"= c #EB7F7F",
"- c #EC7F7F",
"; c #FF6060",
": c #FF6161",
"> c #FF6565",
", c #FF6B6B",
"< c #FF6C6C",
"1 c #F77777",
"2 c #F87676",
"3 c #F87777",
"4 c #F97676",
"5 c #FF7070",
"6 c gray75",
"7 c #E38484",
"8 c #E58383",
"9 c #E68383",
"0 c #E78383",
"q c #E48484",
"w c #E98080",
"e c #E98181",
"r c #EA8080",
/* pixels */
"66666666666666666666666",
"677788-3,>&@&>,2-887776",
"677qe3>@XXX..XX@>3e7776",
"67q=5&X.. . . ..+&5eq76",
"67=5&.           .&<=76",
"683&X             .&286",
"6->+.             .X>=6",
"63@.              ..@26",
"6<X                 X<6",
"6>X                 X>6",
"6&.                 X&6",
"6@.                 .&6",
"6&.                 X@6",
"6>X                 X>6",
"6,+.               .+<6",
"63@.               X@36",
"6=>+.             .X>=6",
"683&X.            X&3q6",
"67=<&X           X&5e76",
"677=<&X.   .   .X&<eqq6",
"677qe3>@+XX...X@>3eq776",
"6778qq-3<&&&&><3-887776",
"66666666666666666666666"
};
