import maya.cmds as cmds

def clone_pose_from_current_frame_to_selected_range():
    # 1. Obtener controlador seleccionado
    selection = cmds.ls(selection=True)
    if not selection:
        cmds.warning("Selecciona al menos un controlador.")
        return
    ctrl = selection[0]

    # 2. Obtener frame actual (pose fuente)
    source_frame = cmds.currentTime(query=True)

    # 3. Obtener atributos animables
    attrs = cmds.listAnimatable(ctrl) or []
    clean_attrs = [attr for attr in attrs if cmds.getAttr(attr, settable=True)]

    # 4. Guardar valores actuales en frame actual
    cmds.currentTime(source_frame, update=False)
    attr_values = {}
    for attr in clean_attrs:
        try:
            val = cmds.getAttr(attr)
            attr_values[attr] = val
        except:
            pass

    # 5. Obtener rango del TimeSlider
    playback_slider = 'timeControl1'
    time_range = cmds.timeControl(playback_slider, query=True, rangeArray=True)
    if not time_range:
        cmds.warning("No hay rango seleccionado en el TimeSlider.")
        return

    start, end = int(time_range[0]), int(time_range[1])

    # Determinar dirección
    step = 1 if start <= end else -1

    # 6. Iniciar bloque de undo
    cmds.undoInfo(openChunk=True)
    
    try:
        for frame in range(start, end + step, step):
            if frame == source_frame:
                continue  # Evita volver a escribir en el frame original

            cmds.currentTime(frame, update=False)
            cmds.timeControl(playback_slider, edit=True, beginScrub=True)

            for attr, val in attr_values.items():
                try:
                    cmds.setAttr(attr, val)
                except:
                    pass

            cmds.setKeyframe(ctrl)

            cmds.timeControl(playback_slider, edit=True, endScrub=True)

        print(f"Pose desde frame {source_frame} clonada a frames del rango {start}-{end}.")
    finally:
        cmds.undoInfo(closeChunk=True)

clone_pose_from_current_frame_to_selected_range()
