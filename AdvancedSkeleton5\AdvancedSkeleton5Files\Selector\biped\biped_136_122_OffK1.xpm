/* XPM */
static char *biped_136_122_OffK1[] = {
/* columns rows colors chars-per-pixel */
"48 18 68 1",
"  c #D07C7C",
". c #D07D7C",
"X c #D17C7C",
"o c #D17D7D",
"O c #D27D7D",
"+ c #D37D7D",
"@ c #D37E7E",
"# c #D47E7E",
"$ c #D67E7E",
"% c #D67E7F",
"& c #D67F7F",
"* c #D77F7F",
"= c gray75",
"- c #D98080",
"; c #DA8080",
": c #DB8080",
"> c #DB8181",
", c #DC8181",
"< c #DD8181",
"1 c #DE8281",
"2 c #DE8282",
"3 c #DF8282",
"4 c #E08383",
"5 c #E18383",
"6 c #E28383",
"7 c #E28484",
"8 c #E38484",
"9 c #E48484",
"0 c #E58585",
"q c #E68585",
"w c #E78686",
"e c #E98686",
"r c #E98787",
"t c #F18A89",
"y c #F28A8A",
"u c #F38A8A",
"i c #F58B8B",
"p c #FF8F8F",
"a c #FF9090",
"s c #FF9191",
"d c #FF9494",
"f c #FF9796",
"g c #FF9797",
"h c #FF9899",
"j c #FF9999",
"k c #FF9B9C",
"l c #FF9C9C",
"z c #FF9D9D",
"x c #FF9F9F",
"c c #FFA0A0",
"v c #FFA0A1",
"b c #FFA1A1",
"n c #FFA2A2",
"m c #FFA3A3",
"M c #FFA3A4",
"N c #FFA4A4",
"B c #FFA4A5",
"V c #FFA5A4",
"C c #FFA5A5",
"Z c #FFA6A5",
"A c #FFA6A6",
"S c #FFA7A7",
"D c #FFA8A7",
"F c #FFA8A8",
"G c #FFA9A8",
"H c #FFA9A9",
"J c #FFAAA9",
"K c #FFAAAA",
/* pixels */
"================================================",
"=888,wvKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKc91w44=",
"=888*uvKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKvu-999=",
"=888+pCKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKCp 4w4=",
"=888+dCKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKDd.449=",
"=888.hKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKDh+4w4=",
"=888+zKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKl*994=",
"=884-zKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKz-49w=",
"=88,8vKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKV4,w9=",
"=84-uVKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKvu*49=",
"=ww+sCKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKCp+49=",
"=46.gCKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKDg 94=",
"=68+kKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKl 49=",
"=48*zKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKc*49=",
"=66,vKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKv46w=",
"=w-eCKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKVr-4=",
"=4*iCKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKCi*4=",
"================================================"
};
