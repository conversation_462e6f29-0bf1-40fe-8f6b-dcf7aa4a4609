import os, time
import maya.cmds as cmds
import maya.mel as mel

WIN = 'fbxExportWin'
PBAR = 'fbxProgressBar'
STAT = 'fbxStatusText'

def _ensure_fbx():
    """Asegura que el plugin FBX esté cargado"""
    if not cmds.pluginInfo("fbxmaya", q=True, l=True):
        cmds.loadPlugin("fbxmaya")

def _scene_paths():
    """Obtiene las rutas de la escena actual y el archivo FBX de destino"""
    sp = cmds.file(q=True, sn=True)
    if not sp:
        cmds.error("Guarda la escena antes de exportar.")
    base, _ = os.path.splitext(sp)
    return sp, base + ".fbx"

def _timeline():
    """Obtiene el rango de tiempo de la timeline"""
    s = int(cmds.playbackOptions(q=True, minTime=True))
    e = int(cmds.playbackOptions(q=True, maxTime=True))
    return s, e

def _ui_open(msg="Iniciando..."):
    """Abre la ventana de progreso"""
    if cmds.window(WIN, exists=True):
        cmds.deleteUI(WIN)
    cmds.window(WIN, title='Exportación FBX Automática', widthHeight=(400, 120), toolbox=True)
    cmds.columnLayout(adj=True)
    cmds.text(STAT, label=msg, h=20)
    cmds.separator(h=8, style='in')
    cmds.progressBar(PBAR, maxValue=100, h=20)
    cmds.showWindow(WIN)
    cmds.refresh(); time.sleep(0.05)

def _ui_update(pct=None, msg=None):
    """Actualiza la interfaz de progreso"""
    if msg: cmds.text(STAT, e=True, label=msg)
    if pct is not None: cmds.progressBar(PBAR, e=True, progress=pct)
    cmds.refresh(); time.sleep(0.02)

def _ui_close():
    """Cierra la ventana de progreso"""
    if cmds.window(WIN, exists=True):
        cmds.deleteUI(WIN)

def _get_all_joints():
    """Obtiene todos los joints de la escena"""
    return cmds.ls(type="joint") or []

def _get_all_meshes():
    """Obtiene todas las mallas de la escena que tienen skinning"""
    all_meshes = cmds.ls(type="mesh")
    skinned_meshes = []

    for mesh in all_meshes:
        # Obtener el transform parent
        transforms = cmds.listRelatives(mesh, parent=True, type="transform") or []
        for transform in transforms:
            # Verificar si tiene skin cluster
            history = cmds.listHistory(transform, pruneDagObjects=True) or []
            skin_clusters = cmds.ls(history, type="skinCluster")
            if skin_clusters:
                skinned_meshes.append(transform)
                break

    return skinned_meshes

def _clean_mesh_names():
    """Limpia los nombres de las mallas para evitar problemas en Unreal"""
    all_transforms = cmds.ls(type="transform")
    renamed_count = 0

    for transform in all_transforms:
        # Verificar si tiene una mesh como child
        shapes = cmds.listRelatives(transform, shapes=True, type="mesh") or []
        if not shapes:
            continue

        # Obtener el nombre actual sin path
        current_name = transform.split("|")[-1]

        # Verificar si necesita renombrado
        needs_rename = (
            not current_name or
            current_name.lower() in ["geometry", "polysurface"] or
            current_name.startswith("polySurface") or
            current_name.startswith("pCube") or
            current_name.startswith("pSphere") or
            ":" in current_name and current_name.split(":")[-1].lower() in ["geometry", "body", "mesh"]
        )

        if needs_rename:
            # Crear un nombre limpio y único
            new_name = f"PenguinMesh_{renamed_count:02d}"
            try:
                # Verificar que el nombre no exista
                counter = 0
                test_name = new_name
                while cmds.objExists(test_name):
                    test_name = f"PenguinMesh_{renamed_count:02d}_{counter}"
                    counter += 1

                cmds.rename(transform, test_name)
                print(f"Mesh renombrada: {current_name} → {test_name}")
                renamed_count += 1
            except Exception as e:
                print(f"Error renombrando mesh {current_name}: {e}")

    return renamed_count

def _get_penguin_bone_mapping():
    """Mapeo exacto para el esqueleto SK_Penguin_Skeleton basado en las imágenes proporcionadas"""
    return {
        # ✅ MAPEO EXACTO MAYA → UNREAL
        # Basado en la comparación de jerarquías mostradas

        # Columna vertebral (ya coinciden)
        "root": "root",
        "pelvis": "pelvis",
        "spine_01": "spine_01",
        "spine_02": "spine_02",
        "spine_03": "spine_03",
        "neck_01": "neck_01",
        "head": "head",

        # Cabeza y cara
        "HeadEnd": "HeadEnd",  # Mantener como está
        "Eye_l": "Eye_l",
        "EyeEnd_l": "EyeEnd_l",
        "Eye_r": "Eye_r",
        "EyeEnd_r": "EyeEnd_r",
        "Jaw": "Jaw",
        "JawEnd": "JawEnd",
        "Beak_up": "Beak_up",
        "BeakEnd_up": "BeakEnd_up",

        # Brazos izquierdo (ya coinciden perfectamente)
        "clavicle_l": "clavicle_l",
        "upperarm_l": "upperarm_l",
        "upperarm_twist_01_l": "upperarm_twist_01_l",
        "lowerarm_l": "lowerarm_l",
        "lowerarm_twist_01_l": "lowerarm_twist_01_l",
        "hand_l": "hand_l",
        "middle_01_l": "middle_01_l",
        "middle_02_l": "middle_02_l",
        "middle_03_l": "middle_03_l",
        "MiddleFinger4_l": "MiddleFinger4_l",

        # Brazos derecho (ya coinciden perfectamente)
        "clavicle_r": "clavicle_r",
        "upperarm_r": "upperarm_r",
        "upperarm_twist_01_r": "upperarm_twist_01_r",
        "lowerarm_r": "lowerarm_r",
        "lowerarm_twist_01_r": "lowerarm_twist_01_r",
        "hand_r": "hand_r",
        "middle_01_r": "middle_01_r",
        "middle_02_r": "middle_02_r",
        "middle_03_r": "middle_03_r",
        "MiddleFinger4_r": "MiddleFinger4_r",

        # Piernas izquierda (ya coinciden perfectamente)
        "thigh_l": "thigh_l",
        "thigh_twist_01_l": "thigh_twist_01_l",
        "calf_l": "calf_l",
        "calf_twist_01_l": "calf_twist_01_l",
        "foot_l": "foot_l",
        "ball_l": "ball_l",
        "ToesEnd_l": "ToesEnd_l",

        # Piernas derecha (ya coinciden perfectamente)
        "thigh_r": "thigh_r",
        "thigh_twist_01_r": "thigh_twist_01_r",
        "calf_r": "calf_r",
        "calf_twist_01_r": "calf_twist_01_r",
        "foot_r": "foot_r",
        "ball_r": "ball_r",
        "ToesEnd_r": "ToesEnd_r",
    }

def _clean_joint_names():
    """Limpia nombres de joints para evitar duplicados y conflictos"""
    joints = cmds.ls(type="joint")
    renamed_count = 0

    # Usar mapeo específico para pingüino
    name_mapping = _get_penguin_bone_mapping()

    # Mapeo adicional de Advanced Skeleton a nombres estándar
    advanced_skeleton_mapping = {
        # Columna vertebral
        "Root_M": "root",
        "RootX_M": "pelvis",
        "Spine1_M": "spine_01",
        "Spine2_M": "spine_02",
        "Chest_M": "spine_03",
        "Neck_M": "neck_01",
        "Head_M": "head",
        "HeadEnd_M": "head_end",

        # Brazo izquierdo
        "Scapula_L": "clavicle_l",
        "Shoulder_L": "upperarm_l",
        "Elbow_L": "lowerarm_l",
        "Wrist_L": "hand_l",

        # Brazo derecho
        "Scapula_R": "clavicle_r",
        "Shoulder_R": "upperarm_r",
        "Elbow_R": "lowerarm_r",
        "Wrist_R": "hand_r",

        # Pierna izquierda
        "Hip_L": "thigh_l",
        "Knee_L": "calf_l",
        "Ankle_L": "foot_l",
        "Toes_L": "ball_l",

        # Pierna derecha
        "Hip_R": "thigh_r",
        "Knee_R": "calf_r",
        "Ankle_R": "foot_r",
        "Toes_R": "ball_r",
    }

    # Combinar ambos mapeos
    name_mapping.update(advanced_skeleton_mapping)

    # Aplicar mapeo de nombres
    for old_name, new_name in name_mapping.items():
        if cmds.objExists(old_name):
            try:
                # Verificar que el nuevo nombre no exista ya
                if not cmds.objExists(new_name):
                    cmds.rename(old_name, new_name)
                    print(f"Joint renombrado: {old_name} → {new_name}")
                    renamed_count += 1
            except:
                pass

    # Limpiar nombres problemáticos
    for joint in joints:
        current_name = joint.split("|")[-1]

        # Remover namespace si existe
        if ":" in current_name:
            clean_name = current_name.split(":")[-1]
            try:
                if not cmds.objExists(clean_name):
                    cmds.rename(joint, clean_name)
                    print(f"Namespace removido: {current_name} → {clean_name}")
                    renamed_count += 1
            except:
                pass

    return renamed_count

def _remove_unsupported_bones():
    """Elimina huesos que NO existen en SK_Penguin_Skeleton para evitar errores en Unreal"""

    # Lista exacta de huesos que aparecen en el error de Unreal
    unsupported_bones = [
        "Spine1", "Spine2", "Chest", "Neck", "Eye", "EyeEnd",
        "Scapula", "Shoulder", "Elbow", "Wrist",
        "MiddleFinger1", "MiddleFinger2", "MiddleFinger3", "MiddleFinger4",
        "Hip", "Knee", "Ankle", "Heel", "Toes", "ToesEnd"
    ]

    removed_count = 0
    all_joints = cmds.ls(type="joint")

    for joint in all_joints:
        joint_name = joint.split("|")[-1]  # Obtener solo el nombre sin path
        joint_name_clean = joint_name.split(":")[-1] if ":" in joint_name else joint_name  # Sin namespace

        # Verificar si este joint está en la lista de no soportados
        if joint_name_clean in unsupported_bones:
            try:
                # ELIMINAR completamente el joint para que no aparezca en el FBX
                print(f"🗑️ Eliminando hueso no soportado: {joint}")

                # Primero desconectar de padres/hijos si es necesario
                children = cmds.listRelatives(joint, children=True, type="joint") or []
                parent = cmds.listRelatives(joint, parent=True, type="joint")

                # Si tiene hijos, reconectarlos al padre
                if children and parent:
                    for child in children:
                        cmds.parent(child, parent[0])
                        print(f"  ↳ Reconectado {child} a {parent[0]}")
                elif children and not parent:
                    # Si no tiene padre, hacer los hijos independientes
                    for child in children:
                        cmds.parent(child, world=True)
                        print(f"  ↳ {child} ahora es independiente")

                # Eliminar el joint
                cmds.delete(joint)
                removed_count += 1

            except Exception as e:
                print(f"❌ Error eliminando {joint}: {e}")
                # Si no se puede eliminar, renombrar para ignorar
                try:
                    new_name = f"IGNORE_{joint_name_clean}_{removed_count}"
                    cmds.rename(joint, new_name)
                    print(f"  ↳ Renombrado a: {new_name}")
                    removed_count += 1
                except:
                    pass

    return removed_count

def _clean_duplicates_and_prepare_for_export():
    """Limpia duplicados y prepara la escena para exportación limpia"""
    cleaned_count = 0

    # 1. Eliminar objetos duplicados o innecesarios
    all_objects = cmds.ls(dag=True, long=True)

    for obj in all_objects:
        obj_name = obj.split("|")[-1]

        # Eliminar objetos con nombres problemáticos
        if (obj_name.endswith("_IGNORE") or
            obj_name.startswith("IGNORE_") or
            obj_name.endswith("Shape1") or  # Shapes duplicadas
            obj_name.endswith("Orig")):     # Objetos originales

            try:
                if cmds.objExists(obj):
                    cmds.delete(obj)
                    print(f"🗑️ Eliminado objeto duplicado: {obj_name}")
                    cleaned_count += 1
            except:
                pass

    # 2. Limpiar historia de construcción en meshes
    meshes = cmds.ls(type="mesh")
    for mesh in meshes:
        transforms = cmds.listRelatives(mesh, parent=True, type="transform") or []
        for transform in transforms:
            try:
                # Eliminar historia no deformadora
                cmds.bakePartialHistory(transform, prePostDeformers=True)
            except:
                pass

    return cleaned_count

def _get_controllers():
    """Busca controladores comunes en la escena"""
    controllers = []

    # Patrones comunes de controladores
    controller_patterns = [
        "*_ctrl", "*_control", "*_con", "*Ctrl*", "*Control*",
        "*:*_ctrl", "*:*_control", "*:*_con", "*:*Ctrl*", "*:*Control*"
    ]

    for pattern in controller_patterns:
        found = cmds.ls(pattern, type="transform") or []
        controllers.extend(found)

    # Remover duplicados
    controllers = list(set(controllers))

    # Filtrar solo los que tienen keyframes
    animated_controllers = []
    for ctrl in controllers:
        if cmds.keyframe(ctrl, query=True, keyframeCount=True):
            animated_controllers.append(ctrl)

    return animated_controllers

def _bake_controllers_to_joints(controllers, joints, start, end):
    """Bakea la animación de controladores a joints"""
    if not controllers or not joints:
        return

    # Bakear controladores primero
    try:
        cmds.bakeResults(
            controllers,
            time=(start, end),
            sampleBy=1,
            simulation=True,
            preserveOutsideKeys=True,
            sparseAnimCurveBake=False,
            removeBakedAttributeFromLayer=False,
            bakeOnOverrideLayer=False,
            controlPoints=False,
            shape=False
        )
    except:
        pass  # Algunos controladores pueden no ser bakeables

    # Luego bakear joints
    try:
        cmds.bakeResults(
            joints,
            time=(start, end),
            sampleBy=1,
            simulation=True,
            preserveOutsideKeys=True,
            sparseAnimCurveBake=False,
            removeBakedAttributeFromLayer=False,
            bakeOnOverrideLayer=False,
            controlPoints=False,
            shape=False
        )
    except:
        pass

def _safe_fbx_command(command):
    """Ejecuta un comando FBX de forma segura, ignorando si no existe"""
    try:
        mel.eval(command)
        return True
    except:
        print(f"⚠️ Comando FBX no disponible: {command}")
        return False

def _configure_fbx_export(start, end, export_mesh=True):
    """Configura las opciones de exportación FBX optimizadas para Unreal (compatible con todas las versiones)"""

    # Reset básico
    _safe_fbx_command('FBXResetExport;')

    # Configuración básica (siempre disponible)
    _safe_fbx_command('FBXExportCameras -v false;')
    _safe_fbx_command('FBXExportLights -v false;')
    _safe_fbx_command('FBXExportConstraints -v false;')
    _safe_fbx_command('FBXExportInputConnections -v false;')

    # Configuración de geometría
    if export_mesh:
        _safe_fbx_command('FBXExportSmoothingGroups -v true;')
        _safe_fbx_command('FBXExportHardEdges -v false;')
        _safe_fbx_command('FBXExportTangents -v false;')
        _safe_fbx_command('FBXExportSmoothMesh -v true;')
        _safe_fbx_command('FBXExportInstances -v false;')
        _safe_fbx_command('FBXExportReferencedAssetsContent -v true;')
        # Configuración específica para Unreal
        _safe_fbx_command('FBXExportTriangulate -v true;')  # Triangular para Unreal
        _safe_fbx_command('FBXExportEmbeddedTextures -v false;')  # No embeber texturas

    # Configuración de skinning (crítico para animación)
    _safe_fbx_command('FBXExportSkins -v true;')
    _safe_fbx_command('FBXExportShapes -v false;')  # Blend shapes off por defecto

    # Configuración de animación optimizada para Unreal
    _safe_fbx_command('FBXExportBakeComplexAnimation -v true;')
    _safe_fbx_command(f'FBXExportBakeComplexStart -v {start};')
    _safe_fbx_command(f'FBXExportBakeComplexEnd -v {end};')
    _safe_fbx_command('FBXExportBakeComplexStep -v 1;')
    _safe_fbx_command('FBXExportBakeResampleAnimation -v true;')

    # Configuración de rotaciones - importante para Unreal
    _safe_fbx_command('FBXExportQuaternion -v resample;')
    _safe_fbx_command('FBXExportUpAxis y;')  # Unreal usa Y-up

    # Configuración de unidades - Unreal usa centímetros
    _safe_fbx_command('FBXExportScaleFactor 1.0;')
    _safe_fbx_command('FBXExportConvertUnitString cm;')

    # Configuraciones adicionales (opcionales)
    _safe_fbx_command('FBXExportGenerateLog -v true;')  # Generar log para debug
    _safe_fbx_command('FBXExportSplitAnimationIntoTakes -v false;')  # Una sola toma
    _safe_fbx_command('FBXExportUseSceneName -v false;')  # No usar nombre de escena
    _safe_fbx_command('FBXExportReferencedContainersContent -v false;')  # No contenedores referenciados

    print("✅ Configuración FBX aplicada (comandos compatibles)")

def _configure_fbx_export_basic(start, end):
    """Configuración FBX básica y segura para todas las versiones de Maya"""
    try:
        # Solo comandos básicos que siempre funcionan
        mel.eval('FBXResetExport;')
        mel.eval('FBXExportCameras -v false;')
        mel.eval('FBXExportLights -v false;')
        mel.eval('FBXExportSkins -v true;')
        mel.eval('FBXExportBakeComplexAnimation -v true;')
        mel.eval(f'FBXExportBakeComplexStart -v {start};')
        mel.eval(f'FBXExportBakeComplexEnd -v {end};')
        mel.eval('FBXExportBakeComplexStep -v 1;')
        mel.eval('FBXExportUpAxis y;')
        print("✅ Configuración FBX básica aplicada")
        return True
    except Exception as e:
        print(f"❌ Error en configuración FBX básica: {e}")
        return False

def export_animation_auto(export_mesh=True, export_controllers=True):
    """
    Exporta animación automáticamente detectando controladores y joints

    Args:
        export_mesh (bool): Si exportar también la geometría
        export_controllers (bool): Si bakear controladores a joints
    """
    _ui_open('⏳ Iniciando exportación automática...')

    try:
        # 1. Preparación inicial
        _ensure_fbx()
        scene_path, fbx_path = _scene_paths()
        start, end = _timeline()

        _ui_update(10, '🔍 Detectando elementos de la escena...')

        # 2. Detectar elementos
        all_joints = _get_all_joints()
        controllers = _get_controllers() if export_controllers else []
        meshes = _get_all_meshes() if export_mesh else []

        if not all_joints:
            cmds.error("No se encontraron joints en la escena.")

        print(f"Encontrados: {len(all_joints)} joints, {len(controllers)} controladores, {len(meshes)} meshes")

        _ui_update(15, '🧹 Limpiando nombres para Unreal...')

        # 3. Limpiar nombres para evitar conflictos en Unreal
        joints_renamed = _clean_joint_names()
        meshes_renamed = _clean_mesh_names() if export_mesh else 0

        _ui_update(20, '🚫 Eliminando huesos no soportados...')

        # 4. Eliminar/renombrar huesos que no existen en SK_Penguin_Skeleton
        bones_removed = _remove_unsupported_bones()

        _ui_update(22, '🧹 Limpiando duplicados...')

        # 5. Limpiar duplicados y preparar para exportación
        duplicates_cleaned = _clean_duplicates_and_prepare_for_export()

        print(f"Limpieza completada: {joints_renamed} joints renombrados, {meshes_renamed} meshes renombrados, {bones_removed} huesos eliminados, {duplicates_cleaned} duplicados limpiados")

        # Actualizar listas después de toda la limpieza
        all_joints = _get_all_joints()
        meshes = _get_all_meshes() if export_mesh else []

        _ui_update(25, f'🎛️ Bakeando animación ({len(controllers)} controladores → {len(all_joints)} joints)...')

        # 3. Bakear animación
        if controllers and all_joints:
            _bake_controllers_to_joints(controllers, all_joints, start, end)
        elif all_joints:
            # Solo bakear joints si no hay controladores
            try:
                cmds.bakeResults(
                    all_joints,
                    time=(start, end),
                    sampleBy=1,
                    simulation=True,
                    preserveOutsideKeys=True
                )
            except:
                pass

        _ui_update(50, '📦 Preparando selección para exportar...')

        # 4. Seleccionar elementos para exportar
        export_selection = []
        export_selection.extend(all_joints)

        if export_mesh and meshes:
            export_selection.extend(meshes)

        if not export_selection:
            cmds.error("No hay elementos válidos para exportar.")

        cmds.select(export_selection, replace=True)

        _ui_update(70, '⚙️ Configurando opciones FBX...')

        # 5. Configurar FBX (con fallback a configuración básica)
        try:
            _configure_fbx_export(start, end, export_mesh)
        except Exception as e:
            print(f"⚠️ Configuración avanzada falló, usando básica: {e}")
            _configure_fbx_export_basic(start, end)

        _ui_update(85, '💾 Exportando archivo FBX...')

        # 6. Exportar
        mel.eval('FBXExport -f "{}" -s;'.format(fbx_path.replace('\\', '/')))

        _ui_update(100, '✅ Exportación completada')

        # 7. Mensaje final
        total_cleaned = joints_renamed + meshes_renamed + bones_removed + duplicates_cleaned
        cmds.inViewMessage(
            amg=f'<hl>FBX exportado para Unreal</hl><br>📁 {os.path.basename(fbx_path)}<br>🎬 Frames: {start}-{end}<br>🦴 Joints: {len(all_joints)}<br>🎮 Controladores: {len(controllers)}<br>📐 Meshes: {len(meshes)}<br>🧹 Total limpiado: {total_cleaned}<br>🚫 Huesos eliminados: {bones_removed}<br>🗑️ Duplicados: {duplicates_cleaned}',
            pos='midCenter',
            fade=True,
            fadeStayTime=8
        )

        # Generar resumen final para debug
        debug_summary = f"""
=== RESUMEN FINAL DE EXPORTACIÓN ===
✅ Archivo exportado: {fbx_path}
🎬 Frames: {start}-{end}
🦴 Joints finales: {len(all_joints)}
🎮 Controladores procesados: {len(controllers)}
📐 Meshes exportadas: {len(meshes)}

🧹 LIMPIEZA REALIZADA:
  • {joints_renamed} joints renombrados
  • {meshes_renamed} meshes renombrados
  • {bones_removed} huesos no soportados eliminados
  • {duplicates_cleaned} duplicados limpiados
  • Total elementos procesados: {total_cleaned}

💡 El FBX debería importarse sin errores en Unreal Engine
====================================="""

        print(debug_summary)

        # Guardar debug en variable global para acceso posterior
        global _last_export_debug
        _last_export_debug = debug_summary

        return fbx_path

    except Exception as e:
        _ui_update(0, f'❌ Error: {str(e)}')
        cmds.error(f"Error durante la exportación: {str(e)}")

    finally:
        _ui_close()

# Función de compatibilidad con el script anterior
def export_anim_AS(expected_root_name='root', force_root_keys=True):
    """Función de compatibilidad - usa la nueva función automática"""
    return export_animation_auto(export_mesh=True, export_controllers=True)

def analyze_scene_for_unreal():
    """Analiza la escena y genera un reporte para ayudar con el mapeo de huesos"""
    print("\n" + "="*60)
    print("🔍 ANÁLISIS DE ESCENA PARA UNREAL ENGINE")
    print("="*60)

    # Analizar joints
    joints = _get_all_joints()
    print(f"\n🦴 JOINTS ENCONTRADOS ({len(joints)}):")
    for joint in sorted(joints):
        print(f"  • {joint}")

    # Analizar controladores
    controllers = _get_controllers()
    print(f"\n🎮 CONTROLADORES ANIMADOS ({len(controllers)}):")
    for ctrl in sorted(controllers):
        keyframes = cmds.keyframe(ctrl, query=True, keyframeCount=True) or 0
        print(f"  • {ctrl} ({keyframes} keyframes)")

    # Analizar meshes
    meshes = _get_all_meshes()
    print(f"\n📐 MESHES CON SKINNING ({len(meshes)}):")
    for mesh in meshes:
        print(f"  • {mesh}")

    # Sugerencias para mapeo
    print(f"\n💡 SUGERENCIAS PARA UNREAL:")
    print("1. Verifica que los nombres de joints coincidan con tu SK_Penguin_Skeleton")
    print("2. Los joints que fallan en Unreal necesitan ser mapeados o eliminados")
    print("3. Usa la función _get_penguin_bone_mapping() para personalizar el mapeo")

    # Generar archivo de configuración
    config_path = os.path.join(os.path.dirname(_scene_paths()[0]), "bone_mapping_config.txt")
    try:
        with open(config_path, 'w') as f:
            f.write("# CONFIGURACIÓN DE MAPEO DE HUESOS PARA UNREAL\n")
            f.write("# Edita este archivo para personalizar el mapeo\n\n")
            f.write("# Joints encontrados en Maya:\n")
            for joint in sorted(joints):
                f.write(f"# {joint}\n")
            f.write("\n# Mapeo sugerido (Maya -> Unreal):\n")
            mapping = _get_penguin_bone_mapping()
            for maya_bone, unreal_bone in mapping.items():
                f.write(f'"{maya_bone}": "{unreal_bone}",\n')
        print(f"\n📄 Archivo de configuración creado: {config_path}")
    except:
        print("❌ No se pudo crear el archivo de configuración")

    print("="*60)

# Función para crear botón en shelf
def create_shelf_button():
    """Crea un botón en el shelf actual para la exportación automática"""
    try:
        # Obtener el shelf actual
        current_shelf = cmds.tabLayout('ShelfLayout', query=True, selectTab=True)
        cmds.setParent(current_shelf)

        # Crear el botón principal
        cmds.shelfButton(
            command='import as_fbx_exporter; as_fbx_exporter.export_animation_auto()',
            annotation='Exportar Animación FBX para Unreal Engine',
            label='FBX\nUnreal',
            image='out_time.png',
            sourceType='python'
        )

        # Crear botón de análisis
        cmds.shelfButton(
            command='import as_fbx_exporter; as_fbx_exporter.analyze_scene_for_unreal()',
            annotation='Analizar escena para mapeo de huesos',
            label='Analyze\nBones',
            image='nodeGrapherBookmarksList.png',
            sourceType='python'
        )

        print("✅ Botones creados en el shelf:")
        print("  • 'FBX Unreal' - Exportar animación")
        print("  • 'Analyze Bones' - Analizar escena")

    except Exception as e:
        print(f"❌ Error creando botones: {str(e)}")

def check_fbx_commands():
    """Verifica qué comandos FBX están disponibles en esta versión de Maya"""
    print("\n" + "="*60)
    print("🔍 VERIFICACIÓN DE COMANDOS FBX DISPONIBLES")
    print("="*60)

    # Lista de comandos FBX comunes
    fbx_commands = [
        'FBXResetExport',
        'FBXExportCameras -v false',
        'FBXExportLights -v false',
        'FBXExportConstraints -v false',
        'FBXExportSkins -v true',
        'FBXExportShapes -v false',
        'FBXExportDeformers -v true',  # Este es el que falla
        'FBXExportBakeComplexAnimation -v true',
        'FBXExportBakeResampleAnimation -v true',
        'FBXExportQuaternion -v resample',
        'FBXExportUpAxis y',
        'FBXExportTriangulate -v true',
        'FBXExportSmoothingGroups -v true'
    ]

    available = []
    unavailable = []

    for cmd in fbx_commands:
        if _safe_fbx_command(cmd):
            available.append(cmd)
        else:
            unavailable.append(cmd)

    print(f"\n✅ COMANDOS DISPONIBLES ({len(available)}):")
    for cmd in available:
        print(f"  • {cmd}")

    print(f"\n❌ COMANDOS NO DISPONIBLES ({len(unavailable)}):")
    for cmd in unavailable:
        print(f"  • {cmd}")

    print(f"\n📊 RESUMEN: {len(available)}/{len(fbx_commands)} comandos disponibles")
    print("="*60)

def test_export_with_debug():
    """Función de prueba con debug detallado"""
    print("\n" + "="*60)
    print("🧪 PRUEBA DE EXPORTACIÓN CON DEBUG")
    print("="*60)

    try:
        # Verificar comandos FBX primero
        print("\n0️⃣ VERIFICACIÓN FBX:")
        check_fbx_commands()

        # Análisis previo
        print("\n1️⃣ ANÁLISIS PREVIO:")
        joints_before = _get_all_joints()
        meshes_before = _get_all_meshes()
        print(f"   Joints: {len(joints_before)}")
        print(f"   Meshes: {len(meshes_before)}")

        # Limpieza paso a paso
        print("\n2️⃣ LIMPIEZA DE NOMBRES:")
        joints_renamed = _clean_joint_names()
        print(f"   Joints renombrados: {joints_renamed}")

        meshes_renamed = _clean_mesh_names()
        print(f"   Meshes renombrados: {meshes_renamed}")

        print("\n3️⃣ ELIMINACIÓN DE HUESOS NO SOPORTADOS:")
        bones_removed = _remove_unsupported_bones()
        print(f"   Huesos eliminados: {bones_removed}")

        print("\n4️⃣ LIMPIEZA DE DUPLICADOS:")
        duplicates_cleaned = _clean_duplicates_and_prepare_for_export()
        print(f"   Duplicados limpiados: {duplicates_cleaned}")

        # Análisis posterior
        print("\n5️⃣ ANÁLISIS POSTERIOR:")
        joints_after = _get_all_joints()
        meshes_after = _get_all_meshes()
        print(f"   Joints finales: {len(joints_after)}")
        print(f"   Meshes finales: {len(meshes_after)}")

        # Exportar
        print("\n6️⃣ EXPORTANDO...")
        result = export_animation_auto(export_mesh=True, export_controllers=True)

        print(f"\n✅ PRUEBA COMPLETADA: {result}")

    except Exception as e:
        print(f"\n❌ ERROR EN PRUEBA: {e}")
        import traceback
        traceback.print_exc()

def get_debug_summary():
    """Obtiene el resumen de debug de la última exportación"""
    global _last_export_debug
    if '_last_export_debug' in globals():
        return _last_export_debug
    else:
        return "No hay información de debug disponible. Ejecuta una exportación primero."

def print_all_debug():
    """Imprime todo el debug importante al final para copiar fácilmente"""
    print("\n" + "="*80)
    print("🔍 DEBUG COMPLETO PARA COPIAR AL CHAT")
    print("="*80)

    # 1. Información de la escena
    print("\n📊 INFORMACIÓN DE LA ESCENA:")
    joints = _get_all_joints()
    controllers = _get_controllers()
    meshes = _get_all_meshes()
    print(f"Joints encontrados: {len(joints)}")
    print(f"Controladores animados: {len(controllers)}")
    print(f"Meshes con skinning: {len(meshes)}")

    # 2. Lista de joints
    print(f"\n🦴 LISTA DE JOINTS ({len(joints)}):")
    for i, joint in enumerate(sorted(joints)[:20]):  # Solo primeros 20
        print(f"  {i+1:2d}. {joint}")
    if len(joints) > 20:
        print(f"  ... y {len(joints)-20} más")

    # 3. Lista de controladores
    print(f"\n🎮 CONTROLADORES ANIMADOS ({len(controllers)}):")
    for i, ctrl in enumerate(sorted(controllers)[:10]):  # Solo primeros 10
        keyframes = cmds.keyframe(ctrl, query=True, keyframeCount=True) or 0
        print(f"  {i+1:2d}. {ctrl} ({keyframes} keyframes)")
    if len(controllers) > 10:
        print(f"  ... y {len(controllers)-10} más")

    # 4. Lista de meshes
    print(f"\n📐 MESHES CON SKINNING ({len(meshes)}):")
    for i, mesh in enumerate(meshes):
        print(f"  {i+1:2d}. {mesh}")

    # 5. Verificación de comandos FBX
    print(f"\n🔧 COMANDOS FBX:")
    fbx_test_commands = [
        'FBXResetExport',
        'FBXExportSkins -v true',
        'FBXExportBakeComplexAnimation -v true',
        'FBXExportDeformers -v true'  # El problemático
    ]

    for cmd in fbx_test_commands:
        status = "✅" if _safe_fbx_command(cmd) else "❌"
        print(f"  {status} {cmd}")

    # 6. Resumen de última exportación
    print(f"\n📋 ÚLTIMA EXPORTACIÓN:")
    print(get_debug_summary())

    # 7. Timeline info
    try:
        start, end = _timeline()
        print(f"\n⏰ TIMELINE: Frames {start} a {end} ({end-start+1} frames)")
    except:
        print(f"\n⏰ TIMELINE: No disponible")

    print("\n" + "="*80)
    print("📝 COPIA TODO ESTE DEBUG AL CHAT PARA ANÁLISIS")
    print("="*80)

if __name__ == "__main__":
    # Si se ejecuta directamente, ejecutar prueba con debug y mostrar resumen
    test_export_with_debug()
    print_all_debug()
