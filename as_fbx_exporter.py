import os, time
import maya.cmds as cmds
import maya.mel as mel

WIN = 'fbxExportWin'
PBAR = 'fbxProgressBar'
STAT = 'fbxStatusText'

def _ensure_fbx():
    """Asegura que el plugin FBX esté cargado"""
    if not cmds.pluginInfo("fbxmaya", q=True, l=True):
        cmds.loadPlugin("fbxmaya")

def _scene_paths():
    """Obtiene las rutas de la escena actual y el archivo FBX de destino"""
    sp = cmds.file(q=True, sn=True)
    if not sp:
        cmds.error("Guarda la escena antes de exportar.")
    base, _ = os.path.splitext(sp)
    return sp, base + ".fbx"

def _timeline():
    """Obtiene el rango de tiempo de la timeline"""
    s = int(cmds.playbackOptions(q=True, minTime=True))
    e = int(cmds.playbackOptions(q=True, maxTime=True))
    return s, e

def _ui_open(msg="Iniciando..."):
    """Abre la ventana de progreso"""
    if cmds.window(WIN, exists=True):
        cmds.deleteUI(WIN)
    cmds.window(WIN, title='Exportación FBX Automática', widthHeight=(400, 120), toolbox=True)
    cmds.columnLayout(adj=True)
    cmds.text(STAT, label=msg, h=20)
    cmds.separator(h=8, style='in')
    cmds.progressBar(PBAR, maxValue=100, h=20)
    cmds.showWindow(WIN)
    cmds.refresh(); time.sleep(0.05)

def _ui_update(pct=None, msg=None):
    """Actualiza la interfaz de progreso"""
    if msg: cmds.text(STAT, e=True, label=msg)
    if pct is not None: cmds.progressBar(PBAR, e=True, progress=pct)
    cmds.refresh(); time.sleep(0.02)

def _ui_close():
    """Cierra la ventana de progreso"""
    if cmds.window(WIN, exists=True):
        cmds.deleteUI(WIN)

def _get_all_joints():
    """Obtiene todos los joints de la escena"""
    return cmds.ls(type="joint") or []

def _get_all_meshes():
    """Obtiene todas las mallas de la escena que tienen skinning"""
    all_meshes = cmds.ls(type="mesh")
    skinned_meshes = []

    for mesh in all_meshes:
        # Obtener el transform parent
        transforms = cmds.listRelatives(mesh, parent=True, type="transform") or []
        for transform in transforms:
            # Verificar si tiene skin cluster
            history = cmds.listHistory(transform, pruneDagObjects=True) or []
            skin_clusters = cmds.ls(history, type="skinCluster")
            if skin_clusters:
                skinned_meshes.append(transform)
                break

    return skinned_meshes

def _get_controllers():
    """Busca controladores comunes en la escena"""
    controllers = []

    # Patrones comunes de controladores
    controller_patterns = [
        "*_ctrl", "*_control", "*_con", "*Ctrl*", "*Control*",
        "*:*_ctrl", "*:*_control", "*:*_con", "*:*Ctrl*", "*:*Control*"
    ]

    for pattern in controller_patterns:
        found = cmds.ls(pattern, type="transform") or []
        controllers.extend(found)

    # Remover duplicados
    controllers = list(set(controllers))

    # Filtrar solo los que tienen keyframes
    animated_controllers = []
    for ctrl in controllers:
        if cmds.keyframe(ctrl, query=True, keyframeCount=True):
            animated_controllers.append(ctrl)

    return animated_controllers

def _bake_controllers_to_joints(controllers, joints, start, end):
    """Bakea la animación de controladores a joints"""
    if not controllers or not joints:
        return

    # Bakear controladores primero
    try:
        cmds.bakeResults(
            controllers,
            time=(start, end),
            sampleBy=1,
            simulation=True,
            preserveOutsideKeys=True,
            sparseAnimCurveBake=False,
            removeBakedAttributeFromLayer=False,
            bakeOnOverrideLayer=False,
            controlPoints=False,
            shape=False
        )
    except:
        pass  # Algunos controladores pueden no ser bakeables

    # Luego bakear joints
    try:
        cmds.bakeResults(
            joints,
            time=(start, end),
            sampleBy=1,
            simulation=True,
            preserveOutsideKeys=True,
            sparseAnimCurveBake=False,
            removeBakedAttributeFromLayer=False,
            bakeOnOverrideLayer=False,
            controlPoints=False,
            shape=False
        )
    except:
        pass

def _configure_fbx_export(start, end, export_mesh=True):
    """Configura las opciones de exportación FBX"""
    mel.eval('FBXResetExport;')

    # Configuración básica
    mel.eval('FBXExportCameras -v false;')
    mel.eval('FBXExportLights -v false;')
    mel.eval('FBXExportConstraints -v false;')
    mel.eval('FBXExportInputConnections -v false;')

    # Configuración de geometría
    if export_mesh:
        mel.eval('FBXExportSmoothingGroups -v true;')
        mel.eval('FBXExportHardEdges -v false;')
        mel.eval('FBXExportTangents -v false;')
        mel.eval('FBXExportSmoothMesh -v true;')
        mel.eval('FBXExportInstances -v false;')
        mel.eval('FBXExportReferencedAssetsContent -v true;')

    # Configuración de skinning y deformadores
    mel.eval('FBXExportSkins -v true;')
    mel.eval('FBXExportShapes -v false;')  # Blend shapes off por defecto

    # Configuración de animación
    mel.eval('FBXExportBakeComplexAnimation -v true;')
    mel.eval(f'FBXExportBakeComplexStart -v {start};')
    mel.eval(f'FBXExportBakeComplexEnd -v {end};')
    mel.eval('FBXExportBakeComplexStep -v 1;')
    mel.eval('FBXExportBakeResampleAnimation -v true;')

    # Configuración de rotaciones
    mel.eval('FBXExportQuaternion -v resample;')
    mel.eval('FBXExportUpAxis y;')

    # Configuración de unidades
    mel.eval('FBXExportScaleFactor 1.0;')
    mel.eval('FBXExportConvertUnitString cm;')

def export_animation_auto(export_mesh=True, export_controllers=True):
    """
    Exporta animación automáticamente detectando controladores y joints

    Args:
        export_mesh (bool): Si exportar también la geometría
        export_controllers (bool): Si bakear controladores a joints
    """
    _ui_open('⏳ Iniciando exportación automática...')

    try:
        # 1. Preparación inicial
        _ensure_fbx()
        scene_path, fbx_path = _scene_paths()
        start, end = _timeline()

        _ui_update(10, '🔍 Detectando elementos de la escena...')

        # 2. Detectar elementos
        all_joints = _get_all_joints()
        controllers = _get_controllers() if export_controllers else []
        meshes = _get_all_meshes() if export_mesh else []

        if not all_joints:
            cmds.error("No se encontraron joints en la escena.")

        print(f"Encontrados: {len(all_joints)} joints, {len(controllers)} controladores, {len(meshes)} meshes")

        _ui_update(25, f'🎛️ Bakeando animación ({len(controllers)} controladores → {len(all_joints)} joints)...')

        # 3. Bakear animación
        if controllers and all_joints:
            _bake_controllers_to_joints(controllers, all_joints, start, end)
        elif all_joints:
            # Solo bakear joints si no hay controladores
            try:
                cmds.bakeResults(
                    all_joints,
                    time=(start, end),
                    sampleBy=1,
                    simulation=True,
                    preserveOutsideKeys=True
                )
            except:
                pass

        _ui_update(50, '📦 Preparando selección para exportar...')

        # 4. Seleccionar elementos para exportar
        export_selection = []
        export_selection.extend(all_joints)

        if export_mesh and meshes:
            export_selection.extend(meshes)

        if not export_selection:
            cmds.error("No hay elementos válidos para exportar.")

        cmds.select(export_selection, replace=True)

        _ui_update(70, '⚙️ Configurando opciones FBX...')

        # 5. Configurar FBX
        _configure_fbx_export(start, end, export_mesh)

        _ui_update(85, '💾 Exportando archivo FBX...')

        # 6. Exportar
        mel.eval('FBXExport -f "{}" -s;'.format(fbx_path.replace('\\', '/')))

        _ui_update(100, '✅ Exportación completada')

        # 7. Mensaje final
        cmds.inViewMessage(
            amg=f'<hl>FBX exportado exitosamente</hl><br>📁 {os.path.basename(fbx_path)}<br>🎬 Frames: {start}-{end}<br>🦴 Joints: {len(all_joints)}<br>🎮 Controladores: {len(controllers)}<br>📐 Meshes: {len(meshes)}',
            pos='midCenter',
            fade=True,
            fadeStayTime=5
        )

        print(f"✅ Exportación FBX completada: {fbx_path}")
        return fbx_path

    except Exception as e:
        _ui_update(0, f'❌ Error: {str(e)}')
        cmds.error(f"Error durante la exportación: {str(e)}")

    finally:
        _ui_close()

# Función de compatibilidad con el script anterior
def export_anim_AS(expected_root_name='root', force_root_keys=True):
    """Función de compatibilidad - usa la nueva función automática"""
    return export_animation_auto(export_mesh=True, export_controllers=True)

# Función para crear botón en shelf
def create_shelf_button():
    """Crea un botón en el shelf actual para la exportación automática"""
    try:
        # Obtener el shelf actual
        current_shelf = cmds.tabLayout('ShelfLayout', query=True, selectTab=True)
        cmds.setParent(current_shelf)

        # Crear el botón
        cmds.shelfButton(
            command='import as_fbx_exporter; as_fbx_exporter.export_animation_auto()',
            annotation='Exportar Animación FBX Automático',
            label='FBX Auto',
            image='out_time.png',  # Icono de Maya
            sourceType='python'
        )

        print("✅ Botón 'FBX Auto' creado en el shelf actual")

    except Exception as e:
        print(f"❌ Error creando botón: {str(e)}")

if __name__ == "__main__":
    # Si se ejecuta directamente, crear el botón y ejecutar exportación
    create_shelf_button()
    export_animation_auto()
