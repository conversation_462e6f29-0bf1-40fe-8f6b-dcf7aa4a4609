import os, time
import maya.cmds as cmds
import maya.mel as mel

WIN = 'fbxExportWin'
PBAR = 'fbxProgressBar'
STAT = 'fbxStatusText'

def _ensure_fbx():
    """Asegura que el plugin FBX esté cargado"""
    if not cmds.pluginInfo("fbxmaya", q=True, l=True):
        cmds.loadPlugin("fbxmaya")

def _scene_paths():
    """Obtiene las rutas de la escena actual y el archivo FBX de destino"""
    sp = cmds.file(q=True, sn=True)
    if not sp:
        cmds.error("Guarda la escena antes de exportar.")
    base, _ = os.path.splitext(sp)
    return sp, base + ".fbx"

def _timeline():
    """Obtiene el rango de tiempo de la timeline"""
    s = int(cmds.playbackOptions(q=True, minTime=True))
    e = int(cmds.playbackOptions(q=True, maxTime=True))
    return s, e

def _ui_open(msg="Iniciando..."):
    """Abre la ventana de progreso"""
    if cmds.window(WIN, exists=True):
        cmds.deleteUI(WIN)
    cmds.window(WIN, title='Exportación FBX Automática', widthHeight=(400, 120), toolbox=True)
    cmds.columnLayout(adj=True)
    cmds.text(STAT, label=msg, h=20)
    cmds.separator(h=8, style='in')
    cmds.progressBar(PBAR, maxValue=100, h=20)
    cmds.showWindow(WIN)
    cmds.refresh(); time.sleep(0.05)

def _ui_update(pct=None, msg=None):
    """Actualiza la interfaz de progreso"""
    if msg: cmds.text(STAT, e=True, label=msg)
    if pct is not None: cmds.progressBar(PBAR, e=True, progress=pct)
    cmds.refresh(); time.sleep(0.02)

def _ui_close():
    """Cierra la ventana de progreso"""
    if cmds.window(WIN, exists=True):
        cmds.deleteUI(WIN)

def _get_all_joints():
    """Obtiene todos los joints de la escena"""
    return cmds.ls(type="joint") or []

def _get_all_meshes():
    """Obtiene todas las mallas de la escena que tienen skinning"""
    all_meshes = cmds.ls(type="mesh")
    skinned_meshes = []

    for mesh in all_meshes:
        # Obtener el transform parent
        transforms = cmds.listRelatives(mesh, parent=True, type="transform") or []
        for transform in transforms:
            # Verificar si tiene skin cluster
            history = cmds.listHistory(transform, pruneDagObjects=True) or []
            skin_clusters = cmds.ls(history, type="skinCluster")
            if skin_clusters:
                skinned_meshes.append(transform)
                break

    return skinned_meshes

def _clean_mesh_names():
    """Limpia los nombres de las mallas para evitar problemas en Unreal"""
    all_transforms = cmds.ls(type="transform")
    renamed_count = 0

    for transform in all_transforms:
        # Verificar si tiene una mesh como child
        shapes = cmds.listRelatives(transform, shapes=True, type="mesh") or []
        if not shapes:
            continue

        # Obtener el nombre actual sin path
        current_name = transform.split("|")[-1]

        # Verificar si necesita renombrado
        needs_rename = (
            not current_name or
            current_name.lower() in ["geometry", "polysurface"] or
            current_name.startswith("polySurface") or
            current_name.startswith("pCube") or
            current_name.startswith("pSphere") or
            ":" in current_name and current_name.split(":")[-1].lower() in ["geometry", "body", "mesh"]
        )

        if needs_rename:
            # Crear un nombre limpio y único
            new_name = f"PenguinMesh_{renamed_count:02d}"
            try:
                # Verificar que el nombre no exista
                counter = 0
                test_name = new_name
                while cmds.objExists(test_name):
                    test_name = f"PenguinMesh_{renamed_count:02d}_{counter}"
                    counter += 1

                cmds.rename(transform, test_name)
                print(f"Mesh renombrada: {current_name} → {test_name}")
                renamed_count += 1
            except Exception as e:
                print(f"Error renombrando mesh {current_name}: {e}")

    return renamed_count

def _get_penguin_bone_mapping():
    """Mapeo exacto para el esqueleto SK_Penguin_Skeleton basado en las imágenes proporcionadas"""
    return {
        # ✅ MAPEO EXACTO MAYA → UNREAL
        # Basado en la comparación de jerarquías mostradas

        # Columna vertebral (ya coinciden)
        "root": "root",
        "pelvis": "pelvis",
        "spine_01": "spine_01",
        "spine_02": "spine_02",
        "spine_03": "spine_03",
        "neck_01": "neck_01",
        "head": "head",

        # Cabeza y cara
        "HeadEnd": "HeadEnd",  # Mantener como está
        "Eye_l": "Eye_l",
        "EyeEnd_l": "EyeEnd_l",
        "Eye_r": "Eye_r",
        "EyeEnd_r": "EyeEnd_r",
        "Jaw": "Jaw",
        "JawEnd": "JawEnd",
        "Beak_up": "Beak_up",
        "BeakEnd_up": "BeakEnd_up",

        # Brazos izquierdo (ya coinciden perfectamente)
        "clavicle_l": "clavicle_l",
        "upperarm_l": "upperarm_l",
        "upperarm_twist_01_l": "upperarm_twist_01_l",
        "lowerarm_l": "lowerarm_l",
        "lowerarm_twist_01_l": "lowerarm_twist_01_l",
        "hand_l": "hand_l",
        "middle_01_l": "middle_01_l",
        "middle_02_l": "middle_02_l",
        "middle_03_l": "middle_03_l",
        "MiddleFinger4_l": "MiddleFinger4_l",

        # Brazos derecho (ya coinciden perfectamente)
        "clavicle_r": "clavicle_r",
        "upperarm_r": "upperarm_r",
        "upperarm_twist_01_r": "upperarm_twist_01_r",
        "lowerarm_r": "lowerarm_r",
        "lowerarm_twist_01_r": "lowerarm_twist_01_r",
        "hand_r": "hand_r",
        "middle_01_r": "middle_01_r",
        "middle_02_r": "middle_02_r",
        "middle_03_r": "middle_03_r",
        "MiddleFinger4_r": "MiddleFinger4_r",

        # Piernas izquierda (ya coinciden perfectamente)
        "thigh_l": "thigh_l",
        "thigh_twist_01_l": "thigh_twist_01_l",
        "calf_l": "calf_l",
        "calf_twist_01_l": "calf_twist_01_l",
        "foot_l": "foot_l",
        "ball_l": "ball_l",
        "ToesEnd_l": "ToesEnd_l",

        # Piernas derecha (ya coinciden perfectamente)
        "thigh_r": "thigh_r",
        "thigh_twist_01_r": "thigh_twist_01_r",
        "calf_r": "calf_r",
        "calf_twist_01_r": "calf_twist_01_r",
        "foot_r": "foot_r",
        "ball_r": "ball_r",
        "ToesEnd_r": "ToesEnd_r",
    }

def _clean_joint_names():
    """Limpia nombres de joints para evitar duplicados y conflictos"""
    joints = cmds.ls(type="joint")
    renamed_count = 0

    # Usar mapeo específico para pingüino
    name_mapping = _get_penguin_bone_mapping()

    # Mapeo adicional de Advanced Skeleton a nombres estándar
    advanced_skeleton_mapping = {
        # Columna vertebral
        "Root_M": "root",
        "RootX_M": "pelvis",
        "Spine1_M": "spine_01",
        "Spine2_M": "spine_02",
        "Chest_M": "spine_03",
        "Neck_M": "neck_01",
        "Head_M": "head",
        "HeadEnd_M": "head_end",

        # Brazo izquierdo
        "Scapula_L": "clavicle_l",
        "Shoulder_L": "upperarm_l",
        "Elbow_L": "lowerarm_l",
        "Wrist_L": "hand_l",

        # Brazo derecho
        "Scapula_R": "clavicle_r",
        "Shoulder_R": "upperarm_r",
        "Elbow_R": "lowerarm_r",
        "Wrist_R": "hand_r",

        # Pierna izquierda
        "Hip_L": "thigh_l",
        "Knee_L": "calf_l",
        "Ankle_L": "foot_l",
        "Toes_L": "ball_l",

        # Pierna derecha
        "Hip_R": "thigh_r",
        "Knee_R": "calf_r",
        "Ankle_R": "foot_r",
        "Toes_R": "ball_r",
    }

    # Combinar ambos mapeos
    name_mapping.update(advanced_skeleton_mapping)

    # Aplicar mapeo de nombres
    for old_name, new_name in name_mapping.items():
        if cmds.objExists(old_name):
            try:
                # Verificar que el nuevo nombre no exista ya
                if not cmds.objExists(new_name):
                    cmds.rename(old_name, new_name)
                    print(f"Joint renombrado: {old_name} → {new_name}")
                    renamed_count += 1
            except:
                pass

    # Limpiar nombres problemáticos
    for joint in joints:
        current_name = joint.split("|")[-1]

        # Remover namespace si existe
        if ":" in current_name:
            clean_name = current_name.split(":")[-1]
            try:
                if not cmds.objExists(clean_name):
                    cmds.rename(joint, clean_name)
                    print(f"Namespace removido: {current_name} → {clean_name}")
                    renamed_count += 1
            except:
                pass

    return renamed_count

def _remove_unsupported_bones():
    """DESHABILITADO: No eliminar huesos de Advanced Skeleton - pueden ser necesarios"""

    print("⚠️ ELIMINACIÓN DE HUESOS DESHABILITADA")
    print("   Los huesos de Advanced Skeleton se mantendrán para preservar la jerarquía")
    print("   Si hay errores en Unreal, se pueden mapear individualmente")

    return 0

def _clean_duplicates_and_prepare_for_export():
    """Limpia duplicados y prepara la escena para exportación limpia"""
    cleaned_count = 0

    # 1. Eliminar objetos duplicados o innecesarios
    all_objects = cmds.ls(dag=True, long=True)

    for obj in all_objects:
        obj_name = obj.split("|")[-1]

        # Eliminar objetos con nombres problemáticos
        if (obj_name.endswith("_IGNORE") or
            obj_name.startswith("IGNORE_") or
            obj_name.endswith("Shape1") or  # Shapes duplicadas
            obj_name.endswith("Orig")):     # Objetos originales

            try:
                if cmds.objExists(obj):
                    cmds.delete(obj)
                    print(f"🗑️ Eliminado objeto duplicado: {obj_name}")
                    cleaned_count += 1
            except:
                pass

    # 2. Limpiar historia de construcción en meshes
    meshes = cmds.ls(type="mesh")
    for mesh in meshes:
        transforms = cmds.listRelatives(mesh, parent=True, type="transform") or []
        for transform in transforms:
            try:
                # Eliminar historia no deformadora
                cmds.bakePartialHistory(transform, prePostDeformers=True)
            except:
                pass

    return cleaned_count

def _get_controllers():
    """Busca controladores de Advanced Skeleton y otros sistemas"""
    controllers = []

    # 1. Buscar en grupos específicos de Advanced Skeleton
    as_groups = ["Group", "motion system", "MotionSystem", "Controls", "Rig"]

    for group_name in as_groups:
        if cmds.objExists(group_name):
            try:
                # Obtener todos los hijos del grupo
                children = cmds.listRelatives(group_name, allDescendents=True, type="transform") or []
                controllers.extend(children)
                print(f"Encontrados {len(children)} objetos en grupo '{group_name}'")
            except:
                pass

    # 2. Buscar por patrones de nombres comunes
    controller_patterns = [
        "*_ctrl", "*_control", "*_con", "*Ctrl*", "*Control*",
        "*:*_ctrl", "*:*_control", "*:*_con", "*:*Ctrl*", "*:*Control*",
        # Patrones específicos de Advanced Skeleton
        "*_M", "*_L", "*_R",  # Sufijos de Advanced Skeleton
        "FK*", "IK*", "Main*", "Root*", "Hip*", "Spine*", "Chest*", "Neck*", "Head*",
        "Shoulder*", "Elbow*", "Wrist*", "Knee*", "Ankle*", "Toes*"
    ]

    for pattern in controller_patterns:
        found = cmds.ls(pattern, type="transform") or []
        controllers.extend(found)

    # 3. Buscar objetos con curvas de animación
    all_transforms = cmds.ls(type="transform")
    for obj in all_transforms:
        # Verificar si tiene conexiones de animación
        connections = cmds.listConnections(obj, type="animCurve") or []
        if connections:
            controllers.append(obj)

    # Remover duplicados
    controllers = list(set(controllers))

    # Filtrar solo los que tienen keyframes
    animated_controllers = []
    for ctrl in controllers:
        try:
            keyframe_count = cmds.keyframe(ctrl, query=True, keyframeCount=True) or 0
            if keyframe_count > 0:
                animated_controllers.append(ctrl)
        except:
            pass

    print(f"Total controladores encontrados: {len(controllers)}")
    print(f"Controladores animados: {len(animated_controllers)}")

    return animated_controllers

def _get_advanced_skeleton_controllers():
    """Busca controladores específicamente en la jerarquía de Advanced Skeleton"""
    controllers = []

    print("🔍 Buscando controladores de Advanced Skeleton...")

    # 1. Buscar en la jerarquía Group|MotionSystem
    motion_system_path = "|Group|MotionSystem"
    if cmds.objExists(motion_system_path):
        print(f"✅ Encontrado: {motion_system_path}")
        try:
            # Obtener todos los descendientes
            descendants = cmds.listRelatives(motion_system_path, allDescendents=True, type="transform") or []
            print(f"   Descendientes encontrados: {len(descendants)}")

            # Filtrar solo los que tienen animación
            for obj in descendants:
                try:
                    keyframe_count = cmds.keyframe(obj, query=True, keyframeCount=True) or 0
                    if keyframe_count > 0:
                        controllers.append(obj)
                except:
                    pass

        except Exception as e:
            print(f"❌ Error procesando MotionSystem: {e}")
    else:
        print(f"❌ No encontrado: {motion_system_path}")

    # 2. Buscar en sets de Advanced Skeleton
    as_sets = ["ControlSet", "DeformSet", "AllSet"]

    for set_name in as_sets:
        if cmds.objExists(set_name):
            try:
                members = cmds.sets(set_name, query=True) or []
                animated_members = []
                for member in members:
                    if cmds.objExists(member) and cmds.nodeType(member) == "transform":
                        keyframe_count = cmds.keyframe(member, query=True, keyframeCount=True) or 0
                        if keyframe_count > 0:
                            controllers.append(member)
                            animated_members.append(member)

                print(f"Set '{set_name}': {len(members)} miembros, {len(animated_members)} animados")
            except Exception as e:
                print(f"Error procesando set {set_name}: {e}")

    # 3. Buscar controladores principales por nombre
    main_controllers = [
        "Main_M", "Root_M", "RootX_M",
        "Hip_L", "Hip_R", "Knee_L", "Knee_R", "Ankle_L", "Ankle_R",
        "Spine1_M", "Spine2_M", "Chest_M", "Neck_M", "Head_M",
        "Scapula_L", "Scapula_R", "Shoulder_L", "Shoulder_R",
        "Elbow_L", "Elbow_R", "Wrist_L", "Wrist_R"
    ]

    for ctrl_name in main_controllers:
        if cmds.objExists(ctrl_name):
            keyframe_count = cmds.keyframe(ctrl_name, query=True, keyframeCount=True) or 0
            if keyframe_count > 0:
                controllers.append(ctrl_name)
                print(f"✅ Controlador principal: {ctrl_name} ({keyframe_count} keyframes)")

    # Remover duplicados
    controllers = list(set(controllers))

    print(f"🎮 Total controladores Advanced Skeleton encontrados: {len(controllers)}")

    # Mostrar los primeros controladores encontrados
    for i, ctrl in enumerate(sorted(controllers)[:15]):
        keyframes = cmds.keyframe(ctrl, query=True, keyframeCount=True) or 0
        print(f"  {i+1:2d}. {ctrl} ({keyframes} keyframes)")

    if len(controllers) > 15:
        print(f"  ... y {len(controllers)-15} más")

    return controllers

def _get_animated_joints():
    """Busca joints que tienen animación directamente"""
    all_joints = _get_all_joints()
    animated_joints = []

    for joint in all_joints:
        # Verificar si tiene keyframes en cualquier atributo de transformación
        keyframe_count = 0
        for attr in ["tx", "ty", "tz", "rx", "ry", "rz", "sx", "sy", "sz"]:
            try:
                count = cmds.keyframe(f"{joint}.{attr}", query=True, keyframeCount=True) or 0
                keyframe_count += count
            except:
                pass

        if keyframe_count > 0:
            animated_joints.append(joint)

    return animated_joints

def _get_all_animated_objects():
    """Obtiene todos los objetos animados (controladores + joints)"""
    # Buscar controladores con múltiples métodos
    controllers_generic = _get_controllers()
    controllers_as = _get_advanced_skeleton_controllers()
    animated_joints = _get_animated_joints()

    # Combinar todos los controladores
    all_controllers = list(set(controllers_generic + controllers_as))

    # Combinar todo y remover duplicados
    all_animated = list(set(all_controllers + animated_joints))

    print(f"🔍 DETECCIÓN DE ANIMACIÓN:")
    print(f"  • Controladores genéricos: {len(controllers_generic)}")
    print(f"  • Controladores Advanced Skeleton: {len(controllers_as)}")
    print(f"  • Joints animados: {len(animated_joints)}")
    print(f"  • Total objetos animados: {len(all_animated)}")

    return all_animated, all_controllers, animated_joints

def _bake_controllers_to_joints(controllers, joints, start, end):
    """Bakea la animación de controladores a joints"""
    if not controllers or not joints:
        return

    # Bakear controladores primero
    try:
        cmds.bakeResults(
            controllers,
            time=(start, end),
            sampleBy=1,
            simulation=True,
            preserveOutsideKeys=True,
            sparseAnimCurveBake=False,
            removeBakedAttributeFromLayer=False,
            bakeOnOverrideLayer=False,
            controlPoints=False,
            shape=False
        )
    except:
        pass  # Algunos controladores pueden no ser bakeables

    # Luego bakear joints
    try:
        cmds.bakeResults(
            joints,
            time=(start, end),
            sampleBy=1,
            simulation=True,
            preserveOutsideKeys=True,
            sparseAnimCurveBake=False,
            removeBakedAttributeFromLayer=False,
            bakeOnOverrideLayer=False,
            controlPoints=False,
            shape=False
        )
    except:
        pass

def _safe_fbx_command(command):
    """Ejecuta un comando FBX de forma segura, ignorando si no existe"""
    try:
        mel.eval(command)
        return True
    except:
        print(f"⚠️ Comando FBX no disponible: {command}")
        return False

def _configure_fbx_export(start, end, export_mesh=True):
    """Configura las opciones de exportación FBX optimizadas para Unreal (compatible con todas las versiones)"""

    # Reset básico
    _safe_fbx_command('FBXResetExport;')

    # Configuración básica (siempre disponible)
    _safe_fbx_command('FBXExportCameras -v false;')
    _safe_fbx_command('FBXExportLights -v false;')
    _safe_fbx_command('FBXExportConstraints -v false;')
    _safe_fbx_command('FBXExportInputConnections -v false;')

    # Configuración de geometría
    if export_mesh:
        _safe_fbx_command('FBXExportSmoothingGroups -v true;')
        _safe_fbx_command('FBXExportHardEdges -v false;')
        _safe_fbx_command('FBXExportTangents -v false;')
        _safe_fbx_command('FBXExportSmoothMesh -v true;')
        _safe_fbx_command('FBXExportInstances -v false;')
        _safe_fbx_command('FBXExportReferencedAssetsContent -v true;')
        # Configuración específica para Unreal
        _safe_fbx_command('FBXExportTriangulate -v true;')  # Triangular para Unreal
        _safe_fbx_command('FBXExportEmbeddedTextures -v false;')  # No embeber texturas

    # Configuración de skinning (crítico para animación)
    _safe_fbx_command('FBXExportSkins -v true;')
    _safe_fbx_command('FBXExportShapes -v false;')  # Blend shapes off por defecto

    # Configuración de animación optimizada para Unreal
    _safe_fbx_command('FBXExportBakeComplexAnimation -v true;')
    _safe_fbx_command(f'FBXExportBakeComplexStart -v {start};')
    _safe_fbx_command(f'FBXExportBakeComplexEnd -v {end};')
    _safe_fbx_command('FBXExportBakeComplexStep -v 1;')
    _safe_fbx_command('FBXExportBakeResampleAnimation -v true;')

    # Configuración de rotaciones - importante para Unreal
    _safe_fbx_command('FBXExportQuaternion -v resample;')
    _safe_fbx_command('FBXExportUpAxis y;')  # Unreal usa Y-up

    # Configuración de unidades - Unreal usa centímetros
    _safe_fbx_command('FBXExportScaleFactor 1.0;')
    _safe_fbx_command('FBXExportConvertUnitString cm;')

    # Configuraciones adicionales (opcionales)
    _safe_fbx_command('FBXExportGenerateLog -v true;')  # Generar log para debug
    _safe_fbx_command('FBXExportSplitAnimationIntoTakes -v false;')  # Una sola toma
    _safe_fbx_command('FBXExportUseSceneName -v false;')  # No usar nombre de escena
    _safe_fbx_command('FBXExportReferencedContainersContent -v false;')  # No contenedores referenciados

    print("✅ Configuración FBX aplicada (comandos compatibles)")

def _configure_fbx_export_basic(start, end):
    """Configuración FBX básica y segura para todas las versiones de Maya"""
    try:
        # Solo comandos básicos que siempre funcionan
        mel.eval('FBXResetExport;')
        mel.eval('FBXExportCameras -v false;')
        mel.eval('FBXExportLights -v false;')
        mel.eval('FBXExportSkins -v true;')
        mel.eval('FBXExportBakeComplexAnimation -v true;')
        mel.eval(f'FBXExportBakeComplexStart -v {start};')
        mel.eval(f'FBXExportBakeComplexEnd -v {end};')
        mel.eval('FBXExportBakeComplexStep -v 1;')
        mel.eval('FBXExportUpAxis y;')
        print("✅ Configuración FBX básica aplicada")
        return True
    except Exception as e:
        print(f"❌ Error en configuración FBX básica: {e}")
        return False

def export_animation_auto(export_mesh=True, export_controllers=True):
    """
    Exporta animación automáticamente detectando controladores y joints

    Args:
        export_mesh (bool): Si exportar también la geometría
        export_controllers (bool): Si bakear controladores a joints
    """
    _ui_open('⏳ Iniciando exportación automática...')

    try:
        # 1. Preparación inicial
        _ensure_fbx()
        scene_path, fbx_path = _scene_paths()
        start, end = _timeline()

        _ui_update(10, '🔍 Detectando elementos de la escena...')

        # 2. Detectar elementos y animación
        all_joints = _get_all_joints()
        meshes = _get_all_meshes() if export_mesh else []

        # Detectar animación
        all_animated, all_controllers, animated_joints = _get_all_animated_objects()

        if not all_joints:
            cmds.error("No se encontraron joints en la escena.")

        print(f"Encontrados: {len(all_joints)} joints, {len(all_controllers)} controladores, {len(animated_joints)} joints animados, {len(meshes)} meshes")

        if not all_animated:
            print("⚠️ ADVERTENCIA: No se encontró animación en controladores ni joints")
            print("   El FBX se exportará pero sin animación")
        else:
            print(f"✅ Animación detectada en {len(all_animated)} objetos")

        _ui_update(15, '🧹 Limpiando nombres para Unreal...')

        # 3. Limpiar nombres para evitar conflictos en Unreal
        joints_renamed = _clean_joint_names()
        meshes_renamed = _clean_mesh_names() if export_mesh else 0

        _ui_update(20, '🧹 Limpiando duplicados...')

        # 4. Limpiar duplicados y preparar para exportación (sin eliminar huesos)
        duplicates_cleaned = _clean_duplicates_and_prepare_for_export()
        bones_removed = 0  # No eliminamos huesos de Advanced Skeleton

        print(f"Limpieza completada: {joints_renamed} joints renombrados, {meshes_renamed} meshes renombrados, {duplicates_cleaned} duplicados limpiados")

        # Actualizar listas después de toda la limpieza
        all_joints = _get_all_joints()
        meshes = _get_all_meshes() if export_mesh else []

        _ui_update(25, f'🎛️ Bakeando animación ({len(all_animated)} objetos animados → {len(all_joints)} joints)...')

        # 3. Bakear animación
        if all_animated and all_joints:
            # Bakear todos los objetos animados (controladores + joints animados)
            try:
                print(f"Bakeando {len(all_animated)} objetos animados...")
                cmds.bakeResults(
                    all_animated,
                    time=(start, end),
                    sampleBy=1,
                    simulation=True,
                    preserveOutsideKeys=True,
                    sparseAnimCurveBake=False,
                    removeBakedAttributeFromLayer=False,
                    bakeOnOverrideLayer=False,
                    controlPoints=False,
                    shape=False
                )
                print("✅ Baking completado")
            except Exception as e:
                print(f"⚠️ Error en baking: {e}")
                # Intentar bakear solo los joints principales
                try:
                    main_joints = [j for j in all_joints if not any(x in j.lower() for x in ['fkx', 'ikx', 'twist', 'untwist'])]
                    if main_joints:
                        cmds.bakeResults(main_joints, time=(start, end), sampleBy=1, simulation=True)
                        print(f"✅ Baking alternativo completado en {len(main_joints)} joints principales")
                except:
                    print("❌ No se pudo bakear la animación")
        else:
            print("⚠️ No hay animación para bakear")

        _ui_update(50, '📦 Preparando selección para exportar...')

        # 4. Seleccionar elementos para exportar
        export_selection = []
        export_selection.extend(all_joints)

        if export_mesh and meshes:
            export_selection.extend(meshes)

        if not export_selection:
            cmds.error("No hay elementos válidos para exportar.")

        cmds.select(export_selection, replace=True)

        _ui_update(70, '⚙️ Configurando opciones FBX...')

        # 5. Configurar FBX (con fallback a configuración básica)
        try:
            _configure_fbx_export(start, end, export_mesh)
        except Exception as e:
            print(f"⚠️ Configuración avanzada falló, usando básica: {e}")
            _configure_fbx_export_basic(start, end)

        _ui_update(85, '💾 Exportando archivo FBX...')

        # 6. Exportar
        mel.eval('FBXExport -f "{}" -s;'.format(fbx_path.replace('\\', '/')))

        _ui_update(100, '✅ Exportación completada')

        # 7. Mensaje final
        total_cleaned = joints_renamed + meshes_renamed + bones_removed + duplicates_cleaned
        cmds.inViewMessage(
            amg=f'<hl>FBX exportado para Unreal</hl><br>📁 {os.path.basename(fbx_path)}<br>🎬 Frames: {start}-{end}<br>🦴 Joints: {len(all_joints)}<br>🎮 Controladores: {len(all_controllers)}<br>🎯 Joints animados: {len(animated_joints)}<br>📐 Meshes: {len(meshes)}<br>🧹 Total limpiado: {total_cleaned}<br>🚫 Huesos eliminados: {bones_removed}',
            pos='midCenter',
            fade=True,
            fadeStayTime=8
        )

        # Generar resumen final para debug
        debug_summary = f"""
=== RESUMEN FINAL DE EXPORTACIÓN ===
✅ Archivo exportado: {fbx_path}
🎬 Frames: {start}-{end}
🦴 Joints finales: {len(all_joints)}
🎮 Controladores procesados: {len(all_controllers)}
📐 Meshes exportadas: {len(meshes)}

🧹 LIMPIEZA REALIZADA:
  • {joints_renamed} joints renombrados
  • {meshes_renamed} meshes renombrados
  • {bones_removed} huesos no soportados eliminados
  • {duplicates_cleaned} duplicados limpiados
  • Total elementos procesados: {total_cleaned}

💡 El FBX debería importarse sin errores en Unreal Engine
====================================="""

        print(debug_summary)

        # Guardar debug en variable global para acceso posterior
        global _last_export_debug
        _last_export_debug = debug_summary

        return fbx_path

    except Exception as e:
        _ui_update(0, f'❌ Error: {str(e)}')
        cmds.error(f"Error durante la exportación: {str(e)}")

    finally:
        _ui_close()

def _fix_orientation_for_unreal(root_joint="root"):
    """Corrige la orientación del personaje para Unreal Engine (Z-up)"""
    if not cmds.objExists(root_joint):
        print(f"⚠️ Root joint '{root_joint}' no encontrado para corrección de orientación")
        return False

    try:
        # Rotar el root joint 90 grados en X para que esté de pie en Unreal
        current_rotation = cmds.getAttr(f"{root_joint}.rotateX")
        cmds.setAttr(f"{root_joint}.rotateX", current_rotation + 90)
        print(f"✅ Orientación corregida: {root_joint} rotado +90° en X")
        return True
    except Exception as e:
        print(f"❌ Error corrigiendo orientación: {e}")
        return False

def _optimize_baking(controllers, joints, start, end):
    """Baking optimizado con cache y limpieza"""
    if not controllers and not joints:
        return False

    print("🚀 Iniciando baking optimizado...")

    # 1. Cache de objetos a bakear
    objects_to_bake = []

    # Añadir controladores con animación
    for ctrl in controllers:
        if cmds.keyframe(ctrl, query=True, keyframeCount=True):
            objects_to_bake.append(ctrl)

    # Añadir joints principales (evitar auxiliares)
    main_joints = []
    for joint in joints:
        joint_name = joint.lower()
        # Filtrar joints auxiliares para mejor rendimiento
        if not any(aux in joint_name for aux in ['ikx', 'fkx', 'twist', 'untwist', 'balancer']):
            main_joints.append(joint)

    objects_to_bake.extend(main_joints)

    if not objects_to_bake:
        print("⚠️ No hay objetos para bakear")
        return False

    print(f"📊 Baking {len(objects_to_bake)} objetos ({len(controllers)} controladores + {len(main_joints)} joints principales)")

    # 2. Baking con configuración optimizada
    try:
        cmds.bakeResults(
            objects_to_bake,
            time=(start, end),
            sampleBy=1,  # Cada frame para suavidad
            simulation=True,
            preserveOutsideKeys=True,
            sparseAnimCurveBake=False,  # Mantener todas las claves
            removeBakedAttributeFromLayer=False,
            bakeOnOverrideLayer=False,
            controlPoints=False,
            shape=False
        )

        # 3. Limpieza opcional de keyframes redundantes
        print("🧹 Limpiando keyframes redundantes...")
        for obj in main_joints:
            try:
                # Simplificar curvas de animación
                cmds.keyTangent(obj, inTangentType='auto', outTangentType='auto')
            except:
                pass

        print("✅ Baking optimizado completado")
        return True

    except Exception as e:
        print(f"❌ Error en baking optimizado: {e}")
        return False

def _validate_scene_for_export():
    """Validación previa de la escena antes de exportar"""
    issues = []
    warnings = []

    # 1. Verificar que la escena esté guardada
    scene_path = cmds.file(q=True, sn=True)
    if not scene_path:
        issues.append("La escena no está guardada")

    # 2. Verificar timeline
    start = cmds.playbackOptions(q=True, minTime=True)
    end = cmds.playbackOptions(q=True, maxTime=True)
    if end - start < 1:
        warnings.append(f"Timeline muy corto: {end-start+1} frames")

    # 3. Verificar joints principales
    main_joints = ["root", "pelvis", "spine_01", "head"]
    missing_joints = []
    for joint in main_joints:
        if not cmds.objExists(joint):
            missing_joints.append(joint)

    if missing_joints:
        issues.append(f"Joints principales faltantes: {missing_joints}")

    # 4. Verificar controladores animados
    controllers = _get_advanced_skeleton_controllers()
    if not controllers:
        warnings.append("No se encontraron controladores animados")

    # 5. Verificar meshes
    meshes = _get_all_meshes()
    if not meshes:
        warnings.append("No se encontraron meshes con skinning")

    return issues, warnings

def export_advanced_skeleton_optimized(fix_orientation=True, frame_range=None, export_mesh=True):
    """Exportación optimizada de Advanced Skeleton para Unreal Engine"""
    print("\n" + "="*70)
    print("🚀 EXPORTACIÓN ADVANCED SKELETON OPTIMIZADA PARA UNREAL ENGINE")
    print("="*70)

    try:
        # 1. Validación previa
        print("🔍 Validando escena...")
        issues, warnings = _validate_scene_for_export()

        if issues:
            print("❌ ERRORES CRÍTICOS:")
            for issue in issues:
                print(f"  • {issue}")
            return None

        if warnings:
            print("⚠️ ADVERTENCIAS:")
            for warning in warnings:
                print(f"  • {warning}")

        # 2. Preparación básica
        _ensure_fbx()
        scene_path, fbx_path = _scene_paths()

        # Usar rango personalizado o timeline
        if frame_range:
            start, end = frame_range
        else:
            start, end = _timeline()

        print(f"\n📁 Exportando: {os.path.basename(fbx_path)}")
        print(f"⏰ Frames: {start} a {end} ({end-start+1} frames)")

        # 3. Buscar controladores (con cache)
        print("\n🔍 Detectando controladores de Advanced Skeleton...")
        controllers = _get_advanced_skeleton_controllers()

        # 4. Limpieza de nombres para evitar conflictos
        print(f"\n🧹 Limpiando nombres para Unreal Engine...")
        joints_renamed = _clean_joint_names()
        meshes_renamed = _clean_mesh_names() if export_mesh else 0
        duplicates_cleaned = _clean_duplicates_and_prepare_for_export()

        print(f"   • {joints_renamed} joints renombrados")
        print(f"   • {meshes_renamed} meshes renombrados")
        print(f"   • {duplicates_cleaned} duplicados limpiados")

        # 5. Obtener joints de deformación para Unreal (después de la limpieza)
        unreal_joints = [
            "root", "pelvis", "spine_01", "spine_02", "spine_03", "neck_01", "head",
            "clavicle_l", "clavicle_r", "upperarm_l", "upperarm_r",
            "lowerarm_l", "lowerarm_r", "hand_l", "hand_r",
            "thigh_l", "thigh_r", "calf_l", "calf_r", "foot_l", "foot_r",
            "ball_l", "ball_r", "middle_01_l", "middle_01_r", "middle_02_l", "middle_02_r",
            "middle_03_l", "middle_03_r", "MiddleFinger4_l", "MiddleFinger4_r",
            "upperarm_twist_01_l", "upperarm_twist_01_r", "lowerarm_twist_01_l", "lowerarm_twist_01_r",
            "thigh_twist_01_l", "thigh_twist_01_r", "calf_twist_01_l", "calf_twist_01_r",
            "ToesEnd_l", "ToesEnd_r"
        ]

        # Verificar joints existentes
        existing_joints = [j for j in unreal_joints if cmds.objExists(j)]
        print(f"🦴 Joints compatibles con Unreal: {len(existing_joints)}/{len(unreal_joints)}")

        # 6. Baking optimizado
        if controllers or existing_joints:
            print(f"\n🎛️ Baking optimizado...")
            _optimize_baking(controllers, existing_joints, start, end)

        # 7. Corrección de orientación para Unreal
        if fix_orientation:
            print(f"\n🔄 Corrigiendo orientación para Unreal Engine...")
            _fix_orientation_for_unreal("root")

        # 8. Seleccionar SOLO los joints compatibles con Unreal (filtrar problemáticos)
        # Filtrar joints que causan problemas en Unreal
        problematic_joints = [
            "Spine1", "Spine2", "Chest", "Neck", "Eye", "EyeEnd",
            "Scapula", "Shoulder", "Elbow", "Wrist",
            "MiddleFinger1", "MiddleFinger2", "MiddleFinger3", "MiddleFinger4",
            "Hip", "Knee", "Ankle", "Heel", "Toes", "ToesEnd"
        ]

        # Filtrar joints problemáticos de la selección
        filtered_joints = []
        for joint in existing_joints:
            joint_name = joint.split("|")[-1].split(":")[-1]  # Nombre limpio
            if joint_name not in problematic_joints:
                filtered_joints.append(joint)
            else:
                print(f"   ⚠️ Excluyendo joint problemático: {joint}")

        export_selection = filtered_joints.copy()

        if export_mesh:
            meshes = _get_all_meshes()
            export_selection.extend(meshes)
            print(f"📦 Exportando: {len(filtered_joints)} joints + {len(meshes)} meshes")
            print(f"   (Excluidos {len(existing_joints) - len(filtered_joints)} joints problemáticos)")
        else:
            print(f"📦 Exportando: {len(filtered_joints)} joints (solo animación)")

        if not export_selection:
            print("❌ No hay elementos válidos para exportar")
            return None

        cmds.select(export_selection, replace=True)

        # 9. Configuración FBX optimizada para Unreal
        print(f"\n⚙️ Configurando FBX para Unreal Engine...")
        _configure_fbx_export_basic(start, end)

        # 10. Exportar
        print(f"💾 Exportando FBX...")
        mel.eval('FBXExport -f "{}" -s;'.format(fbx_path.replace('\\', '/')))

        # 11. Restaurar orientación si se cambió
        if fix_orientation:
            print(f"🔄 Restaurando orientación original...")
            current_rotation = cmds.getAttr("root.rotateX")
            cmds.setAttr("root.rotateX", current_rotation - 90)

        # 11. Resumen final
        print(f"\n✅ EXPORTACIÓN COMPLETADA PARA UNREAL ENGINE")
        print(f"📊 Resumen:")
        print(f"  • Archivo: {os.path.basename(fbx_path)}")
        print(f"  • Frames: {start}-{end} ({end-start+1} frames)")
        print(f"  • Controladores: {len(controllers)}")
        print(f"  • Joints: {len(existing_joints)}")
        print(f"  • Meshes: {len(_get_all_meshes()) if export_mesh else 0}")
        print(f"  • Orientación corregida: {'Sí' if fix_orientation else 'No'}")
        print(f"💡 El personaje debería aparecer de pie en Unreal Engine")

        return fbx_path

    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return None

# Función simplificada (mantener compatibilidad)
def export_advanced_skeleton_simple():
    """Exportación simplificada - usa la versión optimizada"""
    return export_advanced_skeleton_optimized(fix_orientation=True, export_mesh=True)

def show_unreal_export_dialog():
    """Muestra una interfaz gráfica para exportar a Unreal Engine"""

    # Nombre de la ventana
    window_name = "unreal_export_dialog"

    # Eliminar ventana si ya existe
    if cmds.window(window_name, exists=True):
        cmds.deleteUI(window_name)

    # Crear ventana
    window = cmds.window(
        window_name,
        title="Exportar Animación para Unreal Engine",
        widthHeight=(400, 350),
        resizeToFitChildren=True,
        sizeable=False
    )

    # Layout principal
    main_layout = cmds.columnLayout(adjustableColumn=True, marginWidth=10, marginHeight=10)

    # Título
    cmds.text(label="🚀 Exportación Advanced Skeleton → Unreal Engine",
              font="boldLabelFont", height=25)
    cmds.separator(height=10, style='in')

    # Información de la escena
    start, end = _timeline()
    scene_name = os.path.basename(cmds.file(q=True, sn=True) or "Sin guardar")

    cmds.text(label=f"📁 Escena: {scene_name}", align='left')
    cmds.text(label=f"⏰ Timeline: Frame {int(start)} a {int(end)} ({int(end-start+1)} frames)", align='left')
    cmds.separator(height=10)

    # Opciones de exportación
    cmds.text(label="⚙️ Opciones de Exportación:", font="boldLabelFont", align='left')

    # Checkbox: Corregir orientación
    fix_orientation_cb = cmds.checkBox(
        label="🔄 Corregir orientación para Unreal (personaje de pie)",
        value=True,
        annotation="Rota automáticamente el root joint para que el personaje aparezca de pie en Unreal"
    )

    # Checkbox: Exportar mesh
    export_mesh_cb = cmds.checkBox(
        label="📐 Exportar geometría (mesh + animación)",
        value=True,
        annotation="Incluir la malla del personaje junto con la animación"
    )

    # Checkbox: Usar rango personalizado
    custom_range_cb = cmds.checkBox(
        label="⏰ Usar rango de frames personalizado",
        value=False,
        annotation="Especificar un rango de frames diferente al timeline"
    )

    # Campos de rango personalizado
    range_layout = cmds.rowLayout(numberOfColumns=4, columnWidth4=(80, 60, 40, 60), enable=False)
    cmds.text(label="Desde:")
    start_field = cmds.intField(value=int(start), minValue=0)
    cmds.text(label="Hasta:")
    end_field = cmds.intField(value=int(end), minValue=0)
    cmds.setParent('..')

    # Función para habilitar/deshabilitar campos de rango
    def toggle_range_fields(*args):
        enabled = cmds.checkBox(custom_range_cb, query=True, value=True)
        cmds.rowLayout(range_layout, edit=True, enable=enabled)

    cmds.checkBox(custom_range_cb, edit=True, changeCommand=toggle_range_fields)

    cmds.separator(height=15)

    # Información adicional
    cmds.text(label="💡 Información:", font="boldLabelFont", align='left')
    cmds.text(label="• Se detectarán automáticamente los controladores de Advanced Skeleton",
              align='left', wordWrap=True)
    cmds.text(label="• La animación se bakeará a los joints de deformación",
              align='left', wordWrap=True)
    cmds.text(label="• Solo se exportarán joints compatibles con Unreal Engine",
              align='left', wordWrap=True)

    cmds.separator(height=15)

    # Botones
    button_layout = cmds.rowLayout(numberOfColumns=3, columnWidth3=(120, 120, 120))

    def export_action(*args):
        """Acción del botón exportar"""
        try:
            # Obtener valores de la interfaz
            fix_orientation = cmds.checkBox(fix_orientation_cb, query=True, value=True)
            export_mesh = cmds.checkBox(export_mesh_cb, query=True, value=True)
            use_custom_range = cmds.checkBox(custom_range_cb, query=True, value=True)

            frame_range = None
            if use_custom_range:
                start_frame = cmds.intField(start_field, query=True, value=True)
                end_frame = cmds.intField(end_field, query=True, value=True)
                frame_range = (start_frame, end_frame)

            # Cerrar ventana
            cmds.deleteUI(window)

            # Ejecutar exportación
            result = export_advanced_skeleton_optimized(
                fix_orientation=fix_orientation,
                frame_range=frame_range,
                export_mesh=export_mesh
            )

            if result:
                cmds.confirmDialog(
                    title="Exportación Exitosa",
                    message=f"✅ Animación exportada correctamente!\n\n📁 {os.path.basename(result)}\n\n💡 El personaje debería aparecer de pie en Unreal Engine.",
                    button=['OK']
                )
            else:
                cmds.confirmDialog(
                    title="Error",
                    message="❌ Error durante la exportación. Revisa el Script Editor para más detalles.",
                    button=['OK']
                )

        except Exception as e:
            cmds.confirmDialog(
                title="Error",
                message=f"❌ Error: {str(e)}",
                button=['OK']
            )

    def cancel_action(*args):
        """Acción del botón cancelar"""
        cmds.deleteUI(window)

    def preview_action(*args):
        """Acción del botón preview"""
        try:
            # Mostrar información de lo que se exportaría
            controllers = _get_advanced_skeleton_controllers()
            joints = [j for j in ["root", "pelvis", "spine_01", "spine_02", "spine_03", "neck_01", "head"] if cmds.objExists(j)]
            meshes = _get_all_meshes()

            message = f"📊 PREVIEW DE EXPORTACIÓN:\n\n"
            message += f"🎮 Controladores detectados: {len(controllers)}\n"
            message += f"🦴 Joints principales: {len(joints)}\n"
            message += f"📐 Meshes con skinning: {len(meshes)}\n\n"

            if controllers:
                message += f"Primeros controladores:\n"
                for ctrl in controllers[:5]:
                    keyframes = cmds.keyframe(ctrl, query=True, keyframeCount=True) or 0
                    message += f"• {ctrl} ({keyframes} keyframes)\n"
                if len(controllers) > 5:
                    message += f"... y {len(controllers)-5} más\n"
            else:
                message += "⚠️ No se detectaron controladores animados\n"

            cmds.confirmDialog(
                title="Preview de Exportación",
                message=message,
                button=['OK']
            )

        except Exception as e:
            cmds.confirmDialog(
                title="Error en Preview",
                message=f"❌ Error: {str(e)}",
                button=['OK']
            )

    # Botones
    cmds.button(label="👁️ Preview", command=preview_action,
                annotation="Ver qué se exportaría sin hacer la exportación")
    cmds.button(label="🚀 Exportar", command=export_action, backgroundColor=(0.4, 0.7, 0.4),
                annotation="Exportar animación para Unreal Engine")
    cmds.button(label="❌ Cancelar", command=cancel_action,
                annotation="Cerrar sin exportar")

    cmds.setParent('..')
    cmds.setParent('..')

    # Mostrar ventana
    cmds.showWindow(window)

    return window

# Función de compatibilidad con el script anterior
def export_anim_AS(expected_root_name='root', force_root_keys=True):
    """Función de compatibilidad - usa la exportación simplificada"""
    return export_advanced_skeleton_simple()

def analyze_scene_for_unreal():
    """Analiza la escena y genera un reporte para ayudar con el mapeo de huesos"""
    print("\n" + "="*60)
    print("🔍 ANÁLISIS DE ESCENA PARA UNREAL ENGINE")
    print("="*60)

    # Analizar joints
    joints = _get_all_joints()
    print(f"\n🦴 JOINTS ENCONTRADOS ({len(joints)}):")
    for joint in sorted(joints):
        print(f"  • {joint}")

    # Analizar controladores
    controllers = _get_controllers()
    print(f"\n🎮 CONTROLADORES ANIMADOS ({len(controllers)}):")
    for ctrl in sorted(controllers):
        keyframes = cmds.keyframe(ctrl, query=True, keyframeCount=True) or 0
        print(f"  • {ctrl} ({keyframes} keyframes)")

    # Analizar meshes
    meshes = _get_all_meshes()
    print(f"\n📐 MESHES CON SKINNING ({len(meshes)}):")
    for mesh in meshes:
        print(f"  • {mesh}")

    # Sugerencias para mapeo
    print(f"\n💡 SUGERENCIAS PARA UNREAL:")
    print("1. Verifica que los nombres de joints coincidan con tu SK_Penguin_Skeleton")
    print("2. Los joints que fallan en Unreal necesitan ser mapeados o eliminados")
    print("3. Usa la función _get_penguin_bone_mapping() para personalizar el mapeo")

    # Generar archivo de configuración
    config_path = os.path.join(os.path.dirname(_scene_paths()[0]), "bone_mapping_config.txt")
    try:
        with open(config_path, 'w') as f:
            f.write("# CONFIGURACIÓN DE MAPEO DE HUESOS PARA UNREAL\n")
            f.write("# Edita este archivo para personalizar el mapeo\n\n")
            f.write("# Joints encontrados en Maya:\n")
            for joint in sorted(joints):
                f.write(f"# {joint}\n")
            f.write("\n# Mapeo sugerido (Maya -> Unreal):\n")
            mapping = _get_penguin_bone_mapping()
            for maya_bone, unreal_bone in mapping.items():
                f.write(f'"{maya_bone}": "{unreal_bone}",\n')
        print(f"\n📄 Archivo de configuración creado: {config_path}")
    except:
        print("❌ No se pudo crear el archivo de configuración")

    print("="*60)

# Función para crear botón en shelf
def create_shelf_button():
    """Crea un botón en el shelf actual para la exportación automática"""
    try:
        # Obtener el shelf actual
        current_shelf = cmds.tabLayout('ShelfLayout', query=True, selectTab=True)
        cmds.setParent(current_shelf)

        # Crear el botón principal con interfaz
        cmds.shelfButton(
            command='import importlib; import as_fbx_exporter; importlib.reload(as_fbx_exporter); as_fbx_exporter.show_unreal_export_dialog()',
            annotation='🚀 Exportar para Unreal Engine (Interfaz Gráfica)',
            label='UE\nExport',
            image='out_time.png',
            sourceType='python'
        )

        # Crear botón rápido (sin interfaz)
        cmds.shelfButton(
            command='import importlib; import as_fbx_exporter; importlib.reload(as_fbx_exporter); as_fbx_exporter.export_advanced_skeleton_optimized()',
            annotation='⚡ Exportar Rápido para Unreal (Sin Interfaz)',
            label='UE\nQuick',
            image='playbackNext.png',
            sourceType='python'
        )

        # Crear botón de análisis
        cmds.shelfButton(
            command='import as_fbx_exporter; as_fbx_exporter.analyze_scene_for_unreal()',
            annotation='Analizar escena para mapeo de huesos',
            label='Analyze\nBones',
            image='nodeGrapherBookmarksList.png',
            sourceType='python'
        )

        # Crear botón de debug
        cmds.shelfButton(
            command='import as_fbx_exporter; as_fbx_exporter.print_all_debug()',
            annotation='Mostrar debug completo para copiar al chat',
            label='Debug\nInfo',
            image='commandButton.png',
            sourceType='python'
        )

        print("✅ Botones creados en el shelf:")
        print("  • 'UE Export' - 🚀 Exportar para Unreal (Interfaz Gráfica)")
        print("  • 'UE Quick' - ⚡ Exportar Rápido para Unreal (Sin Interfaz)")
        print("  • 'Analyze Bones' - Analizar escena")
        print("  • 'Debug Info' - Mostrar debug completo para copiar")
        print("\n💡 Recomendado: Usa 'UE Export' para opciones completas")

    except Exception as e:
        print(f"❌ Error creando botones: {str(e)}")

def check_fbx_commands():
    """Verifica qué comandos FBX están disponibles en esta versión de Maya"""
    print("\n" + "="*60)
    print("🔍 VERIFICACIÓN DE COMANDOS FBX DISPONIBLES")
    print("="*60)

    # Lista de comandos FBX comunes
    fbx_commands = [
        'FBXResetExport',
        'FBXExportCameras -v false',
        'FBXExportLights -v false',
        'FBXExportConstraints -v false',
        'FBXExportSkins -v true',
        'FBXExportShapes -v false',
        'FBXExportDeformers -v true',  # Este es el que falla
        'FBXExportBakeComplexAnimation -v true',
        'FBXExportBakeResampleAnimation -v true',
        'FBXExportQuaternion -v resample',
        'FBXExportUpAxis y',
        'FBXExportTriangulate -v true',
        'FBXExportSmoothingGroups -v true'
    ]

    available = []
    unavailable = []

    for cmd in fbx_commands:
        if _safe_fbx_command(cmd):
            available.append(cmd)
        else:
            unavailable.append(cmd)

    print(f"\n✅ COMANDOS DISPONIBLES ({len(available)}):")
    for cmd in available:
        print(f"  • {cmd}")

    print(f"\n❌ COMANDOS NO DISPONIBLES ({len(unavailable)}):")
    for cmd in unavailable:
        print(f"  • {cmd}")

    print(f"\n📊 RESUMEN: {len(available)}/{len(fbx_commands)} comandos disponibles")
    print("="*60)

def test_export_with_debug():
    """Función de prueba con debug detallado"""
    print("\n" + "="*60)
    print("🧪 PRUEBA DE EXPORTACIÓN CON DEBUG")
    print("="*60)

    try:
        # Verificar comandos FBX primero
        print("\n0️⃣ VERIFICACIÓN FBX:")
        check_fbx_commands()

        # Análisis previo
        print("\n1️⃣ ANÁLISIS PREVIO:")
        joints_before = _get_all_joints()
        meshes_before = _get_all_meshes()
        print(f"   Joints: {len(joints_before)}")
        print(f"   Meshes: {len(meshes_before)}")

        # Limpieza paso a paso
        print("\n2️⃣ LIMPIEZA DE NOMBRES:")
        joints_renamed = _clean_joint_names()
        print(f"   Joints renombrados: {joints_renamed}")

        meshes_renamed = _clean_mesh_names()
        print(f"   Meshes renombrados: {meshes_renamed}")

        print("\n3️⃣ ELIMINACIÓN DE HUESOS NO SOPORTADOS:")
        bones_removed = _remove_unsupported_bones()
        print(f"   Huesos eliminados: {bones_removed}")

        print("\n4️⃣ LIMPIEZA DE DUPLICADOS:")
        duplicates_cleaned = _clean_duplicates_and_prepare_for_export()
        print(f"   Duplicados limpiados: {duplicates_cleaned}")

        # Análisis posterior
        print("\n5️⃣ ANÁLISIS POSTERIOR:")
        joints_after = _get_all_joints()
        meshes_after = _get_all_meshes()
        print(f"   Joints finales: {len(joints_after)}")
        print(f"   Meshes finales: {len(meshes_after)}")

        # Exportar
        print("\n6️⃣ EXPORTANDO...")
        result = export_animation_auto(export_mesh=True, export_controllers=True)

        print(f"\n✅ PRUEBA COMPLETADA: {result}")

    except Exception as e:
        print(f"\n❌ ERROR EN PRUEBA: {e}")
        import traceback
        traceback.print_exc()

def get_debug_summary():
    """Obtiene el resumen de debug de la última exportación"""
    global _last_export_debug
    if '_last_export_debug' in globals():
        return _last_export_debug
    else:
        return "No hay información de debug disponible. Ejecuta una exportación primero."

def print_all_debug():
    """Imprime todo el debug importante al final para copiar fácilmente"""
    print("\n" + "="*80)
    print("🔍 DEBUG COMPLETO PARA COPIAR AL CHAT")
    print("="*80)

    # 1. Información de la escena
    print("\n📊 INFORMACIÓN DE LA ESCENA:")
    joints = _get_all_joints()
    all_animated, all_controllers, animated_joints = _get_all_animated_objects()
    meshes = _get_all_meshes()
    print(f"Joints encontrados: {len(joints)}")
    print(f"Controladores animados: {len(all_controllers)}")
    print(f"Joints animados: {len(animated_joints)}")
    print(f"Total objetos animados: {len(all_animated)}")
    print(f"Meshes con skinning: {len(meshes)}")

    # 2. Lista de joints
    print(f"\n🦴 LISTA DE JOINTS ({len(joints)}):")
    for i, joint in enumerate(sorted(joints)[:20]):  # Solo primeros 20
        print(f"  {i+1:2d}. {joint}")
    if len(joints) > 20:
        print(f"  ... y {len(joints)-20} más")

    # 3. Lista de controladores
    print(f"\n🎮 CONTROLADORES ANIMADOS ({len(all_controllers)}):")
    for i, ctrl in enumerate(sorted(all_controllers)[:10]):  # Solo primeros 10
        keyframes = cmds.keyframe(ctrl, query=True, keyframeCount=True) or 0
        print(f"  {i+1:2d}. {ctrl} ({keyframes} keyframes)")
    if len(all_controllers) > 10:
        print(f"  ... y {len(all_controllers)-10} más")

    # 3.5. Lista de joints animados
    print(f"\n🎯 JOINTS ANIMADOS ({len(animated_joints)}):")
    for i, joint in enumerate(sorted(animated_joints)[:15]):  # Solo primeros 15
        keyframes = 0
        for attr in ["tx", "ty", "tz", "rx", "ry", "rz"]:
            try:
                keyframes += cmds.keyframe(f"{joint}.{attr}", query=True, keyframeCount=True) or 0
            except:
                pass
        print(f"  {i+1:2d}. {joint} ({keyframes} keyframes)")
    if len(animated_joints) > 15:
        print(f"  ... y {len(animated_joints)-15} más")

    # 4. Lista de meshes
    print(f"\n📐 MESHES CON SKINNING ({len(meshes)}):")
    for i, mesh in enumerate(meshes):
        print(f"  {i+1:2d}. {mesh}")

    # 5. Verificación de comandos FBX
    print(f"\n🔧 COMANDOS FBX:")
    fbx_test_commands = [
        'FBXResetExport',
        'FBXExportSkins -v true',
        'FBXExportBakeComplexAnimation -v true',
        'FBXExportDeformers -v true'  # El problemático
    ]

    for cmd in fbx_test_commands:
        status = "✅" if _safe_fbx_command(cmd) else "❌"
        print(f"  {status} {cmd}")

    # 6. Resumen de última exportación
    print(f"\n📋 ÚLTIMA EXPORTACIÓN:")
    print(get_debug_summary())

    # 7. Timeline info
    try:
        start, end = _timeline()
        print(f"\n⏰ TIMELINE: Frames {start} a {end} ({end-start+1} frames)")
    except:
        print(f"\n⏰ TIMELINE: No disponible")

    print("\n" + "="*80)
    print("📝 COPIA TODO ESTE DEBUG AL CHAT PARA ANÁLISIS")
    print("="*80)

if __name__ == "__main__":
    # Si se ejecuta directamente, ejecutar prueba con debug y mostrar resumen
    test_export_with_debug()
    print_all_debug()
