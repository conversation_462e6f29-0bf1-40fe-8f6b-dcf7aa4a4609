import os, time
import maya.cmds as cmds
import maya.mel as mel

WIN = 'fbxExportWin'
PBAR = 'fbxProgressBar'
STAT = 'fbxStatusText'

def _ensure_fbx():
    """Asegura que el plugin FBX esté cargado"""
    if not cmds.pluginInfo("fbxmaya", q=True, l=True):
        cmds.loadPlugin("fbxmaya")

def _scene_paths():
    """Obtiene las rutas de la escena actual y el archivo FBX de destino"""
    sp = cmds.file(q=True, sn=True)
    if not sp:
        cmds.error("Guarda la escena antes de exportar.")
    base, _ = os.path.splitext(sp)
    return sp, base + ".fbx"

def _timeline():
    """Obtiene el rango de tiempo de la timeline"""
    s = int(cmds.playbackOptions(q=True, minTime=True))
    e = int(cmds.playbackOptions(q=True, maxTime=True))
    return s, e

def _ui_open(msg="Iniciando..."):
    """Abre la ventana de progreso"""
    if cmds.window(WIN, exists=True):
        cmds.deleteUI(WIN)
    cmds.window(WIN, title='Exportación FBX Automática', widthHeight=(400, 120), toolbox=True)
    cmds.columnLayout(adj=True)
    cmds.text(STAT, label=msg, h=20)
    cmds.separator(h=8, style='in')
    cmds.progressBar(PBAR, maxValue=100, h=20)
    cmds.showWindow(WIN)
    cmds.refresh(); time.sleep(0.05)

def _ui_update(pct=None, msg=None):
    """Actualiza la interfaz de progreso"""
    if msg: cmds.text(STAT, e=True, label=msg)
    if pct is not None: cmds.progressBar(PBAR, e=True, progress=pct)
    cmds.refresh(); time.sleep(0.02)

def _ui_close():
    """Cierra la ventana de progreso"""
    if cmds.window(WIN, exists=True):
        cmds.deleteUI(WIN)

def _get_all_joints():
    """Obtiene todos los joints de la escena"""
    return cmds.ls(type="joint") or []

def _get_all_meshes():
    """Obtiene todas las mallas de la escena que tienen skinning"""
    all_meshes = cmds.ls(type="mesh")
    skinned_meshes = []

    for mesh in all_meshes:
        # Obtener el transform parent
        transforms = cmds.listRelatives(mesh, parent=True, type="transform") or []
        for transform in transforms:
            # Verificar si tiene skin cluster
            history = cmds.listHistory(transform, pruneDagObjects=True) or []
            skin_clusters = cmds.ls(history, type="skinCluster")
            if skin_clusters:
                skinned_meshes.append(transform)
                break

    return skinned_meshes

def _clean_mesh_names():
    """Limpia los nombres de las mallas para evitar problemas en Unreal"""
    meshes = cmds.ls(type="mesh")
    renamed_count = 0

    for mesh in meshes:
        # Obtener el transform parent
        transforms = cmds.listRelatives(mesh, parent=True, type="transform") or []
        for transform in transforms:
            # Si el nombre contiene caracteres problemáticos o está vacío
            current_name = transform.split("|")[-1]  # Obtener solo el nombre, sin path

            # Verificar si necesita renombrado
            if (not current_name or
                current_name.lower() == "geometry" or
                ":" in current_name or
                current_name.startswith("polySurface")):

                # Crear un nombre limpio
                new_name = f"Mesh_{renamed_count:02d}"
                try:
                    cmds.rename(transform, new_name)
                    print(f"Renombrado: {current_name} → {new_name}")
                    renamed_count += 1
                except:
                    pass

    return renamed_count

def _get_penguin_bone_mapping():
    """Mapeo específico para el esqueleto de pingüino SK_Penguin_Skeleton"""
    return {
        # Mapeo basado en los huesos que Unreal espera vs los que tienes en Maya
        # Columna vertebral - mapear a los huesos que SÍ existen en SK_Penguin_Skeleton
        "Spine1": "spine_01",  # Si existe en tu skeleton
        "Spine2": "spine_02",  # Si existe en tu skeleton
        "Chest": "spine_03",   # Si existe en tu skeleton
        "Neck": "neck_01",     # Si existe en tu skeleton
        "Head": "head",        # Si existe en tu skeleton

        # Extremidades - mapear solo si existen en SK_Penguin_Skeleton
        "Scapula": "clavicle_l",  # Ejemplo, ajustar según tu skeleton
        "Shoulder": "upperarm_l",
        "Elbow": "lowerarm_l",
        "Wrist": "hand_l",

        # Piernas
        "Hip": "thigh_l",
        "Knee": "calf_l",
        "Ankle": "foot_l",
        "Heel": "heel_l",
        "Toes": "ball_l",
        "ToesEnd": "ball_l_end",

        # Dedos - solo si existen en tu skeleton
        "MiddleFinger1": "index_01_l",
        "MiddleFinger2": "index_02_l",
        "MiddleFinger3": "index_03_l",
        "MiddleFinger4": "index_04_l",

        # Ojos - solo si existen
        "Eye": "eye_l",
        "EyeEnd": "eye_l_end",
    }

def _clean_joint_names():
    """Limpia nombres de joints para evitar duplicados y conflictos"""
    joints = cmds.ls(type="joint")
    renamed_count = 0

    # Usar mapeo específico para pingüino
    name_mapping = _get_penguin_bone_mapping()

    # Mapeo adicional de Advanced Skeleton a nombres estándar
    advanced_skeleton_mapping = {
        # Columna vertebral
        "Root_M": "root",
        "RootX_M": "pelvis",
        "Spine1_M": "spine_01",
        "Spine2_M": "spine_02",
        "Chest_M": "spine_03",
        "Neck_M": "neck_01",
        "Head_M": "head",
        "HeadEnd_M": "head_end",

        # Brazo izquierdo
        "Scapula_L": "clavicle_l",
        "Shoulder_L": "upperarm_l",
        "Elbow_L": "lowerarm_l",
        "Wrist_L": "hand_l",

        # Brazo derecho
        "Scapula_R": "clavicle_r",
        "Shoulder_R": "upperarm_r",
        "Elbow_R": "lowerarm_r",
        "Wrist_R": "hand_r",

        # Pierna izquierda
        "Hip_L": "thigh_l",
        "Knee_L": "calf_l",
        "Ankle_L": "foot_l",
        "Toes_L": "ball_l",

        # Pierna derecha
        "Hip_R": "thigh_r",
        "Knee_R": "calf_r",
        "Ankle_R": "foot_r",
        "Toes_R": "ball_r",
    }

    # Combinar ambos mapeos
    name_mapping.update(advanced_skeleton_mapping)

    # Aplicar mapeo de nombres
    for old_name, new_name in name_mapping.items():
        if cmds.objExists(old_name):
            try:
                # Verificar que el nuevo nombre no exista ya
                if not cmds.objExists(new_name):
                    cmds.rename(old_name, new_name)
                    print(f"Joint renombrado: {old_name} → {new_name}")
                    renamed_count += 1
            except:
                pass

    # Limpiar nombres problemáticos
    for joint in joints:
        current_name = joint.split("|")[-1]

        # Remover namespace si existe
        if ":" in current_name:
            clean_name = current_name.split(":")[-1]
            try:
                if not cmds.objExists(clean_name):
                    cmds.rename(joint, clean_name)
                    print(f"Namespace removido: {current_name} → {clean_name}")
                    renamed_count += 1
            except:
                pass

    return renamed_count

def _get_controllers():
    """Busca controladores comunes en la escena"""
    controllers = []

    # Patrones comunes de controladores
    controller_patterns = [
        "*_ctrl", "*_control", "*_con", "*Ctrl*", "*Control*",
        "*:*_ctrl", "*:*_control", "*:*_con", "*:*Ctrl*", "*:*Control*"
    ]

    for pattern in controller_patterns:
        found = cmds.ls(pattern, type="transform") or []
        controllers.extend(found)

    # Remover duplicados
    controllers = list(set(controllers))

    # Filtrar solo los que tienen keyframes
    animated_controllers = []
    for ctrl in controllers:
        if cmds.keyframe(ctrl, query=True, keyframeCount=True):
            animated_controllers.append(ctrl)

    return animated_controllers

def _bake_controllers_to_joints(controllers, joints, start, end):
    """Bakea la animación de controladores a joints"""
    if not controllers or not joints:
        return

    # Bakear controladores primero
    try:
        cmds.bakeResults(
            controllers,
            time=(start, end),
            sampleBy=1,
            simulation=True,
            preserveOutsideKeys=True,
            sparseAnimCurveBake=False,
            removeBakedAttributeFromLayer=False,
            bakeOnOverrideLayer=False,
            controlPoints=False,
            shape=False
        )
    except:
        pass  # Algunos controladores pueden no ser bakeables

    # Luego bakear joints
    try:
        cmds.bakeResults(
            joints,
            time=(start, end),
            sampleBy=1,
            simulation=True,
            preserveOutsideKeys=True,
            sparseAnimCurveBake=False,
            removeBakedAttributeFromLayer=False,
            bakeOnOverrideLayer=False,
            controlPoints=False,
            shape=False
        )
    except:
        pass

def _configure_fbx_export(start, end, export_mesh=True):
    """Configura las opciones de exportación FBX optimizadas para Unreal"""
    mel.eval('FBXResetExport;')

    # Configuración básica
    mel.eval('FBXExportCameras -v false;')
    mel.eval('FBXExportLights -v false;')
    mel.eval('FBXExportConstraints -v false;')
    mel.eval('FBXExportInputConnections -v false;')

    # Configuración de geometría
    if export_mesh:
        mel.eval('FBXExportSmoothingGroups -v true;')
        mel.eval('FBXExportHardEdges -v false;')
        mel.eval('FBXExportTangents -v false;')
        mel.eval('FBXExportSmoothMesh -v true;')
        mel.eval('FBXExportInstances -v false;')
        mel.eval('FBXExportReferencedAssetsContent -v true;')
        # Configuración específica para Unreal
        mel.eval('FBXExportTriangulate -v true;')  # Triangular para Unreal
        mel.eval('FBXExportEmbeddedTextures -v false;')  # No embeber texturas

    # Configuración de skinning y deformadores
    mel.eval('FBXExportSkins -v true;')
    mel.eval('FBXExportShapes -v false;')  # Blend shapes off por defecto
    mel.eval('FBXExportDeformers -v true;')  # Incluir deformadores

    # Configuración de animación optimizada para Unreal
    mel.eval('FBXExportBakeComplexAnimation -v true;')
    mel.eval(f'FBXExportBakeComplexStart -v {start};')
    mel.eval(f'FBXExportBakeComplexEnd -v {end};')
    mel.eval('FBXExportBakeComplexStep -v 1;')
    mel.eval('FBXExportBakeResampleAnimation -v true;')

    # Configuración de rotaciones - importante para Unreal
    mel.eval('FBXExportQuaternion -v resample;')
    mel.eval('FBXExportUpAxis y;')  # Unreal usa Y-up

    # Configuración de unidades - Unreal usa centímetros
    mel.eval('FBXExportScaleFactor 1.0;')
    mel.eval('FBXExportConvertUnitString cm;')

    # Configuraciones adicionales para evitar problemas en Unreal
    mel.eval('FBXExportGenerateLog -v true;')  # Generar log para debug
    mel.eval('FBXExportSplitAnimationIntoTakes -v false;')  # Una sola toma
    mel.eval('FBXExportUseSceneName -v false;')  # No usar nombre de escena
    mel.eval('FBXExportReferencedContainersContent -v false;')  # No contenedores referenciados

def export_animation_auto(export_mesh=True, export_controllers=True):
    """
    Exporta animación automáticamente detectando controladores y joints

    Args:
        export_mesh (bool): Si exportar también la geometría
        export_controllers (bool): Si bakear controladores a joints
    """
    _ui_open('⏳ Iniciando exportación automática...')

    try:
        # 1. Preparación inicial
        _ensure_fbx()
        scene_path, fbx_path = _scene_paths()
        start, end = _timeline()

        _ui_update(10, '🔍 Detectando elementos de la escena...')

        # 2. Detectar elementos
        all_joints = _get_all_joints()
        controllers = _get_controllers() if export_controllers else []
        meshes = _get_all_meshes() if export_mesh else []

        if not all_joints:
            cmds.error("No se encontraron joints en la escena.")

        print(f"Encontrados: {len(all_joints)} joints, {len(controllers)} controladores, {len(meshes)} meshes")

        _ui_update(15, '🧹 Limpiando nombres para Unreal...')

        # 3. Limpiar nombres para evitar conflictos en Unreal
        joints_renamed = _clean_joint_names()
        meshes_renamed = _clean_mesh_names() if export_mesh else 0

        print(f"Limpieza completada: {joints_renamed} joints renombrados, {meshes_renamed} meshes renombrados")

        # Actualizar listas después de la limpieza
        all_joints = _get_all_joints()
        meshes = _get_all_meshes() if export_mesh else []

        _ui_update(25, f'🎛️ Bakeando animación ({len(controllers)} controladores → {len(all_joints)} joints)...')

        # 3. Bakear animación
        if controllers and all_joints:
            _bake_controllers_to_joints(controllers, all_joints, start, end)
        elif all_joints:
            # Solo bakear joints si no hay controladores
            try:
                cmds.bakeResults(
                    all_joints,
                    time=(start, end),
                    sampleBy=1,
                    simulation=True,
                    preserveOutsideKeys=True
                )
            except:
                pass

        _ui_update(50, '📦 Preparando selección para exportar...')

        # 4. Seleccionar elementos para exportar
        export_selection = []
        export_selection.extend(all_joints)

        if export_mesh and meshes:
            export_selection.extend(meshes)

        if not export_selection:
            cmds.error("No hay elementos válidos para exportar.")

        cmds.select(export_selection, replace=True)

        _ui_update(70, '⚙️ Configurando opciones FBX...')

        # 5. Configurar FBX
        _configure_fbx_export(start, end, export_mesh)

        _ui_update(85, '💾 Exportando archivo FBX...')

        # 6. Exportar
        mel.eval('FBXExport -f "{}" -s;'.format(fbx_path.replace('\\', '/')))

        _ui_update(100, '✅ Exportación completada')

        # 7. Mensaje final
        cmds.inViewMessage(
            amg=f'<hl>FBX exportado para Unreal</hl><br>📁 {os.path.basename(fbx_path)}<br>🎬 Frames: {start}-{end}<br>🦴 Joints: {len(all_joints)}<br>🎮 Controladores: {len(controllers)}<br>📐 Meshes: {len(meshes)}<br>🧹 Limpieza: {joints_renamed + meshes_renamed} elementos renombrados',
            pos='midCenter',
            fade=True,
            fadeStayTime=6
        )

        print(f"✅ Exportación FBX completada para Unreal: {fbx_path}")
        print(f"📊 Resumen: {len(all_joints)} joints, {len(controllers)} controladores, {len(meshes)} meshes")
        print(f"🧹 Limpieza: {joints_renamed} joints + {meshes_renamed} meshes renombrados")
        return fbx_path

    except Exception as e:
        _ui_update(0, f'❌ Error: {str(e)}')
        cmds.error(f"Error durante la exportación: {str(e)}")

    finally:
        _ui_close()

# Función de compatibilidad con el script anterior
def export_anim_AS(expected_root_name='root', force_root_keys=True):
    """Función de compatibilidad - usa la nueva función automática"""
    return export_animation_auto(export_mesh=True, export_controllers=True)

def analyze_scene_for_unreal():
    """Analiza la escena y genera un reporte para ayudar con el mapeo de huesos"""
    print("\n" + "="*60)
    print("🔍 ANÁLISIS DE ESCENA PARA UNREAL ENGINE")
    print("="*60)

    # Analizar joints
    joints = _get_all_joints()
    print(f"\n🦴 JOINTS ENCONTRADOS ({len(joints)}):")
    for joint in sorted(joints):
        print(f"  • {joint}")

    # Analizar controladores
    controllers = _get_controllers()
    print(f"\n🎮 CONTROLADORES ANIMADOS ({len(controllers)}):")
    for ctrl in sorted(controllers):
        keyframes = cmds.keyframe(ctrl, query=True, keyframeCount=True) or 0
        print(f"  • {ctrl} ({keyframes} keyframes)")

    # Analizar meshes
    meshes = _get_all_meshes()
    print(f"\n📐 MESHES CON SKINNING ({len(meshes)}):")
    for mesh in meshes:
        print(f"  • {mesh}")

    # Sugerencias para mapeo
    print(f"\n💡 SUGERENCIAS PARA UNREAL:")
    print("1. Verifica que los nombres de joints coincidan con tu SK_Penguin_Skeleton")
    print("2. Los joints que fallan en Unreal necesitan ser mapeados o eliminados")
    print("3. Usa la función _get_penguin_bone_mapping() para personalizar el mapeo")

    # Generar archivo de configuración
    config_path = os.path.join(os.path.dirname(_scene_paths()[0]), "bone_mapping_config.txt")
    try:
        with open(config_path, 'w') as f:
            f.write("# CONFIGURACIÓN DE MAPEO DE HUESOS PARA UNREAL\n")
            f.write("# Edita este archivo para personalizar el mapeo\n\n")
            f.write("# Joints encontrados en Maya:\n")
            for joint in sorted(joints):
                f.write(f"# {joint}\n")
            f.write("\n# Mapeo sugerido (Maya -> Unreal):\n")
            mapping = _get_penguin_bone_mapping()
            for maya_bone, unreal_bone in mapping.items():
                f.write(f'"{maya_bone}": "{unreal_bone}",\n')
        print(f"\n📄 Archivo de configuración creado: {config_path}")
    except:
        print("❌ No se pudo crear el archivo de configuración")

    print("="*60)

# Función para crear botón en shelf
def create_shelf_button():
    """Crea un botón en el shelf actual para la exportación automática"""
    try:
        # Obtener el shelf actual
        current_shelf = cmds.tabLayout('ShelfLayout', query=True, selectTab=True)
        cmds.setParent(current_shelf)

        # Crear el botón principal
        cmds.shelfButton(
            command='import as_fbx_exporter; as_fbx_exporter.export_animation_auto()',
            annotation='Exportar Animación FBX para Unreal Engine',
            label='FBX\nUnreal',
            image='out_time.png',
            sourceType='python'
        )

        # Crear botón de análisis
        cmds.shelfButton(
            command='import as_fbx_exporter; as_fbx_exporter.analyze_scene_for_unreal()',
            annotation='Analizar escena para mapeo de huesos',
            label='Analyze\nBones',
            image='nodeGrapherBookmarksList.png',
            sourceType='python'
        )

        print("✅ Botones creados en el shelf:")
        print("  • 'FBX Unreal' - Exportar animación")
        print("  • 'Analyze Bones' - Analizar escena")

    except Exception as e:
        print(f"❌ Error creando botones: {str(e)}")

if __name__ == "__main__":
    # Si se ejecuta directamente, crear los botones y analizar la escena
    create_shelf_button()
    analyze_scene_for_unreal()
