import os, time
import maya.cmds as cmds
import maya.mel as mel

WIN = 'fbxExportWin'
PBAR = 'fbxProgressBar'
STAT = 'fbxStatusText'

def _ensure_fbx():
    if not cmds.pluginInfo("fbxmaya", q=True, l=True):
        cmds.loadPlugin("fbxmaya")

def _scene_paths():
    sp = cmds.file(q=True, sn=True)
    if not sp:
        cmds.error("Guarda la escena antes de exportar.")
    base, _ = os.path.splitext(sp)
    return sp, base + ".fbx"

def _timeline():
    s = int(cmds.playbackOptions(q=True, minTime=True))
    e = int(cmds.playbackOptions(q=True, maxTime=True))
    return s, e

def _ui_open(msg="Iniciando..."):
    if cmds.window(WIN, exists=True):
        cmds.deleteUI(WIN)
    cmds.window(WIN, title='Exportación FBX', widthHeight=(360, 110), toolbox=True)
    cmds.columnLayout(adj=True)
    cmds.text(STAT, label=msg, h=20)
    cmds.separator(h=8, style='in')
    cmds.progressBar(PBAR, maxValue=100, h=20)
    cmds.showWindow(WIN)
    cmds.refresh(); time.sleep(0.05)

def _ui_update(pct=None, msg=None):
    if msg: cmds.text(STAT, e=True, label=msg)
    if pct is not None: cmds.progressBar(PBAR, e=True, progress=pct)
    cmds.refresh(); time.sleep(0.02)

def _ui_close():
    if cmds.window(WIN, exists=True):
        cmds.deleteUI(WIN)

def _all_joints_under(roots):
    j = []
    for r in roots:
        j += [r] + (cmds.listRelatives(r, ad=True, type="joint") or [])
    return [n for n in j if cmds.nodeType(n) == "joint"]

def _find_joint_by_name(name):
    joints = cmds.ls(type="joint")
    for j in joints:
        if j.split("|")[-1] == name:
            return j
    return None

def _force_keys_on_joint(joint, start, end):
    if not joint: return
    for t in (start, end):
        cmds.currentTime(t, e=True)
        for attr in ("tx","ty","tz","rx","ry","rz","sx","sy","sz"):
            try: cmds.setKeyframe(joint, at=attr)
            except: pass

def _fbx_opts(start, end):
    mel.eval('FBXResetExport;')
    mel.eval('FBXExportCameras -v false; FBXExportLights -v false;')
    mel.eval('FBXExportSkins -v true;')
    mel.eval('FBXExportConstraints -v false;')
    mel.eval('FBXExportShapes -v false;')
    mel.eval('FBXExportBakeComplexAnimation -v true;')
    mel.eval(f'FBXExportBakeComplexStart -v {start};')
    mel.eval(f'FBXExportBakeComplexEnd -v {end};')
    mel.eval('FBXExportBakeComplexStep -v 1;')
    mel.eval('FBXExportQuaternion -v resample;')
    mel.eval('FBXExportUpAxis y;')
    mel.eval('FBXExportInputConnections -v false;')

def export_anim_AS(expected_root_name='root', force_root_keys=True):
    _ui_open('⏳ Preparando...')
    try:
        _ensure_fbx()
        scene_path, fbx_path = _scene_paths()
        start, end = _timeline()
        root_joint = _find_joint_by_name(expected_root_name)
        if not root_joint:
            cmds.error(f"No se encontró el joint raíz '{expected_root_name}'.")
        export_joints = _all_joints_under([root_joint])
        _ui_update(40, '🎛️ Bakeando joints...')
        cmds.bakeResults(export_joints, t=(start, end), sampleBy=1, simulation=True)
        if force_root_keys:
            _force_keys_on_joint(root_joint, start, end)
        cmds.select(export_joints, r=True)
        _ui_update(75, '⚙️ Configurando FBX...')
        _fbx_opts(start, end)
        mel.eval('FBXExport -f "{}" -s;'.format(fbx_path.replace('\\', '/')))
        _ui_update(100, '✅ Exportación completada')
        cmds.inViewMessage(amg='<hl>FBX exportado</hl>: {}'.format(os.path.basename(fbx_path)), pos='midCenter', fade=True)
        return fbx_path
    finally:
        _ui_close()
