/* XPM */
static char *biped_98_35_OffK0[] = {
/* columns rows colors chars-per-pixel */
"11 11 43 1",
"  c #B77D37",
". c #B77C38",
"X c #B77D39",
"o c #B77E3A",
"O c #B77E3B",
"+ c #B67E3C",
"@ c #B67F3D",
"# c #B77E3C",
"$ c #B87A32",
"% c #B87A33",
"& c #B87B33",
"* c #B87B35",
"= c #B87C37",
"- c #B87D37",
"; c #AF8859",
": c #AF895A",
"> c #B3824A",
", c #B3834B",
"< c #B3844D",
"1 c #B2844F",
"2 c #B18551",
"3 c #B18653",
"4 c #B28450",
"5 c #B08755",
"6 c #B18655",
"7 c #B08859",
"8 c #A7937C",
"9 c #A7947D",
"0 c #A7947E",
"q c #A99074",
"w c #A99175",
"e c #A99177",
"r c #A8937A",
"t c #A8937B",
"y c #9F9D9B",
"u c #9F9E9B",
"i c gray62",
"p c #A59683",
"a c #A59684",
"s c #A09D99",
"d c #A19D99",
"f c #A09D9A",
"g c gray75",
/* pixels */
"ggggggggggg",
"gidt<-<8dig",
"gy96-&-69ig",
"gp;X&&&X;ag",
"g;+$&&&&+;g",
"gX&$&&$$*+g",
"g+$$$$$$*+g",
"g4*$$$$&X2g",
"gt<=&$$*2tg",
"gdq>=&&>edg",
"ggggggggggg"
};
