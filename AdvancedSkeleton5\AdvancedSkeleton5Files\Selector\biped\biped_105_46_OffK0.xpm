/* XPM */
static char *biped_105_46_OffK0[] = {
/* columns rows colors chars-per-pixel */
"34 20 183 2",
"   c gray54",
".  c #8B8B8B",
"X  c #8C8C8B",
"o  c gray55",
"O  c #8C8C8D",
"+  c #8C8D8C",
"@  c #8D8C8D",
"#  c #8D8D8D",
"$  c #8F8F8E",
"%  c gray56",
"&  c #908F8F",
"*  c #909090",
"=  c #909191",
"-  c #919190",
";  c gray57",
":  c #919192",
">  c #929191",
",  c #929292",
"<  c #939292",
"1  c #939293",
"2  c #939393",
"3  c gray58",
"4  c #959595",
"5  c gray59",
"6  c #969797",
"7  c #979897",
"8  c #989898",
"9  c #999998",
"0  c gray60",
"q  c #9A9A9A",
"w  c #9B9B9B",
"e  c #9B9C9B",
"r  c #9D9D9D",
"t  c gray62",
"y  c #A1A1A2",
"u  c #A1A2A1",
"i  c #A2A2A2",
"p  c #A3A3A2",
"a  c #A3A3A4",
"s  c #A4A3A4",
"d  c #A4A4A4",
"f  c #A9A9A8",
"g  c #A9A9A9",
"h  c #ABABAA",
"j  c #ABACAB",
"k  c gray68",
"l  c #AEAEAE",
"z  c #AFAFAF",
"x  c #B0B0B1",
"c  c #B2B1B1",
"v  c gray70",
"b  c #B5B5B4",
"n  c #B5B6B5",
"m  c #B7B8B7",
"M  c #B9B9B9",
"N  c #BBBBBB",
"B  c #BCBDBD",
"V  c gray",
"C  c #BFBFBE",
"Z  c gray75",
"A  c #C0C1C0",
"S  c #C1C1C0",
"D  c #C3C3C2",
"F  c #C5C6C6",
"G  c #C6C6C6",
"H  c #C7C6C6",
"J  c #CBCBCB",
"K  c #CBCCCB",
"L  c #CCCDCC",
"P  c #CCCDCD",
"I  c #CECECF",
"U  c #D1D1D2",
"Y  c #D1D2D1",
"T  c #D5D5D5",
"R  c #D6D6D7",
"E  c #D6D7D7",
"W  c #D7D7D7",
"Q  c #D8D7D7",
"!  c #D8D9D8",
"~  c gray85",
"^  c #DDDEDE",
"/  c gray87",
"(  c #DEDFDF",
")  c #DFDFDF",
"_  c #DFE0E0",
"`  c #DFE1E1",
"'  c #E1E2E1",
"]  c #E3E2E2",
"[  c gray89",
"{  c #E3E4E3",
"}  c #E5E4E5",
"|  c #E5E5E4",
" . c #E7E6E6",
".. c #E7E7E7",
"X. c #E7E8E7",
"o. c #E8E7E8",
"O. c gray91",
"+. c #E9E8E8",
"@. c #E9E9E9",
"#. c #EAE9EA",
"$. c #EBEAEB",
"%. c #EBECEB",
"&. c #ECEBEB",
"*. c gray93",
"=. c #EDEEEE",
"-. c #EEECEC",
";. c #EEEDEE",
":. c #EEEFEF",
">. c gray94",
",. c #F0F0F1",
"<. c #F1F0F1",
"1. c #F1F2F1",
"2. c #F3F3F4",
"3. c #F3F4F4",
"4. c #F4F4F3",
"5. c #F4F4F4",
"6. c #F4F4F5",
"7. c #F6F6F5",
"8. c #F6F6F6",
"9. c #F7F7F6",
"0. c #F8F7F8",
"q. c #F8F9F9",
"w. c #F9F8F8",
"e. c #F9F9F8",
"r. c #F8FAF8",
"t. c #F9FAF9",
"y. c #F9FAFA",
"u. c #FAF8F9",
"i. c #FAF9F8",
"p. c #FAF9F9",
"a. c #FAF9FA",
"s. c #FBFCFC",
"d. c #FCFCFB",
"f. c gray99",
"g. c #FCFCFD",
"h. c #FCFDFD",
"j. c #FDFCFD",
"k. c #FDFDFC",
"l. c #FDFDFD",
"z. c #FCFCFF",
"x. c #FCFDFE",
"c. c #FCFDFF",
"v. c #FDFCFE",
"b. c #FDFCFF",
"n. c #FDFDFE",
"m. c #FDFDFF",
"M. c #FCFFFD",
"N. c #FDFEFC",
"B. c #FDFEFD",
"V. c #FDFFFC",
"C. c #FDFFFD",
"Z. c #FCFEFE",
"A. c #FCFEFF",
"S. c #FCFFFE",
"D. c #FDFEFE",
"F. c #FDFEFF",
"G. c #FDFFFE",
"H. c #FDFFFF",
"J. c #FEFCFD",
"K. c #FEFDFC",
"L. c #FEFDFD",
"P. c #FFFCFD",
"I. c #FFFDFD",
"U. c #FEFCFE",
"Y. c #FEFCFF",
"T. c #FEFDFE",
"R. c #FEFDFF",
"E. c #FFFDFE",
"W. c #FFFDFF",
"Q. c #FEFEFC",
"!. c #FEFEFD",
"~. c #FEFFFD",
"^. c #FFFEFC",
"/. c #FFFEFD",
"(. c #FFFFFD",
"). c #FEFEFE",
"_. c #FEFEFF",
"`. c #FEFFFE",
"'. c #FEFFFF",
"]. c #FFFEFE",
"[. c #FFFEFF",
"{. c #FFFFFE",
"}. c gray100",
/* pixels */
"Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z C C Z Z Z Z Z Z Z Z Z Z Z Z ",
"Z 8 8 8 5 5 : # O O . . . . O # O O # # @ @ @ * 1 5 8 t d g z n Z Z ",
"Z i i i d g k v M Z D K U T T T W W E E W W W / ] ] [ ..O.o.&.&.,.Z ",
"Z / / ( ` ] | O.$.=.2.2.2.y.y.y.i.i.y.y.y.y.y.P.).).).).).).).).).Z ",
"Z s.s.).).).K.).P.).s.).).).).s.K.K.s.).).).P.).).).).).).).K.).P.Z ",
"Z ).).).).).K.).).).).).).).).).).K.).).).).).).).).).).).).K.).).Z ",
"Z ).).).).).).K.).).).).K.).).).K.).).).K.).).K.).).).).).).).).).Z ",
"Z ).).).).).K.).s.).).).).).).).).).).).).).).).).).).).).K.).).).C ",
"Z ).).).).).).).).).s.).).P.).).).).).).).).).K.).P.d.).).).).).).C ",
"Z ).P.).).).s.).).).).).P.P.P.s.).).).).K.).).).P.).).).).).s.).).Z ",
"C ).).).).).).).).).).).).z.).).).).).).).).).K.).).).).s.).s.).).Z ",
"C ).d.).).).).).).s.s.).).).).).).).).).).).).).).K.).K.).K.K.K.).Z ",
"C ).P.P.).).).).).).K.s.).K.).).P.).).).).).).).).).K.).).).).s.).Z ",
"Z ).).).).).).).).s.).).d.).).).).).).K.).).).).).).K.).).s.).s.).Z ",
"Z | | O.&.<.2.2.2.y.).).).).).).s.K.).).).).s.).).).).K.).P.).).).Z ",
"Z k v v n N Z Z D F K Y E _ X.&.4.y.K.K.s.).).d.).).s.P.).).).).).Z ",
"Z : = = $ $ $ = $ : 3 e a j b B D I T W _ { O.<.2.d.d.).).s.).).).Z ",
"Z t t t t t t t t t t e 7 : $ # # : 5 8 i j n Z K T / { X.%.<.2.9.Z ",
"Z t t t t t t t t t t t t t t t t t t e 8 : # X .   - 9 a x Z K T Z ",
"Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z C C C Z Z Z Z Z Z Z Z Z Z Z "
};
